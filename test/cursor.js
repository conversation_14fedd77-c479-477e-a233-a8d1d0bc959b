const Baobab = require('baobab');

const tree = new Baobab({
  palette: {
    colors: ['yellow', 'purple'],
    name: 'Glorious colors',
    type: true,
  },

  app: {
    isLogged: true,
    nav: {
      selected: true,
      items: ['kevin', 'kevin2', 'kevin3'],
    },
  },
}, { asynchronous: false });

window.tree = tree;

const appCursor = tree.select('app');

appCursor.on('update', (data) => {
  console.log('appCursor updated %s', Date.now(), data);
});

// Create a new key on the tree
tree.set('newKey', true);

// Test the set key
tree.get();

// Create an new key on 'app' through the tree
console.log(Date.now());
tree.set(['app', 'newKey'], true);

// Create a new key but through the Cursor
appCursor.set('newKey', true);

// Create a new cursor
const paletteCursor = tree.select('palette');

paletteCursor.on('update', (data) => {
  console.log('paletteCursor updated %s', Date.now(), data);
});

// Update the key on appCursor
console.log(Date.now());
appCursor.set('newKey', false);

// Test the set key
tree.get();

// Create a new key but through the Cursor
paletteCursor.set('newKey', true);

// Lets try sub-appCursors
const subAppCursor = tree.select('app', 'nav');

subAppCursor.on('update', (data) => {
  console.log('subAppCursor updated %s', Date.now(), data);
});

// Update appCursor key
appCursor.set('newKey', true);

// Update appCursor key
subAppCursor.set('selected', true);
