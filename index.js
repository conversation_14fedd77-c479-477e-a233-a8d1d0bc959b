// polyfill
import agentPickerContext from './app/models/agentPickerContext';

Number.isNaN = Number.isNaN || ((value) => typeof value === 'number' && isNaN(value));

window.__noop = () => {
};

function App() {
}
window.__app = null;
window.__app = App;

// Safari Private Browsing Shim
require('./app/thirdparty/safari-localstorage-shim')();
require('./app/thirdparty/number-isinteger-polyfill')();

// Cursor tree & default
App.cursor = require('./app/cursor')();
App.cursorDefaults = require('./app/cursor')(true);

// Lib
App.utils = require('./app/lib/utils')(App);
App.api = require('./app/lib/api/index')(App);
App.events = require('./app/lib/events');
App.constants = require('./app/lib/constants');

// Include more actions here
App.models = {
  agentPicker: require('./app/models/agentpicker')(App),
};

App.actions = {
  userPreferences: require('./app/actions/userPreferences')(App),
  leads: require('./app/actions/leads')(App),
  // Screens
  onboarding: require('./app/actions/onboarding')(App),
  home: require('./app/actions/home')(App),
  landing: require('./app/actions/landing')(App),
  agent: require('./app/actions/agent')(App),
  listing: require('./app/actions/listing')(App),
  grid: require('./app/actions/grid')(App),
  map: require('./app/actions/map')(App),
  tagging: require('./app/actions/tagging')(App),
  featured: require('./app/actions/featured')(App),
  facebook: require('./app/actions/facebook')(App),
  buyer: require('./app/actions/buyer')(App),
  homeWorth: require('./app/actions/homeWorth')(App),
  memberSearch: require('./app/actions/memberSearch')(App),
  memberListings: require('./app/actions/memberListings')(App),
  // Panels
  panels: require('./app/actions/panels')(App),
  menu: require('./app/actions/menu')(App),
  login: require('./app/actions/login')(App),
  logout: require('./app/actions/logout')(App),
  help: require('./app/actions/help')(App),
  // Common
  common: require('./app/actions/common')(App),

  error: require('./app/actions/error')(App),
  demo: require('./app/actions/demo')(App),

  read: require('./app/actions/read')(App),
  analytics: require('./app/actions/analytics')(App),
};

agentPickerContext.setApplication(App);

// add to homescreen
require('./app/thirdparty/add-to-homescreen/addtohomescreen.js');
const React = require('react');
const ReactDOMServer = require('react-dom/server');
const AddToHomescreenComponent = require('./app/views/components/add_to_homescreen');

const AddToHomescreen = React.createFactory(AddToHomescreenComponent);

// this matches the landing page (which is just the root, http://homeasap.com) and the agent landing page (http://homeasap.com/agentname)
function homepageOrLandingPage(url) {
  return url.match(/^(\/)?([a-zA-Z0-9-]+)?(\/)?$/gi) && !url.match(/(member-search|member-listings)/i);
}
let route = window.location.href;
route = route.replace(window.location.origin, '');
// if homepage or landing page, show the add to homescreen
window.athModal = window.addToHomescreen({
  skipFirstVisit: true,
  maxDisplayCount: 2,
  lifespan: 20,
  icon: false,
  autostart: false,
  appID: 'com.homeasap.addtohome',
  message: ReactDOMServer.renderToStaticMarkup(AddToHomescreen()),
  onShow() {
    App.actions.common.blurContent();
    App.actions.common.disableBodyScroll();
  },
  onRemove() {
    App.actions.common.unblurContent();
    App.actions.common.enableBodyScroll();
  },
});

if (window.addToHomescreen.isCompatible && homepageOrLandingPage(route)) {
  window.athModal.show();
}

// Leaflet maps
App.leaflet = require('./app/lib/leaflet')(App);

// Google maps
App.googleParcel = require('./app/lib/google/google-parcel')(App);

// Load the router
App.router = require('./app/lib/router')(App);

// Bind any custom watchers
App.actions.common.bindWatchers();

// App.actions.common.routerPreStart();
// Load react views & sart the router
App.actions.common.onInit();

if (App.utils.useMobileSite()) {
  require('./app/views/index-mobile')(App, () => {
    App.router.start();
  });
} else {
  require('./app/views')(App, () => {
    App.router.start();
  });
}

console.log('-- App Load Time: %sms --', Date.now() - window.START_TIME);
