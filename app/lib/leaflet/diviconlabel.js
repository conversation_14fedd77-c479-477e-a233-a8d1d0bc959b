L.DivIcon.Label = L.DivIcon.extend({
  options: {
    /*
     labelAnchor: (Point) (top left position of the label within the wrapper, default is right)
     wrapperAnchor: (Point) (position of icon and label relative to Lat/Lng)
     iconAnchor: (Point) (top left position of icon within wrapper)
     labelText: (String) (label's text component, if this is null the element will not be created)
     */
    /* DivIcon options:
     iconSize: (Point) (can be set through CSS)
     iconAnchor: (Point) (centered by default if size is specified, can be set in CSS with negative margins)
     popupAnchor: (Point) (if not specified, popup opens in the anchor point)
     className: (String) (A custom class name to assign to the icon. 'leaflet-div-icon' by default.)
     html: (String) (A custom HTML code to put inside the div element, empty by default.)
     */
    labelClassName: 'label',
  },

  initialize(options) {
    L.Util.setOptions(this, options);
    L.Icon.prototype.initialize.call(this, this.options);
  },

  setLabelAsHidden() {
    this._labelHidden = true;
  },

  createIcon() {
    return this._createLabel(L.DivIcon.prototype.createIcon.call(this));
  },

  updateLabel(icon, text) {
    if (icon.nodename && icon.nodeName.toUpperCase() === 'DIV') {
      icon.lastChild.innerHTML = text;
      this.options.labelText = text;
    }
  },

  showLabel(icon) {
    if (!this._labelTextIsSet()) {
      return;
    }
    icon.lastChild.style.display = 'inline';
  },

  hideLabel(icon) {
    if (!this._labelTextIsSet()) {
      return;
    }
    icon.lastChild.style.display = 'none';
  },

  _createLabel(wrapper) {
    if (!this._labelTextIsSet()) {
      return wrapper;
    }

    const label = document.createElement('span');

    // set up label
    label.className = this.options.labelClassName;
    label.innerHTML = this.options.labelText;

    label.style.marginLeft = `${this.options.labelAnchor.x}px`;
    label.style.marginTop = `${this.options.labelAnchor.y}px`;

    if (this._labelHidden) {
      label.style.display = 'none';
      // Ensure that the pointer cursor shows
      label.style.cursor = 'pointer';
    }

    wrapper.appendChild(label);

    return wrapper;
  },

  _labelTextIsSet() {
    return typeof this.options.labelText !== 'undefined' && this.options.labelText !== null;
  },
});

L.DivIcon.Label.Default = L.DivIcon.Label.extend({
  options: {
    // This is the top left position of the label within the wrapper. By default it will display at the right
    // middle position of the default icon. x = width of icon + padding
    // If the icon height is greater than the label height you will need to set the y value.
    // y = (icon height - label height) / 2
    labelAnchor: new L.Point(0, 0),

    // This is now the top left position of the icon within the wrapper.
    // If the label height is greater than the icon you will need to set the y value.
    // y = (label height - icon height) / 2
    iconAnchor: new L.Point(0, 0),

    // label's text component, if this is null the element will not be created
    labelText: null,
  },
});
