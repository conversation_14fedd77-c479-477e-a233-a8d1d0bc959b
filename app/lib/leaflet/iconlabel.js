L.Icon.Label = L.Icon.extend({
  options: {
    /*
     labelAnchor: (Point) (top left position of the label within the wrapper, default is right)
     wrapperAnchor: (Point) (position of icon and label relative to Lat/Lng)
     iconAnchor: (Point) (top left position of icon within wrapper)
     labelText: (String) (label's text component, if this is null the element will not be created)
     */
    /* Icon options:
     iconUrl: (String) (required)
     iconSize: (Point) (can be set through CSS)
     iconAnchor: (Point) (centered by default if size is specified, can be set in CSS with negative margins)
     popupAnchor: (Point) (if not specified, popup opens in the anchor point)
     shadowUrl: (Point) (no shadow by default)
     shadowSize: (Point)
     */
    labelClassName: 'label',
  },

  initialize(options) {
    L.Util.setOptions(this, options);
    L.Icon.prototype.initialize.call(this, this.options);
  },

  setLabelAsHidden() {
    this._labelHidden = true;
  },

  createIcon() {
    return this._createLabel(L.Icon.prototype.createIcon.call(this));
  },

  createShadow() {
    if (!this.options.shadowUrl) {
      return null;
    }
    const shadow = L.Icon.prototype.createShadow.call(this);
    // need to reposition the shadow
    if (shadow) {
      shadow.style.marginLeft = `${-this.options.wrapperAnchor.x}px`;
      shadow.style.marginTop = `${-this.options.wrapperAnchor.y}px`;
    }
    return shadow;
  },

  updateLabel(icon, text) {
    if (icon.nodeName.toUpperCase() === 'DIV') {
      icon.childNodes[1].innerHTML = text;

      this.options.labelText = text;
    }
  },

  showLabel(icon) {
    if (!this._labelTextIsSet()) {
      return;
    }

    icon.childNodes[1].style.display = 'block';
  },

  hideLabel(icon) {
    if (!this._labelTextIsSet()) {
      return;
    }

    icon.childNodes[1].style.display = 'none';
  },

  _createLabel(img) {
    if (!this._labelTextIsSet()) {
      return img;
    }

    const wrapper = document.createElement('div');
    const label = document.createElement('span');

    // set up label
    label.className = this.options.labelClassName;
    label.innerHTML = this.options.labelText;

    label.style.marginLeft = `${this.options.labelAnchor.x}px`;
    label.style.marginTop = `${this.options.labelAnchor.y}px`;

    if (this._labelHidden) {
      label.style.display = 'none';
      // Ensure that the pointer cursor shows
      img.style.cursor = 'pointer';
    }

    wrapper.appendChild(img);
    wrapper.appendChild(label);

    return wrapper;

    /*
     var wrapper = document.createElement('div'),
     label = document.createElement('span');

     // set up wrapper anchor
     wrapper.style.marginLeft = (-this.options.wrapperAnchor.x) + 'px';
     wrapper.style.marginTop = (-this.options.wrapperAnchor.y) + 'px';

     wrapper.className = 'leaflet-marker-icon-wrapper leaflet-zoom-animated';

     // set up label
     label.className = 'leaflet-marker-iconlabel ' + this.options.labelClassName;

     label.innerHTML = this.options.labelText;

     label.style.marginLeft = this.options.labelAnchor.x + 'px';
     label.style.marginTop = this.options.labelAnchor.y + 'px';

     if (this._labelHidden) {
     label.style.display = 'none';
     // Ensure that the pointer cursor shows
     img.style.cursor = 'pointer';
     }

     //reset icons margins (as super makes them -ve)
     img.style.marginLeft = this.options.iconAnchor.x + 'px';
     img.style.marginTop = this.options.iconAnchor.y + 'px';
     */

    /*
    wrapper.appendChild(img);
    wrapper.appendChild(label);

    return wrapper;
    */
  },

  _labelTextIsSet() {
    return typeof this.options.labelText !== 'undefined' && this.options.labelText !== null;
  },
});

L.Icon.Label.Default = L.Icon.Label.extend({
  options: {
    // This is the top left position of the label within the wrapper. By default it will display at the right
    // middle position of the default icon. x = width of icon + padding
    // If the icon height is greater than the label height you will need to set the y value.
    // y = (icon height - label height) / 2
    labelAnchor: new L.Point(0, 0),

    // This is the position of the wrapper div. Use this to position icon + label relative to the Lat/Lng.
    // By default the point of the default icon is anchor
    wrapperAnchor: new L.Point(0, 0),

    // This is now the top left position of the icon within the wrapper.
    // If the label height is greater than the icon you will need to set the y value.
    // y = (label height - icon height) / 2
    iconAnchor: new L.Point(0, 0),

    // label's text component, if this is null the element will not be created
    labelText: '',

    /* From L.Icon.Default */
    iconUrl: '/images/pin.png',
    iconSize: new L.Point(24, 29),
    popupAnchor: new L.Point(0, 0),

    shadowUrl: null,
    shadowSize: null,
  },
});
