const extend = require('extend');

module.exports = function (opts) {
  opts = extend(false, {}, {
    position: 'topleft',
    className: '',
    title: '',
    content: '',
    container: null,
    handler() {
      console.log('Toolbar clicked.');
    },
  }, opts);

  // Adding control
  const Control = L.Control.extend({

    options: {
      position: opts.position,
      title: opts.title,
    },

    onAdd() {
      let container;

      if (opts.container) {
        container = opts.container;
      } else {
        container = L.DomUtil.create('div', 'custom-control leaflet-control');
      }

      // Creating controls
      this.customControl = L.DomUtil.create('a', opts.className, container);
      this.customControl.href = '#';

      if (opts.title) {
        this.customControl.title = this.options.title;
      }

      if (opts.content) {
        this.customControl.innerHTML = opts.content;
      }

      L.DomEvent.on(this.customControl, 'click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        opts.handler();
      }, this);

      return container;
    },

    onRemove() {
      // when removed
    },
  });

  return new Control();
};
