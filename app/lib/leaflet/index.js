const _ = require('lodash');
const LeafletStream = require('leaflet-geojson-stream');
// Include Leaflet and plugins
require('leaflet');
require('./circle');
require('./iconlabel');
require('./diviconlabel');
require('./markerlabel');
require('./mapbox-gl');

const debounce = require('lodash.debounce');
const defer = require('lodash.defer');
const forEach = require('lodash.foreach');

const React = require('react');
const ReactDOMServer = require('react-dom/server');
const MapPopup = require('../../views/components/MapPopup.jsx');
const YelpPopup = require('../../views/components/YelpPopup.jsx');

L.Icon.Default.imagePath = 'http://cdn.leafletjs.com/leaflet/v0.7.7/images';

const Toolbar = require('./toolbar');

const constants = require('../constants');

require('../../thirdparty/leaflet-contextmenu/leaflet.contextmenu');

module.exports = function (app) {
  /** *
   * Static methods for Leaflet
   * */
  function Leaflet() {
  }

  // Store leaflet map instance
  Leaflet.m = null;
  Leaflet.cursor = app.cursor.select(['panels', 'map']);
  Leaflet.listingsCursor = app.cursor.select(['panels', 'listings']);
  Leaflet.taggingCursor = app.cursor.select(['shared', 'tagging']);
  Leaflet.viewPropertiesCursor = app.cursor.select(['shared', 'viewedProperties']);

  // Extend data methods
  Leaflet.data = require('./data')(app);

  Leaflet.mapBounds = null;
  Leaflet.circleBounds = null;
  Leaflet.mapCircleIntersectionBounds = null;

  // Debouncer for radius dragger
  // Debouncer for 50ms
  Leaflet.debouncedRadiusHandler = debounce(
    app.actions.map.onRadiusChange, 50, { leading: true },
  );

  // ====== Leaflet Opts
  Leaflet.opts = {
    center: [39.0997, -94.5783],
    circleDefaultRadius: constants.SEARCH_RADIUS_DEFAULT_METERS,

    /// ////////// Features ///////////////////////////

    // Tiles
    showBasicMapTiles: true,
    showAerialMapTiles: true,
    showParcelMapTiles: false,

    // Markers
    showGeneralMarkers: true,
    showTinyMarkers: true,

    /// /////////// Listings //////////////////////////

    // Enable/disable listings
    showListingSummary: true, // Not working yet
    showListingDetail: true, // Not working yet

    // Listings to load per API call
    numListingsMobile: 100,
    numListingsDesktop: 200,
    numTinyListingsMobile: 100,
    numTinyListingsDesktop: 1000,

    // Density calculations
    maxGeneralPins: 50,

    /// ////////// Zoom settings //////////////////////

    minZoomPhone: 3,
    minZoom: 4,
    maxStateOverlayZoom: 6,
    minListingsZoomPhone: 10,
    minListingsZoom: 11,
    minTinyListingsZoom: 12,
    maxAutoDragZoom: 13,
    initialZoom: 14,
    minShowListingsOutsideCircleZoom: 14,
    maxTinyListingsZoom: 15,
    maxDeclutterZoom: 16,
    maxNativeZoom: 18,
    maxZoom: 19,

    /// ////////// Drag settings //////////////////////
    autoPanBorderPadding: 200,
    autoPositionDragEndDelay: 750, // in ms

    // Stops from scrolling around the world, which messes up the coordinate system
    maxBounds: [
      [-45, -260, true], // bottom: south lat, left: west long
      [90, 65], // top: north lat, right: east long
    ],
  };

  Leaflet.canShowListings = function () {
    const mapIsInitialized = app.actions.map.currentRoute;
    const isZoomingTo = app.actions.map.zoomToLevel;

    return (mapIsInitialized && ((isZoomingTo || Leaflet.m.getZoom()) >= Leaflet.getMinListingsZoom()));
  };

  Leaflet.getMinListingsZoom = function () {
    return app.utils.useMobileSite() ? Leaflet.opts.minListingsZoomPhone : Leaflet.opts.minListingsZoom;
  };

  Leaflet.getMinZoom = function () {
    return app.utils.useMobileSite() ? Leaflet.opts.minZoomPhone : Leaflet.opts.minZoom;
  };

  Leaflet.getMaxZoom = function () {
    return Leaflet.opts.maxZoom;
  };

  /// //////////////////////////// Tile Layers /////////////////////////////////////////////////

  /// / ====== Basic Layer

  Leaflet.getBasicLayer = function () {
    const themeToMap = {
      rateplug: '09ef05d7-13ac-451d-a08e-e09ed587e784',
      more: '97d8a407-1cdc-4de8-b36b-cb69d5b37494',
      afordal: '8303eb9b-9ea6-4eba-ad04-dd2d461dd137',
      fairway: '01982939-5544-72ea-adfe-79841bc24db8',
    };

    const vectorStyleId = themeToMap[document.body.dataset.theme]
      || '9590b6c2-0baf-4369-ad63-fabbfb5b08ea'; // HomeASAP Theme
    const rasterStyleId = themeToMap[document.body.dataset.theme]
      || 'basic-v2'; // HomeASAP Theme doesn't allow raster tiles
    if (app.utils.webGlSupported && !app.utils.useMobileSite()) {
      const layer = L.mapboxGL({
        style: `https://maptiler-oss.homeasapcontent.com/maps/${vectorStyleId}/style.json`,
        minZoom: 0, // /* app.utils.useMobileSite() ? Leaflet.opts.minZoomPhone : */ Leaflet.opts.minZoom,
        maxZoom: Leaflet.opts.maxZoom,
        maxNativeZoom: Leaflet.opts.maxNativeZoom,
      });
      layer.getAttribution = () => '<a href="http://www.openmaptiles.org/" target="_blank">© OpenMapTiles</a> <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap contributors</a>';
      return layer;
    }
    return L.tileLayer(`https://maptiler-oss.homeasapcontent.com/maps/${rasterStyleId}/{z}/{x}/{y}@2x.png`, {
      subdomains: ['1', '2', '3', '4'],
      tileSize: 512,
      zoomOffset: -1,
      minZoom: 1,
      maxZoom: Leaflet.opts.maxZoom,
      maxNativeZoom: Leaflet.opts.maxNativeZoom,
      attribution: '\u003ca href="https://www.maptiler.com/copyright/" target="_blank"\u003e\u0026copy; MapTiler\u003c/a\u003e \u003ca href="https://www.openstreetmap.org/copyright" target="_blank"\u003e\u0026copy; OpenStreetMap contributors\u003c/a\u003e',
      crossOrigin: true,
    });
    // return L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
    //   subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
    //   minZoom: /* app.utils.useMobileSite() ? Leaflet.opts.minZoomPhone : */ Leaflet.opts.minZoom,
    //   maxZoom: Leaflet.opts.maxZoom,
    //   maxNativeZoom: Leaflet.opts.maxNativeZoom,
    //   attribution: `Map data ©${(new Date()).getFullYear()} Google`,
    // });
  };

  Leaflet.basicLayer = Leaflet.getBasicLayer();

  // ====== Satellite Layer

  Leaflet.satelliteLayer = L.tileLayer('https://{s}.google.com/vt/lyrs=y&x={x}&y={y}&z={z}', {
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
    minZoom: Leaflet.getMinZoom(),
    maxZoom: Leaflet.getMaxZoom(),
    maxNativeZoom: Leaflet.opts.maxNativeZoom,
    attribution: `Imagery ©${(new Date()).getFullYear()} Google, Map data ©${(new Date()).getFullYear()} Google`,
  }).on('tileerror', (err) => {
    // ToDo: Do something?
    // Maybe a global error handler & alert system
    console.log('-- Error loading Leaflet data for url %s --', err.url);
  });

  // ====== Parcel Line Layer
  Leaflet.parcelLayer = L.tileLayer('{url}', {
    url(args) {
      let url = `https://parcelstream.com/getmap.aspx?tileid=${Leaflet.TileXYToQuadKey(args.x, args.y, args.z)}`;
      url += '&layers=SS.BASE.PARCELS/PARCELTILES&color=FFFFFF';

      // Add the CANDY and UID
      try {
        url = Dmp.Env.Connections.SS.finalizeUrl(url);
      } catch (err) {
        console.warn('ParcelStream not loaded');
      }
      return url;
    },
    minZoom: Leaflet.opts.maxNativeZoom,
    maxZoom: Leaflet.opts.maxZoom,
    maxNativeZoom: Leaflet.opts.maxNativeZoom,
    opacity: 0.6,
  });

  /// //////////////////////////// Overlay Layers /////////////////////////////////////////////////

  // ====== State ovelay  Layer =================================
  Leaflet.stateOverlay_mouseoverStyle = {
    weight: 0,
    stroke: false,
    fill: true,
    color: '#223949',
    fillColor: '#F15F4C',
    fillOpacity: 0.35,
    opacity: 0.5,
    clickable: true,
    pointerEvents: 'all',
  };

  Leaflet.stateOverlay_nouseoutStyle = {
    weight: 0,
    stroke: false,
    opacity: 0.5,
    color: '#223949',
    fill: false,
    fillOpacity: 0,
    clickable: true,
    pointerEvents: 'all',
  };

  Leaflet.stateOverlay_onmouseover = function (e) {
    const layer = e.target;
    layer.setStyle(Leaflet.stateOverlay_mouseoverStyle);
  };

  Leaflet.stateOverlay_onmouseout = function (e) {
    const layer = e.target;
    layer.setStyle(Leaflet.stateOverlay_nouseoutStyle);
  };

  Leaflet.stateOverlay_click = function (e) {
    Leaflet.m.fitBounds(e.target.getBounds());
  };

  Leaflet.stateOverlay_dblclick = function () {
    // Propagate dblclick to the mpa
    Leaflet.m.fire('dblclick');
  };

  Leaflet.stateOverlay_onEachFeature = function (feature, layer) {
    layer.setStyle(Leaflet.stateOverlay_nouseoutStyle);
    layer.on({
      mouseover: Leaflet.stateOverlay_onmouseover,
      mouseout: Leaflet.stateOverlay_onmouseout,
      click: Leaflet.stateOverlay_click,
      dblclick: Leaflet.stateOverlay_dblclick,
    });
  };

  Leaflet.stateOverlayLayer = L.geoJson(null, {
    onEachFeature: Leaflet.stateOverlay_onEachFeature,
  });

  // ====== Parcel Tiles overlay Layer =================================
  Leaflet.addParcelLayer = function () {
    try {
      // First see if the ParcelStream objects exist.  If they don't the exception will stop parcelLayer from instantiating
      if (Dmp.Env.Connections.SS.isReady()) {
        Leaflet.parcelLayer = L.tileLayer('{url}', {
          url(args) {
            let url = `https://parcelstream.com/getmap.aspx?layers=SS.BASE.PARCELS/PARCELTILES&color=FFFFFF&tileid=${Leaflet.TileXYToQuadKey(args.x, args.y, args.z)}`;
            // Add the CANDY and UID
            url = Dmp.Env.Connections.SS.finalizeUrl(url);
            return url;
          },
          minZoom: Leaflet.opts.maxNativeZoom,
          maxZoom: Leaflet.opts.maxZoom,
          maxNativeZoom: Leaflet.opts.maxNativeZoom,
          opacity: 0.6,
        });

        Leaflet.m.addLayer(Leaflet.parcelLayer);
        Leaflet.parcelLayer.bringToFront();

        return Leaflet.parcelLayer;
      }
    } catch (err) {
      console.warn('ParcelStream not loaded');
    }

    return null;
  };

  Leaflet.addParcels = function () {
    if (!Leaflet.parcelLayer
        || (Leaflet.parcelLayer && !Leaflet.m.hasLayer(Leaflet.parcelLayer))) {
      if (!Leaflet.addParcelLayer()) {
        // Keep trying to add the parcel layer every second
        setTimeout(Leaflet.addParcels, 1000);
      }
    }
  };

  Leaflet.TileXYToQuadKey = function (tileX, tileY, levelOfDetail) {
    let quadKey = '';
    for (let i = levelOfDetail; i > 0; i--) {
      let digit = '0';
      const mask = 1 << (i - 1);
      if ((tileX & mask) != 0) {
        digit++;
      }
      if ((tileY & mask) != 0) {
        digit++;
        digit++;
      }
      quadKey += digit;
    } // for i
    return quadKey;
  }; // TileXYToQuadKey

  Leaflet.switchLayersOnZoom = true;

  Leaflet.toggleLayers = function () {
    app.actions.map.switchLayersOnZoom = false;

    if (Leaflet.m.hasLayer(Leaflet.basicLayer)) {
      Leaflet.satelliteLayer.addTo(Leaflet.m);
      Leaflet.m.removeLayer(Leaflet.basicLayer);
      // If we toggle to the satellite layer, leave it there
      Leaflet.switchLayersOnZoom = false;
    } else if (Leaflet.m.hasLayer(Leaflet.satelliteLayer)) {
      Leaflet.basicLayer.addTo(Leaflet.m);
      Leaflet.m.removeLayer(Leaflet.satelliteLayer);
      // If we toggle to the satellite back to basic, allow the switch again
      Leaflet.switchLayersOnZoom = true;
    }
  };

  Leaflet.setMapLayer = function (zoomLevel) {
    if (!zoomLevel) {
      zoomLevel = Leaflet.m.getZoom();
    }

    if (zoomLevel <= Leaflet.opts.maxStateOverlayZoom) {
      // Show the basic map
      if (Leaflet.switchLayersOnZoom) {
        if (!Leaflet.m.hasLayer(Leaflet.basicLayer)) {
          if (Leaflet.opts.showBasicMapTiles) {
            Leaflet.basicLayer.addTo(Leaflet.m);
          }
        }
        if (Leaflet.m.hasLayer(Leaflet.satelliteLayer)) {
          Leaflet.m.removeLayer(Leaflet.satelliteLayer);
        }
      }
      // Show the state overlay
      if (!Leaflet.m.hasLayer(Leaflet.stateOverlayLayer)) {
        Leaflet.stateOverlayLayer.addTo(Leaflet.m);
      }
    } else if (zoomLevel < Leaflet.getMinListingsZoom()) {
      // Show the basic map
      if (Leaflet.switchLayersOnZoom) {
        if (!Leaflet.m.hasLayer(Leaflet.basicLayer)) {
          if (Leaflet.opts.showBasicMapTiles) {
            Leaflet.basicLayer.addTo(Leaflet.m);
          }
        }
        if (Leaflet.m.hasLayer(Leaflet.satelliteLayer)) {
          Leaflet.m.removeLayer(Leaflet.satelliteLayer);
        }
      }
      // Remove the state overlay
      if (Leaflet.m.hasLayer(Leaflet.stateOverlayLayer)) {
        Leaflet.m.removeLayer(Leaflet.stateOverlayLayer);
      }
    } else if (zoomLevel < Leaflet.opts.maxNativeZoom) {
      // Show the basic map
      if (Leaflet.switchLayersOnZoom) {
        if (!Leaflet.m.hasLayer(Leaflet.basicLayer)) {
          if (Leaflet.opts.showBasicMapTiles) {
            Leaflet.basicLayer.addTo(Leaflet.m);
          }
        }
        if (Leaflet.m.hasLayer(Leaflet.satelliteLayer)) {
          Leaflet.m.removeLayer(Leaflet.satelliteLayer);
        }
      }
      // Remove the state overlay
      if (Leaflet.m.hasLayer(Leaflet.stateOverlayLayer)) {
        Leaflet.m.removeLayer(Leaflet.stateOverlayLayer);
      }
    } else {
      // Show the satellite map
      if (Leaflet.switchLayersOnZoom) {
        if (!Leaflet.m.hasLayer(Leaflet.satelliteLayer)) {
          if (Leaflet.opts.showAerialMapTiles) {
            Leaflet.satelliteLayer.addTo(Leaflet.m);
          }
          this.switchLayersOnZoom = true;
        }
        if (Leaflet.m.hasLayer(Leaflet.basicLayer)) {
          Leaflet.m.removeLayer(Leaflet.basicLayer);
        }
      }
      // Remove the state overlay
      if (Leaflet.m.hasLayer(Leaflet.stateOverlayLayer)) {
        Leaflet.m.removeLayer(Leaflet.stateOverlayLayer);
      }
    }
  };

  /// //////////////////////////// Event Handlers /////////////////////////////////////////////////

  /// / Zoom events
  Leaflet.zoomState = {
    previousZoom: null,
  };

  Leaflet.setZoom = function (zoomLevel) {
    Leaflet.m.setZoom(zoomLevel);
  };

  Leaflet.onZoomStart_circleVisible = null;

  Leaflet.circleVisible = function () {
    const circleLayers = Leaflet.circleLayerGroup && Leaflet.circleLayerGroup.getLayers();
    return (circleLayers && (circleLayers.length > 0) // The circle layer group exists and has layers
              && Leaflet.circle && Leaflet.m.getBounds().contains(Leaflet.circle.getLatLng())); // The circle layer itself exists, and the center of the circle is  visible on the map
  };

  Leaflet.onMouseMove = function (e) {
    if (!app.utils.isMobile()) {
      // Don't use mouse position for touch
      Leaflet.mousePosition = e.latlng;
    }
  };

  Leaflet.onZoomStart = function (e) {
    console.log(`Zoom start from ${e.target._zoom}`);
    app.api.abort();

    Leaflet.onZoomStart_circleVisible = Leaflet.circleVisible();

    Leaflet.zoomState.previousZoom = e.target._zoom;
  };

  Leaflet.onZoomEnd = function (e) {
    console.log(`Zoom end to ${e.target._zoom}`);

    if (e.target && e.target._zoom) {
      app.actions.map.updateZoomInRoute(e.target._zoom);
    }

    if (Leaflet.zoomState.previousZoom < e.target._zoom) {
      /// /////////////////// ZOOMING IN //////////////////////////////
      app.actions.map.onZoomIn(e);

      // Zooming in past max native zoom
      if (e.target._zoom >= Leaflet.opts.maxNativeZoom) {
        Leaflet.onMaxNativeZoomIn(e);
      }

      // Zooming in on desktop
      const isCircleVisible = Leaflet.circleVisible();
      if (Leaflet.canShowListings() && !app.utils.useMobileSite()) {
        // Zooming in from outside listing zoom to inside listing zoom - make sure the circle is visible
        if (
          (e.target._zoom < Leaflet.opts.minShowListingsOutsideCircleZoom)
              && ((Leaflet.zoomState.previousZoom < Leaflet.getMinListingsZoom()) || Leaflet.onZoomStart_circleVisible)
              && !isCircleVisible
        ) {
          if (Leaflet.mousePosition && Leaflet.m.getBounds().contains(Leaflet.mousePosition)) {
            const pos = app.utils.validateLocationStr(Leaflet.listingsCursor.get(['meta', 'locationStr']));
            pos.Lat = Leaflet.mousePosition.lat;
            pos.Lon = Leaflet.mousePosition.lng;

            app.actions.map.dontPanToRoute();
            Leaflet.routeToMouse({ dontResize: true });
            app.actions.map.allowPanToRoute();
            Leaflet.setCircle(pos, { maxRadius: 16093 });
          } else {
            Leaflet.routeToCenter({ dontResize: true });
            Leaflet.setCircle(null, { maxRadius: 16093 });
          }
        }
      }
    } else if (Leaflet.zoomState.previousZoom > e.target._zoom) {
      /// /////////////////// ZOOMING OUT //////////////////////////////
      app.actions.map.onZoomOut(e);

      // Special cases, specifying where we are ZOOMING OUT FROM
      if (e.target._zoom < Leaflet.opts.maxNativeZoom) {
        Leaflet.onMaxNativeZoomOut(e);
      }
    }

    // Make sure the proper map is showing
    Leaflet.setMapLayer();

    // Refresh listings
    Leaflet.refreshListings({ dontResetViewport: true, circleUnchanged: true });
    Leaflet.refreshTinyMarkers();

    Leaflet.hideListingsOnWideZoom();
  };

  Leaflet.onMaxNativeZoomIn = function () {
    const l = Leaflet;

    // Switch from basic to satellite layer (if necessary)
    if (l.opts.showAerialMapTiles) {
      if (!l.m.hasLayer(l.satelliteLayer)) {
        l.m.addLayer(l.satelliteLayer);
        this.switchLayersOnZoom = true;
      }
    }

    // If Parcel Layer is present, bring it to front
    if (l.opts.showParcelMapTiles) {
      if (l.parcelLayer && l.m.hasLayer(l.parcelLayer)) {
        l.parcelLayer.bringToFront(); // Renders behind base layer otherwise
      } else {
        l.addParcels();
      }
    }
  };

  Leaflet.onMaxNativeZoomOut = function () {
    const l = Leaflet;

    // Switch from satellite layer back to basic (only if we switched on the way in)
    if (this.switchLayersOnZoom) {
      // Add Basic Tile Layer back, if configured to do so, and it's not already added for some reason
      if (l.opts.showBasicMapTiles && !l.m.hasLayer(l.basicLayer)) {
        l.m.addLayer(l.basicLayer);
      }

      // Remove Aerial Tile Layer, if it's present
      if (l.m.hasLayer(l.satelliteLayer)) {
        l.m.removeLayer(l.satelliteLayer);
      }
    }

    if (l.m.hasLayer(l.parcelLayer)) {
      l.parcelLayer.redraw(); // Center tile stays visible otherwise
    }
  };

  Leaflet.hideListingsOnWideZoom = function () {
    if (Leaflet.m.getZoom() < Leaflet.getMinListingsZoom()) {
      Leaflet.clearAllLayers();
    }
  };

  // Drag events
  Leaflet.dragRepositionTimer = null;

  Leaflet.onDrag = function () {
    if (Leaflet.dragRepositionTimer) {
      clearTimeout(Leaflet.dragRepositionTimer);
      Leaflet.dragRepositionTimer = null;
    }
  };

  Leaflet.onDragStart = function () {
    if (Leaflet.hasBoundingBox()) {
      app.api.abort();
      Leaflet.schoolOverlayPriorZoomLevel = null;
    }

    // on the mobile site, we consider dragging to be interacting
    if (app.utils.useMobileSite()) {
      app.actions.common.flagUserAsInteractedWithSite();
    }
  };

  Leaflet.onDragEnd = function () {
    if (app.utils.useMobileSite()) {
      defer(Leaflet.onDragEndMobile);
    } else {
      defer(Leaflet.onDragEndDesktop);
    }

    Leaflet.hideListingsOnWideZoom();
  };

  Leaflet.onDragEndMobile = function () {
    Leaflet.refreshListings({ dontResetViewport: true });
    Leaflet.routeToCenter({ dontResize: true });
  };

  Leaflet.onDragEndDesktop = function () {
    // Auto reposition only if the center of the circle is completely off screen
    //    This will refresh the tiny listings when it's done
    Leaflet.autoReposition(Leaflet.m.getBounds().getCenter());
  };

  Leaflet.autoReposition = function (point) {
    app.actions.common.flagUserAsInteractedWithSite();
    if (Leaflet.canShowListings()
        && (!Leaflet.circle || !Leaflet.m.getBounds().contains(Leaflet.circle.getLatLng())) // Circle doesn't exist, or center is outside of viewable map
        && (Leaflet.m.getZoom() <= Leaflet.opts.maxAutoDragZoom) // Zoome level is less than the configured auto-reposition maximum
    ) {
      app.actions.map.allowResetViewport();
      Leaflet.dragRepositionTimer = setTimeout(() => {
        Leaflet.routeToPoint(point, { dontResize: true });
      }, Leaflet.opts.autoPositionDragEndDelay);
    }

    Leaflet.refreshListings();
    Leaflet.refreshTinyMarkers();
  };

  /// //////////////////////////// Properties /////////////////////////////////////////////////

  // ====== Marker Groups
  Leaflet.tinyMarkerLayerGroup = null;
  Leaflet.markerLayerGroup = null;
  Leaflet.schoolsMarkerLayerGroup = null;

  // Overlays
  Leaflet.schoolOverlay = null;
  Leaflet.schoolOverlayLayerGroup = null;
  Leaflet.schoolOverlayPriorZoomLevel = null;

  Leaflet.neighborhoodOverlay = null;
  Leaflet.neighborhoodPopup = null;

  Leaflet.yelpLayerGroup = new L.FeatureGroup();

  // ====== Circle LayerGroup
  Leaflet.circleLayerGroup = new L.layerGroup();

  // ====== Pulse marker LayerGroup
  //  Sits above the circle but below the general markers
  Leaflet.pulseMarkerLayerGroup = new L.layerGroup();

  // ====== Home marker LayerGroup
  Leaflet.homeMarkerLayerGroup = new L.layerGroup();

  // ====== Popup for hover

  // *** NOTE: If you change the height or width, you must also change it in map.less
  Leaflet.popupWidth = 275;
  Leaflet.popupHeight = 120;

  Leaflet.popupYOffset = 40; // Offset of the bottom-middle of the popup from the coordinates (accounts for the pin height)
  Leaflet.popupWidthPadding = 50; // If the popup would be off-screen on the left or right, the amount of padding to set from the edge in its new location
  Leaflet.popupHeightPadding = 10; // If the popup would be off-screen on the top, the amount of padding to set from the bottom of the original coordinate

  Leaflet.popup = L.popup({
    closeButton: app.utils.useMobileSite(),
    autoPan: false,
    keepInView: true,
    closeOnClick: app.utils.useMobileSite(),
    zoomAnimation: false,
    className: 'map-popup',
    offset: [0, -Leaflet.popupYOffset],
    minWidth: Leaflet.popupWidth,
    maxWidth: Leaflet.popupWidth,
  });

  Leaflet.mousePosition = null;

  // ====== Leaflet Center Control
  Leaflet.panTo = function (pos, opts) {
    app.api.abort();

    pos = pos || Leaflet.cursor.get('pos');
    Leaflet.m.panTo([pos.Lat, pos.Lon]);

    app.actions.map.listingsCursor.set('data', null);
    app.cursor.commit();

    // Set a one-time moveend handler that refreshes the listings
    Leaflet.m.once('moveend', () => {
      const prevPos = app.utils.validateLocationStr(Leaflet.listingsCursor.get(['meta', 'locationStr']));

      const prevRoute = '/search/map/'.concat(app.utils.toLocationStr(prevPos.Lat, prevPos.Lon, prevPos.radius));
      app.actions.common.goToRoute(prevRoute);

      if (!opts || (opts && !opts.dontResize)) {
        Leaflet.resizeMap();
      }
    });
  };

  Leaflet.routeToCenter = function (opts) {
    app.api.abort();
    Leaflet.routeToPoint(Leaflet.m.getBounds().getCenter(), opts);

    if (!opts || (opts && !opts.dontResize)) {
      Leaflet.resizeMap();
    }
  };

  Leaflet.routeToMouse = function (opts) {
    app.api.abort();
    Leaflet.routeToPoint(Leaflet.mousePosition, opts);
  };

  Leaflet.routeToPoint = function (point, opts) {
    if (point && point.lat && point.lng) {
      const pos = app.utils.validateLocationStr(
        Leaflet.listingsCursor.get(['meta', 'locationStr']),
      );

      if (pos) {
        const { currentRoute } = app.actions.map;
        let locationStr = app.utils.toLocationStr(point.lat, point.lng, pos.radius, pos.zoom);

        if (currentRoute && currentRoute.params && currentRoute.params.id) {
          locationStr = locationStr.concat('/', currentRoute.params.id);
        }

        app.actions.common.goToRoute('/search/map/'.concat(locationStr), opts);
      }
    }
  };

  Leaflet.panControl = Toolbar({
    className: 'map-center-control',
    content: '<span></span>', // '&#9673;',
    title: 'Recenter',
    handler: Leaflet.panTo,
  });

  Leaflet.centerControl = Toolbar({
    className: 'map-center-control',
    content: '<span></span>', // '&#9673;',
    title: 'Reposition',
    handler: Leaflet.routeToCenter,
  });

  // ====== Leaflet Layout - Listening to cursor
  Leaflet.cursor
    .select(['ref', 'className'])
    .on('update', () => {
      setTimeout(() => {
        Leaflet.m.invalidateSize(false);
      }, 0);
    });

  // ====== Memoize Markers
  // ====== Instance of marker icons
  Leaflet.generalMarker = {};
  Leaflet.generalMarker_svg = {};

  forEach([0, 1, 2, 3, 4, 5], (i) => {
    Leaflet.generalMarker[i] = new L.DivIcon({
      className: `map-icon small num-${i}`,
      html: '<div class="pin"></div><div class="dot"></div>',

      // Leaflet will not mess with scale and positioning
      // Handle through css
      iconSize: undefined,
      iconAnchor: undefined,
    });

    Leaflet.generalMarker_svg[i] = new L.DivIcon({
      className: 'map-icon-svg',
      html: `<svg class="pin"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pin_${i}"></use></svg><svg class="dot"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pin_small"></use></svg>`,
      iconSize: [30, 46], // Pin is 30x36, dot is 10x10
      iconAnchor: [15, 41], // Default anchor is the center of the dot.  Pins without the "dot" must offset top by +5px
    });
  });

  Leaflet.favoriteMarker_svg = new L.DivIcon({
    className: 'map-icon-svg',
    html: '<svg class="pin"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-fav_pin"></use></svg><svg class="dot"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pin_small"></use></svg>',
    iconSize: [30, 46], // Pin is 30x36, dot is 10x10
    iconAnchor: [15, 41], // Default anchor is the center of the dot.  Pins without the "dot" must offset top by +5px
  });

  Leaflet.homeMarker_svg = new L.DivIcon({
    className: 'map-icon-svg home-pin',
    html: '<svg class="pin"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-home_pin"></use></svg><svg class="dot"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pin_small"></use></svg>',
    iconSize: [22, 22], // Pin is 30x36, dot is 10x10
    iconAnchor: [11, 11], // Default anchor is the center of the dot.  Pins without the "dot" must offset top by +5px
  });

  Leaflet.taggedMarker_svg = new L.DivIcon({
    className: 'map-icon-svg',
    html: '<svg class="pin"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-tag_pin"></use></svg><svg class="dot"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-pin_small"></use></svg>',
    iconSize: [30, 46], // Pin is 30x36, dot is 10x10
    iconAnchor: [15, 41], // Default anchor is the center of the dot.  Pins without the "dot" must offset top by +5px
  });

  Leaflet.pulseIcon = new L.DivIcon({
    className: 'map-icon-pulse',
    html: '<div class="pulse"></div>',

    // Leaflet will not mess with scale and positioning
    // Handle through css
    iconSize: undefined,
    iconAnchor: undefined,
  });
  Leaflet.pulseMarker = null;

  Leaflet.initMap = function () {
    // Initial zoom
    //     Map is initialized before router is instantiated, so pull the route from the browser
    const urlMatchArray = window.location.href.match(/(\/(grid|map)\/)([0-9.,-]*)[?/]*(.*)$/i);
    let pos = null;

    if (urlMatchArray && (urlMatchArray.length >= 4)) {
      pos = app.utils.validateLocationStr(urlMatchArray[3]);
    }

    const initialZoom = pos && pos.zoom ? pos.zoom : Leaflet.opts.initialZoom;

    // If configured, add basic tiles immediately and set zoom
    if (Leaflet.opts.showBasicMapTiles) {
      Leaflet.setMapLayer(initialZoom);
    }
    Leaflet.m.setView(Leaflet.opts.center, initialZoom);

    // Add the circle layer
    Leaflet.circleLayerGroup.addTo(Leaflet.m);

    // Add the pulse marker layer
    Leaflet.pulseMarkerLayerGroup.addTo(Leaflet.m);

    if (window.home_pin) {
      const { lat, lng } = window.home_pin;
      // Add the home marker layer
      const homeMaker = new L.Marker([lat, lng], {
        icon: Leaflet.homeMarker_svg,
        clickable: false,
        draggable: false,
        keyboard: false,
        zIndexOffset: -100,
      });
      homeMaker.addTo(Leaflet.homeMarkerLayerGroup);
      Leaflet.homeMarkerLayerGroup.addTo(Leaflet.m);
    }

    // Bind Popup Click
    // Leaflet.m.on('popupopen', function (e) {
    //  var popupContainer = document.getElementById('listing-popup-header')
    //  var listingId = popupContainer.dataset.listingId
    //
    //  popupContainer.parentNode.addEventListener('click', function () {
    //    app.actions.map.onNav(listingId, {dontToggle: true});
    //  })
    // })

    Leaflet.yelpLayerGroup.addTo(Leaflet.m);
    // Add the state overlay
    LeafletStream.ajax('/geojson/us-states.json', Leaflet.stateOverlayLayer)
      .on('end', () => {
        console.log('State overlay added');
      });
  };

  Leaflet.setIcon = function (type, o, dontBind) {
    let baseIcon;
    let icon;

    const className = [
      `listing-mlsidcount-${(o.Listing.MlsIds || []).length}`,
      `${(o.Listing.MlsIds || []).map((m) => `listing-mlsid-${m}`).join(' ')}`,
      `${(o.Listing.Tags || []).map(({ Name }) => `--tag:${Name}`).join(' ')}`,
      `${(o.Listing.SpecialFinancePrograms || []).map((programName) => `--tag:SpecialFinancePrograms__${programName}`).join(' ')}`,
      o.Listing.DisplayAddress === false ? 'hidden' : '',
    ].join(' ');

    if (app.utils.isMobile()) {
      // Get one of 5 base icons containing # bedrooms
      baseIcon = Leaflet.generalMarker_svg[o.info.bd > 4 ? 5 : o.info.bd];
      icon = new L.DivIcon.Label.Default({ ...baseIcon.options, className: `${baseIcon.options.className} ${className}` });
    } else {
      // Get one of 5 base icons containing # bedrooms
      baseIcon = Leaflet.generalMarker[o.info.bd > 4 ? 5 : o.info.bd];
      icon = new L.DivIcon.Label.Default({ ...baseIcon.options, className: `${baseIcon.options.className} ${className}` });
    }

    icon.options.labelText = o.info.priceLabel;

    const i = L.marker([o.Lat, o.Lon], {
      icon,
      draggable: false,
      riseOnHover: true,
      keyboard: false,
      clickable: true,

      contextmenu: !app.utils.useMobileSite(),
      contextmenuWidth: 180,
      contextmenuItems: [{
        text: 'Open detail view',
        index: 1,
        callback: (e) => {
          if (!e.target && e.relatedTarget) {
            e.target = e.relatedTarget;
          }
          Leaflet.iconOnClick(e);
        },
      },
      {
        text: 'Open in new tab',
        index: 2,
        callback: (e) => {
          if (!e.target && e.relatedTarget) {
            e.target = e.relatedTarget;
          }
          window.open(app.router.generateUrl(app.router.ROUTE_NAMES.MAP, {
            agentId: app.router.currentRoute.params.agentId,
            location: app.router.currentRoute.params.location,
            id: e.target.Id,
          }), '_blank');
        },
      }],
    });

    i.Id = o.Id;
    i._CacheKey = o._CacheKey;
    i.o = o;

    // Bind event async-ly
    if (dontBind !== true) {
      Leaflet.bindIcon(i);
    }

    return i;
  };

  Leaflet.panToListing = function (id) {
    const marker = Leaflet.getMarkerLayerByID(id);
    if (marker) {
      Leaflet.m.panTo(marker.getLatLng(), { animate: false, dontResize: true });
    }
  };

  Leaflet.panToCurrentRoute = function (opts) {
    const pos = app.utils.validateLocationStr(
      Leaflet.listingsCursor.get(['meta', 'locationStr']),
    );

    Leaflet.setMap(pos, opts);
  };

  Leaflet.iconOnClick = function (e) {
    app.actions.common.flagUserAsInteractedWithSite();
    if (app.utils.useMobileSite()) {
      app.actions.common.forceDeliverChargedLeadFromAd();
    }
    if (app.utils.useMobileSite() && (e.target.Id.substring(0, 3) != 'tax')) {
      // Leaflet.showPopup(e.target.Id);
      app.actions.map.setMobileCardDataWithId(e.target.Id);

      const markers = Leaflet.listingsCursor.get('data');
      Leaflet.setActiveMarker(
        markers,
        Leaflet.getMarkerByID(markers, e.target.Id),
      );
    } else if (e && e.originalEvent && (e.originalEvent.ctrlKey || e.originalEvent.metaKey)) {
      window.open(app.router.generateUrl(app.router.ROUTE_NAMES.MAP, {
        agentId: app.router.currentRoute.params.agentId,
        location: app.router.currentRoute.params.location,
        id: e.target.Id,
      }), '_blank');
    } else {
      app.actions.map.onNav(e.target.Id, { dontToggle: true });
    }

    app.actions.analytics.sendEvent('map actions', 'pin click');
  };

  Leaflet.iconOnMouseOver = function (e) {
    if (!app.utils.useMobileSite()) {
      Leaflet.modifyIconByID('mouseover', e.target.Id);
      Leaflet.showPopup(e.target.Id);
    }
  };

  Leaflet.showPopup = function (id) {
    const listing = Leaflet.data.getListing(id);
    if (listing === null) {
      return false;
    }

    // Set LatLon
    let latLng = [listing.Location.Lat, listing.Location.Lon];
    const point = Leaflet.m.project(latLng);
    const popupRight = point.x + Leaflet.popupWidth / 2;
    const popupLeft = point.x - Leaflet.popupWidth / 2;
    const popupTop = point.y - Leaflet.popupHeight - Leaflet.popupYOffset;

    // Check if popup is off screen on the left or right
    if (popupLeft < (Leaflet.m.getPixelBounds().min.x + Leaflet.popupWidthPadding)) {
      point.x += Leaflet.m.getPixelBounds().min.x - popupLeft + Leaflet.popupWidthPadding;
    } else if (popupRight > (Leaflet.m.getPixelBounds().max.x - Leaflet.popupWidthPadding)) {
      point.x -= popupRight - Leaflet.m.getPixelBounds().max.x + Leaflet.popupWidthPadding;
    }

    // Check if popup is off screen on the top
    if (popupTop < Leaflet.m.getPixelBounds().min.y) {
      point.y += Leaflet.popupHeight + Leaflet.popupYOffset + Leaflet.popupHeightPadding;
    }

    latLng = Leaflet.m.unproject(point);
    Leaflet.popup.setLatLng(latLng);

    // first default to the listing's first valid MLS data
    let mlsData = _.find(app.cursor.get(['shared', 'mlsData']), (mls, _id) => mls && _.includes(listing.MlsIds, _id));

    const agentMlsData = app.cursor.get(['shared', 'agent', 'mlsData']);

    // if the listing contains the current agent's MLS, we prioritize that
    if (agentMlsData && _.includes(listing.MlsIds, agentMlsData.Id)) {
      mlsData = agentMlsData;
    }

    if (!mlsData) {
      return; // Maybe we need to Fetch the Data
    }

    // get some data to pass into the popup component as props
    const showDaysOnMarket = mlsData.ShowDaysOnMarket;

    const brokerLogoUrl = mlsData.LogoUrlForCards || mlsData.LogoUrl;
    const showLogoOnPin = mlsData.ShowLogoOnPin;

    const showBrokerOnPin = mlsData.ShowBrokerOnPin;
    const showAgentOnPin = mlsData.ShowAgentOnPin;
    // const brokerName = mlsData.Name;

    const agentMlsId = mlsData.Id;

    const showRange = mlsData.ShowOnlyRangeLivingSquare;
    const squareFootage = app.utils.formatSquareFeet(listing, showRange);
    const html = ReactDOMServer.renderToStaticMarkup(React.createElement(MapPopup, {
      listing,
      popupClass: Leaflet.getPopupClass(listing),
      showDaysOnMarket,
      showBrokerOnPin,
      showLogoOnPin,
      brokerLogoUrl,
      squareFootage,
      agentMlsId,
      showAgentOnPin,
    }));

    Leaflet.popup.setContent(html);

    Leaflet.popup.openOn(Leaflet.m);
  };

  Leaflet.hidePopup = function (id) {
    Leaflet.modifyIconByID('mouseout', id);
    Leaflet.m.closePopup();
  };

  Leaflet.getPopupClass = function (item) {
    return item.SaleType === 1 ? 'sale' : 'rent';
  };

  Leaflet.iconOnMouseOut = function (e) {
    if (!app.utils.useMobileSite()) {
      Leaflet.modifyIconByID('mouseout', e.target.Id);
      Leaflet.m.closePopup();
    }
    return false;
  };

  Leaflet.bindIcon = function (icon) {
    // Lazy bind marker click handler, coz coz..
    // Defer until the current call stack has cleared
    // console.log("!!! Bind icon: " + icon);
    defer(() => {
      icon.on('click', Leaflet.iconOnClick)
        .on('mouseover', Leaflet.iconOnMouseOver)
        .on('mouseout', Leaflet.iconOnMouseOut);
    });
  };

  Leaflet.unbindIcon = function (icon) {
    icon.off('click', Leaflet.iconOnClick)
      .off('mouseover', Leaflet.iconOnMouseOver)
      .off('mouseout', Leaflet.iconOnMouseOut);
  };

  Leaflet.modifyIconsForTagging = function () {
    const tagging = Leaflet.taggingCursor.get();

    forEach(tagging, (tagsObject, listingId) => {
      tagsObject = tagsObject || {};
      if (Object.keys(tagsObject).length === 0) {
        Leaflet.modifyIconByID('untagged', listingId);
      } else if (tagsObject.favorite) {
        Leaflet.modifyIconByID('favorite', listingId);
      } else if (tagsObject.dislike) {
        Leaflet.modifyIconByID('dislike', listingId);
      } else {
        Leaflet.modifyIconByID('tagged', listingId);
      }
    });

    const currentListing = app.cursor.get(['screens', 'map', 'data']);
    if (currentListing && currentListing.Id) {
      setTimeout(((id) => {
        Leaflet.modifyIconByID('active', id);
      }).bind(this, currentListing.Id), 500);
    }
  };

  Leaflet.modifyIconForTaggingById = function (listingId) {
    const tagsObject = Leaflet.taggingCursor.get(listingId) || {};

    if (Object.keys(tagsObject).length === 0) {
      Leaflet.modifyIconByID('untagged', listingId);
    } else if (tagsObject.favorite) {
      Leaflet.modifyIconByID('favorite', listingId);
    } else if (tagsObject.dislike) {
      Leaflet.modifyIconByID('dislike', listingId);
    } else {
      Leaflet.modifyIconByID('tagged', listingId);
    }
  };

  Leaflet.modifyIconByID = function (type, id) {
    const marker = Leaflet.getMarkerLayerByID(id);
    if (marker) {
      Leaflet.modifyIcon(type, marker);
      return true;
    }
    return false;
  };

  Leaflet.addClassConditionally = function (iconElement, type) {
    if (!L.DomUtil.hasClass(iconElement, type)) {
      L.DomUtil.addClass(iconElement, type);
    }
  };
  Leaflet.removeClassConditionally = function (iconElement, type) {
    if (L.DomUtil.hasClass(iconElement, type)) {
      L.DomUtil.removeClass(iconElement, type);
    }
  };

  Leaflet.modifyIcon = function (type, icon) {
    if (icon && icon._icon) {
      if (app.utils.isMobile()) {
        // For mobile, we need to actually change the icon for "special" SVG marker types (those without numbers)
        Leaflet.modifyBaseIcon(icon.Id, type);
      }

      // Select one fo the 4 main pin types
      switch (type) {
        case 'active':
          Leaflet.addClassConditionally(icon._icon, 'active');
          Leaflet.removeClassConditionally(icon._icon, 'general');
          Leaflet.removeClassConditionally(icon._icon, 'small');
          break;

        case 'general':
          Leaflet.addClassConditionally(icon._icon, 'general');
          Leaflet.removeClassConditionally(icon._icon, 'active');
          Leaflet.removeClassConditionally(icon._icon, 'small');
          break;

        case 'small':
          Leaflet.addClassConditionally(icon._icon, 'small');
          Leaflet.removeClassConditionally(icon._icon, 'general');
          Leaflet.removeClassConditionally(icon._icon, 'active');
          break;

        case 'tagged':
          // Special case: force small tagged pins to be general
          Leaflet.addClassConditionally(icon._icon, 'general');
          Leaflet.removeClassConditionally(icon._icon, 'small');
          break;

        case 'mouseover':
          if (L.DomUtil.hasClass(icon._icon, 'general') || L.DomUtil.hasClass(icon._icon, 'active')) {
            Leaflet.addClassConditionally(icon._icon, 'hover');
          } else if (L.DomUtil.hasClass(icon._icon, 'small')) {
            Leaflet.addClassConditionally(icon._icon, 'small-hover');
          }
          break;

        case 'mouseout':
          Leaflet.removeClassConditionally(icon._icon, 'hover');
          Leaflet.removeClassConditionally(icon._icon, 'small-hover');
          break;

        default: break;
      }

      // Remove all zoom classes and add back a class for the current zoom level
      const minZoomLevel = app.utils.useMobileSite() ? Leaflet.opts.minZoomPhone : Leaflet.opts.minZoom;

      for (let i = minZoomLevel; i <= Leaflet.opts.maxZoom; i++) {
        Leaflet.removeClassConditionally(icon._icon, `zoom${i}`);
      }
      Leaflet.addClassConditionally(icon._icon, `zoom${Leaflet.m._zoom}`);

      if (Leaflet.viewPropertiesCursor.get(icon.Id) === 1) {
        Leaflet.addClassConditionally(icon._icon, 'viewed');
      }

      const tagTypes = ['favorite', 'dislike', 'tagged', 'untagged'];
      if (tagTypes.indexOf(type) !== -1) {
        forEach(tagTypes, (val) => {
          Leaflet.removeClassConditionally(icon._icon, val);
        });

        // Add the class to the icon regardless of desktop/mobile
        Leaflet.addClassConditionally(icon._icon, type);
      }
    }
  };

  Leaflet.modifyBaseIcon = function (id, type) {
    // Change icon in the tree
    const existingMarkerHashTable = Leaflet.cursor.get('markers');
    const marker = existingMarkerHashTable[id];

    if (marker) {
      let newIcon = null;

      switch (type) {
        case 'untagged':
          // eslint-disable-next-line no-case-declarations
          const listing = Leaflet.data.getListing(id);
          if (listing) {
            const o = Leaflet.data.prepMarkerData(listing);
            if (o) {
              newIcon = Leaflet.generalMarker_svg[o.info.bd > 4 ? 5 : o.info.bd];
            }
          }
          break;
        case 'tagged':
          newIcon = Leaflet.taggedMarker_svg;
          break;
        case 'favorite':
          newIcon = Leaflet.favoriteMarker_svg;
          break;
        default: break;
      }

      // Set icon only if it has changed
      if (newIcon && newIcon !== marker.options.icon) {
        marker.setIcon(newIcon);
        marker.update();

        existingMarkerHashTable[id] = marker;
        Leaflet.cursor.set('markers', existingMarkerHashTable);
        app.cursor.commit();
      }
    }
  };

  Leaflet.clearAllLayers = function (opts = {}) {
    Leaflet.m.closePopup();
    !opts.skipMarkerLayer && Leaflet.clearMarkersLayer();
    Leaflet.clearPulseMarker();
    Leaflet.clearSchoolMarkersLayer();

    const isRadiusChanging = Leaflet.cursor.get('onRadiusChange');
    if (!isRadiusChanging) {
      Leaflet.circleLayerGroup.clearLayers();
    }
  };

  Leaflet.clearTinyMarkersLayer = function () {
    // Clean out the layer
    if (Leaflet.tinyMarkerLayerGroup) {
      // const tinyMarkerLayers = Leaflet.tinyMarkerLayerGroup.getLayers();
      Leaflet.m.removeLayer(Leaflet.tinyMarkerLayerGroup);
      Leaflet.tinyMarkerLayerGroup = null;
    }
  };

  Leaflet.clearMarkersLayer = function () {
    if (Leaflet.markerLayerGroup) {
      Leaflet.clearMarkersLayerHelper(Leaflet.markerLayerGroup);
      Leaflet.markerLayerGroup = null;

      Leaflet.cursor.set('markers', null);
      app.cursor.commit();
    }
  };

  Leaflet.clearSchoolMarkersLayer = function () {
    if (Leaflet.schoolsMarkerLayerGroup) {
      // Only clear if there is no location set or if there is a location set that is not a school
      const loc = app.actions.common.getLocationType();
      if (!loc || (loc && loc.locType != 'S')) {
        Leaflet.clearMarkersLayerHelper(Leaflet.schoolsMarkerLayerGroup);
        Leaflet.schoolsMarkerLayerGroup = null;
      }
    }
  };

  Leaflet.clearMarkersLayerHelper = function (layerGroup) {
    // Clean out the layer
    if (layerGroup) {
      const markerLayers = layerGroup.getLayers();
      markerLayers.forEach((layer) => {
        Leaflet.unbindIcon(layer);
      });
      Leaflet.m.removeLayer(layerGroup);
      layerGroup = null;
    }
  };

  /// ///////////////////// Map setup /////////////////////////////////////////////////////

  Leaflet.setMap = function (pos, opts = {}) {
    Leaflet.m.setView(
      [pos.Lat, pos.Lon],
      (pos.zoom || undefined),
      { animate: opts.animate },
    );

    Leaflet.cursor.set('pos', { Lat: pos.Lat, Lon: pos.Lon });

    if (opts.commit) {
      app.cursor.commit();
    }
  };

  Leaflet.zoomPriorToSelection = null;
  Leaflet.centerPriorToSelection = null;

  Leaflet.saveMapState = function () {
    Leaflet.zoomPriorToSelection = Leaflet.m.getZoom();
    Leaflet.centerPriorToSelection = Leaflet.m.getCenter();
    // console.log("Saved zoom: " + Leaflet.zoomPriorToSelection + ", and center: (" + Leaflet.centerPriorToSelection.lat + "," + Leaflet.centerPriorToSelection.lng + ")");
  };
  Leaflet.restoreMapState = function () {
    if (Leaflet.zoomPriorToSelection) {
      Leaflet.m.setZoom(Leaflet.zoomPriorToSelection);
      // console.log("Restoring zoom: " + Leaflet.zoomPriorToSelection);
      Leaflet.zoomPriorToSelection = null;
    }
    if (Leaflet.centerPriorToSelection) {
      Leaflet.m.panTo(Leaflet.centerPriorToSelection);
      // console.log("Restoring center: (" + Leaflet.centerPriorToSelection.lat + "," + Leaflet.centerPriorToSelection.lng + ")");
      Leaflet.centerPriorToSelection = null;
    }
  };

  Leaflet.refreshListings = function (opts) {
    if (Leaflet.canShowListings()) {
      Leaflet.setBoundingBox();
      app.actions.panels.refreshListings(opts);
    }
  };

  Leaflet.refreshTinyMarkers = function () {
    setTimeout(Leaflet.setTinyMarkers, 50);
  };

  Leaflet.declutter = function () {
    const listings = Leaflet.listingsCursor.get('data');
    let selectedListing = null;

    if (listings) {
      const mapCurrentRoute = app.actions.map.currentRoute;
      let listingID = null;
      if (mapCurrentRoute && mapCurrentRoute.params && mapCurrentRoute.params.id) {
        listingID = mapCurrentRoute.params.id;

        for (let i = 0; i < listings.length; i++) {
          if (listings[i].Id == listingID) {
            selectedListing = listings[i];
            break;
          }
        }
      }

      Leaflet.setMarkers(listings, selectedListing, { dontResize: true });
    }
  };

  Leaflet.intersection = function (latLngBounds1, latLngBounds2) {
    const north = Math.min(latLngBounds1.getNorth(), latLngBounds2.getNorth());
    const south = Math.max(latLngBounds1.getSouth(), latLngBounds2.getSouth());
    const east = Math.min(latLngBounds1.getEast(), latLngBounds2.getEast()); // Intersected East longitude is the "most negative" in the Western hemisphere
    const west = Math.max(latLngBounds1.getWest(), latLngBounds2.getWest()); // Intersected East longitude is the "least negative" in the Western hemisphere

    const intersection = new L.LatLngBounds([south, west], [north, east]);
    return intersection;
  };

  Leaflet.resetBoundingBox = function () {
    // On phones, we never reset the bounding box.  It is always the full map
    if (!app.utils.useMobileSite()) {
      Leaflet.mapCircleIntersectionBounds = null;
    }
  };

  Leaflet.setBoundingBox = function () {
    // There is a circle on phone-sized screens but it is only used for choosing the zoom level
    if (app.utils.useMobileSite()) {
      app.cursor.commit();
      //   On phones, we use the whole map as the intersection bounding box
      Leaflet.mapBounds = Leaflet.m.getBounds();
      Leaflet.mapCircleIntersectionBounds = Leaflet.mapBounds;
      return;
    }
    Leaflet.mapBounds = Leaflet.m.getBounds();

    if (Leaflet.circle) {
      Leaflet.circleBounds = Leaflet.circle.getBounds();

      if (Leaflet.mapBounds.contains(Leaflet.circleBounds)) {
        Leaflet.mapCircleIntersectionBounds = null;
      } else {
        Leaflet.mapCircleIntersectionBounds = Leaflet.mapBounds; // Leaflet.intersection(Leaflet.mapBounds, Leaflet.circleBounds);
      }
    }
  };

  Leaflet.hasBoundingBox = function () {
    // Phone sized screens have no circle, so are always have a bounding box
    if (app.utils.useMobileSite() || Leaflet.mapCircleIntersectionBounds) {
      return true;
    }
    return false;
  };

  Leaflet.getBoundingBox = function () {
    return Leaflet.mapCircleIntersectionBounds;
  };

  Leaflet.cleanSelected = function () {
    const existingSelectedMarker = Leaflet.cursor.get('selectedMarker');

    if (existingSelectedMarker) {
      Leaflet.modifyIconByID('general', existingSelectedMarker.Id);
    }

    Leaflet.cursor.set('selectedMarker', null);
    app.cursor.commit();

    Leaflet.clearPulseMarker();
  };

  Leaflet.setActiveMarker = function (markersArr, selectedObj) {
    if (selectedObj) {
      const o = Leaflet.data.prepMarkerData(selectedObj);

      if (o && o.Id) {
        // If selected marker exists and is not the same as current ID - Remove class
        Leaflet.cleanSelected();

        // Add class to make active
        Leaflet.modifyIconByID('active', o.Id);

        if (o.Listing.DisplayAddress !== false) {
          // Position pulse marker
          Leaflet.setPulseMarker(o.Lat, o.Lon);
        }

        // Update the tree
        Leaflet.cursor.set('selectedMarker', o);

        app.cursor.commit();
      }
    } else {
      // No property is selected
      Leaflet.cleanSelected();
    }
  };

  Leaflet.clearUnneededMarkers = function (pos) {
    if (pos && pos.Lat && pos.Lon) {
      const existingMarkerHashTable = Leaflet.cursor.get('markers');

      if (existingMarkerHashTable && Leaflet.markerLayerGroup) {
        forEach(existingMarkerHashTable, (existingMarker, key) => {
          const locationLatLng = existingMarker.getLatLng();
          const centerLatLng = new L.latLng(pos.Lat, pos.Lon);

          const distanceToCenter = centerLatLng.distanceTo(locationLatLng);

          if (distanceToCenter > pos.radius) {
            // Out of the circle
            // Remove Marker
            Leaflet.markerLayerGroup.removeLayer(existingMarker);
            Leaflet.unbindIcon(existingMarker);
            delete existingMarkerHashTable[key];
          }
        });
      }
    }
  };

  // @param opts.dontResize
  // @param opts.dontBindMarker
  // @param opts.commit
  // @param opts.reset
  Leaflet.setMarkers = function (markersArr, selectedObj, opts = {}) {
    /// //////// Check Options ////////////////////////////////////////////////////////////
    if (!Leaflet.opts.showGeneralMarkers) {
      return;
    }

    console.log(`!!!! Begin setMarkers: ${Date.now()}`);

    /// //////// Init MarkerLayerGroup if null /////////////////////////////////////////////
    if (!Leaflet.markerLayerGroup) {
      Leaflet.markerLayerGroup = new L.FeatureGroup();
      Leaflet.markerLayerGroup.addTo(Leaflet.m);
    }

    /// //////// Load cursor data and select steps to execute //////////////////////////////
    const existingSelectedMarker = Leaflet.cursor.get('selectedMarker');
    const existingMarkerHashTable = Leaflet.cursor.get('markers');

    /// //////// Helper methods ////////////////////////////////////////////////////////////

    const cleanSelected = function () {
      if (existingSelectedMarker) {
        Leaflet.modifyIconByID('general', existingSelectedMarker.Id);
      }

      Leaflet.cursor.set('selectedMarker', null);
      app.cursor.commit();

      Leaflet.clearPulseMarker();
    };

    // const setMapState = function () {
    //   if (existingSelectedMarker) {
    //     if (!selectedObj) {
    //       Leaflet.restoreMapState(); // This condition applies only when "de-selecting" a property
    //       Leaflet.saveMapState();
    //     }
    //   } else {
    //     Leaflet.saveMapState();
    //   }
    // };

    const mNewMarkerHashTable = {};
    const mLayerMarkerArray = [];
    const mDensityArray = [];

    // If a markers array is passed in, process it
    if (markersArr) {
      forEach(markersArr, (markerData) => {
        /// ////////  Set the proper marker
        const o = Leaflet.data.prepMarkerData(markerData);

        if (o && o.Id) {
          let marker = null;
          let alreadyExisting = false;
          if (existingMarkerHashTable) {
            if (existingMarkerHashTable[o.Id]) {
              if (existingMarkerHashTable[o.Id]._CacheKey === o._CacheKey) {
                marker = existingMarkerHashTable[o.Id];
                alreadyExisting = true;
              } else {
                // Remove Marker
                Leaflet.markerLayerGroup.removeLayer(existingMarkerHashTable[o.Id]);
                Leaflet.unbindIcon(existingMarkerHashTable[o.Id]);
                delete existingMarkerHashTable[o.Id];
              }
            }
          }
          marker = marker || Leaflet.setIcon('generalMarker', o, opts.dontBindMarker);

          /// /  Prep data for the tree/cursor
          mNewMarkerHashTable[o.Id] = marker;

          /// /  Prep data for the map layer
          mLayerMarkerArray.push(marker);

          /// / Prep data for density calculation
          mDensityArray.push(o);

          if (!alreadyExisting) {
            // Add to map
            Leaflet.markerLayerGroup.addLayer(marker);
          }
        }
      });

      if (Leaflet.markerLayerGroup && existingMarkerHashTable) {
        forEach(existingMarkerHashTable, (existingMarker, key) => {
          if (!mNewMarkerHashTable[key]) {
            // Remove Marker
            Leaflet.markerLayerGroup.removeLayer(existingMarker);
            Leaflet.unbindIcon(existingMarker);
            delete existingMarkerHashTable[key];
          }
        });
      }
      Leaflet.cursor.set('markers', mNewMarkerHashTable);
    }

    // If selected, commit the cursor
    if (opts.commit) {
      app.cursor.commit();
    }

    if (selectedObj) {
      const o = Leaflet.data.prepMarkerData(selectedObj);

      if (o && o.Id) {
        // If selected marker exists and is not the same as current ID - Remove class
        if (existingSelectedMarker && existingSelectedMarker.Id !== o.Id) {
          cleanSelected();
        }

        // Add class to make active
        Leaflet.modifyIconByID('active', o.Id);

        if (o.Listing.DisplayAddress !== false) {
          // Position pulse marker
          Leaflet.setPulseMarker(o.Lat, o.Lon);
        }

        // Update the tree
        Leaflet.cursor.set('selectedMarker', o);
      }
    } else if (!opts.dontResetSelected) {
      // No property is selected
      cleanSelected();
    }

    // Calculate density / declutter
    setTimeout(() => {
      Leaflet.data.calcDensity(mNewMarkerHashTable);
    }, 1);

    // Commit the changes
    app.cursor.commit();

    console.log(`!!!! End setmarkers: ${Date.now()}`);
  };

  Leaflet.clearPulseMarker = function () {
    Leaflet.pulseMarkerLayerGroup.clearLayers();
  };

  Leaflet.setPulseMarker = function (lat, lng) {
    Leaflet.clearPulseMarker();
    if (Leaflet.pulseMarker) {
      Leaflet.pulseMarker.setLatLng([lat, lng]);
    } else {
      Leaflet.pulseMarker = new L.Marker([lat, lng], {
        icon: Leaflet.pulseIcon,
        clickable: false,
        draggable: false,
        keyboard: false,
      });
    }
    Leaflet.pulseMarkerLayerGroup.addLayer(Leaflet.pulseMarker);
  };

  Leaflet.getMarkerLayerByID = function (id) {
    let marker = null;
    if (Leaflet.markerLayerGroup) {
      const layers = Leaflet.markerLayerGroup.getLayers();
      for (let i = 0; i < layers.length; i++) {
        if (layers[i].Id == id) {
          marker = layers[i];
          break;
        }
      }
      return marker;
    }
  };

  Leaflet.getMarkerByID = function (markerArray = [], id) {
    let marker = null;
    for (let i = 0; i < markerArray.length; i++) {
      if (markerArray[i].Id == id) {
        marker = markerArray[i];
        break;
      }
    }
    return marker;
  };

  Leaflet.filterListingsByCircle = function (listings) {
    // Don't filter if...
    //    1.  There is no map set
    //    2.  There is no circle set
    //    3.  There is no bounding box (inidicates the initial load of a circle with no bounding box intersection)
    if (
      !Leaflet.mapBounds || !Leaflet.circleBounds
        || (Leaflet.m.getZoom() >= Leaflet.opts.minShowListingsOutsideCircleZoom)
        || (
          Leaflet.mapBounds.getNorth() == Leaflet.mapBounds.getSouth()
          && Leaflet.mapBounds.getWest() == Leaflet.mapBounds.getEast()
        )
    ) {
      return listings;
    }

    const newListings = [];

    const pos = app.utils.validateLocationStr(Leaflet.listingsCursor.get(['meta', 'locationStr']));
    const maxListings = app.utils.isMobile() ? Leaflet.opts.numListingsMobile : Leaflet.opts.numListingsDesktop;

    for (let i = 0; i < listings.length; i++) {
      const locationLatLng = new L.latLng(listings[i].Location.Lat, listings[i].Location.Lon);
      const centerLatLng = new L.latLng(pos.Lat, pos.Lon);

      const isInMapBounds = Leaflet.mapBounds.contains(locationLatLng);
      const distanceToCenter = centerLatLng.distanceTo(locationLatLng);

      if (isInMapBounds && (distanceToCenter < pos.radius) && (newListings.length < maxListings)) {
        newListings.push(listings[i]);
      }
    }

    return newListings;
  };

  Leaflet.renderMarkerDensity = function (densityData) {
    forEach(densityData, (i, key) => {
      const marker = Leaflet.getMarkerLayerByID(key);
      if (marker) {
        let newType = i;

        if ((Leaflet.m.getZoom() > Leaflet.opts.maxDeclutterZoom)
            && (i != 'active')) {
          newType = 'general';
        }

        Leaflet.modifyIcon(newType, marker);
      }
    });
  };

  Leaflet.setSearchResults = function (numResults) {
    if (!app.utils.useMobileSite()) {
      Leaflet.circle.setSearchResults(numResults);
    }
  };

  Leaflet.repositionMap = function (latitude, longitude) {
    let latLng = new L.LatLng(latitude, longitude);
    if (!Leaflet.m.getBounds().contains(latLng)) {
      // let markerBounds = new L.LatLngBounds([latitude, longitude], [latitude, longitude]);
      // Zoom to fit circle
      Leaflet.resizeMap();
      // Leaflet.m.panInsideBounds(markerBounds);
      // markerBounds = null;
    }
    latLng = null;
  };

  Leaflet.resizeMap = function (opts = {}) {
    // If circle exists
    if (Leaflet.circle) {
      setTimeout(() => {
        Leaflet.m.fitBounds(Leaflet.circle.getBounds(), opts);
      }, 500);
    } else if (Leaflet.markerLayerGroup) {
      // Else If marker layer exists
      setTimeout(() => {
        Leaflet.m.fitBounds(Leaflet.markerLayerGroup.getBounds(), opts);
      }, 500);
    }
  };

  Leaflet.resizeMapToSchool = function (opts = {}) {
    // If circle exists
    if (Leaflet.schoolOverlay) {
      Leaflet.schoolOverlayPriorZoomLevel = Leaflet.m.getZoom();
      setTimeout(() => {
        Leaflet.m.fitBounds(Leaflet.schoolOverlay.getBounds(), opts);
      }, 500);
    }
  };

  Leaflet.setTinyMarkers = function () {
    if (app.utils.useMobileSite() || !Leaflet.opts.showTinyMarkers) {
      console.log('!!!! Tiny markers DISABLED !!!!!');
      return;
    }

    const currentZoom = Leaflet.m.getZoom();
    if (
      !Leaflet.canShowListings()
        || (currentZoom < Leaflet.opts.minTinyListingsZoom)
        || (currentZoom > Leaflet.opts.maxTinyListingsZoom)
    ) {
      Leaflet.clearTinyMarkersLayer();
      return;
    }

    app.actions.map.getTinyListings(Leaflet.m.getBounds(),
      (data) => {
        let markerData = [];

        Leaflet.clearTinyMarkersLayer();

        forEach(data, (i) => {
          let o = Leaflet.data.prepMarkerData(i);
          if (o) {
            let pin = L.circle([o.Lat, o.Lon], 2, {
              clickable: false,
              stroke: false,
              fillOpacity: 1.0,
              color: '#666',
              className: 'map-tiny-marker',
            });

            markerData.push(pin);
            pin = null;
          }

          o = null;
        });

        // Recheck current zoom level before adding layer
        if (Leaflet.m.getZoom() >= Leaflet.opts.minTinyListingsZoom) {
          Leaflet.clearTinyMarkersLayer();
          Leaflet.tinyMarkerLayerGroup = L.layerGroup(markerData);
          Leaflet.tinyMarkerLayerGroup.addTo(Leaflet.m);
        }

        markerData = null;
      });
  };

  Leaflet.dragState = {
    previousDragLatLng: null,
    previousDragLatLngCheckpoint: null,
  };

  Leaflet.setCircle = function (pos, opts) {
    opts = opts || {};

    if (!Leaflet.canShowListings()) {
      return;
    }

    if (!pos) {
      pos = app.utils.validateLocationStr(
        Leaflet.listingsCursor.get(['meta', 'locationStr']),
      );
    }

    // If radius is changing, don't redraw the circle;
    const isRadiusChanging = Leaflet.cursor.get('onRadiusChange');
    if (isRadiusChanging) {
      Leaflet.cursor.set('onRadiusChange', 0);
      app.cursor.commit();
      return;
    }

    // Default Radius in meters
    pos.radius = pos.radius || Leaflet.opts.circleDefaultRadius;

    // Clear all layers
    Leaflet.circleLayerGroup.clearLayers();
    Leaflet.circle = null;

    if (app.utils.useMobileSite()) {
      // The circle is just a way to size the zoom level on phones
      Leaflet.circle = new L.Circle(
        [pos.Lat, pos.Lon], pos.radius, { className: 'map-invisible' },
      );
    } else {
      // Set up the circle for desktop
      Leaflet.circle = new L.CircleEditor(
        [pos.Lat, pos.Lon], pos.radius, {
          className: 'map-circle-editor',
          weight: 2,
          maxRadius: opts.maxRadius || null,
        },
      );

      // Add a circle to the layer
      Leaflet.circleLayerGroup.addLayer(Leaflet.circle);

      Leaflet.circle
        .on('centerchange', (e) => {
          // Leaflet.refreshListings();

          app.actions.common.flagUserAsInteractedWithSite();

          app.actions.map.onCenterChange(
            e.target._latlng.lat,
            e.target._latlng.lng,
          );
        })
        .on('centerdrag', (e) => {
          Leaflet.refreshListings();

          const circleBounds = Leaflet.circle.getBounds();
          // const position = e.target.getLatLng();
          const mapBounds = e.target._map.getBounds();

          if (!mapBounds.contains(circleBounds)) {
            let panByPixels = new L.Point(0, 0);
            // const panByPixelsCounter = new L.Point(0, 0);

            // Pan East or West
            if (circleBounds.getEast() >= mapBounds.getEast()) {
              panByPixels.x = 100;
            } else if (circleBounds.getWest() <= mapBounds.getWest()) {
              panByPixels.x = -100;
            }

            // Pan North or South
            if (circleBounds.getNorth() >= mapBounds.getNorth()) {
              panByPixels.y = -100;
            } else if (circleBounds.getSouth() <= mapBounds.getSouth()) {
              panByPixels.y = 100;
            }

            console.log(`!!!! Pan By: (${panByPixels.x}, ${panByPixels.y}) !!`);
            e.target._map.panBy(panByPixels);

            // panByPixelsCounter = new L.Point(-panByPixels.x, -panByPixels,y);
            // e.target.panBy(panByPixelsCounter);

            panByPixels = null;
          }
        })
        .on('radiuschange', (e) => {
          Leaflet.refreshListings();

          Leaflet.cursor.set('onRadiusChange', 1);
          app.cursor.commit();

          if (app.actions.map.onRadiusChange(e.target._mRadius) === null) {
            // Radius didn't change (zooming outside of max radius)
            Leaflet.cursor.set('onRadiusChange', 0);
            app.cursor.commit();
          }
        })
        .on('radiusdrag', (e) => {
          Leaflet.debouncedRadiusHandler(e.target._mRadius, true);
        });
    }
  };

  Leaflet.schoolIcon = L.divIcon({
    className: 'school-icon',
    iconSize: [24, 24],
    html: '<svg>'
    + '<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-school"></use>'
    + '</svg>',
  });

  Leaflet.addSchoolMarkers = function (schoolsArray) {
    forEach(schoolsArray, (data) => {
      Leaflet.addSchoolMarker(data.school);
    });
  };

  Leaflet.addSchoolMarker = function (school) {
    if (!Leaflet.schoolsMarkerLayerGroup) {
      Leaflet.schoolsMarkerLayerGroup = L.featureGroup();
      Leaflet.schoolsMarkerLayerGroup.addTo(Leaflet.m);
    }

    if (!Leaflet.getSchoolMarkerLayerByID(school.gsId)) {
      const schoolMarker = L.marker(
        [school.lat, school.lon],
        {
          icon: Leaflet.schoolIcon, riseOnHover: true, zIndexOffset: 100, id: school.gsId,
        },
      );

      schoolMarker.bindPopup(school.name);

      if (school.geometry) {
        schoolMarker.geometry = school.geometry;
        schoolMarker.on('click', (e) => {
          Leaflet.showSchoolAttendanceBoundary(school.name, e.target.geometry);
        });
      }

      Leaflet.schoolsMarkerLayerGroup.addLayer(schoolMarker);
      Leaflet.schoolsMarkerLayerGroup.bringToFront();
    }
  };

  Leaflet.getSchoolMarkerLayerByID = function (id) {
    let marker = null;
    if (Leaflet.markerLayerGroup) {
      const layers = Leaflet.schoolsMarkerLayerGroup.getLayers();
      for (let i = 0; i < layers.length; i++) {
        if (layers[i].options.id == id) {
          marker = layers[i];
          break;
        }
      }
      return marker;
    }
  };

  Leaflet.showSchoolMarkerPopup = function (schoolgsId) {
    if (Leaflet.schoolsMarkerLayerGroup) {
      forEach(Leaflet.schoolsMarkerLayerGroup.getLayers(), (marker) => {
        if (marker.options.id === schoolgsId) {
          marker.openPopup();
        }
      });
    }
  };

  Leaflet.clearBoundaryOverlays = function () {
    Leaflet.clearNeighborhoodBoundary();
    Leaflet.clearSchoolAttendanceBoundary({ dontPriorZoom: true });
    Leaflet.clearPostalCodeBoundary();
  };

  Leaflet.showSchoolAttendanceBoundary = function (schoolName, geoJson) {
    Leaflet.clearBoundaryOverlays();
    if (!Leaflet.schoolOverlayLayerGroup) {
      Leaflet.schoolOverlayLayerGroup = L.featureGroup();
      Leaflet.schoolOverlayLayerGroup.addTo(Leaflet.m);
    }

    Leaflet.schoolOverlay = L.geoJson(geoJson, {
      style: () => ({
        weight: 3, color: 'red', fill: true, fillColor: '#5d9ed6', fillOpacity: 0.25, clickable: true, pointerEvents: 'visibleStroke',
      }),
      onEachFeature: (feature, layer) => {
        layer.bindPopup(`<div style="font-size: 10px"><p>School: ${schoolName}</p><p style="text-align:center">Click to hide</p></div>`, { closeButton: false });
        layer.on('click', Leaflet.clearSchoolAttendanceBoundaryMode);
        layer.on('mouseover', Leaflet.showSchoolPopup);
        layer.on('mouseout', Leaflet.hideSchoolPopup);
      },
    }).addTo(Leaflet.schoolOverlayLayerGroup);
  };

  Leaflet.showPostalCodeBoundary = function (geoJson) {
    Leaflet.clearBoundaryOverlays();

    if (!Leaflet.postalCodeOverlayLayerGroup) {
      Leaflet.postalCodeOverlayLayerGroup = L.featureGroup();
      Leaflet.postalCodeOverlayLayerGroup.addTo(Leaflet.m);
    }

    Leaflet.postalCodeOverlay = L.geoJson(geoJson, {
      style: () => ({
        weight: 3, color: 'red', fill: true, fillColor: '#5d9ed6', fillOpacity: 0.25, clickable: true, pointerEvents: 'visibleStroke',
      }),
      onEachFeature: (feature, layer) => {
        layer.on('click', Leaflet.clearPostalCodeBoundary);
      },
    }).addTo(Leaflet.postalCodeOverlayLayerGroup);
  };

  Leaflet.clearPostalCodeBoundary = function () {
    if (Leaflet.postalCodeOverlay && Leaflet.postalCodeOverlayLayerGroup && Leaflet.postalCodeOverlayLayerGroup.hasLayer(Leaflet.postalCodeOverlay)) {
      Leaflet.postalCodeOverlayLayerGroup.removeLayer(Leaflet.postalCodeOverlay);
      Leaflet.postalCodeOverlay = null;
    }
  };

  Leaflet.clearSchoolAttendanceBoundaryMode = function () {
    app.actions.common.clearLocationType();
    Leaflet.clearSchoolAttendanceBoundary();
  };

  Leaflet.clearSchoolAttendanceBoundary = function (opts) {
    if (Leaflet.schoolOverlay && Leaflet.schoolOverlayLayerGroup && Leaflet.schoolOverlayLayerGroup.hasLayer(Leaflet.schoolOverlay)) {
      Leaflet.schoolOverlayLayerGroup.removeLayer(Leaflet.schoolOverlay);
      Leaflet.schoolOverlay = null;
    }

    if (!opts || (opts && !opts.dontPriorZoom)) {
      if (Leaflet.schoolOverlayPriorZoomLevel) {
        Leaflet.panToCurrentRoute({ zoom: Leaflet.schoolOverlayPriorZoomLevel, animate: true });
        Leaflet.schoolOverlayPriorZoomLevel = null;
      }
    }
  };

  Leaflet.showNeighborhoodBoundary = function (neighborhoodName, geoJson) {
    Leaflet.clearBoundaryOverlays();

    Leaflet.neighborhoodOverlay = L.geoJson(geoJson, {
      style: () => ({
        weight: 3, color: 'red', fill: true, fillColor: '#5d9ed6', fillOpacity: 0.25, clickable: true, pointerEvents: 'visibleStroke',
      }),
      onEachFeature: (feature, layer) => {
        layer.bindPopup(`<div style="font-size: 10px"><p>Neighborhood: ${neighborhoodName}</p><p style="text-align:center">Click to hide</p></div>`, { closeButton: false });
        layer.on('click', Leaflet.clearNeighborhoodBoundaryMode);
        layer.on('mouseover', Leaflet.showNeighborhoodPopup);
        layer.on('mouseout', Leaflet.hideNeighborhoodPopup);
      },
    }).addTo(Leaflet.m);
    Leaflet.neighborhoodOverlay.addTo(Leaflet.m);
  };

  Leaflet.clearNeighborhoodBoundaryMode = function (e) {
    // Hide boundary
    Leaflet.clearNeighborhoodBoundary(e);
    app.actions.common.clearLocationType();
  };

  Leaflet.clearNeighborhoodBoundary = function (e) {
    if (Leaflet.neighborhoodOverlay && Leaflet.m.hasLayer(Leaflet.neighborhoodOverlay)) {
      Leaflet.m.removeLayer(Leaflet.neighborhoodOverlay);
      Leaflet.neighborhoodOverlay = null;

      Leaflet.hideNeighborhoodPopup(e);
    }
  };

  Leaflet.showNeighborhoodPopup = function (e) {
    if (Leaflet.neighborhoodOverlay) {
      Leaflet.neighborhoodOverlay.openPopup(e.latlng);
    }
  };

  Leaflet.hideNeighborhoodPopup = function () {
    if (Leaflet.neighborhoodOverlay && Leaflet.neighborhoodOverlay.closePopup) {
      Leaflet.neighborhoodOverlay.closePopup();
    }
  };

  Leaflet.showSchoolPopup = function (e) {
    if (Leaflet.schoolOverlay) {
      Leaflet.schoolOverlay.openPopup(e.latlng);
    }
  };

  Leaflet.hideSchoolPopup = function () {
    if (Leaflet.schoolOverlay) {
      Leaflet.schoolOverlay.closePopup();
    }
  };

  Leaflet.isPhoneSizedScreen = function () {
    return app.utils.isPhoneSizedScreen();
  };

  Leaflet.useMobileSite = function () {
    return app.utils.useMobileSite();
  };

  Leaflet.currentLocationIcon = new L.DivIcon({
    className: 'map-icon-current-location',
    html: '<div class="dot med"><span class="point"><span class="pulse"></span></span></div>',

    // Leaflet will not mess with scale and positioning
    // Handle through css
    iconSize: undefined,
    iconAnchor: undefined,
  });

  Leaflet.addCurrentLocationMarker = function (pos) {
    if (!Leaflet.currentLocationMarker) {
      Leaflet.currentLocationMarker = L.marker([pos.lat, pos.lon],
        { icon: Leaflet.currentLocationIcon, zIndexOffset: -100, id: 'current-location-marker' });
      Leaflet.currentLocationMarker.addTo(Leaflet.m);
    } else {
      Leaflet.currentLocationMarker.setLatLng([pos.lat, pos.lon]);
      app.actions.map.moveMapToCurrentLocationIfNeeded([[pos.lat, pos.lon]]);
    }
  };

  Leaflet.addCurrentLocationMarkerHelper = function (err, position) {
    if (err) {
      return;
    }
    Leaflet.addCurrentLocationMarker(
      { lat: position.coords.latitude, lon: position.coords.longitude },
    );
  };

  Leaflet.watchCurrentLocation = function () {
    app.utils.geolocationHelperCallbacks.push(Leaflet.addCurrentLocationMarkerHelper);
  };

  Leaflet.addYelpSearchResults = function (results) {
    Leaflet.yelpLayerGroup.clearLayers();

    _.each(results, (result, i) => {
      const icon = L.divIcon({ className: 'yelp-result' });
      const marker = L.marker([result.Lat, result.Lon], { icon }).addTo(Leaflet.yelpLayerGroup);
      marker.index = i;

      const html = ReactDOMServer.renderToStaticMarkup(React.createElement(YelpPopup, {
        business: result,
      }));

      const paddingAmount = app.utils.useMobileSite() ? 0 : 50;
      marker.bindPopup(html, { autoPanPaddingTopLeft: new L.point(paddingAmount, paddingAmount), offset: new L.point(0, -33), className: 'yelp-popup' });
      marker.on('mouseover', () => {
        marker.openPopup();
      });
      marker.on('mouseout', () => {
        marker.closePopup();
      });
    });
  };

  Leaflet.fitViewToYelpMarkers = function () {
    if (!_.isEmpty(Leaflet.yelpLayerGroup.getLayers())) {
      const bounds = Leaflet.yelpLayerGroup.getBounds();
      const activeListing = app.cursor.get(['screens', 'map', 'data']);
      if (activeListing) {
        bounds.extend(new L.latLng([activeListing.Location.Lat, activeListing.Location.Lon]));
      }

      Leaflet.m.fitBounds(bounds, { padding: [50, 50] });
    }
  };

  Leaflet.clearYelpMarkers = function () {
    Leaflet.yelpLayerGroup.clearLayers();
  };

  Leaflet.modifyYelpMarkerToActive = function (marker) {
    marker.isActive = true;
    marker.setIcon(L.divIcon({ className: 'yelp-result  yelp-result-active' }));
    marker.setZIndexOffset(1000);
    marker._popup.options.offset = new L.point(0, -33);
    marker.openPopup();
  };

  Leaflet.modifyYelpMarkerToInactive = function (marker) {
    marker.isActive = false;
    marker.setIcon(L.divIcon({ className: 'yelp-result' }));
    marker.closePopup();
    marker.setZIndexOffset(1);
  };

  Leaflet.clearActiveYelpMarker = function () {
    if (!Leaflet.yelpLayerGroup) {
      return;
    }
    const markers = Leaflet.yelpLayerGroup.getLayers();
    const marker = _.find(markers, (m) => m.isActive);
    if (marker) {
      Leaflet.modifyYelpMarkerToInactive(marker);
    }
  };

  Leaflet.setYelpMarkerActive = function (index) {
    if (!app.actions.listing.cursor.get('showYelpMarkers')) {
      return false;
    }
    const markers = Leaflet.yelpLayerGroup.getLayers();
    const marker = _.find(markers, (m) => m.index == index);
    if (marker) {
      Leaflet.modifyYelpMarkerToActive(marker);
    }
  };

  return Leaflet;
};
