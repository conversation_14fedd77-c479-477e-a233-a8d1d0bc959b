const forEach = require('lodash.foreach');
const defer = require('lodash.defer');
const filter = require('lodash.filter');
const { get } = require('lodash');

module.exports = function (app) {
  /** *
   * Static methods for Data
   * */
  function Data() {
  }

  Data.cursor = app.cursor.select(['panels', 'map']);
  Data.listingsCursor = app.cursor.select(['panels', 'listings']);

  Data.reset = function (commit) {
    Data.cursor.set('markers', null);
    Data.cursor.set('selectedMarker', null);
    Data.cursor.set('densityData', null);

    if (commit) {
      app.cursor.commit();
    }
  };

  Data.prepMarkerData = function (_o) {
    if (!(_o && _o.Id && _o.Location.Lat && _o.Location.Lon)) {
      return null;
    }

    const info = {
      bd: Number(_o.TotalBedrooms || 0),
      price: Number(_o.ListPrice),
      RangePriceFlag: _o.RangePriceFlag,
      RangeHighPrice: _o.RangeHighPrice,
      Tags: _o.Tags,
    };

    const o = {
      Id: _o.Id,
      Lat: _o.Location.Lat,
      Lon: _o.Location.Lon,
      Listing: _o,
      info,
    };

    const priceValueLabel = ((o.info.RangePriceFlag == 'Y') && o.info.RangeHighPrice)
      ? app.utils.formatPriceLabel(o.info.RangeHighPrice, o)
      : app.utils.formatPriceLabel(o.info.price, o);
    if (priceValueLabel) {
      o.info.priceLabel = `${app.utils.getDollarSymbol(o.Listing)}${priceValueLabel}`;
    }

    o._CacheKey = `${o.Id}-${o.info.priceLabel}`;

    return o;
  };

  Data.getListing = function (id) {
    let d = null;
    const listings = Data.listingsCursor.get('data');

    forEach(listings, (i) => {
      if (id === i.Id) {
        d = i;
        return false;
      }
    });

    return d;
  };

  Data.densityRange = {
    // Always set to the max of the above levels
    max: 6000,
    // Zoom levels, range in meters
    8: 6000,
    9: 3200,
    10: 1800,
    11: 1050,
    12: 650,
    13: 375,
    14: 250,
    15: 160,
    16: 100,
    17: 50,
    18: 30,
    19: 10,
  };

  if (app.utils.useMobileSite()) {
    forEach(Data.densityRange, (val, key) => {
      Data.densityRange[key] = val * 1.5;
    });
  }

  Data.calcDensity = function (data, zoom) {
    data = data || Data.cursor.get('markers');
    const result = {};

    // Sort data keys based on whether the agent's MLSId exists in listing's MLSIds array
    // This will ensure the listings under agent's MLSId are always priorized during decluttering
    // Also, if special financing option is selected, listings with the selected special financing option will be priorized
    const agentMlsId = app.cursor.get(['shared', 'agent', 'data', 'MlsId']);
    const specialFinancingOption = (app.cursor.get(['shared', 'menu', 'specialFinancing']) || '').toString();

    const dataKeys = Object.keys(data);
    dataKeys.sort((a, b) => {
      if (specialFinancingOption) {
        const aSpecialFinancingOptions = get(data[a], 'o.Listing.SpecialFinancePrograms') || [];
        const bSpecialFinancingOptions = get(data[b], 'o.Listing.SpecialFinancePrograms') || [];
        if (aSpecialFinancingOptions.includes(specialFinancingOption) && !bSpecialFinancingOptions.includes(specialFinancingOption)) {
          return -1;
        } if (!aSpecialFinancingOptions.includes(specialFinancingOption) && bSpecialFinancingOptions.includes(specialFinancingOption)) {
          return 1;
        }
      }

      const aMlsIds = get(data[a], 'o.Listing.MlsIds') || [];
      const bMlsIds = get(data[b], 'o.Listing.MlsIds') || [];

      if (aMlsIds.includes(agentMlsId)) {
        return -1;
      }
      if (bMlsIds.includes(agentMlsId)) {
        return 1;
      }
      return 0;
    });

    // Step #2: Get distance of each point to
    // every other point
    for (const k of dataKeys) {
      const i = data[k];
      let density = [];

      for (const j of dataKeys) {
        const m = data[j];
        if (i.Id !== m.Id) {
          density.push({
            Id: m.Id,
            distance: i._latlng.distanceTo(m._latlng),
          });
        }
      }

      density = filter(density, (n) => n.distance < Data.densityRange.max);

      result[i.Id] = density;
    }

    Data.cursor.set('densityData', result);
    app.cursor.commit();

    defer(Data.buildMarkerDensity, zoom);
  };

  Data.buildMarkerDensity = function (zoom) {
    const data = Data.cursor.get('densityData');
    if (!data) {
      return;
    }

    zoom = zoom || app.leaflet.m._zoom;

    const selected = Data.cursor.get('selectedMarker');
    const results = {};
    const range = Data.densityRange[zoom];

    let generalCount = 0;

    forEach(data, (i, key) => {
      if (selected && key === selected.Id) {
        // Active should be set regardless of pin count
        results[selected.Id] = 'active';
      } else if (generalCount >= app.leaflet.opts.maxGeneralPins) {
        // Otherwise, if more than the maximum general pins have been set, make the rest small
        results[key] = 'small';
      } else {
        // If no marker is nearby
        if (i.length === 0) {
          results[key] = 'general';
        } else {
          let exists = false;

          // Build new density based on range
          const Density = filter(i, (n) => n.distance < range);

          // Check if any of the density markers already exists
          forEach(Density, (x) => {
            if (results[x.Id]) {
              exists = true;
              return false;
            }
          });

          exists === true
            ? results[key] = 'small'
            : results[key] = 'general';
        }
      }

      // Both active and general pins are "big" pins
      if ((results[key] == 'general') || (results[key] == 'active')) {
        generalCount++;
      }
    });

    console.log('-- Zoom & New Density --', zoom);

    // Back to leaflet to render the markers
    app.leaflet.renderMarkerDensity(results);
  };

  return Data;
};
