<PERSON>.CircleEditor = L.Circle.extend({

  options: {
    icon_center: new L.DivIcon({
      className: 'map-circle-center',
      iconSize: [20, 20],
      html: '<span class="tooltipspan" style="display:none;">Drag ZoomIT tool to the area you want to live</span><svg class="toolsvg"><g><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-zoom-tool-center"></use></g></svg>',
    }),
    icon_radius: new L.DivIcon({
      className: 'map-circle-radius',
      iconSize: [28, 28],
      html: '<span class="tooltipspan" style="display:none;">Resize to find awesome homes in the area</span><svg class="toolsvg"><g><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-radius-adjustment-tool"></use></g></svg><span class="toolspan" style="display:none;"></span>',
    }),
    circle_inactive: {
      weight: 2,
      fillOpacity: 0.1,
    },
    circle_hover: {
      weight: 4,
      fillOpacity: 0.1,
    },
    circle_active: {
      weight: 4,
      fillOpacity: 0.3,
    },
  },

  onAdd(map) {
    L.Path.prototype.onAdd.call(this, map);
    this.addHooks();
    this.setStyle(this.options.circle_inactive);
  },

  onRemove(map) {
    this.removeHooks();
    L.Path.prototype.onRemove.call(this, map);
  },

  addHooks() {
    if (this._map) {
      if (!this._markerGroup) {
        this._initMarkers();
      }
      this._map.addLayer(this._markerGroup);
    }
  },

  removeHooks() {
    if (this._map) {
      this._map.removeLayer(this._markerGroup);
      delete this._markerGroup;
      delete this._markers;
    }
  },

  updateMarkers() {
    this._markerGroup.clearLayers();
    this._initMarkers();
  },

  setSearchResults(numResults) {
    const radiusIcon = this._markers[1]._icon;
    const radiusResultsBox = radiusIcon.children[2];

    this._clearFade(radiusResultsBox);

    radiusResultsBox.style.display = 'block';
    radiusResultsBox.innerHTML = `${numResults} homes`;

    const self = this;
    this._resultsTimer = setTimeout(() => {
      self.resultsTimer = null;

      radiusResultsBox.innerHTML = `${self._convertToMiles(self._mRadius)} miles`; // | 24 min avg commute';

      if (radiusResultsBox.style.display != 'none') {
        self._fadeOut(self, radiusResultsBox);
      }
    }, 2000);
  },

  setCirclePosition(latLng) {
    // NOTE:  This function is causing issues.  DO NOT USE YET!!  BB
    const circleBounds = this.getBounds();
    const center = circleBounds.getCenter();
    const neCoord = circleBounds.getNorthEast();
    let radiusLatLng = new L.LatLng(center.lat, neCoord.lng);

    // Has circle center moved?
    if ((center.lat != latLng.Lat) || (center.Lon != latLng.lng)) {
      this.setLatLng(latLng);

      this._markers[1].setLatLng(radiusLatLng);
      radiusLatLng = null;

      // this.fire('centerchange');
    }
  },

  _initMarkers() {
    this._markerGroup = new L.LayerGroup();
    this._markers = [];

    const markerCenter = this._createMarker(this._latlng, 0, true);
    this._markers.push(markerCenter);

    const circleBounds = this.getBounds();
    const center = circleBounds.getCenter();
    const neCoord = circleBounds.getNorthEast();
    const northCenterCoord = new L.LatLng(center.lat, neCoord.lng, true);
    const markerNorthCenter = this._createMarker(northCenterCoord, 1);

    this._markers.push(markerNorthCenter);
  },

  _createMarker(latlng, index, isCenter) {
    const marker = new L.Marker(latlng, {
      draggable: true,
      riseOnHover: false,
      keyboard: false,
      icon: this.options[isCenter === true ? 'icon_center' : 'icon_radius'],
    });

    marker._origLatLng = latlng;
    marker._index = index;
    marker._isCenter = isCenter;

    marker.on('mouseover', this._onMarkerMouseOver, this)
      .on('mousedown', this._onMarkerActiveStart, this)
      .on('touchstart', this._onMarkerActiveStart, this)
      .on('dragstart', this._onMarkerActiveStart, this)
      .on('mouseout', this._onMarkerMouseOut, this);

    if (isCenter === true) {
      marker.on('drag', this._onCenterMove, this)
        .on('dragend', this._onCenterMoveEnd, this)
        .on('mousedown', this._onCenterMouseDown, this);
    } else {
      marker.on('mouseover', this._onMarkerMouseOver, this)
        .on('mousedown', this._onRadiusMouseDown, this)
        .on('touchstart', this._onMarkerActiveStart, this)
        .on('dragstart', this._onMarkerActiveStart, this)
        .on('drag', this._onRadiusDrag, this)
        .on('dragend', this._onRadiusDragEnd, this)
        .on('click', this._onMarkerActiveEnd, this);
    }

    this._markerGroup.addLayer(marker);

    return marker;
  },

  _onCenterMove(e) {
    this._isDragging = true;

    const marker = e.target;
    L.Util.extend(marker._origLatLng, marker._latlng);

    console.log('_onCenterMove');
    this._showCenterTooltip();
    this._hideRadiusTool();
    this.setStyle(this.options.circle_active);
    this.redraw();
  },

  _onCenterMoveEnd() {
    this._isDragging = false;

    // const marker = e.target;

    // now resetting the side point
    const circleBounds = this.getBounds();
    const center = circleBounds.getCenter();
    const neCoord = circleBounds.getNorthEast();
    const northCenterCoord = new L.LatLng(center.lat, neCoord.lng, true);

    const mm = this._markers[1];
    mm.setLatLng(northCenterCoord);

    const radiusIcon = this._markers[1]._icon;
    radiusIcon.style.display = 'inline-block';

    this.fire('centerchange');
  },

  _onCenterMouseDown() {
    this._isDragging = false;

    console.log('_onCenterMouseDown');
    this._showCenterTooltip();
    this._hideRadiusTool();
    this._hideRadiusBox();
  },

  _onRadiusMouseDown(e) {
    const radiusIcon = this._markers[1]._icon;

    const radiusResultsBox = radiusIcon.children[2];
    this._clearFade(radiusResultsBox);

    this._onMarkerActiveStart(e);
    this._hideCenterTooltip();
    this._hideRadiusTooltip();
    this._showRadiusBox();
  },

  _onMarkerMouseOver() {
    if (this._isDragging) {
      return;
    }
    if (this._markers && this._markers.length > 1) {
      const centerIcon = this._markers[0]._icon;
      const radiusIcon = this._markers[1]._icon;

      if (!L.DomUtil.hasClass(radiusIcon, 'map-circle-radius-drag')) {
        L.DomUtil.addClass(radiusIcon, 'map-circle-radius-drag');
      }

      if (!L.DomUtil.hasClass(centerIcon, 'map-circle-center-drag')) {
        L.DomUtil.addClass(centerIcon, 'map-circle-center-drag');
      }
      this.setStyle(this.options.circle_hover);
    }
    console.log('_onMarkerMouseOver');
    this._showCenterTooltip();
    this._showRadiusTooltip();
  },

  _onMarkerMouseOut(e) {
    if (this._isDragging) {
      return;
    }
    this._onMarkerActiveEnd(e);
    this._hideRadiusBox();
    this._hideCenterTooltip();
    this._hideRadiusTooltip();
  },

  _onMarkerActiveStart(e) {
    this._isDragging = true;
    this._onMarkerMouseOver(e);
    this._drawRadiusPath(e.target);
    this.setStyle(this.options.circle_active);

    this._hideCenterTooltip();
    this._hideRadiusTooltip();
  },

  _showCenterTooltip() {
    if (this._markers && this._markers.length > 0) {
      const centerIcon = this._markers[0]._icon;
      const centerTooltip = centerIcon.children[0];

      centerTooltip.style.display = 'block';
    }
  },

  _hideCenterTooltip() {
    if (this._markers && this._markers.length > 0) {
      const centerIcon = this._markers[0]._icon;
      const centerTooltip = centerIcon.children[0];
      centerTooltip.style.display = 'none';
    }
  },

  _showRadiusTooltip() {
    if (this._isDragging) {
      return;
    }

    if (this._markers && this._markers.length > 1) {
      const radiusIcon = this._markers[1]._icon;
      const radiusTooltip = radiusIcon.children[0];
      radiusTooltip.style.display = 'block';
    }
  },

  _hideRadiusTooltip() {
    if (this._markers && this._markers.length > 1) {
      const radiusIcon = this._markers[1]._icon;
      const radiusTooltip = radiusIcon.children[0];
      radiusTooltip.style.display = 'none';
    }
  },

  _onMarkerActiveEnd() {
    this._isDragging = false;

    this._removeRadiusPath();

    if (this._markers && this._markers.length > 1) {
      const centerIcon = this._markers[0]._icon;
      const radiusIcon = this._markers[1]._icon;

      if (centerIcon && L.DomUtil.hasClass(centerIcon, 'map-circle-center-drag')) {
        L.DomUtil.removeClass(centerIcon, 'map-circle-center-drag');
      }

      if (radiusIcon && L.DomUtil.hasClass(radiusIcon, 'map-circle-radius-drag')) {
        L.DomUtil.removeClass(radiusIcon, 'map-circle-radius-drag');
      }

      // Move back marker to border of circle
      const centerLatLng = this.getLatLng();
      const radius = this.getRadius();
      const radiusIconLatLng = this._markers[1].getLatLng();

      const distance = centerLatLng.distanceTo(radiusIconLatLng);
      if (distance && distance > radius) {
        const ratio = radius / distance;
        const newLatLng = L.latLng(
          (radiusIconLatLng.lat - centerLatLng.lat) * ratio + centerLatLng.lat,
          (radiusIconLatLng.lng - centerLatLng.lng) * ratio + centerLatLng.lng,
        );
        this._markers[1].setLatLng(newLatLng);
      }
    }

    this.setStyle(this.options.circle_inactive);
  },

  _showRadiusBox() {
    if (this._markers && this._markers.length > 1) {
      const radiusIcon = this._markers[1]._icon;
      const radiusResultsBox = radiusIcon.children[2];

      // Only set text if it's not already set
      if (radiusResultsBox.innerHTML == '') {
        radiusResultsBox.innerHTML = `${this._convertToMiles(this._mRadius)} miles`;
      }
      radiusResultsBox.style.display = 'inline-block';
    }
  },

  _hideRadiusBox() {
    if (!this._freezeRadiusBox) {
      if (this._markers && this._markers.length > 1) {
        const radiusIcon = this._markers[1]._icon;
        const radiusResultsBox = radiusIcon.children[2];
        radiusResultsBox.style.display = 'none';
      }
    }
  },

  _hideRadiusTool() {
    const radiusIcon = this._markers[1]._icon;
    radiusIcon.style.display = 'none';
  },

  _drawRadiusPath(radiusMarker) {
    // Redraw radius path
    this._removeRadiusPath();

    const pointA = this.getBounds().getCenter();
    const pointB = radiusMarker.getLatLng();
    const pointList = [pointA, pointB];

    this._radiusPath = new L.polyline(pointList, {
      className: 'map-circle-radiusline',
    });
    this._markerGroup.addLayer(this._radiusPath);
  },

  _removeRadiusPath() {
    if (this._markerGroup && this._radiusPath && this._markerGroup.hasLayer(this._radiusPath)) {
      this._markerGroup.removeLayer(this._radiusPath);
      this._radiusPath = null;
    }
  },

  _onRadiusDrag(e) {
    const marker = e.target;
    const icon = marker._icon;
    const radiusResultsBox = icon.children[2];

    this._clearFade(radiusResultsBox, true);

    // Update status message
    const center = this._markers[0].getLatLng();
    const axis = marker._latlng;
    let distance = center.distanceTo(axis);
    if (this.options.maxRadius) {
      distance = distance < this.options.maxRadius ? distance : this.options.maxRadius;
    }
    this._hasDragged = true;

    this._drawRadiusPath(marker);
    this.setRadius(distance);
    this.setStyle(this.options.circle_active);

    icon.children[2].innerHTML = `${this._convertToMiles(distance)} miles`;

    this.fire('radiusdrag');
  },

  _onRadiusDragEnd(e) {
    this._freezeRadiusBox = true;
    this._onMarkerActiveEnd(e);
    this.fire('radiuschange');
  },

  _convertToMiles(meters) {
    meters = Number(meters || 0);
    return (meters * 0.000621371).toFixed(2);
  },

  _freezeRadiusBox: false,
  _fadeTimer: null,
  _resultsTimer: null,

  _fadeOut(circle, element) {
    // Pause for 1 second and fade at 50 ms intervals by 10%
    setTimeout(() => {
      circle._startFade(circle, element);
    }, 2000);
  },

  _startFade(circle, element) {
    let op = 1; // initial opacity
    circle._fadeTimer = setInterval(() => {
      if (op <= 0.1) {
        circle._clearFade(element);
      } else {
        op -= op * 0.1;
        circle._setFade(element, op);
      }
    }, 50);
  },

  _setFade(element, op) {
    element.style.opacity = op;
    element.style.filter = `alpha(opacity=${op * 100})`;
  },

  _clearFade(element, visible) {
    if (this._resultsTimer) {
      clearTimeout(this._resultstimer);
      this.resultsTimer = null;
    }
    if (this._fadeTimer) {
      clearInterval(this._fadeTimer);
      this._fadeTimer = null;
      this._freezeRadiusBox = false;
    }

    if (!visible) {
      element.style.display = 'none';
      this._setFade(element, 1);
    }
  },

});
