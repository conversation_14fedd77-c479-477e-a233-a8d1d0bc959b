const app = window.__app;
const clone = require('clone');

module.exports = {

  componentWillMount() {
    const that = this;

    // Expose the cursor object
    this.cursor = app.cursor;

    // Common bindHandler
    const bindHandler = function (key) {
      return function (data) {
        const res = {};
        res[key] = data.target.get();
        that.setState(res);
      };
    };

    if (this.cursors) {
      this.__cursors = {};
      this.__cursorHandlers = {};

      const thisCursors = clone(this.cursors);

      for (const i in thisCursors) {
        // Take in object like {location: 'props', path: ['listing', 'Id']} to get string from props
        for (const j in thisCursors[i]) {
          if (typeof thisCursors[i][j] === 'object') {
            if (thisCursors[i][j].location === 'props') {
              let value = this.props;
              for (let p = 0; p < thisCursors[i][j].path.length; p++) {
                value = value[thisCursors[i][j].path[p]];
                if (!value) { // Handle Undefined
                  delete thisCursors[i];
                  break;
                }
              }
              if (thisCursors[i]) {
                thisCursors[i][j] = value;
              }
            }
          }
        }

        this.__cursors[i] = app.cursor.select(thisCursors[i]);
        this.__cursorHandlers[i] = bindHandler(i);

        // Set initial data:
        const res = {};
        res[i] = this.__cursors[i].get();
        this.setState(res);

        // On cursor update
        // console.log('-- Binding cursor: "%s" @ %s --', this.__cursors[i].hash, this.constructor.displayName );
        this.__cursors[i].on('update', this.__cursorHandlers[i]);
      }
    }

    if (this.facets) {
      this.__facets = {};
      this.__facetHandlers = {};

      for (const i in this.facets) {
        this.__facets[i] = app.cursor.facets[this.facets[i]];
        this.__facetHandlers[i] = bindHandler(i);

        // Set initial data:
        const res = {};
        res[i] = this.__facets[i].get();
        this.setState(res);

        // On facet update
        console.log('-- Binding facet: "%s" @ %s --', this.facets[i], this.constructor.displayName);
        this.__facets[i].on('update', this.__facetHandlers[i]);
      }
    }
  },

  componentWillUnmount() {
    if (this.__cursors) {
      for (const i in this.__cursors) {
        // console.log('-- Unbinding cursor: "%s" @ %s --', this.__cursors[i].hash, this.constructor.displayName );
        this.__cursors[i].off('update', this.__cursorHandlers[i]);
      }
    }

    if (this.__facets) {
      for (const i in this.__facets) {
        // console.log('-- Unbinding facet: "%s" @ %s --', this.facets[i], this.constructor.displayName );
        this.__facets[i].off('update', this.__facetHandlers[i]);
      }
    }
  },

};
