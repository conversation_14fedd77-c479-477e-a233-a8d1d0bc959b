const app = window.__app;

module.exports = {

  cursors: require('./cursors'),

  pureRender: require('react-addons-pure-render-mixin'),

  leaflet: {
    componentWillMount() {
      this.leaflet = app.leaflet;
    },
  },

  leafletParcel: {
    componentWillMount() {
      this.leafletParcel = app.leafletParcel;
    },
  },

  googleParcel: {
    componentWillMount() {
      this.googleParcel = app.googleParcel;
    },
  },

  actions: {
    componentWillMount() {
      this.actions = app.actions;
    },
  },

  debug: process.env.NODE_ENV === 'production' ? {} : {
    componentWillMount() {
      console.info('-- Begin WillMount: %s --', this.constructor.displayName, Date.now());
    },

    // componentDidMount: function() {
    // console.info('-- End mount: %s --', this.constructor.displayName, Date.now() );
    // },

    componentWillUpdate() {
      console.info('-- <PERSON><PERSON> willUpdate: %s --', this.constructor.displayName, Date.now());
    },

    componentDidUpdate() {
      console.info('-- End Updated: %s --', this.constructor.displayName, Date.now());
    },

  },

  initState: {
    getInitialState() {
      return {};
    },
  },

  router: {
    componentWillMount() {
      this.router = app.router;
    },
  },

  utils: {
    componentWillMount() {
      this.utils = app.utils;
    },
  },

  events: {
    componentWillMount() {
      this.events = app.events;
    },
  },

};
