module.exports = function (app) {
  function GoogleParcel() {
  }

  // The map
  GoogleParcel.m = null;
  GoogleParcel.marker = null;

  GoogleParcel.createMap = function (activeListing, domNode) {
    if (activeListing && domNode) {
      const propertyLatLon = new google.maps.LatLng(activeListing.Location.Lat, activeListing.Location.Lon);

      const mapOptions = {
        mapTypeId: google.maps.MapTypeId.HYBRID,
        maxZoom: 21,
        zoom: 19,
        tilt: 0,
        mapTypeControl: false,
        streetViewControl: false,
        overviewMapControl: false,
        addressControl: false,
        center: propertyLatLon,
        zoomControl: true,
        scrollwheel: false,
        zoomControlOptions: {
          style: google.maps.ZoomControlStyle.LARGE,
          position: google.maps.ControlPosition.TOP_RIGHT,
        },
        panControl: false,
      };

      GoogleParcel.m = new google.maps.Map(domNode, mapOptions);
      app.actions.analytics.sendEvent('detail view', 'google parcel', 'create map');

      if (activeListing.DisplayAddress) {
        this.marker = new google.maps.Marker({
          position: propertyLatLon,
          map: GoogleParcel.m,
          title: 'House',
        });
      }

      GoogleParcel.setOverlay(activeListing);
    }

    return GoogleParcel.m;
  };

  GoogleParcel.setOverlay = function (activeListing) {
    // NPLAY-5240 Remove Policy Map Data
    // if (Dmp.Env.Connections["SS"].isReady()) {
    //   app.api.getParcelDataByGeo(activeListing.Location.Lat, activeListing.Location.Lon, function (LOCID, APN, Geometry) {
    //      if (Dmp && Dmp.Layer) {
    //         var tileLayer = new Dmp.Layer.TileLayer("SS", "SS.BASE.PARCELS/PARCELTILES", {
    //           minZoom:17, maxZoom:21
    //         });
    //         GoogleParcel.m.overlayMapTypes.push(new google.maps.ImageMapType(tileLayer));

    //         var dimensionLayer = new Dmp.Layer.WMSLayer("dimensionLayer", "SS",
    //                        { showField: 'LOCID', showValues: LOCID});

    //         dimensionLayer.addChild("dimensionLayer", "SS.BASE.PARCELS/Parcels", "Styles/Measurement_USBoundary.sld.xml",
    //           { zoomRange: { min: 16, max: 19} });

    //         if (dimensionLayer.setMap) {
    //          dimensionLayer.setMap(GoogleParcel.m);
    //        }
    //       }
    //    });
    // } else {
    setTimeout(() => {
      GoogleParcel.setOverlay(activeListing);
    }, 1000);
    // }
  };

  return GoogleParcel;
};
