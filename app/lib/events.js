const EventEmitter = require('events');

class Events extends EventEmitter {
}

const events = new Events();

events.LISTING_VIEWED = 'listing_viewed';
events.PROGRESS_CLICKED = 'progress_clicked';
events.CHECK_FACEBOOK_LOGIN_STATUS_RESPONSE = 'check_facebook_login_status_response';
events.ROUTE_COMPLETE = 'route_complete';
events.PROMPT_FOR_LOGIN = 'prompt_for_login';
events.CONTAINER_SCROLL = 'scroll';
events.GALLERY_PHOTO_VIEWED = 'gallery_photo_viewed';

module.exports = events;
