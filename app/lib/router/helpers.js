import agentPickerContext from '../../models/agentPickerContext';

module.exports = function (app) {
  function Helpers() {}

  Helpers.abortXhr = function (route, next) {
    app.api.abort();
    return next();
  };

  Helpers.validateLocation = function (route, next) {
    if (app.utils.validateLocationStr(route.params.location)) {
      return next();
    }

    return app.router.go('/', { force: true, replace: true });
  };

  // On 404 - Redirect back to Error Page
  Helpers.redirectToError = function (errorMessage = 'Page not found.') {
    app.cursor.set(['layout', 'error'], errorMessage);
    return app.router.go('/oh-shucks', { force: true, replace: true });
  };

  Helpers.errorUrlRewrite = function (route, next) {
    route.errorUrl = location.href;
    next();
  };

  Helpers.extractAgentId = function (route, next) {
    const { agentId } = route.params;

    app.actions.common.saveAgentId(agentId, null, (err, data) => {
      if (!err) {
        console.log('Save Agent Id Retrieved');

        if (route.qs.p) { // Service overwrite with query string
          app.actions.common.servicesOverwrite[data.Id] = [route.qs.p.toUpperCase()];
        }

        if (data.CustomURL && data.CustomURL !== agentId) {
          // Use Custom URL
          app.router.go(route.url.replace(agentId, data.CustomURL)
              + window.location.search, {
            replace: true,
          });
        } else {
          return next();
        }
      } else {
        Helpers.redirectToError('Unable to retrieve your agent.');
      }
      return null;
    });
  };

  Helpers.checkDemo = function (route, next) {
    const data = app.cursor.get('shared', 'agent', 'data');
    app.actions.demo.checkDemo(route.qs.cid, data, (valid) => {
      if (valid) {
        return next();
      }
      const listingId = route.params && route.params.id;

      const newRouteIsAllowed = route.url.match(/.+(\/agent|\/featured|\/buyer)/);
      const noIdxMobileLanding = (!data.HomeSearchRegisteredDateTime) && (route.url.match(/^\/[^/]+$/) || route.url.match(/^\/[^/]+[/]$/)) && app.utils.isMobile();

      // if on map or grid and viewing a listing, use the agent-less listing route to pick a new agent
      if (listingId
        && route.url.match(/.+(\/map|\/grid)/)
        || (route.url.match(/(.+\/listing)/) && !route.url.match(/.+(\/agent|\/featured|\/buyer)/))
      ) {
        app.router.go(`/listing/${listingId}`);
      } else if (!newRouteIsAllowed && !noIdxMobileLanding) {
        // if not on agent page and going to invalid route, redirect to agent page
        app.router.go(`/${data.CustomURL || data.Id}/agent${location.search}`, {
          replace: true,
        });
      } else {
        // otherwise let it through which may cause an agent switch
        next();
      }
      return null;
    });
  };

  Helpers.extractLocationQuery = function (route, next) {
    const q = route.qs && route.qs.q;
    if (q) {
      app.actions.common.saveLocationQuery(decodeURIComponent(decodeURIComponent(q)));
    }

    const saleType = route.qs && route.qs.st;
    if (saleType == 1 || saleType == 2) {
      app.actions.menu.updateSearchField([{ key: 'saleType', value: saleType == 2 ? 2 : 1 }]);
    }
    if (route.qs && route.qs.fr) {
      // Coming From Onboarding
    }
    next();
  };

  Helpers.redirectOnMobile = function (route, next) {
    if (app.utils.useMobileSite()) {
      if (route.url.match(/\/tagging/)) {
        return app.router.go(route.url.replace(/\/tagging/, '/buyer'));
      }
      if (route.url.match(/\/featured/)) {
        if (app.cursor.get(['layout', 'agent'])) {
          app.utils.updateCursor({
            cursor: app.cursor.select(['layout']),
            defaults: app.cursorDefaults.layout,
            finalState: {},
          });
          app.cursor.commit();
        }
        setTimeout(() => {
          app.actions.agent.setMobileFeature();
          app.router.go(route.url.replace(/\/featured/, '/agent'));
        }, 0);
        return;
      }
    }

    next();
  };

  Helpers.mobileMoveToTop = function (route, next) {
    if (app.utils.useMobileSite()) {
      window.scrollTo(0, 0);
    }

    next();
  };

  Helpers.handleSavedSearch = function (route) {
    const { savedSearchId } = route.params;

    if (!savedSearchId) {
      Helpers.redirectToHomeOrAgentLanding();
      return;
    }

    app.api.searchHistoryById(savedSearchId, (res) => {
      if (res && typeof res === 'object') {
        const searchQuery = [
          res.Latitude,
          res.Longitude,
          res.Distance,
        ].join(',');
        const opts = app.utils._paramsToOpts(res.Filters);

        app.actions.common.setPendingOnboardingAgentId(res.AgentId);
        app.actions.menu.resumeSearchHistory(
          res.AgentId,
          res.SearchText,
          searchQuery,
          opts,
          new Date(res.LastSearchDate),
          res.Theme,
          res.RateplugBuyerId,
        );
      } else {
        Helpers.redirectToHomeOrAgentLanding();
      }
    });
  };

  Helpers.redirectAgentlessMap = function (route) {
    const location = app.utils.validateLocationStr(route.params.location);
    if (!location) {
      return Helpers.redirectToError();
    }

    const context = {
      lat: location.Lat,
      lng: location.Lon,
      onboardingAgentId: app.actions.common.getOnboardingAgentId(),
    };

    const cb = (agent) => {
      if (agent && (agent.CustomURL || agent.Id)) {
        const url = `/${agent.CustomURL || agent.Id}/search/map/${location.Lat},${location.Lon},${location.radius},${location.zoom || app.leaflet.opts.initialZoom}${document.location.search}`;
        return app.router.go(url);
      }

      return Helpers.redirectToError('Cannot find an agent in this area.');
    };

    app.models.agentPicker.setRouteCallback(cb);
    agentPickerContext.set(context);
  };

  Helpers.redirectAgentlessListing = function (route) {
    // GET complete
    app.api.listing(route.params.id, (res) => {
      const Lat = res && res.Location && res.Location.Lat;
      const Lon = res && res.Location && res.Location.Lon;
      if (!Lat || !Lon) {
        return Helpers.redirectToError('Cannot find your listing.');
      }

      const context = {
        lat: Lat,
        lng: Lon,
        onboardingAgentId: app.actions.common.getOnboardingAgentId(),
        mlsIds: res.MlsIds,
      };

      const cb = (agent) => {
        if (agent && (agent.CustomURL || agent.Id)) {
          const url = `/${agent.CustomURL || agent.Id}/search/map/${Lat},${Lon},${app.leaflet.opts.circleDefaultRadius}/${route.params.id}`;
          return app.router.go(url);
        }

        return Helpers.redirectToError('Cannot find an agent in this area.');
      };

      app.models.agentPicker.setRouteCallback(cb);
      agentPickerContext.set(context);
    });
  };

  Helpers.redirectAgentlessMapListing = function (route) {
    const location = app.utils.validateLocationStr(route.params.location);
    if (!location) {
      return Helpers.redirectToError();
    }

    app.api.listing(route.params.id, (listing) => {
      if (listing === 404) {
        listing = null;
      }

      let mlsIds = null;
      if (listing) {
        mlsIds = listing.MlsIds;
      }

      const context = {
        lat: location.Lat,
        lng: location.Lon,
        onboardingAgentId: app.actions.common.getOnboardingAgentId(),
        mlsIds,
      };

      const cb = (agent) => {
        if (agent && (agent.CustomURL || agent.Id)) {
          let url;
          if (listing) {
            url = app.router.generateUrl(app.router.ROUTE_NAMES.MAP, {
              agentId: agent.CustomURL || agent.Id,
              location: `${listing.Location.Lat},${listing.Location.Lon},${location.radius}${location.zoom ? `,${location.zoom}` : ''}`,
              id: route.params.id,
            });
          } else {
            url = app.router.generateUrl(app.router.ROUTE_NAMES.MAP, {
              agentId: agent.CustomURL || agent.Id,
              location: `${location.Lat},${location.Lon},${location.radius}${location.zoom ? `,${location.zoom}` : ''}`,
            });
          }

          return app.router.go(url);
        }

        return Helpers.redirectToError('Cannot find an agent in this area.');
      };

      app.models.agentPicker.setRouteCallback(cb);
      agentPickerContext.set(context);
    });
  };

  Helpers.redirectToHomeOrAgentLanding = function () {
    app.router.go('/'.concat(app.actions.common.getOnboardingAgentId() || ''));
  };

  Helpers.checkQuerystring = function (route, next) {
    next();
  };

  return Helpers;
};
