const _ = require('lodash');
const Router = require('web-client-router');
const moment = require('moment');
const pathToRegexp = require('path-to-regexp');
const querystring = require('querystring');

const ROUTE_NAMES = {
  HOME: 'Home',
  MORE_HOME: 'MORE Home',
  ERROR: 'Error',
  TAGGING: 'Tagging',
  LOGOUT: 'Logout',
  FACEBOOK: 'Facebook',
  FRAME: 'Frame',
  MAP: 'Map',
  GRID: 'Grid',
  MENU: 'Menu',
  FEATURED: 'Featured',
  BUYER: 'Buyer',
  AGENT: 'Agent',
  LISTING: 'Listing',
  AGENTLESS_MAP: 'Agentless Map',
  AGENTLESS_LISTING: 'Agentless Listing',
  AGENTLESS_MAP_LISTING: 'Agentless Map Listing',
  AGENT_SEARCH: 'Agent Search',
  DEMO_EXPIRED: 'Demo Expired',
  LANDING: 'Landing',
  EMPLOYEE_UI_REDIRECT: 'Employee UI',
  <PERSON>AR<PERSON>: 'Search',
  SAVED_SEARCH: 'Saved Search',
  AGENT_FALLTHROUGH_ERROR: 'Agent Fallthrough',
  FALLTHROUGH_ERROR: 'Fallthrough',
  HOME_WORTH: 'Home Value',
  DREAM_SWEEPS: 'Dream Sweeps',
  MEMBER_SEARCH: 'Member Search',
  MEMBER_LISITNGS: 'Member Listings',
};

module.exports = function (app, opts) {
  const helpers = require('./helpers')(app);

  const routes = {

    [ROUTE_NAMES.HOME]: {
      path: '/',
      handler: app.actions.home.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring],
    },

    [ROUTE_NAMES.MORE_HOME]: {
      path: '/more',
      handler: app.actions.home.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring],
    },

    [ROUTE_NAMES.SEARCH]: {
      path: '/search',
      handler: app.actions.home.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring],
    },

    [ROUTE_NAMES.EMPLOYEE_UI_REDIRECT]: {
      path: '/employee',
      handler() {
        app.actions.common.enableEmployeeUi();
        app.router.go('/');
      },
      pre: [helpers.abortXhr],
    },

    [ROUTE_NAMES.ERROR]: {
      path: '/oh-shucks',
      handler: app.actions.error.route,
      pre: [helpers.abortXhr, helpers.errorUrlRewrite],
    },

    [ROUTE_NAMES.DEMO_EXPIRED]: {
      path: '/demo-expired',
      handler: app.actions.demo.expiredRoute,
      pre: [helpers.abortXhr],
    },

    [ROUTE_NAMES.MEMBER_SEARCH]: {
      path: '/member-search',
      handler: app.actions.memberSearch.route,
      pre: [helpers.abortXhr],
    },

    [ROUTE_NAMES.MEMBER_LISITNGS]: {
      path: '/member-listings/:location?',
      handler: app.actions.memberSearch.route,
      // Remove member listing page and always show member search page
      // handler: app.actions.memberListings.route,
      pre: [helpers.abortXhr],
    },

    [ROUTE_NAMES.SAVED_SEARCH]: {
      path: '/saved-search/:savedSearchId',
      handler() {},
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.handleSavedSearch],
    },

    [ROUTE_NAMES.AGENTLESS_LISTING]: {
      path: '/listing/:id/:address?',
      handler() {
        // should never reach this handler, pre handler should redirect
      },
      pre: [helpers.abortXhr, helpers.redirectAgentlessListing],
    },

    [ROUTE_NAMES.AGENTLESS_MAP_LISTING]: {
      path: '/search/map/:location/listing/:id',
      handler() {
        // should never reach this handler, pre handler should redirect
      },
      pre: [helpers.validateLocation, helpers.abortXhr, helpers.extractLocationQuery, helpers.redirectAgentlessMapListing],
    },

    [ROUTE_NAMES.AGENTLESS_MAP]: {
      path: '/search/map/:location',
      handler() {
        // should never reach this handler, pre handler should redirect
      },
      pre: [helpers.validateLocation, helpers.abortXhr, helpers.extractLocationQuery, helpers.redirectAgentlessMap],
    },

    [ROUTE_NAMES.SEARCH_REDIRECT]: {
      path: '/search/(.*)',
      handler(route) {
        app.router.go(route.url.replace(/^\/search/, ''));
      },
    },

    [ROUTE_NAMES.AGENT_SEARCH]: {
      path: '/select/map/:location',
      handler: app.actions.map.agentSearchRoute,
      pre: [helpers.validateLocation, helpers.abortXhr, helpers.checkQuerystring, helpers.extractLocationQuery],
    },

    [ROUTE_NAMES.LANDING]: {
      path: '/:agentId',
      handler: app.actions.landing.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.HOME_WORTH]: {
      path: '/:agentId/homevalue',
      handler: app.actions.homeWorth.route,
      pre: [helpers.abortXhr, helpers.extractAgentId],
    },

    [ROUTE_NAMES.DREAM_SWEEPS]: {
      path: '/:agentId/sweepstakes',
      handler: app.actions.buyer.dsRoute,
      pre: [helpers.abortXhr, helpers.extractAgentId],
    },

    [ROUTE_NAMES.AGENT]: {
      path: '/:agentId/agent/:id?',
      handler: app.actions.agent.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.mobileMoveToTop],
    },

    [ROUTE_NAMES.BUYER]: {
      path: '/:agentId/buyer/:id?',
      handler: app.actions.buyer.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.mobileMoveToTop],
    },

    [ROUTE_NAMES.LISTING]: {
      path: '/:agentId/listing/:id',
      handler: app.actions.listing.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.MAP]: {
      path: '/:agentId/search/map/:location/:id?',
      handler: app.actions.map.route,
      pre: [helpers.validateLocation, helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.extractLocationQuery],
    },

    [ROUTE_NAMES.GRID]: {
      path: '/:agentId/search/grid/:location/:id?',
      handler: app.actions.grid.route,
      pre: [helpers.validateLocation, helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.extractLocationQuery],
    },

    [ROUTE_NAMES.TAGGING]: {
      path: '/:agentId/tagging/:id?',
      handler: app.actions.tagging.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.redirectOnMobile],
    },

    [ROUTE_NAMES.FEATURED]: {
      path: '/:agentId/featured/:id?',
      handler: app.actions.featured.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo, helpers.redirectOnMobile],
    },

    [ROUTE_NAMES.MENU]: {
      path: '/:agentId/menu/:something?',
      handler: app.actions.menu.route,
      pre: [helpers.abortXhr, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.LOGOUT]: {
      path: '/:agentId/logout',
      handler: app.actions.logout.route,
      pre: [helpers.abortXhr, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.FACEBOOK]: {
      path: '/:agentId/facebook/:tab',
      handler: app.actions.facebook.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.FRAME]: {
      path: '/:agentId/frame/:tab',
      handler: app.actions.facebook.route,
      pre: [helpers.abortXhr, helpers.checkQuerystring, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.AGENT_FALLTHROUGH_ERROR]: {
      path: '/:agentId/(.*)',
      handler: app.actions.error.route,
      pre: [helpers.abortXhr, helpers.extractAgentId, helpers.checkDemo],
    },

    [ROUTE_NAMES.FALLTHROUGH_ERROR]: {
      path: '(.*)',
      pre: [helpers.redirectToError],
      // Should never execute this,
      // but required for router
      handler() {
      },
    },
  };

  function router() {
  }

  // web-client-router expects an object where the keys are the express-like paths
  const formattedRoutes = _.reduce(routes, (result, route, name) => {
    // also set their name property
    route.name = name;
    result[route.path] = route;
    return result;
  }, {});

  // Instantiate the router
  router = new Router(formattedRoutes, opts);

  router.ROUTE_NAMES = ROUTE_NAMES;

  // current route = completed loading, but not set until route finished
  router.currentRoute = null;

  // active route = started loading, stays set when complete
  router.activeRoute = null;

  router.firstRoute = true;

  router.history = [];

  router.getHistoryLength = function () {
    return router.history.length;
  };

  router.getPreviousRoute = function () {
    if (router.getHistoryLength() <= 2) {
      return null;
    }
    return router.history[router.getHistoryLength() - 2];
  };

  router.currentlyOnRoute = function (name) {
    return router.currentRoute && (router.currentRoute.name == name);
  };

  // Router events
  router.events.on('route_start', (route) => {
    console.info('route_start:', route);
    router.activeRoute = route;
  });

  router.events.on('route_complete', (route) => {
    console.info('route_complete:', route);
    app.actions.analytics.sendPageView();
    router.history.push(route);

    router.currentRoute = route;
    /*
      on the very first route, set this agent as the onboarding agent.
      also make sure sufficient time has passed to prevent setting onboarding agent after a refresh (12 hours)
      and check the no_onboarding flag || nob = "no onboarding"
      always set onboarding agent when ob (onboarding) is set
    */
    if ((router.history.length === 1)
        && route.params.agentId
        && !app.actions.common.userHasNavigatedAfter(moment().subtract(12, 'hours'))
        && !('nob' in route.qs)
        || ('ob' in route.qs)
    ) {
      app.actions.common.setOnboardingAgent(app.cursor.get(['shared', 'agent', 'data']));
    }

    if ((router.history.length === 1) && 'lt' in route.qs) {
      // lt will remove the HA_LENDINGTREE_VIEWED flag in session storage
      // Show lending tree modal on listing detail route even if the lending tree modal has been viewed in the session.
      window.sessionStorageAlias.removeItem('HA_LENDINGTREE_VIEWED');
    }

    let hasFlags = false;
    const qs = new URLSearchParams(document.location.search);
    for (const q of ['nob', 'ob', 'p', 'lt']) {
      if (qs.has(q)) {
        hasFlags = true;
        app.actions.analytics.sendEvent('router-qs', q, qs.get(q));
        qs.delete(q);
      }
    }
    if (hasFlags && qs.toString()) {
      window.history.replaceState({}, document.title, `${location.protocol}//${location.host}${location.pathname}?${qs.toString()}`);
    }

    // log last user nav timestamp, after any checks in this function
    app.actions.common.logLastUserNavigationTimestamp();

    // send event agent visit once per session
    if (!window.sessionStorageAlias.getItem('AgentLoggedSent')
      && window.document.cookie.replace(/(?:(?:^|.*;\s*)AgentLogged\s*=\s*([^;]*).*$)|^.*$/, '$1')) {
      app.actions.analytics.sendEvent('tags', 'agent visit', route.name);
      window.sessionStorageAlias.setItem('AgentLoggedSent', '1');
    }

    // active campaign tracking
    if (window.trackcmp) {
      window.trackcmp.parentNode.removeChild(window.trackcmp);
    }
    window.trackcmp = document.createElement('script');
    window.trackcmp.async = true;
    window.trackcmp.type = 'text/javascript';
    const trackcmpS = document.getElementsByTagName('script');
    if (trackcmpS.length) {
      trackcmpS[0].parentNode.appendChild(window.trackcmp);
    } else {
      const trackcmpH = document.getElementsByTagName('head');
      trackcmpH.length && trackcmpH[0].appendChild(window.trackcmp);
    }
    const email = route && route.qs && route.qs.email;
    const trackcmpEmail = (typeof (email) !== 'undefined') ? email : '';
    window.trackcmp.src = `//trackcmp.net/visit?actid=25094270&e=${
      encodeURIComponent(trackcmpEmail)
    }&r=${encodeURIComponent(document.referrer)
    }&u=${encodeURIComponent(window.location.href)}`;

    const listingViewed = route.name == ROUTE_NAMES.LISTING
      || (route.name == ROUTE_NAMES.MAP && route.params.id)
      || (route.name == ROUTE_NAMES.GRID && route.params.id)
      || (route.name == ROUTE_NAMES.AGENT && route.params.id)
      || (route.name == ROUTE_NAMES.FEATURED && route.params.id)
      || (route.name == ROUTE_NAMES.LISTING && route.params.id);

    if (listingViewed) {
      const previousRoute = router.getPreviousRoute();
      if ((!previousRoute) || (previousRoute.params.id != route.params.id)) {
        app.events.emit(app.events.LISTING_VIEWED, route);
      }
    }

    app.events.emit(app.events.ROUTE_COMPLETE, route);
  });

  router.generateUrl = function (routeName, params = {}, queryParams = {}, options = {}) {
    // look up the route
    const route = _.find(routes, (r) => r.name == routeName);

    // excise false params
    const validParams = _.omitBy(params, (param) => !param);

    // generate the url string
    const toPath = pathToRegexp.compile(route.path);
    let url = decodeURIComponent(toPath(validParams));

    // preserve query params if flag is set
    if (options.preserveQuery) {
      queryParams = app.actions.common.getUpdatedQueryString(queryParams);
    }

    // add on query params
    const qs = querystring.stringify(queryParams);
    if (qs) {
      url = url.concat(`?${qs}`);
    }

    return url;
  };

  // router.events.on('pre_complete', function(preData) {
  //   console.log('pre_complete:', preData)
  // });

  // router.events.on('get_complete', function(getData) {
  //   console.log('get_complete:', getData)
  // });

  // router.events.on('route_matched', function(url) {
  //   console.log('route_matched:', url)
  // });

  // router.events.on('route_error', function(err) {
  //   console.log('route_error:', err)
  // });

  // router.events.on('route_not_found', function(url) {
  //   console.log('route_not_found:', url)
  // });

  return router;
};
