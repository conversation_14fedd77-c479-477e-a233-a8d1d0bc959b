const wkx = require('wkx');

function SpatialStream() {
}

/** **
 * General utilities
 *** */

// Takes in a SpatialStream geometry string and returns an array of Leaflet LatLng objects
SpatialStream.wntStringToGeoJson = function (geometryString) {
  const geometry = wkx.Geometry.parse(geometryString);
  return geometry.toGeoJSON();
};

SpatialStream.toTitleCase = function (str) {
  return (str || '').replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
};

/** **
 * School Attendance Zones
 *** */

SpatialStream.latitude = null;
SpatialStream.longitude = null;
SpatialStream.schoolDistrict = null;

// School data from SpatialStream
SpatialStream.schoolsAttendedCallback = null;
SpatialStream.schoolsAttendedByGeo_Success = function (json) {
  if (json.Response.Results.totalRecords <= 0) {
    SpatialStream.schoolsAttended_Failure();
    return;
  }

  let data = json.Response.Results.Data.Row;
  if (!data) {
    data = [];
  } else if (typeof data === 'object' && !data.length) {
    data = [data];
  }
  data.forEach((school) => {
    school.SCH_NAME = SpatialStream.toTitleCase(school.SCH_NAME);

    if (school.GEOMETRY) {
      school.GEOMETRY = SpatialStream.wntStringToGeoJson(school.GEOMETRY);
    }
    //   NOTE: Uncomment this line to pull the school district data from SpatialStream
    //         Currently, we get the district name from GreatSchools, but we must use SpatialStream for the geometry data
    // school.DISTRICT = SpatialStream.schoolDistrict;
  });

  if (SpatialStream.schoolsAttendedCallback) {
    SpatialStream.schoolsAttendedCallback(data);
  }
};

SpatialStream.schoolsAttendedByAddress_Success = function (json) {
  const recordSets = json.Response.Results.RecordSet;
  if (json.Response.Results.totalRecords <= 0) {
    console.log('School attendance zone not found');
    return;
  }

  // Recordsets are...
  //  0 = Geocode data for the address
  //  1 = Schools by attendance zone
  //  2 = School district
  const schools = recordSets[1].Data.Row;
  // const schoolDistrict = recordSets[2].Data.Row;

  if (SpatialStream.schoolsAttendedCallback) {
    SpatialStream.schoolsAttendedCallback(schools);
  }
};

SpatialStream.schoolsAttended_Failure = function () {
  if (SpatialStream.schoolsAttendedCallback) {
    SpatialStream.schoolsAttendedCallback();
  }
};

SpatialStream.schoolsAttendedByGeo = function (latitude, longitude, cb) {
  if (cb) {
    SpatialStream.schoolsAttendedCallback = cb;
  }

  SpatialStream.latitude = latitude;
  SpatialStream.longitude = longitude;

  // If school district is set, get the list of schools, otherwise pull the district first
  //   NOTE: Uncomment this line to pull the school district data from SpatialStream
  //         Currently, we get the district name from GreatSchools, but we must use SpatialStream for the geometry data
  // if (SpatialStream.schoolDistrict) {
  let url = 'GetByGeometry.aspx?';
  url += `geo=POINT(${longitude} ${latitude})`;
  url += '&fields=NCES_SCHID,SCH_NAME,ED_LEVEL,GEOMETRY';
  url += '&dataSource=SS.Admin.MP.SchoolAttendanceZones/SchoolAttendanceZones_DOFS';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.schoolsAttendedByGeo_Success,
    SpatialStream.schoolsAttended_Failure);

  // NOTE: Uncomment these lines to pull the school district data from SpatialStream
  // } else {
  //    SpatialStream.schoolDistrictByGeo(latitude, longitude, SpatialStream.schoolDistrict_Success)
  // }
};

SpatialStream.schoolsAttendedByAddress = function (address, zip, cb) {
  if (cb) {
    SpatialStream.schoolsAttendedCallback = cb;
  }

  let url = 'GetGeocode.aspx?';
  url += `address=${address}`;
  url += `&zip=${zip}`;
  url += '&fields=MPSCHOOLAZ(NCES_SCHID,SCH_NAME,ED_LEVEL),MPSCHOOLDISTRICT(NCES_DISID,DISTNAME,DISTWBSITE)'; // "*,[resource](GEOMETRY),MPSCHOOLAZ(*),MPSCHOOLDISTRICT(*)";
  url += '&datasource=SS.Base.Parcels/Parcels';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.schoolsAttendedByAddress_Success,
    SpatialStream.schoolsAttended_Failure);
};

/** **
 * School District
 *** */
SpatialStream.schoolDistrictCallback = null;
SpatialStream.schoolDistrict_Success = function (json) {
  // Success
  if (json.Response.Results.totalRecords <= 0) {
    SpatialStream.schoolDistrict_Failure();
    return;
  }

  const data = json.Response.Results.Data.Row;
  data.DISTNAME = SpatialStream.toTitleCase(data.DISTNAME);
  data.DISTWBSITE = data.DISTWBSITE.toLowerCase();
  SpatialStream.schoolDistrict = data;

  SpatialStream.schoolsAttendedByGeo(
    SpatialStream.latitude,
    SpatialStream.longitude,
  );
};
SpatialStream.schoolDistrict_Failure = function () {
  if (SpatialStream.schoolDistrictCallback) {
    SpatialStream.schoolDistrictCallback();
  }
};

SpatialStream.schoolDistrictByGeo = function (latitude, longitude, cb) {
  if (cb) {
    SpatialStream.schoolDistrictCallback = cb;
  }

  let url = 'GetByGeometry.aspx?';
  url += `geo=POINT(${longitude} ${latitude})`;
  url += '&fields=NCES_DISID,DISTNAME,STATE,DISTWBSITE';
  url += '&dataSource=SS.Admin.MP.SchoolDistricts/SchoolDistricts_DOFS';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.schoolDistrict_Success,
    SpatialStream.schoolDistrict_Failure);
};

/** **
 * School Geometry
 *** */
SpatialStream.schoolGeometryCallback = null;
SpatialStream.schoolGeometry_Success = function (json) {
  // Success
  if (json.Response.Results.totalRecords <= 0) {
    SpatialStream.schoolGeometry_Failure();
    return;
  }

  let data = json.Response.Results.Data.Row;
  let geometry = null;
  let schoolName = null;

  if (data) {
    // If there are multiple results, use the first one
    if (data.constructor === Array) {
      // eslint-disable-next-line prefer-destructuring
      data = data[0];
    }

    if (data.GEOMETRY) {
      geometry = SpatialStream.wntStringToGeoJson(data.GEOMETRY);
    }

    if (data.SCH_NAME) {
      schoolName = SpatialStream.toTitleCase(data.SCH_NAME);
    }
  }

  if (SpatialStream.schoolGeometryCallback) {
    SpatialStream.schoolGeometryCallback(schoolName, geometry);
  }
};
SpatialStream.schoolGeometry_Failure = function () {
  if (SpatialStream.schoolGeometryCallback) {
    SpatialStream.schoolGeometryCallback();
  }
};
SpatialStream.schoolGeometryByNCESId = function (NCESId, cb) {
  if (cb) {
    SpatialStream.schoolGeometryCallback = cb;
  }

  let url = 'GetByKey.aspx?';
  url += 'keyName=NCES_SCHID';
  url += `&keyValue=${NCESId}`;
  url += '&fields=SCH_NAME,GEOMETRY';
  url += '&dataSource=SS.Admin.MP.SchoolAttendanceZones/SchoolAttendanceZones_DOFS';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.schoolGeometry_Success,
    SpatialStream.schoolGeometry_Failure);
};

/** **
 * Neighborhood Geometry
 *** */
SpatialStream.neighborhoodGeometryCallback = null;
SpatialStream.neighborhoodGeometry_Success = function (json) {
  // Success
  if (json.Response.Results.totalRecords <= 0) {
    SpatialStream.neighborhoodGeometry_Failure();
    return;
  }

  let data = null;
  if (json.Response.Results.Data.Row.length) {
    for (let i = 0; i < json.Response.Results.Data.Row.length; i++) {
      if (json.Response.Results.Data.Row[i].NBR_TYPE == 'N') {
        data = json.Response.Results.Data.Row[i];
      }
    }

    // Default to the last neighborhood in the list
    if (!data) {
      data = json.Response.Results.Data.Row[json.Response.Results.Data.Row.length - 1];
    }
  } else {
    data = json.Response.Results.Data.Row;
  }

  let neighborhoodName = null;
  let geometry = null;

  if (data) {
    if (data.NEIGHBORHD) {
      neighborhoodName = data.NEIGHBORHD;
    }
    if (data.GEOMETRY) {
      geometry = SpatialStream.wntStringToGeoJson(data.GEOMETRY);
    }
  }

  if (SpatialStream.neighborhoodGeometryCallback) {
    SpatialStream.neighborhoodGeometryCallback(neighborhoodName, geometry);
  }
};
SpatialStream.neighborhoodGeometry_Failure = function () {
  if (SpatialStream.neighborhoodGeometryCallback) {
    SpatialStream.neighborhoodGeometryCallback();
  }
};
SpatialStream.neighborhoodGeometryByGeo = function (latitude, longitude, cb) {
  if (cb) {
    SpatialStream.neighborhoodGeometryCallback = cb;
  }

  let url = 'GetByGeometry.aspx?';
  url += `geo=POINT(${longitude} ${latitude})`;
  url += '&fields=*'; // NEIGHBORHD,GEOMETRY";
  url += '&dataSource=SS.Admin.MP.Neighborhoods/Neighborhoods_DOFS';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.neighborhoodGeometry_Success,
    SpatialStream.neighborhoodGeometry_Failure);
};

/** **
 * Parcel data
 *** */

SpatialStream.getParcelDataCallback = null;
SpatialStream.getParcelDataByGeo_Success = function (json) {
  if (json.Response.Results.totalRecords <= 0) {
    SpatialStream.getParcelData_Failure();
    return;
  }

  const data = json.Response.Results.Data.Row;
  let LOCID = null;
  let APN = null;
  let Geometry = null;
  if (data) {
    LOCID = data.LOCID;
    APN = data.APN;
    Geometry = data.GEOMETRY;
  }

  if (SpatialStream.getParcelDataCallback) {
    SpatialStream.getParcelDataCallback(LOCID, APN, Geometry);
  }
};
SpatialStream.getParcelData_Failure = function () {
  if (SpatialStream.getParcelDataCallback) {
    SpatialStream.getParcelDataCallback();
  }
};
SpatialStream.getParcelDataByGeo = function (latitude, longitude, cb) {
  if (cb) {
    SpatialStream.getParcelDataCallback = cb;
  }

  SpatialStream.latitude = latitude;
  SpatialStream.longitude = longitude;

  let url = 'GetByGeometry.aspx?';
  url += `geo=POINT(${longitude} ${latitude})`;
  url += '&fields=LOCID,GEOMETRY,APN';
  url += '&dataSource=SS.Base.Parcels/Parcels';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    SpatialStream.getParcelDataByGeo_Success,
    SpatialStream.getParcelData_Failure);
};

SpatialStream.getPostalCodeGeometry = function (postalCode, callback) {
  let url = 'GetQuery.aspx';
  url += '?fields=*';
  url += '&datasource=ss.base.postalcode/postalcode';
  url += `&query=postcode='${postalCode}'`;
  url += '&returnfullwkt=true';
  url += '&showschema=false';

  Dmp && Dmp.Env.Connections.SS.getJson(url,
    (json) => {
      let data = json.Response.Results.Data.Row;
      let geometry = null;
      // const schoolName = null;

      if (data) {
        // If there are multiple results, use the first one
        if (data.constructor === Array) {
          // eslint-disable-next-line prefer-destructuring
          data = data[0];
        }

        if (data.GEOMETRY) {
          geometry = SpatialStream.wntStringToGeoJson(data.GEOMETRY);
        }
      }

      callback(null, geometry);
    },
    () => {
      callback('failed to fetch zip geometry');
    });
};

module.exports = SpatialStream;
