const map = require('lodash.map');
const forEach = require('lodash.foreach');
// spatialStream = require('./spatialstream'),
const _ = require('lodash');
const xhr = require('./xhr');

// ====== Urls
// BASE_URL has moved to index.jade and been renamed BASE_API_URL
const LISTINGS_URL = 'listings/search/';
const LISTINGS_SEARCH_HISTORY = 'listingsearchhistory/';
const LISTING_URL = 'listings/';
const LISTING_IMAGE_CAPTIONS_URL = 'listings/imagecaptions/';
const LISTINGS_BY_TAG_URL = 'listing/tags';
const FEATURED_LISTINGS_URL = 'listings/search';
const LISTING_TAG_URL = 'tag/listing';
const LISTING_TAG_WITH_COUNT_URL = LISTING_TAG_URL.concat('/getwithlistingcount/');
const LISTING_TAG_BY_LISTING_URL = LISTING_TAG_URL.concat('/getbylisting/');
const LISTING_TAG_BY_TAG_URL = LISTING_TAG_URL.concat('/getbytag/');
const LISTING_TAG_BY_RADIUS_URL = LISTING_TAG_URL.concat('/getbyradius/');
const LISTING_TAG_ALL = 'listingtag';
const SCHOOLS_URL = 'schools/nearby';
// const DEMOGRAPHICS_URL = 'demographics/';
const AGENT_URL = 'agentsearch/getagent/';
const AGENT_COUNT_URL = 'agentsearch/count';
const AGENT_AUTH_URL = 'agentauth/';
const AGENT_SELF_URL = 'agent/';
const REGISTER_AUTO = 'register/auto';
const PICK_AGENT_URL = 'asap/agent/pick';
const SIMULATE_PICK_AGENT_URL = PICK_AGENT_URL; // 'heatmap/agent/pick';
const MLS_URL = 'mls/';
const STATE_URL = 'state/';
const MLSES_URL = 'listings/getmls/';
const SUGGESTIONS_URL = 'listings/search/suggestions/';
const EMAIL_URL = 'messageagent/';
const SHOWING_URL = 'scheduleshowing/';
const CMA_URL = 'requestdetailmarketanalysis/post/';
const LEAD_IDXPIN_URL = 'lead/idxpin/';
// const SCHOOLS_PUBLIC_URL = 'schools/public/';
const SCHOOLS_PRIVATE_URL = 'schools/private/';
const BUYERAUTH_URL = 'buyerauth/';
const BUYER_URL = 'buyer/';
const SHAREPROPERTY_URL = 'shareproperty';
const COOKIE_URL = 'cookies/';
const MLS_BY_STATE_URL = 'mls/state/';
const YELP_CATEGORIES = 'yelp/categories/';
const YELP_SEARCH = 'yelp/';
const SHORTEN_URL = 'webtools/getshorturl';
const DELIVER_FREE_LEAD_TRAFFIC = 'asap/lead/deliver/free';
const DELIVER_CHARGED_LEAD = 'asap/lead/deliver/charge';
const USER_ACTIVITY = 'useractivity/';
const LOGIN_PROMPT_ANALYTICS = 'logintracking';
const CONTEST_SUBMISSION_URL = 'postcontestentry/';
// const CONTEST_LIST_FETCH_URL = 'contests/';
const TERMS_FETCH_URL = 'templates/';
const TRACK_SELECT_AGENT_ACTIVITY_URL = 'SelectAgentactivity';
const DREAMSWEEPS = 'sweepstakes';
const GET_DREAMSWEEPS_ENTRY = 'sweepstakeapplication';
const CREATE_DREAMSWEEPS_LEAD = 'lead/sweepstakeapplication/buyer';
const CREATE_LENDINGTREE_LEAD = 'lead/lendingtree';
const HOME_INSPECTION = 'homeinspection';
const HOME_WORTH_FORM_URL = 'askmyhomevalue';

const NLP_FULL_URL = 'https://nplay-nlp.herokuapp.com/';

module.exports = function (app) {
  /** *
   * Static methods for Api
   * */
  function Api() {
  }

  // Expose xhr abort
  Api.abort = xhr.abort;

  Api.listings = function (opts, cb) {
    let qsParams = app.utils._optsToParams(opts);

    let url;
    if (opts.BoundingBox) {
      // {LatitudeTop}/{LongitudeLeft}/{LatitudeBottom}/{LongitudeRight}/  -- Trailing slash is *required*
      url = BASE_API_URL.concat(LISTINGS_URL, encodeURIComponent(opts.BoundingBox.getNorthEast().lat), '/', encodeURIComponent(opts.BoundingBox.getSouthWest().lng), '/',
        encodeURIComponent(opts.BoundingBox.getSouthWest().lat), '/', encodeURIComponent(opts.BoundingBox.getNorthEast().lng), '/');
    } else {
      url = BASE_API_URL.concat(LISTINGS_URL, opts.Lat, '/', opts.Lon, '/', opts.radius, 'm');
    }
    if (opts.specialFinancing === 'Assumable' || (opts.specialFinancing || [])[0] === 'Assumable') {
      qsParams = {
        ct: 1000,
        sa: 1,
        st: qsParams.st,
        sf: 'Assumable',
      };
    }

    if (opts.specialFinancing === 'FHACondo' || (opts.specialFinancing || [])[0] === 'FHACondo') {
      qsParams = {
        ct: 1000,
        sa: 1,
        st: qsParams.st,
        sf: 'FHACondo',
      };
    }

    if (opts.specialFinancing === 'USDA' || (opts.specialFinancing || [])[0] === 'USDA') {
      qsParams = {
        ct: 1000,
        sa: 1,
        st: qsParams.st,
        sf: 'USDA',
      };
    }

    app.cursor.set(['loading', 'listings'], true);
    app.cursor.commit();

    xhr.get(url,
      qsParams,
      (statusCode, res) => {
        app.cursor.set(['loading', 'listings'], false);
        app.cursor.commit();

        if (statusCode === 404) {
          cb(404);
        } else if (statusCode === 200) {
          // Don't save search history for bounding box queries on desktop
          if (// (!opts.BoundingBox || app.utils.useMobileSite()) &&  // Do it for all since radius change update
            !opts.circleUnchanged && res && res.NumResults && res.NumResults > 0) {
            Api.postSearchHistory(opts.Lat, opts.Lon, opts.radius, res.NumResults, qsParams);
          }

          const finalCallback = (results) => {
            const mappedResults = map(results, (o) => {
              o.Id = String(o.Id); // Cast ID to string
              o.ListedOn = new Date(o.ListedOn);

              // Build image
              o.Image = app.utils.getImageUrls(o, true);

              return o;
            });

            if (mappedResults.length > 0) {
              try {
                window.fbq('track', 'Search', {
                  content_type: 'home_listing',
                  content_ids: mappedResults.map((r) => r.Id).slice(0, 10),
                  city: mappedResults[0].CityName,
                  region: mappedResults[0].State,
                  country: 'US',
                  listing_type: opts.saleType === 2 ? 'for_rent_by_agent' : 'for_sale_by_agent',
                  preferred_baths_range: (opts.minBaths || opts.maxBaths) && [opts.minBaths || 0, opts.maxBaths || 100] || undefined,
                  preferred_beds_range: (opts.minBeds || opts.maxBeds) && [opts.minBeds || 0, opts.maxBeds || 100] || undefined,
                  preferred_price_range: (opts.minPrice || opts.maxPrice) && [opts.minPrice || 0, opts.maxPrice || 1000000000] || undefined,
                });
              } finally {}
            }

            cb(mappedResults, res.NumResults);
          };

          finalCallback(res.Results);
        } else {
          cb(null);
        }
      }, { reqId: 'Api.listings' });
  };

  Api.listingsRaw = function (opts, cb) {
    const qsParams = app.utils._optsToParams(opts);

    xhr.get(BASE_API_URL.concat(LISTINGS_URL),
      qsParams,
      (statusCode, res) => {
        if (statusCode === 200) {
          const mappedResults = map(res.Results, (o) => {
            o.Id = String(o.Id); // Cast ID to string
            o.ListedOn = new Date(o.ListedOn);

            // Build image
            o.Image = app.utils.getImageUrls(o, true);

            return o;
          });

          return cb(mappedResults, res.NumResults);
        }

        if (statusCode === 404) {
          return cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingsRaw' });
  };

  Api.tinyListings = function (opts, cb) {
    // GET api/listings/search/{LatitudeTop}/{LongitudeLeft}/{LatitudeBottom}/{LongitudeRight}/{LatitudeCenter}/{LongitudeCenter}/{Distance}

    if (!(opts.bounds && opts.radius && opts.Lat && opts.Lon)) {
      return cb && cb(null);
    }

    const qsParams = app.utils._optsToParams(opts);

    xhr.get(BASE_API_URL.concat(LISTINGS_URL,
      [opts.bounds._northEast.lat,
        opts.bounds._southWest.lng,
        opts.bounds._southWest.lat,
        opts.bounds._northEast.lng,
        opts.Lat,
        opts.Lon,
        opts.radius].join('/'), 'm'),

    qsParams,

    (statusCode, res) => {
      if (statusCode === 200) {
        return cb(res);
      }
      return cb && cb(null);
    }, { reqId: 'Api.tinyListings' });
  };

  Api.featuredListings = function (opts, cb) {
    xhr.get(BASE_API_URL.concat(FEATURED_LISTINGS_URL), { ...opts, ct: 1000 },
      (statusCode, res) => {
        if (statusCode === 200) {
          const mappedResults = map(res.Results, (o) => {
            o.Id = String(o.Id); // Cast ID to string
            o.ListedOn = new Date(o.ListedOn);

            // Build image
            o.Image = app.utils.getImageUrls(o, true);

            return o;
          });

          if (mappedResults.length > 0) {
            try {
              window.fbq('track', 'Search', {
                content_type: 'home_listing',
                content_ids: mappedResults.map((r) => r.Id).slice(0, 10),
                city: mappedResults[0].CityName,
                region: mappedResults[0].State,
                country: 'US',
              });
            } finally { }
          }

          return cb(mappedResults);
        }

        if (statusCode === 404) {
          return cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.featuredListings', shouldNotAbort: true });
  };

  Api.listing = function (id, cb, doNotAbort) {
    xhr.get(BASE_API_URL.concat(LISTING_URL, id), app.utils.getRateQueryParameters(),
      (statusCode, res) => {
        if (statusCode === 200) {
          res.Id = String(res.Id); // Cast ID to string
          res.ListedOn = new Date(res.ListedOn);

          // Build images
          res.Images = app.utils.getImageUrls(res);

          if (id.match(/^tax/)) {
            res.Id = id;
            res.isTaxProperty = true;
          }

          window.fbq('track', 'ViewContent', app.utils.getListingPixelAttributes(res));

          return cb(res);
        }

        if (statusCode === 404) {
          return cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listing', shouldNotAbort: doNotAbort });
  };

  Api.postSearchHistory = function (lat, lon, radius, resultsCount, filters) {
    const qs = {
      Latitude: lat,
      Longitude: lon,
      Distance: radius,
      ResultsCount: resultsCount,
      Filters: filters,
      CustomName: app.actions.common.getLocationCustomName(lat, lon),
      RateplugBuyerId: window.rateplug.rp_buyer,
      Theme: window.sessionStorage.getItem('HA_THEME') || undefined,
    };

    app.models.agentPicker.oneTimeAgentCallbacks.push((agentId) => {
      qs.agentId = agentId;

      xhr.post(BASE_API_URL.concat(LISTINGS_SEARCH_HISTORY.concat(app.actions.common.isLoggedIn() ? '' : 'anon')),
        qs,
        (statusCode, res) => {
          console.log(`POST Search History StatusCode: ${statusCode}`);
          if (statusCode === 200) {
            if (app.actions.common.isLoggedIn()) {
              app.actions.menu.updateSearchHistory(3000);
            } else {
              app.actions.common.addLocalSavedSearch(res);

              // We cannot register a Rateplug MORE user before Saved Search is created
              // Therefore we are calling buyer login for Rateplug users here to ensure
              // the email template can pull a correct saved search id
              if (window.rateplug.rp_buyer && !window.rateplug.LOGIN_TRIGGERED) {
                window.rateplug.LOGIN_TRIGGERED = true;
                app.actions.login.authApi(`RP_${window.rateplug.rp_buyer}`);
              }
            }
          }
        }, { reqId: 'Api.postSearchHistory', shouldNotAbort: true });
    });
  };

  Api.searchHistory = function (delay, cb) {
    if (!app.actions.common.isLoggedIn()) {
      return;
    }
    setTimeout(() => {
      xhr.get(BASE_API_URL.concat(LISTINGS_SEARCH_HISTORY, ['null', 'null'].join('/')),
        {},
        (statusCode, res) => {
          if (statusCode === 200) {
            return cb(res);
          }
          if (statusCode === 404) {
            return cb(404);
          }
          return cb && cb(null);
        }, { reqId: 'Api.searchHistory' });
    }, delay || 0);
  };

  Api.searchHistoryById = function (id, cb) {
    xhr.get(BASE_API_URL.concat(LISTINGS_SEARCH_HISTORY, id),
      {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb(res);
        }
        if (statusCode === 404) {
          return cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.searchHistoryById' });
  };

  Api.agent = function (id, cb) {
    xhr.get(BASE_API_URL.concat(AGENT_URL), { id },
      (statusCode, res) => {
        app.actions.common.setPendingOnboardingAgent(res);
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.agent', shouldNotAbort: true });
  };

  Api.agentAuth = function (accessToken, cb) {
    xhr.post(BASE_API_URL.concat(AGENT_AUTH_URL), accessToken, cb, { reqId: 'Api.agentAuth', shouldNotAbort: true });
  };
  Api.agentSelf = function (cb) {
    xhr.get(BASE_API_URL.concat(AGENT_SELF_URL), null, cb, { reqId: 'Api.agentSelf', shouldNotAbort: true });
  };

  Api.agentCount = function (cb) {
    xhr.get(BASE_API_URL.concat(AGENT_COUNT_URL), null, cb, { reqId: 'Api.agentCount', shouldNotAbort: true });
  };

  Api.buyerAuth = function (accessToken, cb) {
    xhr.post(BASE_API_URL.concat(BUYERAUTH_URL, '?',
      document.body.dataset.theme === 'more'
        ? `&partner=${app.api.LEAD_PARTNER.MORE}&agentId=${app.actions.common.getAgentId()}`
        : document.body.dataset.theme === 'afordal'
          ? `&partner=${app.api.LEAD_PARTNER.AFORDAL}&agentId=${app.actions.common.getAgentId()}`
          : document.body.dataset.theme === 'fairway'
            ? `&partner=${app.api.LEAD_PARTNER.FAIRWAY}&agentId=${app.actions.common.getAgentId()}`
            : ''), accessToken, cb, { reqId: 'Api.buyerAuth', shouldNotAbort: true });
  };
  Api.buyer = function (cb) {
    xhr.get(BASE_API_URL.concat(BUYER_URL), null, cb, { reqId: 'Api.buyer', shouldNotAbort: true });
  };

  Api.emailRegister = function (param, cb) {
    xhr.post(BASE_API_URL.concat(REGISTER_AUTO), param, cb, { reqId: 'Api.emailRegister', shouldNotAbort: true });
  };

  Api.autosuggest = function (val, cb, locTypeFilter) {
    // Escape things
    // https://developer.mozilla.org/en/docs/Web/JavaScript/Guide/Regular_Expressions#Using_Special_Characters
    val = (val || '').trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    val = val.replace(/:/g, ''); // Remove more special characters

    xhr.get(BASE_API_URL.concat(SUGGESTIONS_URL, [encodeURIComponent(val), 20].join('/')), {},
      (statusCode, res) => {
        let res2 = [];
        if (locTypeFilter && (statusCode === 200)) {
          res.forEach((result) => {
            if (result.location && result.location.locType && (locTypeFilter.indexOf(result.location.locType) < 0)) {
              res2.push(result);
            }
          });
        } else {
          res2 = res;
        }

        const agentMlsId = (app.actions.common.getAgentData() || {}).MlsId;
        if (agentMlsId) {
          res2.sort((s1, s2) => {
            const s1HasAgentMlsId = (_.get(s1, 'location.mlsIds') || []).indexOf(agentMlsId) !== -1 ? 1 : 0;
            const s2HasAgentMlsId = (_.get(s2, 'location.mlsIds') || []).indexOf(agentMlsId) !== -1 ? 1 : 0;
            return s2HasAgentMlsId - s1HasAgentMlsId;
          });
        }

        return cb(statusCode === 200 ? res2 : null);
      }, { reqId: 'Api.autosuggest' });
  };

  Api.geocodeAddress = function (address, cb, key) {
    xhr.get('https://maps.googleapis.com/maps/api/geocode/json?address='
      .concat(encodeURIComponent(address), '&key=', window.CONFIG.GOOGLE_KEY), {},
    (statusCode1, res1) => {
      if (statusCode1 === 200 && res1.status === 'OK' && res1.results && res1.results.length > 0) {
        return cb(res1.results[0]);
      }
      return cb(null);
    }, { reqId: key || 'Api.googleGeocodeAddress' });
  };

  Api.googlePlacesAutocomplete = function (address, cb = () => {}) {
    xhr.get(`https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(address)}&key=${window.CONFIG.GOOGLE_KEY}&types=address`, {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb(null, res);
        }
        return cb(null);
      }, { reqId: 'Api.googlePlacesAutocomplete' });
  };

  Api.geocodeLatLng = function (lat, lng, cb) {
    xhr.get('https://maps.googleapis.com/maps/api/geocode/json?latlng='
      .concat(lat, ',', lng, '&key=', window.CONFIG.GOOGLE_KEY), {},
    (statusCode, res) => {
      if (statusCode === 200 && res.status === 'OK' && res.results && res.results.length > 0) {
        return cb(res.results[0]);
      }
      return cb(null);
    }, { reqId: 'Api.geocodeLatLng' });
  };

  Api.reverseGeocodeCoord = function (coord, cb) {
    xhr.get('https://maps.googleapis.com/maps/api/geocode/json?latlng='
      .concat(encodeURIComponent(coord.lat), ',', encodeURIComponent(coord.lon),
        '&key=', window.CONFIG.GOOGLE_KEY), {},
    (statusCode, res) => {
      if (statusCode === 200 && res.status === 'OK' && res.results && res.results.length > 0) {
        return cb(res.results[0]);
      }
      return cb(null);
    }, { reqId: 'Api.reverseGeocodeCoord' });
  };

  /** *
   * Tagging Methods
   * */
  Api.addTag = function (tagObject, cb) {
    Api.addTags([tagObject], cb);
  };

  Api.addTags = function (tagsObjects, cb) {
    // [{
    //  ListingId: listingId,
    //  Lat: Lat,
    //  Lon: Lon,
    //  Tags: tags
    // }]

    const onboardingAgentId = app.actions.common.getOnboardingAgentId();
    xhr.post(BASE_API_URL.concat(LISTING_TAG_URL, '/', onboardingAgentId || '0'), tagsObjects,
      (statusCode, res) => {
        setTimeout(() => {
          // Because Elastic takes some time to process the inserts
          if (statusCode === 200) {
            return cb && cb(res);
          }
          if (statusCode === 404) {
            return cb && cb(404);
          }
          cb && cb(null);
        }, 1000);
      }, { reqId: 'Api.addTags' });
  };

  Api.listingsByTags = function (tagName, cb) {
    xhr.get(BASE_API_URL.concat(LISTINGS_BY_TAG_URL, tagName ? `/${tagName}` : ''), { page: 1 },
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 400) {
          return cb && cb(400);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingsByTags' });
  };

  Api.listingTagsWithCount = function (cb) {
    xhr.get(BASE_API_URL.concat(LISTING_TAG_WITH_COUNT_URL), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 400) {
          return cb && cb(400);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingTagsWithCount' });
  };

  Api.listingTagsByListing = function (listingId, cb) {
    xhr.get(BASE_API_URL.concat(LISTING_TAG_BY_LISTING_URL, encodeURIComponent(listingId)), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 400) {
          return cb && cb(400);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingTagsByListing' });
  };

  Api.listingTagsByTag = function (tag, cb) {
    xhr.get(BASE_API_URL.concat(LISTING_TAG_BY_TAG_URL, encodeURIComponent(tag)), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 400) {
          return cb && cb(400);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingTagsByTag' });
  };

  Api.listingTagsByRadius = function (Lat, Lon, Radius, cb) {
    xhr.get(BASE_API_URL.concat(LISTING_TAG_BY_RADIUS_URL, [
      encodeURIComponent(Lat), encodeURIComponent(Lon), encodeURIComponent(`${Radius}m`),
    ].join('/')), {},
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 400) {
        return cb && cb(400);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    }, { reqId: 'Api.listingTagsByRadius' });
  };

  Api.listingTagsAll = function (cb) {
    xhr.get(BASE_API_URL.concat(LISTING_TAG_ALL), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 400) {
          return cb && cb(400);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.listingTagsAll' });
  };

  Api.listingImageCaptions = function (listing, cb) {
    xhr.get(BASE_API_URL.concat(LISTING_IMAGE_CAPTIONS_URL,
      listing.MlsIds && listing.MlsIds[0] && listing.MlsIds[0],
      listing.PropertyListingId && listing.PropertyListingId), {},
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    }, { reqId: 'Api.listingImageCaptions' });
  };

  /** *
   * Leads
   * */

  Api.LEAD_ACTIVITY_TYPE_ENUM = Object.freeze(
    {
      AgentPin: 1,
      PropertyPin: 2,
      IDXPin: 3,
      PromoPin: 4,
      SweepstakeApplication: 5,
      HardSweepstakeFacebookApplication: 6,
      SoftSweepstakeFacebookApplication: 7,
      PageEngageReaction: 8,
      HardLeadActivity: 9,
      SoftLeadActivity: 10,
    },
  );

  Api.PRODUCT_TYPE_ENUM = Object.freeze(
    {
      Legacy: 0,
      IDX: 1, // IDX Home Search
      Ads: 2, // READ Advantage
      PageEngage: 3, // Page Engage
      Sweepstakes: 4, // Dream Sweeps
      OfferGrid: 5, // Offer Grid
      RealEstateAgentDirectory: 6, // Real Estate Agent Directory
      Bundle: 7, // TurnKey Suite
      Broker: 8, // Broker
      PageCreate: 9, // Create a Business Page
    },
  );

  Api.LEAD_PIN_TYPE = Object.freeze(
    {
      Automatic: 1,
      Favorite: 2,
      Deleted: 255,
    },
  );

  Api.LEAD_PLATFORM = Object.freeze(
    {
      FB: 'fb',
      GOOGLE_ADS: 'google_ads',
    },
  );

  Api.LEAD_PARTNER = Object.freeze(
    {
      MORE: 'more',
      AFORDAL: 'afordal',
      FAIRWAY: 'fairway',
    },
  );

  Api.addLead = function (leadObj, cb) {
    let leadUrl = null;

    switch (leadObj.productType) {
      case Api.PRODUCT_TYPE_ENUM.IDX:
        leadUrl = LEAD_IDXPIN_URL;
        break;
      default: break;
    }

    leadUrl && xhr.post(BASE_API_URL.concat(leadUrl), leadObj, cb, { reqId: 'Api.addLead', shouldNotAbort: true });
  };

  /** *
   * Schools
   * */

  Api.schoolsNearbyAddress = function (address, state, zip, cb) {
    xhr.get(BASE_API_URL.concat(SCHOOLS_URL),
      {
        address,
        state,
        zip,
      },
      (statusCode, res) => cb(statusCode, res && res.schools ? res.schools : null), { reqId: 'Api.schoolsNearbyAddress' });
  };

  Api.publicSchoolsByGeo = function (latitude, longitude, state, cb) {
    return cb(null);

    /*
    spatialStream.schoolsAttendedByGeo(latitude, longitude, (data) => {
      publicSchoolsMashup(data, latitude, longitude, state, cb);
    });

    function publicSchoolsMashup(schoolArray, latitude, longitude, state, cb) {
      if (!schoolArray) {
        cb(null);
      } else {
        let apiUrl = BASE_API_URL.concat(SCHOOLS_PUBLIC_URL, encodeURIComponent(latitude), '/', encodeURIComponent(longitude), '/', encodeURIComponent(state), '?');
        let schoolCount = 0;
        schoolArray.forEach((school) => {
          if (school && school.NCES_SCHID) {
            if (schoolCount) apiUrl += '&';
            apiUrl = apiUrl.concat('ncesId=', encodeURIComponent(school.NCES_SCHID));
            schoolCount++;
          }
        });

        xhr.get(apiUrl, {},
          (statusCode, res) => {
            if (statusCode === 200) {
              res.forEach((schoolObj) => {
                schoolObj.school.districtFullName = getDistrictByNCESID(schoolArray, schoolObj.school.ncesId);
                schoolObj.school.geometry = getGeometryByNCESID(schoolArray, schoolObj.school.ncesId);
              });

              return cb(res);
            }
            if (statusCode === 404) return cb(404);
            return cb && cb(null);
          }, { reqId: 'Api.publicSchoolsByGeo' });
      }

      function getGeometryByNCESID(schoolArray, id) {
        let geometry = null;
        schoolArray.forEach((school) => {
          if ((school.NCES_SCHID == id) && school.GEOMETRY) geometry = school.GEOMETRY;
        });
        return geometry;
      }

      function getDistrictByNCESID(schoolArray, id) {
        let districtFullName = '';
        schoolArray.forEach((school) => {
          if ((school.NCES_SCHID == id) && school.DISTRICT && school.DISTRICT.DISTNAME) districtFullName = school.DISTRICT.DISTNAME;
        });
        return districtFullName;
      }
    }
    */
  };

  Api.privateSchoolsByGeo = function (latitude, longitude, state, cb) {
    xhr.get(BASE_API_URL.concat(SCHOOLS_PRIVATE_URL, encodeURIComponent(latitude), '/', encodeURIComponent(longitude), '/', encodeURIComponent(state)), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.privateSchoolsByGeo' });
  };

  Api.schoolGeometryByNCESId = function (NCESId, cb) {
    // NPLAY-5240 Remove Policy Map Data
    return cb(400, null);

    // spatialStream.schoolGeometryByNCESId(NCESId, cb);
  };

  Api.getPostalCodeGeometry = function (postalCode, cb) {
    // NPLAY-5240 Remove Policy Map Data
    return cb(400, null);

    // spatialStream.getPostalCodeGeometry(postalCode, cb);
  };

  Api.neighborhoodGeometryByGeo = function (latitude, longitude, cb) {
    // NPLAY-5240 Remove Policy Map Data
    return cb(400, null);

    // spatialStream.neighborhoodGeometryByGeo(latitude, longitude, cb);
  };

  Api.mls = function (mlsId, cb, reqId) {
    xhr.get(BASE_API_URL.concat(MLS_URL, mlsId), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, reqId ? { reqId, throttle: true } : { reqId: 'Api.mls', shouldNotAbort: true });
  };

  Api.mlses = function (opts, cb) {
    if (!(opts.radius && opts.Lat && opts.Lon)) {
      return cb && cb(null);
    }

    xhr.get(BASE_API_URL.concat(MLSES_URL, opts.Lat, '/', opts.Lon, '/', opts.radius), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb(res);
        }
        return cb && cb(null);
      }, { reqId: 'Api.mlses' });
  };

  Api.state = function (mlsId, cb) {
    xhr.get(BASE_API_URL.concat(STATE_URL, mlsId), {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.state', shouldNotAbort: true });
  };

  /** *
   * Demographics
   */
  Api.demographics = function (latitude, longitude, cb) {
    // NPLAY-5240 Remove Policy Map Data
    return cb(null);

    /*
    xhr.get(BASE_API_URL.concat(DEMOGRAPHICS_URL, encodeURIComponent(latitude), '/', encodeURIComponent(longitude), '/'),
      {},
      (statusCode, res) => {
        if (statusCode === 200) return cb && cb(res);
        if (statusCode === 404) return cb && cb(404);
        return cb && cb(null);
      }, { reqId: 'Api.demographics' });
    */
  };

  /** *
   * Agent contact
   * */

  Api.submitEmail = function (name, email, message, id, agentId, cb) {
    xhr.post(BASE_API_URL.concat(EMAIL_URL), {
      BuyerName: name,
      BuyerEmail: email,
      BuyerMessage: message,
      ID: id,
      AgentID: agentId,
    },
    (statusCode) => cb(statusCode),
    { reqId: 'Api.submitEmail', shouldNotAbort: true });
  };

  Api.submitShowing = function (name, email, id, date, time, agentId, cb) {
    xhr.post(BASE_API_URL.concat(SHOWING_URL),
      {
        BuyerName: name,
        BuyerEmail: email,
        ID: id,
        ShowDate: date,
        ShowTime: time,
        AgentID: agentId,
      },
      (statusCode) => cb(statusCode),
      { reqId: 'Api.submitShowing' });
  };

  Api.submitCMA = function (name, email, id, agentId, cb) {
    xhr.post(BASE_API_URL.concat(CMA_URL),
      {
        BuyerName: name,
        BuyerEmail: email,
        ID: id,
        AgentID: agentId,
      },
      (statusCode) => cb(statusCode),
      { reqId: 'Api.submitCMA' });
  };

  Api.logOut = function (cb) {
    xhr.delete(BASE_API_URL.concat(BUYERAUTH_URL),
      {},
      () => {
        cb();
      },
      { reqId: 'Api.logOut', shouldNotAbort: true });
  };

  Api.shareProperty = function (shareName, shareEmail, shareMessage, buyerName, buyerEmail, id, agentId, cb) {
    xhr.post(BASE_API_URL.concat(SHAREPROPERTY_URL), {
      ShareName: shareName,
      ShareEmail: shareEmail,
      ShareMessage: shareMessage,
      BuyerName: buyerName,
      BuyerEmail: buyerEmail,
      ID: id,
      AgentId: agentId,
    },
    (statusCode, res) => {
      console.log(`status code is${statusCode}is${statusCode === 200}result${res}`);
      if (statusCode === 200) {
        return cb('success');
      }
      if (statusCode === 404) {
        return cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.shareProperty' });
  };

  Api.pickAgent = function (context, cb) {
    if (!context || !context.lat || !context.lng) {
      return;
    }

    const params = {
      latitude: context.lat,
      longitude: context.lng,
      agentid: context.currentAgentId,
      mlsids: _.isEmpty(context.mlsIds) ? null : context.mlsIds,
      onboardingagentid: context.onboardingAgentId,
      interacted: context.interacted ? null : false,
    };

    // if a user came from ad, and we haven't forced the first charge yet, we want to do so
    if (app.actions.common.userCameFromFacebookAd() && (!app.actions.common.haveForcedFirstCharge())) {
      params.forcecharging = true;
      // also delete the current agent, we will not pass that in the first call, so the backend will run the bidding
      delete params.agentid;
    }

    // pass no charge for leads flag if true, or if option was set
    if (context.noCharge) {
      params.noChargeForLeads = true;
    }

    // pass forcecharging if the option was set
    if (context.forceCharge) {
      params.forcecharging = true;
    }

    xhr.get(BASE_API_URL.concat(PICK_AGENT_URL), params, (statusCode, body, res) => {
      if (statusCode === 200) {
        // flag that the charge was forced
        if (app.actions.common.userCameFromFacebookAd() && (!app.actions.common.haveForcedFirstCharge())) {
          app.actions.common.setForcedFirstCharge(true);
        }
        return cb(null, body, res);
      }

      return cb(statusCode);
    }, { reqId: 'Api.pickAgent' });
  };

  Api.simulatePickAgent = function (options = {}, cb) {
    const params = {
      latitude: options.lat,
      longitude: options.lng,
      agentid: options.currentAgentId,
      mlsids: options.mlsIds,
      onboardingagentid: options.onboardingAgentId,
    };

    xhr.get(BASE_API_URL.concat(SIMULATE_PICK_AGENT_URL), params, (statusCode, res) => {
      if (statusCode === 200) {
        return cb(null, res && res.Agent);
      }

      return cb(statusCode);
    }, { reqId: 'Api.simulatePickAgent' });
  };

  Api.nlp = function (query, cb) {
    xhr.get(NLP_FULL_URL, { q: query },
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      }, { reqId: 'Api.nlp' });
  };

  Api.getParcelDataByGeo = function (latitude, longitude, cb) {
    // NPLAY-5240 Remove Policy Map Data
    return cb(null);

    // spatialStream.getParcelDataByGeo(latitude, longitude, (data) => cb(data));
  };

  Api.mlsByState = function (stateCode, cb) {
    xhr.get(BASE_API_URL.concat(MLS_BY_STATE_URL, stateCode),
      {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      },
      { reqId: 'Api.mlsByState' });
  };

  Api.setAgentCookie = function (agentId, cb) {
    xhr.get(BASE_API_URL.concat(COOKIE_URL, agentId),
      {},
      (statusCode, res) => {
        if (statusCode === 200) {
          return cb && cb(res);
        }
        if (statusCode === 404) {
          return cb && cb(404);
        }
        return cb && cb(null);
      },
      { reqId: 'Api.setAgentCookie' });
  };

  /*
    DEMO RELATED ENDPOINTS
   */

  Api.checkCampaignView = function (cid, viewMode, cb) {
    xhr.get(BASE_API_URL
      .concat(['campaignview', cid, viewMode].join('/')),
    {},
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.checkCampaignView' });
  };

  Api.postCampaignView = function (cid, viewMode, cb) {
    xhr.post(BASE_API_URL
      .concat(['campaignview', cid, viewMode].join('/')),
    {},
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.postCampaignView' });
  };

  Api.getCampaignValues = function (cid, cb) {
    xhr.get(BASE_API_URL
      .concat(['campaignvalue', cid].join('/')),
    {},
    (statusCode, res) => {
      const resObj = {};

      forEach(res, (obj) => {
        resObj[obj.Name] = obj.Value;
      });

      if (statusCode === 200) {
        return cb && cb(resObj);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.getCampaignValues' });
  };

  Api.postCampaignValues = function (cid, values, cb) {
    const postValues = [];
    forEach(values, (val, key) => {
      postValues.push({ Name: key, Value: val });
    });

    xhr.post(BASE_API_URL
      .concat(['campaignvalue', cid].join('/')),
    postValues,
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.postCampaignValues' });
  };

  Api.checkCampaignValidate = function (val, cb) {
    xhr.get(BASE_API_URL
      .concat(['campaignvalidate', val.mlsId, val.agentId || 0, val.officeId || 0, val.nplayAgentId || 0].join('/')),
    {},
    (statusCode, res) => {
      if (statusCode === 200) {
        return cb && cb(res);
      }
      if (statusCode === 404) {
        return cb && cb(404);
      }
      return cb && cb(null);
    },
    { reqId: 'Api.checkCampaignValidate' });
  };

  Api.deliverFreeLeadTraffic = function (agentId, latitude, longitude, callback = () => {}) {
    const params = {
      agentId,
      latitude,
      longitude,
    };

    if (!agentId) {
      return console.error('Attempting to deliver traffic without an agentId');
    }

    xhr.get(BASE_API_URL.concat(DELIVER_FREE_LEAD_TRAFFIC), params, (statusCode) => {
      if (statusCode === 200) {
        return callback && callback(null);
      }

      return callback && callback(statusCode);
    },
    { reqId: 'Api.deliverFreeLeadTraffic' });
  };

  Api.deliverChargedLead = function (options, callback = () => {}) {
    const {
      onboardingAgentId, agentId, lat, lng,
    } = options;

    let params = {
      agentId,
      onboardingAgentId,
      lat,
      lng,
    };

    params = _.omitBy(params, _.isNil);

    if (!agentId) {
      return console.error('Attempting to deliver charged lead without an agentId');
    }

    xhr.get(BASE_API_URL.concat(DELIVER_CHARGED_LEAD), params, (statusCode) => {
      if (statusCode === 200) {
        return callback && callback(null);
      }

      return callback && callback(statusCode);
    }, { reqId: 'Api.deliverChargedLead', shouldNotAbort: true });
  };

  Api.fetchYelpCategories = function (callback) {
    xhr.get(BASE_API_URL.concat(YELP_CATEGORIES), {}, (statusCode, results) => {
      if (statusCode === 200) {
        return callback && callback(null, results);
      }

      return callback && callback(statusCode);
    }, { reqId: 'Api.fetchYelpCategories' });
  };

  Api.fetchYelpSearchResultsBoundingBox = function (category, latA, lngA, latB, lngB, callback) {
    if (!(category && latA && lngA && latB && lngB)) {
      return console.warn('Attempting to get yelp search results with invalid parameters');
    }

    app.actions.common.loadingCursor.set('yelpSearchResults', true);
    app.cursor.commit();

    xhr.get(BASE_API_URL.concat(YELP_SEARCH).concat(`${category}/${latA}/${lngA}/${latB}/${lngB}/`), {}, (statusCode, results) => {
      app.actions.common.loadingCursor.set('yelpSearchResults', false);
      app.cursor.commit();

      if (statusCode === 200) {
        return callback && callback(null, results);
      }

      return callback && callback(statusCode);
    }, { reqId: 'Api.fetchYelpSearchResultsBoundingBox' });
  };

  Api.shortenUrl = function (url = window.location.href, callback) {
    xhr.post(BASE_API_URL.concat(SHORTEN_URL), url, (statusCode, res) => {
      if (statusCode === 200) {
        return callback && callback(null, res);
      }

      return callback && callback(statusCode, res);
    });
  };

  Api.sendUserActivity = function (activity, callback) {
    xhr.post(BASE_API_URL.concat(USER_ACTIVITY), activity, (statusCode) => {
      if (statusCode === 200) {
        return callback && callback(null);
      }

      return callback && callback(statusCode);
    }, { reqId: 'Api.sendUserActivity', shouldNotAbort: true });
  };

  /*
    Sort type corresponds to yelp API
      0 = Best match
      1 = distance
      2 = highest rating

    Default radius 4828 is 3 miles in meters
  */
  Api.fetchYelpSearchResultsRadius = function (category, lat, lng, radius = 4828, sortType = 0, callback) {
    if (!(lat && lng)) {
      return console.warn('Attempting to get yelp search results with invalid parameters');
    }

    app.actions.common.loadingCursor.set('yelpSearchResults', true);
    app.cursor.commit();

    const params = {
      Radius: radius,
      SortType: sortType,
    };
    if (!_.isEmpty(category)) {
      params.CategoryFilter = category;
    }

    xhr.get(BASE_API_URL.concat(YELP_SEARCH).concat(`radius/${lat}/${lng}/`), params, (statusCode, results) => {
      app.actions.common.loadingCursor.set('yelpSearchResults', false);
      app.cursor.commit();

      if (statusCode === 200) {
        return callback && callback(null, results);
      }

      return callback && callback(statusCode);
    }, { reqId: 'Api.fetchYelpSearchResultsRadius' });
  };

  Api.createLoginPromptView = function (data = {}, callback) {
    xhr.post(BASE_API_URL.concat(LOGIN_PROMPT_ANALYTICS), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback && callback(null, result);
      }
      if (statusCode) {
        return callback && callback(statusCode);
      }

      console.error('unknown network error');
      callback && callback('unknown network error');
    }, { reqId: 'Api.createLoginPromptView' });
  };

  Api.updateLoginPromptView = function (data = {}, callback) {
    xhr.patch(BASE_API_URL.concat(LOGIN_PROMPT_ANALYTICS), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback && callback(null, result);
      }
      if (statusCode) {
        return callback && callback(statusCode);
      }

      console.error('unknown network error');
      callback && callback('unknown network error');
    }, { reqId: 'Api.updateLoginPromptView' });
  };

  Api.submitContestEntry = function (contestId, callback) {
    if (!contestId) {
      return callback('Null contest ID');
    }

    xhr.post(BASE_API_URL.concat(CONTEST_SUBMISSION_URL).concat(contestId), { contestId }, (statusCode, result) => {
      if (statusCode === 200) {
        return callback && callback(null, result);
      }
      if (statusCode) {
        return callback && callback(statusCode);
      }

      console.error('unknown network error');
      callback('unknown network error');
    }, { reqId: 'Api.submitContestEntry' });
  };

  Api.getIntersectingAgentsByCircle = function (data = {}, callback = () => {}) {
    xhr.get(BASE_API_URL.concat('agents/intersecting/circle'), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getIntersectingAgentsByCircle' });
  };

  Api.getIntersectingAgentsByPoint = function (data = {}, callback = () => {}) {
    xhr.get(BASE_API_URL.concat('agents/intersecting/point'), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getIntersectingAgentsByPoint' });
  };

  Api.fetchTermsTemplate = function (templateId, callback) {
    xhr.get(BASE_API_URL.concat(TERMS_FETCH_URL, templateId), {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback && callback(null, result);
      }
      if (statusCode) {
        return callback && callback(statusCode);
      }

      console.error('unknown network error');
      callback('unknown network error');
    }, { reqId: 'Api.fetchTermsTemplate', shouldNotAbort: true });
  };

  Api.getBestAgentsByPoint = function (data = {}, callback = () => {}) {
    xhr.get(BASE_API_URL.concat('agents/best/point'), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getBestAgentsByPoint' });
  };

  Api.createWindowCloseRecord = function (data = {}) {
    if ((!navigator) || (!navigator.sendBeacon) || (!FormData)) {
      return;
    }

    const blob = new Blob([JSON.stringify(data)], { type: 'application/json;charset=UTF-8' });
    navigator.sendBeacon(BASE_API_URL.concat(LOGIN_PROMPT_ANALYTICS), blob);
  };

  Api.trackSelectAgentActivity = function (agentId, callback = () => {}) {
    xhr.post(BASE_API_URL.concat(TRACK_SELECT_AGENT_ACTIVITY_URL).concat(`?agentId=${agentId}`), {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.trackSelectAgentActivity' });
  };

  Api.submitHomeWorthForm = function (formData = {}, callback) {
    xhr.post(BASE_API_URL.concat(HOME_WORTH_FORM_URL), formData, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.submitHomeWorthForm' });
  };

  Api.getDreamsweeps = function (callback) {
    xhr.get(BASE_API_URL.concat(DREAMSWEEPS), {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getDreamsweeps' });
  };

  Api.getDreamsweepsEntry = function (data = {}, callback) {
    xhr.get(BASE_API_URL.concat(GET_DREAMSWEEPS_ENTRY), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getDreamsweepsEntry' });
  };

  Api.createDreamsweepsLead = function (data = {}, callback) {
    xhr.post(BASE_API_URL.concat(CREATE_DREAMSWEEPS_LEAD), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode, result);
    }, { reqId: 'Api.createDreamsweepsLead' });
  };

  Api.createLendingTreeLead = function (data = {}, callback) {
    xhr.post(BASE_API_URL.concat(CREATE_LENDINGTREE_LEAD), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode, result);
    }, { reqId: 'Api.createLendingTreeLead' });
  };

  Api.getHomeInspectionQuote = function (listingId, callback) {
    xhr.get(BASE_API_URL.concat(HOME_INSPECTION, '/quote'), {
      listingId,
    }, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.getHomeInspectionQuote' });
  };

  Api.submitHomeInspectionRequest = function (data = {}, callback) {
    xhr.post(BASE_API_URL.concat(HOME_INSPECTION, '/submit'), data, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.submitHomeInspectionRequest' });
  };

  Api.checkRatePlugRates = function ({ agentId, listingId, listingPrice }, callback) {
    xhr.get(`https://www.rateplug.com/ExternalRatesAPI/api/RP/GetRateSummary/HOMEASAP/${agentId}/${listingId}/Agent/Customer/${listingPrice}/-1/True`, {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, { reqId: 'Api.checkRatePlugRates' });
  };

  Api.getRateplugCustomerInfo = function (customerId, callback) {
    xhr.get(`https://www.rateplug.com/HASAPSpecFinAPI/api/RP/GetCustomerLeadInfo/${customerId}`, {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, {
      reqId: 'Api.getRateplugCustomerInfo',
      headers: {
        'RP-ApiKey': 'd!2kuzOCHLcLsuxu_rOsW0yis',
      },
      shouldNotAbort: true,
    });
  };

  Api.getRateplugLOInfo = function (loId, callback) {
    xhr.get(`https://www.rateplug.com/HASAPSpecFinAPI/api/RP/GetLOInfo/${loId}`, {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, {
      reqId: 'Api.getRateplugLOInfo',
      headers: {
        'RP-ApiKey': 'd!2kuzOCHLcLsuxu_rOsW0yis',
      },
    });
  };

  Api.getMortgageInfo = function ({
    state, county, propertyType, listPrice, isUSDA,
  }, callback) {
    if (!window.rateplug.rp_lo) {
      return callback(400);
    }

    xhr.get(`https://www.rateplug.com/MortgageInfoAPI/api/RP/GetMortgageinfo${window.CONFIG.IN_PROD ? '' : 'Test'}/${window.rateplug.rp_lo}/${state}/${county}/${propertyType}/${listPrice}/${app.actions.menu.getMenuSelections().downPayment || 0}/${isUSDA ? 'Y' : 'N'}/${window.rateplug.rp_buyer}`, {}, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, {
      reqId: 'Api.getMortgageInfo',
      headers: {
        'RP-ApiKey': 'd!2kuzOCHLcLsuxu_rOsW0yis',
      },
    });
  };

  Api.getRateplugPropertyDetails = function ({
    // eslint-disable-next-line camelcase
    FIPS, FC_PropertyID, AS_PropertyID, FHAEligible, VAEligible, USDAEligible, ListPrice, PropertyType, DownPayment,
  }, callback) {
    xhr.post('https://www.rateplug.com/HASAPSpecFinAPI/api/RP/GetPropertyDetails',
      {
        FIPS, FC_PropertyID, AS_PropertyID, FHAEligible, VAEligible, USDAEligible, ListPrice, PropertyType, DownPayment,
      },
      (statusCode, result) => {
        if (statusCode === 200) {
          return callback(null, result);
        }

        return callback(statusCode);
      }, {
        reqId: 'Api.getRateplugPropertyDetails',
        headers: {
          'RP-ApiKey': 'd!2kuzOCHLcLsuxu_rOsW0yis',
        },
      });
  };

  Api.postRatePlugArchive = function (body, callback) {
    if (!body.ArchiveID) {
      return callback(400);
    }

    xhr.post('https://www.rateplug.com/MortgageInfoAPI/api/RP/SaveArchive', body, (statusCode, result) => {
      if (statusCode === 200) {
        return callback(null, result);
      }

      return callback(statusCode);
    }, {
      reqId: 'Api.postRatePlugArchive',
      headers: {
        'RP-ApiKey': 'd!2kuzOCHLcLsuxu_rOsW0yis',
      },
    });
  };

  Api.sendLOMessage = function ({
    CustomerGuid, LOGuid, ContactSubject, ContactMessage,
  }, callback) {
    let url = '';
    let apiKey = '';
    if (document.body.dataset.theme === 'afordal' || document.body.dataset.theme === 'fairway') {
      if (window.CONFIG.IN_PROD) {
        url = 'https://app.afordal.com/AfordalAPI/api/RP/SendLOMessage';
      } else {
        url = 'https://test.afordal.com/AfordalAPI/api/RP/SendLOMessage';
      }
      apiKey = 'orgv1wQm375!b2AQSlF_sBVF4tDT';
    } else if (document.body.dataset.theme === 'more') {
      url = 'https://www.rateplug.com/MOREAPI/api/RP/SendMORELOMessage';
      apiKey = 'd!2kuzOCHLcLsuxu_rOsW0yis';
    }

    if (!url) {
      return callback(400);
    }

    xhr.post(url,
      {
        CustomerGuid, LOGuid, ContactSubject, ContactMessage,
      },
      (statusCode, result) => {
        if (statusCode === 200) {
          return callback(null, result);
        }

        return callback(statusCode);
      }, {
        reqId: 'Api.sendLOMessage',
        headers: {
          'RP-ApiKey': apiKey,
        },
      });
  };

  return Api;
};
