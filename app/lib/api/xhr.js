/* eslint-disable no-self-assign */
const xhr = require('xhr');
const forEach = require('lodash.foreach');
const map = require('lodash.map');

/** *
 * Static methods for Xhr
 * */
function Xhr() {
}

// Keep a track of existing requests for aborting
// Use case - on new route
Xhr._reqs = {};

Xhr.abort = function (id) {
  if (typeof id === 'string') {
    if (Xhr._reqs[id]) {
      Xhr._reqs[id].abort('timeout');
      delete Xhr._reqs[id];
    }
  } else {
    forEach(Xhr._reqs, (r, k) => {
      r.abort('timeout');
      delete Xhr._reqs[k];
    });
  }
};

Xhr.get = function (url, dataObj, cb, opts = {}) {
  let qs = '';

  // Build the query string from data object
  if (typeof dataObj === 'object') {
    qs = map(dataObj, (val, k) => {
      if (!val) {
        return '';
      }
      if (typeof val !== 'object') {
        return `${encodeURIComponent(k)}=${encodeURIComponent(val)}`;
      }
      let query = `${encodeURIComponent(k)}[]=${encodeURIComponent(val[0])}`;
      for (let i = 1; i < val.length; i++) {
        query += `&${encodeURIComponent(k)}[]=${encodeURIComponent(val[i])}`;
      }
      return query;
    }).join('&').replace(/&+/g, '&').replace(/&$/, '');
  }

  // Clean up the url
  // if (url.charAt(url.length - 1) !== '/') url += '/';

  // Create an id for aborting requests
  const id = opts.reqId || String(Date.now())
    + String(Math.random()).substr(12);

  if (opts.throttle) {
    console.warn(Xhr._reqs[id]);
    if (Xhr._reqs[id]) {
      return;
    }
  }

  if (!opts.shouldNotAbort) {
    Xhr.abort(id);
  }

  const req = xhr({
    uri: url.concat(qs ? `?${qs}` : ''),
    headers: {
      // 'Content-Type': 'application/json',
      Accept: 'application/json',
      ...opts.headers || {},
    },
    withCredentials: url.indexOf(BASE_API_URL) !== -1,
    timeout: 20000, // 20 second timeout
  },
  (err, res) => {
    // Clear up the abort array
    Xhr.abort(id);

    // If abort/timeout then return cb(null);
    // https://github.com/Raynos/xhr/pull/52
    if (res.statusCode === 0) {
      return cb(null);
    }

    res = res || {};

    if (typeof res.body === 'string') {
      try {
        res.body = JSON.parse(res.body);
      } catch (e) {
        console.log('JSON parse error:', e);
        res.body = res.body;
      }
    }

    return cb(res.statusCode || 500, res.body, res);
  });

  if (!opts.shouldNotAbort) {
    // Add the request for aborting.
    Xhr._reqs[id] = req;
  }
};

Xhr.post = function (url, bodyJSON, cb, opts = {}) {
  // Create an id for aborting requests
  const id = opts.reqId || String(Date.now())
    + String(Math.random()).substr(12);

  if (!opts.shouldNotAbort) {
    Xhr.abort(id);
  }

  const req = xhr({
    uri: url,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...opts.headers || {},
    },
    method: 'POST',
    withCredentials: url.indexOf(BASE_API_URL) !== -1,
    json: bodyJSON,
    timeout: 20000, // 20 second timeout
  },
  (err, res) => {
    // Clear up the abort array
    Xhr.abort(id);

    // If abort/timeout then return cb(null);
    // https://github.com/Raynos/xhr/pull/52
    if (res.statusCode === 0) {
      return cb(null);
    }

    res = res || {};

    if (typeof res.body === 'string') {
      try {
        res.body = JSON.parse(res.body);
      } catch (e) {
        console.log('JSON parse error:', e);
        res.body = res.body;
      }
    }

    return cb(res.statusCode || 500, res.body, res);
  });

  if (!opts.shouldNotAbort) {
    // Add the request for aborting.
    Xhr._reqs[id] = req;
  }
};

Xhr.put = function (url, bodyJSON, cb, opts = {}) {
  // Create an id for aborting requests
  const id = opts.reqId || String(Date.now())
    + String(Math.random()).substr(12);

  if (!opts.shouldNotAbort) {
    Xhr.abort(id);
  }

  const req = xhr({
    uri: url,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...opts.headers || {},
    },
    method: 'PUT',
    withCredentials: url.indexOf(BASE_API_URL) !== -1,
    json: bodyJSON,
    timeout: 20000, // 20 second timeout
  },
  (err, res) => {
    // Clear up the abort array
    Xhr.abort(id);

    // If abort/timeout then return cb(null);
    // https://github.com/Raynos/xhr/pull/52
    if (res.statusCode === 0) {
      return cb(null);
    }

    res = res || {};

    if (typeof res.body === 'string') {
      try {
        res.body = JSON.parse(res.body);
      } catch (e) {
        console.log('JSON parse error:', e);
        res.body = res.body;
      }
    }

    return cb(res.statusCode || 500, res.body, res);
  });

  if (!opts.shouldNotAbort) {
    // Add the request for aborting.
    Xhr._reqs[id] = req;
  }
};

Xhr.patch = function (url, bodyJSON, cb, opts = {}) {
  // Create an id for aborting requests
  const id = opts.reqId || String(Date.now())
    + String(Math.random()).substr(12);

  if (!opts.shouldNotAbort) {
    Xhr.abort(id);
  }

  const req = xhr({
    uri: url,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...opts.headers || {},
    },
    method: 'PATCH',
    withCredentials: url.indexOf(BASE_API_URL) !== -1,
    json: bodyJSON,
    timeout: 20000, // 20 second timeout
  },
  (err, res) => {
    // Clear up the abort array
    Xhr.abort(id);

    // If abort/timeout then return cb(null);
    // https://github.com/Raynos/xhr/pull/52
    if (res.statusCode === 0) {
      return cb(null);
    }

    res = res || {};

    if (typeof res.body === 'string') {
      try {
        res.body = JSON.parse(res.body);
      } catch (e) {
        console.log('JSON parse error:', e);
        res.body = res.body;
      }
    }

    return cb(res.statusCode || 500, res.body, res);
  });

  if (!opts.shouldNotAbort) {
    // Add the request for aborting.
    Xhr._reqs[id] = req;
  }
};

Xhr.delete = function (url, bodyJSON, cb, opts = {}) {
  // Create an id for aborting requests
  const id = opts.reqId || String(Date.now())
    + String(Math.random()).substr(12);

  if (!opts.shouldNotAbort) {
    Xhr.abort(id);
  }

  const req = xhr({
    uri: url,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...opts.headers || {},
    },
    method: 'DELETE',
    withCredentials: url.indexOf(BASE_API_URL) !== -1,
    json: bodyJSON,
    timeout: 20000, // 20 second timeout
  },
  (err, res) => {
    // Clear up the abort array
    Xhr.abort(id);

    // If abort/timeout then return cb(null);
    // https://github.com/Raynos/xhr/pull/52
    if (res.statusCode === 0) {
      return cb(null);
    }

    res = res || {};

    if (typeof res.body === 'string') {
      try {
        res.body = JSON.parse(res.body);
      } catch (e) {
        console.log('JSON parse error:', e);
        res.body = res.body;
      }
    }

    return cb(res.statusCode || 500, res.body, res);
  });

  if (!opts.shouldNotAbort) {
    // Add the request for aborting.
    Xhr._reqs[id] = req;
  }
};

Xhr.xhr = xhr;

module.exports = Xhr;
