const Baobab = require('baobab');
const deepFreeze = require('deep-freeze-strict');
const facets = require('./facets');
const tree = require('./tree');

module.exports = function (getDefaults) {
  // Send back a fresh & reference-free default copy
  if (getDefaults) {
    return deepFreeze(JSON.parse(JSON.stringify(tree)));
  }

  return new Baobab(tree, {
    facets,
    // immutable: true,
    // syncwrite: true,
    autoCommit: false,
    asynchronous: false,
  });
};
