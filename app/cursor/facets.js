const app = window.__app;

module.exports = {
  currentActiveHeaderControl: {
    cursors: {
      mapLayout: ['layout', 'map'],
      gridLayout: ['layout', 'grid'],
      locationStr: ['panels', 'listings', 'meta', 'locationStr'],
    },
    get(data) {
      if (data.mapLayout) {
        return 'map';
      }
      if (data.gridLayout && (data.gridLayout.grid || data.gridLayout.detail)) {
        return 'grid';
      }

      return !!data.locationStr;
    },
  },
  headerControlDisabled: {
    cursors: {
      listingData: ['panels', 'listings', 'data'],
    },
    get(data) {
      return data.listingData === null;
    },
  },
  hasLocationStr: {
    cursors: {
      locationStr: ['panels', 'listings', 'meta', 'locationStr'],
    },
    get(data) {
      return !!data.locationStr;
    },
  },
  listingCount: {
    cursors: {
      listings: ['panels', 'listings', 'data'],
      listingTotalCount: ['panels', 'listings', 'lastFullListingCount'],
      listingsSpinner: ['panels', 'listings', 'meta', 'spinner'],
    },
    get(data) {
      if (data.listingsSpinner) {
        return false;
      }
      return data.listings && data.listingTotalCount || 0;
    },
  },
  activeListingData: {
    cursors: {
      listing1: ['screens', 'map', 'data'],
      listing2: ['screens', 'grid', 'data'],
      listing3: ['panels', 'listings', 'data'],
      listing4: ['screens', 'agent', 'activeListing'],
      listing5: ['screens', 'featured', 'detail'],
    },
    get() {
      return app.actions.common.getActiveListing();
    },
  },
  filterCount: {
    cursors: {
      menu: ['shared', 'menu'],
    },
    get(data) {
      let filterCount = 0;
      const { menu } = data;
      if (menu.minPrice || menu.maxPrice) {
        filterCount++;
      }
      if (menu.minMortgagePayment || menu.maxMortgagePayment) {
        filterCount++;
      }
      if (menu.downPayment) {
        filterCount++;
      }
      if (menu.minBeds || menu.minBaths) {
        filterCount++;
      }
      if (menu.homeTypesArray && menu.homeTypesArray.length > 0) {
        filterCount++;
      }
      if (menu.minLivingSqft || menu.maxLivingSqft) {
        filterCount++;
      }
      if (menu.minYear || menu.maxYear) {
        filterCount++;
      }
      if (menu.minLotSizeSqft || menu.maxLotSizeSqft) {
        filterCount++;
      }
      if (menu.minStories) {
        filterCount++;
      }
      if (menu.minGarage) {
        filterCount++;
      }
      if (menu.keywords && menu.keywords.length > 0) {
        filterCount++;
      }
      if (menu.statusEq) {
        filterCount++;
      }
      if (menu.specialFinancing && menu.specialFinancing.length > 0) {
        filterCount++;
      }
      return filterCount;
    },
  },
  shouldHideSearchBar: {
    cursors: {
      agentLayout: ['layout', 'agent'],
      featuredLayout: ['layout', 'featured'],
      login: ['layout', 'login'],
      agentData: ['shared', 'agent', 'data'],
    },
    get(data) {
      return data.agentData && data.agentData.ProductServices.indexOf('IDX') == -1 && (
        data.agentLayout || (data.featuredLayout.grid || data.featuredLayout.detail)
      );
    },
  },
  featuredListings: {
    cursors: {
      data: ['screens', 'featured', 'data'],
      agentData: ['shared', 'agent', 'data'],
    },
    get(data) {
      if (data.data) {
        const featureListingsHideRentals = data.agentData && ((data.agentData.AgentSettings || []).find((s) => s.Key === 'featured_listings_hide_rentals') || {}).Value !== 'true';

        if (featureListingsHideRentals) {
          return data.data;
        }
        return data.data.filter((listing) => listing.SaleType === 1);
      }
      return null;
    },
  },
  activeListingFromListings: {
    cursors: {
      listing1: ['screens', 'map', 'data'],
      listing2: ['screens', 'grid', 'data'],
      listing3: ['panels', 'listings', 'data'],
      listing4: ['screens', 'agent', 'activeListing'],
      listing5: ['screens', 'featured', 'detail'],
      listings: ['panels', 'listings', 'data'],
    },
    get(data) {
      const activeListing = app.actions.common.getActiveListing();

      if (activeListing && data.listings) {
        for (const l of data.listings) {
          if (l.Id === activeListing.Id) {
            return l;
          }
        }
      }
      return null;
    },
  },
};
