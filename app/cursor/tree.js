module.exports = {

  // settings: {
  //   isLogged: null,
  // },
  user: {
    tags: {},
    photoSliderOptions: {
      play: false,
      thumbnails: true,
    },
  },

  shared: {
    login: {
      state: -1,
      extraPermissions: null,
    },
    demoAgent: {
      data: null,
    },
    agent: {
      Id: null,
      data: null,
      mlsData: null,
    },
    agentCount: null,
    buyer: {
      Id: null,
      data: { name: null, id: null },
      friendsPermission: false,
    },
    location: {
      lat: null,
      lng: null,
    },
    agentPickerContext: {
      lat: null,
      lng: null,
      mlsIds: [],
      sources: [],
      forceFree: null,
      interacted: false,
      isLoggedIn: false,
    },
    agentPickerMetadata: {
      lastAgentSwitchTrackingId: null,
      currentAgentPickTrackingId: null,
    },
    photoSliderIndex: 0,
    photo: {
      captions: {},
    },
    menu: {
      sortType: false,

      saleType: 1, // For Sale, 2 For Rent
      maxPrice: null,
      minPrice: null,
      maxMortgagePayment: null,
      minMortgagePayment: null,
      downPayment: null,
      minBeds: null,
      minBaths: null,
      minYear: null,
      maxYear: null,
      minLotSizeSqft: null,
      maxLotSizeSqft: null,
      minLivingSqft: null,
      maxLivingSqft: null,
      minGarage: null,
      maxGarage: null,
      minStories: null,
      maxStories: null,
      openHouse: null,
      homeTypesArray: [],
      specialFinancing: [],
      statusEq: null,
      keywords: [],
      mlsId: null,
    },
    viewedProperties: {},
    tagging: {},
    contact: {
      showAgentContact: false,
      showLoanOfficerContact: false,
      activeTab: 'Email',
      listingData: null,
      showDetails: true,
      loanOfficerData: null,
    },
    mlsData: {},
    shareForm: {
      show: false,
      listingData: null,
    },
    showFilters: false,
    demo: null,
    savedSearch: null,
    notificationCenter: {
      header: null,
    },
    yelpCategories: [],
    detectedAgent: {
      type: null,
      data: null,
      status: null,
    },
    dreamsweeps: null,
  },

  layout: {
    // Panels
    header: false,
    sidebar: false,
    searchBar: false,
    facebookHeader: false,
    facebookGrid: false,
    agentHeaderDropdown: false,
    agentSearch: false,
    homeWorthModal: false,
    rateplugLanding: false,
    dreamsweepsModal: false,
    readHeader: false,
    memberSearch: false,
    memberListings: false,

    // leftbar: false,
    sub: false,
    photos: false,

    // Screens
    home: false,
    onboarding: false,
    landing: false,
    logout: false,
    agents: false,
    listing: false,
    facebook: false,
    grid: {
      grid: false,
      detail: false,
    },
    tagging: {
      grid: false,
      detail: false,
    },
    featured: {
      grid: false,
      detail: false,
    },
    map: false,
    agent: {
      agentData: false,
      listingDetail: false,
    },
    buyer: false,
    hints: false,
    homeWorth: false,

    // Overlay Panels
    menu: false,
    login: false,
    error: false,
    modal: false,
    blankModal: false,
    showResultsHelp: false,
    help: false,
    demo: false,
    lendingTreeModal: false,
    payPerClickModal: false,
  },

  panels: {
    header: {
      data: null,
      loadingAgent: false,
      showHeader: true,
    },
    // leftbar: {
    //   data: null
    // },
    photos: {
      activeId: null,
      data: null,
    },
    help: {
      targets: [],
    },
    map: {
      ref: {
        alert: false,
        className: null,
      },
      pos: {
        Lat: null,
        Lon: null,
      },

      // Search location
      locType: null, // Signifies the type of location searched for
      //  C = City,
      //  N = Neighborhood,
      //  Z = Zip
      //  S = School
      //  A = Address
      locId: null,

      // Markers
      selectedMarker: null,
      markers: null, // @type: Hash-map
      densityData: null, // @type: Hash-map

      // Map route options
      dontResetViewport: false,
      dontPanToSoute: false,
    },
    listings: {
      activeId: null,
      meta: {
        spinner: false,
        zoomMsg: false,
        locationStr: null,
      },
      numResults: 0,
      data: null, // @type: Array
    },
    previousSearches: {
      data: null,
    },
  },

  screens: {
    onboarding: {
      ref: {
        alert: false,
        spinner: false,
      },
      data: null,
    },
    error: {
      ref: {
        hasBack: false,
      },
      data: null,
    },
    facebook: {
      activeTab: 'featured',
    },
    agent: {
      ref: {
        alert: false,
        spinner: false,
      },
      data: null,
      activeListing: null,
      agentProfile: {
        showBrokerage: false,
        mobileShowFeatured: false,
        showHeadlineInside: false,
      },
    },
    agentSearch: {
      agents: null,
    },
    buyer: {
      ref: {
        alert: false,
        spinner: false,
      },
    },
    listing: {
      ref: {
        alert: false,
        spinner: false,
      },
      data: null,
      yelpSearchResults: [],
      showYelpMarkers: false,
    },
    grid: {
      ref: {
        alert: false,
        spinner: false,
      },
      lightsOut: false,
      data: {},
      featuredListings: null,
      taggedListings: null,
      showMenu: true,
    },
    featured: {
      ref: {
        alert: false,
        spinner: false,
      },
      data: null,
      activeId: null,
    },
    map: {
      ref: {
        display: false,
        alert: false,
        spinner: false,
      },
      data: null,
      streetViewAvailable: false,
      sort: true,
      mobilePreviewData: null,
    },
    tagging: {
      ref: {
        alert: false,
        spinner: false,
      },
      data: null,
    },
    homeWorth: {
      searchResult: null,
    },
  },

  loading: {
    listings: false,
    yelpSearchResults: false,
  },
  // nav: {
  //   active: null,
  //   items: [
  //     {
  //       text: 'Abc',
  //       href: '/abc',
  //       select: 'abc'
  //     },
  //     {
  //       text: 'Logout',
  //       href: '/logout',
  //       select: 'logout'
  //     }
  //   ]
  // },

  // screens: {

  //   agent: {
  //     params: {},
  //     profile: {
  //       display: false,
  //       alert: false,
  //       spinner: false,
  //     },
  //     listings: {
  //       alert: false,
  //       spinner: false
  //     },
  //     map: {
  //       display: false,
  //       alert: false
  //     },
  //     content: {
  //       display: false, // display detail content
  //       alert: false,
  //       spinner: false
  //     },
  //     data: {
  //       map:null,
  //       profile: null,
  //       content: null
  //     }
  //   },
  // },

};
