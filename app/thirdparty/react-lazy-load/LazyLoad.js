/* eslint-disable */
const React = require('react');
const ClassNames = require('classnames');
const ReactDOM = require('react-dom');

const LazyLoad = React.createClass({
  displayName: 'LazyLoad',
  propTypes: {
    height: React.PropTypes.string,
  },
  getInitialState() {
    return {
      visible: false,
    };
  },
  handleScroll() {
    const bounds = ReactDOM.findDOMNode(this).getBoundingClientRect();
    const scrollTop = window.pageYOffset;
    const top = bounds.top + scrollTop;
    const height = bounds.bottom - bounds.top;

    if (top < (scrollTop + window.innerHeight + (this.props.buffer || 0)) && (top + height) > scrollTop) {
      this.setState({ visible: true });
      this.handleVisible();
    }
  },
  handleVisible() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleScroll);
  },
  componentDidMount() {
    window.addEventListener('scroll', this.handleScroll);
    window.addEventListener('resize', this.handleScroll);
    this.handleScroll();
  },
  componentDidUpdate() {
    if (!this.state.visible) this.handleScroll();
  },
  componentWillUnmount() {
    this.handleVisible();
  },
  render() {
    const renderEl = '';
    const preloadHeight = {
      height: (this.state.visible || !this.props.height) ? null : this.props.height,
      width: (this.state.visible || !this.props.width) ? null : this.props.width,
    };
    const classes = ClassNames({
      'lazy-load': true,
      'lazy-load-visible': this.state.visible,
    }, this.props.className);

    return (
      React.createElement('div', { style: preloadHeight, className: classes },
        this.state.visible ? this.props.children : '')
    );
  },
});

module.exports = LazyLoad;
