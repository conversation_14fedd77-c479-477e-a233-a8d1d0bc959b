/* eslint-disable */
const React = require('react');
const ClassNames = require('classnames');
const ReactDOM = require('react-dom');

const LazyLoadWithinContainer = React.createClass({
  displayName: 'LazyLoad-WithinContainer',
  propTypes: {
    height: React.PropTypes.string,
  },
  getInitialState() {
    return {
      visible: false,
    };
  },
  handleScroll() {
    if (this.elem.offsetTop < this.offsetParent.scrollTop + this.offsetParent.clientHeight + (this.props.buffer || 0)) {
      this.setState({ visible: true });
      this.handleVisible();
    }
  },
  handleVisible() {
    this.offsetParent && this.offsetParent.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleScroll);
  },
  componentDidMount() {
    this.elem = ReactDOM.findDOMNode(this);
    this.offsetParent = this.elem.offsetParent;
    this.offsetParent.addEventListener('scroll', this.handleScroll);
    window.addEventListener('resize', this.handleScroll);
    window.addEventListener('DOMContentReady', this.handleDocumentReady);
    this.handleScroll();
  },
  handleDocumentReady() {
    this.handleScroll();
  },
  componentDidUpdate() {
    if (!this.state.visible) this.handleScroll();
  },
  componentWillUnmount() {
    this.handleVisible();
  },
  render() {
    const renderEl = '';
    const preloadHeight = {
      height: this.state.visible ? (this.props.afterHeight || null) : this.props.height,
      width: this.state.visible ? (this.props.afterWidth || null) : this.props.width,
    };
    const classes = ClassNames({
      'lazy-load': true,
      'lazy-load-visible': this.state.visible,
    }, this.props.className);

    return (
      React.createElement('div', { style: preloadHeight, className: classes },
        this.state.visible ? this.props.children : '')
    );
  },
});

module.exports = LazyLoadWithinContainer;
