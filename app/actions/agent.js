const Promise = require('promise');

module.exports = function (app) {
  /** *
   * Static methods for Agent
   * */
  function Agent() {
  }
  Agent.CONTACT_MESSAGE = 'CM';
  Agent.CONTACT_TODATE = 'TD';
  Agent.CONTACT_TODATE = 'FD';
  Agent.layoutCursor = app.cursor.select(['layout']);
  Agent.screenCursor = app.cursor.select(['screens', 'agent']);
  Agent.contactCursor = app.cursor.select(['shared', 'contact']);
  Agent.agentDataCursor = app.cursor.select(['shared', 'agent', 'data']);

  Agent.route = function (route) {
    app.actions.menu.setBackRoute(route);

    const agentData = Agent.agentDataCursor.get();
    if (agentData) {
      app.actions.common.setPageTitle(
        `${agentData.FirstName} ${
          agentData.LastName
        }${agentData.BrokerName ? `, ${agentData.BrokerName}` : ''}`,
      );

      window.fbq('track', 'ViewContent', {
        content_type: 'agent',
        content_ids: [agentData.Id],
      });
    } else {
      app.actions.common.resetPageTitle();
    }

    let p1; let
      committed = false;

    // Layout
    const agentLayout = {
      // leftbar: true
    };

    // Account for id:
    if (route.params.id) {
      agentLayout.listingDetail = app.actions.menu.backRoute ? app.actions.menu.backRoute : true;

      p1 = Agent.getListing(route.params.id);
    } else {
      agentLayout.agentData = app.actions.menu.backRoute ? app.actions.menu.backRoute : true;
      Agent.screenCursor.set(['activeListing'], null);
    }

    // Set the layout
    app.utils.updateCursor({
      cursor: Agent.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { agent: agentLayout, header: true, searchBar: true },
    });

    p1 && p1.then((res) => {
      Agent.screenCursor.set(['ref', 'spinner'], false);

      // Handle response depending on situation
      // And account for id:
      if (typeof res[0] !== 'undefined'
        && res[0] === null) {
        Agent.screenCursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
      }

      committed = true;
      app.cursor.commit();
    });
    app.actions.featured.getFeaturedListings();

    if (committed === false) {
      app.cursor.commit();
    }

    if ('lendingtree' in route.qs) {
      app.actions.common.showLendingTreeModal();
    }

    app.models.agentPicker.deliverFreeLeadTrafficForCurrentAgent();
  };

  Agent.getListing = function (id) {
    const p = new Promise(((resolve) => {
      app.actions.panels.getListing(id, (res) => {
        if (res === 404) {
          Agent.screenCursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          Agent.screenCursor.set(['activeListing'], res);
        } else {
          //  Grid.cursor.set(['ref','alert'], 'There was a problem connecting to the server')
        }

        resolve(res);
      });
    }));
    return p;
  };

  Agent.goBack = function () {
    if ((Agent.layoutCursor.get(['agent', 'agentData']) || Agent.layoutCursor.get(['agent', 'listingDetail'])) && (app.router.history.length > 1)) {
      window.history.back();
      return true;
    }
    return false;
  };

  Agent.toggle = function () {
    if (!Agent.goBack()) {
      app.actions.common.goToRoute('/agent');
    }
  };

  Agent.toggleAgentHeaderDropdown = function () {
    const dropdownLayout = app.cursor.get(['layout', 'agentHeaderDropdown']);
    app.cursor.set(['layout', 'agentHeaderDropdown'], !dropdownLayout);
    app.cursor.commit();
  };

  Agent.hideAgentHeaderDropdown = function () {
    app.cursor.set(['layout', 'agentHeaderDropdown'], false);
    app.cursor.commit();
  };

  Agent.setActiveTab = function (tab) {
    Agent.contactCursor.set(['activeTab'], tab);
    app.cursor.commit();
  };

  Agent.showAgentContact = function (listingData) {
    // window.fbq('track', 'InitiateCheckout', app.utils.getListingPixelAttributes(listingData));

    Agent.contactCursor.set(['showAgentContact'], true);
    Agent.contactCursor.set(['listingData'], listingData);
    app.cursor.commit();
  };

  Agent.hideAgentContact = function (clearData) {
    Agent.contactCursor.set(['showAgentContact'], false);
    if (clearData) {
      Agent.contactCursor.set(['listingData'], null);
      Agent.contactCursor.set(['showDetails'], true);
      app.cursor.commit();
    }
  };

  Agent.submitContactEmail = function (name, email, message, id, agentId, cb) {
    app.api.submitEmail(name, email, message, id, agentId, cb);
  };

  Agent.submitShowing = function (name, email, id, date, time, agentId, cb) {
    app.api.submitShowing(name, email, id, date, time, agentId, cb);
  };

  Agent.submitCMA = function (name, email, id, agentId, cb) {
    app.api.submitCMA(name, email, id, agentId, cb);
  };

  Agent.onNav = function (listingId) {
    app.actions.common.goToRoute('/agent'.concat(listingId ? `/${listingId}` : ''));

    app.actions.analytics.sendEvent('navigation', 'agent profile', listingId);
  };

  Agent.onClose = function () {
    if (app.actions.menu.backRoute) {
      app.router.go(app.actions.menu.backRoute);
    }
  };

  Agent.setContactLocalStorage = function (type, value) {
    window.localStorageAlias.setItem(type, JSON.stringify(value));
  };

  Agent.getLocalStorage = function (type) {
    const value = JSON.parse(window.localStorageAlias.getItem(type));
    console.log(`locl${value}`);
    return value || '';
  };

  Agent.showBrokerage = function () {
    Agent.screenCursor.set(['agentProfile', 'showBrokerage'], true);
    app.cursor.commit();
  };

  Agent.hideBrokerage = function () {
    Agent.screenCursor.set(['agentProfile', 'showBrokerage'], false);
    app.cursor.commit();
  };

  Agent.hideDetails = function () {
    Agent.contactCursor.set(['showDetails'], false);
    app.cursor.commit();
  };

  Agent.resetShowDetails = function () {
    console.log('hitting reset');
    Agent.contactCursor.set(['showDetails'], true);
    app.cursor.commit();
  };

  Agent.setMobileFeature = function () {
    Agent.screenCursor.set(['agentProfile', 'mobileShowFeatured'], true);
    app.cursor.commit();
  };

  Agent.resettMobileFeature = function () {
    Agent.screenCursor.set(['agentProfile', 'mobileShowFeatured'], false);
    app.cursor.commit();
  };

  Agent.showLoanOfficerContact = function (loData, tab = 'Email') {
    Agent.contactCursor.set(['showLoanOfficerContact'], true);
    Agent.contactCursor.set(['loanOfficerData'], loData);
    Agent.contactCursor.set(['activeTab'], tab);
    app.cursor.commit();
  };

  Agent.hideLoanOfficerContact = function () {
    Agent.contactCursor.set(['showLoanOfficerContact'], false);
    Agent.contactCursor.set(['loanOfficerData'], null);
    app.cursor.commit();
  };

  Agent.gotoAgentLanding = function () {
    app.actions.common.goToRoute('');
  };

  Agent.setHeadlineInside = function (flag) {
    Agent.screenCursor.set(['agentProfile', 'showHeadlineInside'], flag);
    app.cursor.commit();
  };
  return Agent;
};
