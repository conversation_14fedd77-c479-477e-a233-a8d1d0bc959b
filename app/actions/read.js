module.exports = function (app) {
  /** *
   * Static methods for READ
   * */
  function READ() {
  }

  READ.sharedCursor = app.cursor.select(['shared']);

  READ.DETECTED_AGENT_DATA = 'D_A_D';
  READ.DETECTED_AGENT_DATA_HIDDEN = 'D_A_D_H';
  READ.DETECTED_AGENT_DATA_LAST_SHOWN = 'D_A_D_L_S';

  READ.AGENT_NOTIFICATION_STATUS = {
    FINISH_SETUP: 'FINISH_SETUP',
    PAUSED: 'PAUSED',
    PENDING: 'PENDING',
    COMPLETE: 'COMPLETE',
    GET_APP: 'GET_APP',
  };

  READ.ASAP_STATUS = {
    ACTIVE: 1,
    PAUSED: 2,
  };

  READ.agentData = null;

  READ.getDetectedAgentData = function () {
    const agentData = window.localStorageAlias.getItem(READ.DETECTED_AGENT_DATA);

    if (agentData) {
      try {
        READ.agentData = JSON.parse(agentData);
        return READ.agentData;
      } catch (e) {
        return null;
      }
    }

    return null;
  };

  READ.fetchREADAgent = function () {
    setTimeout(() => {
      if (READ.getDetectedAgentData()) {
        try {
          const previouslyDetectedAgentData = READ.getDetectedAgentData();
          READ.sharedCursor.set('detectedAgent', {
            type: 'previous',
            data: previouslyDetectedAgentData,
            status: READ.getAgentNotificationStatus(previouslyDetectedAgentData, true),
            hidden: READ.getAgentNotificationHidden(),
          });
          app.cursor.commit();
        } catch (ex) {

        }
      }

      // Detect Agent Login Status
      window.addEventListener('message', (e) => {
        if (typeof e.data === 'string' && e.origin === window.location.origin) {
          if (e.data.match(/^HOMEASAP_DETECT_AGENT:SUCCESS::/)) {
            const token = e.data.replace('HOMEASAP_DETECT_AGENT:SUCCESS::', '').replace(/^"/, '').replace(/"$/, '');

            app.api.agentAuth(token, (statusCode, res) => {
              if (statusCode === 200 && res.AgentId) {
                app.api.agentSelf((statusCode1, agentData) => {
                  if (statusCode1 === 200 && agentData) {
                    READ.agentData = agentData;
                    window.localStorageAlias.setItem(READ.DETECTED_AGENT_DATA, JSON.stringify(agentData));

                    READ.sharedCursor.set('detectedAgent', {
                      type: 'active',
                      data: agentData,
                      status: READ.getAgentNotificationStatus(agentData),
                      hidden: READ.getAgentNotificationHidden(),
                    });

                    const currentAgentData = READ.sharedCursor.get(['agent', 'data']);
                    if (currentAgentData && currentAgentData.Id === agentData.agentId) {
                      READ.sharedCursor.set(['agent', 'data', 'IsSearchAllianceMember'], agentData.isSearchAllianceMember);
                      READ.sharedCursor.set(['agent', 'data', 'IsSearchAllianceMember'], false);
                    }

                    app.cursor.commit();
                  }
                });
              }
            });
          } else if (e.data.match(/^HOMEASAP_DETECT_AGENT:FAILED::/)) {
            // Do nothing
          }
        }
      }, false);

      const agentDataIFrame = document.createElement('iframe');
      agentDataIFrame.src = '/iframe-detect-agent.html';
      agentDataIFrame.style.width = '0';
      agentDataIFrame.style.height = '0';
      agentDataIFrame.style.position = 'absolute';
      agentDataIFrame.style.top = '-10000px';
      document.body.appendChild(agentDataIFrame);
    }, 5000);
  };

  READ.getAgentNotificationStatus = function (agentData, isFromPrevious) {
    let status = null;
    if (!agentData.asapActivationDate) { // (1) Finish Setup
      status = READ.AGENT_NOTIFICATION_STATUS.FINISH_SETUP;
    } else if (agentData.asapStatus === READ.ASAP_STATUS.PAUSED) { // (2) Paused
      status = READ.AGENT_NOTIFICATION_STATUS.PAUSED;
    } else if (!agentData.homeSearchRegisteredDateTime && agentData.asapStatus) { // (3) Waiting for MLS Approval
      status = READ.AGENT_NOTIFICATION_STATUS.PENDING;
    } else if (agentData.asapStatus === READ.ASAP_STATUS.ACTIVE) { // (4) Good to go
      if (window.localStorageAlias.getItem(READ.DETECTED_AGENT_DATA_LAST_SHOWN) === READ.AGENT_NOTIFICATION_STATUS.COMPLETE) {
        status = READ.AGENT_NOTIFICATION_STATUS.GET_APP;
      } else if (window.localStorageAlias.getItem(READ.DETECTED_AGENT_DATA_LAST_SHOWN) !== READ.AGENT_NOTIFICATION_STATUS.GET_APP) { // Don't go back to active status from mobile app status
        status = READ.AGENT_NOTIFICATION_STATUS.COMPLETE;
      }
    }
    if (!isFromPrevious && status) {
      window.localStorageAlias.setItem(READ.DETECTED_AGENT_DATA_LAST_SHOWN, status);
    }
    return status;
  };

  READ.getAgentNotificationHidden = function () {
    return window.localStorageAlias.getItem(READ.DETECTED_AGENT_DATA_HIDDEN);
  };
  READ.setAgentNotificationHidden = function (status, hidden = true) {
    READ.sharedCursor.set(['detectedAgent', 'hidden'], hidden ? status || 1 : null);
    app.cursor.commit();
    window.localStorageAlias.setItem(READ.DETECTED_AGENT_DATA_HIDDEN, hidden ? status || 1 : null);
  };

  return READ;
};
