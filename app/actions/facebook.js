module.exports = function (app) {
  /** *
   * Static methods for Facebook
   * */
  function Facebook() {
  }

  Facebook.layoutCursor = app.cursor.select('layout');

  Facebook.pageAdmin = false;
  Facebook.route = function (route) {
    app.actions.common.setPageTitle('Featured Homes');

    Facebook.currentRoute = route;

    if (route.qs.facebookpageadmin) {
      Facebook.pageAdmin = true;
    }

    if (route.qs.bgcolor) {
      const styleElem = document.createElement('style');
      styleElem.type = 'text/css';
      styleElem.innerHTML = `.facebook-embedded .grid-container { background-color: #${route.qs.bgcolor} !important }`;
      document.head.appendChild(styleElem);
    }

    // this is kind of kludgy but there isn't a great way to get the total document height
    let resizeHeightInterval;
    let lastHeight = 0;
    let settingSameHeightCount = 0;
    const setFbIframeHeight = function setFbIframeHeight(height, cssSelector, buffer) {
      try {
        if (settingSameHeightCount === 0 && height) {
          window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height }, '*');
          FB && FB.Canvas.setSize({ height });
        }

        if (cssSelector) {
          const element = document.querySelector(cssSelector);
          if (!element) {
            return;
          }
          let newHeight = Math.ceil((window.pageYOffset || document.documentElement.scrollTop) + element.getBoundingClientRect().bottom + (buffer || 0));
          newHeight = Math.max(height || 0, newHeight);
          if (lastHeight === newHeight) {
            settingSameHeightCount += 1;
            window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height: newHeight }, '*');
            FB && FB.Canvas.setSize({ height: newHeight });
          } else {
            window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height: newHeight }, '*');
            FB && FB.Canvas.setSize({ height: newHeight });
            settingSameHeightCount = 0;
            lastHeight = newHeight;
          }

          if (settingSameHeightCount > 4) {
            clearInterval(resizeHeightInterval);
          }
        } else {
          clearInterval(resizeHeightInterval);
        }

        // if this can ever be improved, the final goal really is to set the FB canvas height to the rendered document height of the page
      } catch (e) {
        console.log(e, 'FB not initialized yet, retrying to set height');
      }
    };

    if (route.qs.nav === '0' || app.utils.useMobileSite()) {
      Facebook.hideNav = true;
    }

    // Layout
    const layout = {
      facebookHeader: (['featured', 'onboarding'].indexOf(route.params.tab) > -1 && Facebook.hideNav)
        ? false : Facebook.pageAdmin ? 'admin' : true,
      facebook: Facebook.pageAdmin ? 'admin' : true,
    };

    switch (route.params.tab) {
      case 'featured':
        layout.facebookGrid = true;
        app.actions.featured.getFeaturedListings();
        layout.featured = {
          grid: false,
          detail: false,
        };
        // try every 100 ms to resize the window
        resizeHeightInterval = setInterval(setFbIframeHeight, 100, 1200, '.n-play-footer', 50);
        break;
      case 'agent':
        layout.agent = {
          agentData: true,
          listingDetail: false,
        };
        // try every 10 ms to resize the window
        resizeHeightInterval = setInterval(setFbIframeHeight, 10, null, '.n-play-footer');
        break;
      case 'onboarding':
        layout.onboarding = true;
        // try every 10 ms to resize the window
        resizeHeightInterval = setInterval(setFbIframeHeight, 10, 455);
        break;
      default:
        // default to "featured"
        layout.facebookGrid = true;
        app.actions.featured.getFeaturedListings();
        layout.featured = {
          grid: false,
          detail: false,
        };
        // try every 10 ms to resize the window
        resizeHeightInterval = setInterval(setFbIframeHeight, 10, 1200);
        break;
    }

    app.cursor.set(['screens', 'facebook', 'activeTab'], route.params.tab);

    // Set the layout
    app.utils.updateCursor({
      cursor: Facebook.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    app.models.agentPicker.deliverFreeLeadTrafficForCurrentAgent();

    // nob = no onboarding. we shorten it so it is less obvious in the URL
    if (!('nob' in route.qs)) {
      app.actions.common.setOnboardingAgent(app.cursor.get(['shared', 'agent', 'data']));
    }

    app.cursor.commit();
  };

  return Facebook;
};
