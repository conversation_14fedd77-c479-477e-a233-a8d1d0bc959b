module.exports = function (app) {
  /** *
   * Static methods for Analytics
   * */
  function Analytics() {
  }

  Analytics.sendPageView = function (url, title) {
    window.sendPageView(url, title);
    this.sendUserActivity('PageView');
  };

  Analytics.sendEvent = function (category, action, label, value, opts) {
    window.sendEvent(category, action, label, value);
    this.sendUserActivity('Event', category, action, label, value, opts);
  };

  Analytics.queuedActivities = [];
  Analytics.sendUserActivity = function (activityType, category, action, label, value, opts = {}) {
    const {
      agentId, onboardingAgentId,
      lat, lon, radius,
      source,
    } = opts;

    const activity = {};
    activity.app = window.CONFIG.HA_ANALYTICS_APP;
    activity.atype = activityType;
    activity.ec = category;
    activity.ea = action;
    activity.el = label;
    activity.ev = value;

    activity.caid = agentId || app.actions.common.getAgentId();
    activity.oaid = onboardingAgentId || app.actions.common.getOnboardingAgentId();

    activity.zoom = app.leaflet.m && app.leaflet.m.getZoom();
    activity.resW = screen.width;
    activity.resH = screen.height;

    activity.rt = document.location.href;
    if (app.router.currentRoute && app.router.currentRoute.params && app.router.currentRoute.params.location) {
      const pos = app.utils.validateLocationStr(app.router.currentRoute.params.location);

      if (pos) {
        activity.lat = lat || pos.Lat;
        activity.lon = lon || pos.Lon;
        activity.rad = radius || pos.radius;
      }
    }

    activity.title = document.title;
    if (source) {
      activity.source = source;
    }

    if (!activity.sourcee) {
      if (window.rateplug.rp_buyer) {
        activity.source = 'rateplug';
      }
    }

    Analytics.queuedActivities.push(activity);
  };

  window.setInterval(() => {
    if (Analytics.queuedActivities.length > 0) {
      const activities = Analytics.queuedActivities;
      Analytics.queuedActivities = [];
      app.api.sendUserActivity(activities);
    }
  }, 2000);

  // One off analytics events
  window.setTimeout(() => {
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      Analytics.sendEvent('rateplug', 'load', 'CustGUID', window.rateplug.rp_buyer, { source: 'rateplug' });
    }
  }, 1000);

  return Analytics;
};
