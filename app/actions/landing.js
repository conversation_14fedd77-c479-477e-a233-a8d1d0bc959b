module.exports = function (app) {
  /** *
   * Static methods for Landing
   * */
  function Landing() {
  }

  Landing.layoutCursor = app.cursor.select('layout');
  Landing.cursor = app.cursor.select(['screens', 'onboarding']);

  Landing.route = function (route) {
    const agentData = app.actions.agent.agentDataCursor.get();
    if (agentData) {
      app.actions.common.setPageTitle(
        `${agentData.FirstName} ${
          agentData.LastName
        }${agentData.BrokerName ? `, ${agentData.BrokerName}` : ''}`,
      );
      app.actions.common.modifyInstalledAppName(`${agentData.FirstName} ${agentData.LastName}`);
    } else {
      app.actions.common.resetPageTitle();
    }

    // nob = no onboarding
    if (!('nob' in route.qs)) {
      app.actions.common.setOnboardingAgent(app.cursor.get(['shared', 'agent', 'data']));
    }

    app.utils.removeQs();

    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map', 'listings']);

    // Set the layout1
    app.utils.updateCursor({
      cursor: Landing.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: {
        landing: true,
        demo: Landing.layoutCursor.get('demo'),
        header: false,
        searchBar: false,
        homeWorth: false,
      },
    });

    app.models.agentPicker.deliverFreeLeadTrafficForCurrentAgent();

    app.cursor.commit();
  };

  Landing.onNav = function () {
    app.actions.common.goToRoute('/');
  };

  Landing.onSearch = function (Lat, Lon, radius, locationQuery, saleType, opts) {
    console.log('-- Suggestion value --', Lat, Lon);

    if (Lat && Number(Lat) && Lon && Number(Lon)) {
      app.actions.map.resetToInitialZoomOnRoute();

      let route = '/search/map/'.concat(Lat, ',', Lon, ',', (radius || app.leaflet.opts.circleDefaultRadius),
        (opts && opts.listingId) ? `/${opts.listingId}` : '');

      if (opts && opts.currentLocation) {
        if (app.utils.useMobileSite()) {
          // Go to List view in mobile
          route = route.replace('/search/map/', '/search/grid/');
        }
        route = route.replace(/,\d+$/, ',1000');
        app.actions.menu.sharedMenuCursor.set('sortType', 'distance_asc');
      }

      route = route.concat('?fr=1',
        locationQuery ? `&q=${encodeURIComponent(locationQuery)}` : '',
        saleType ? `&sa=${encodeURIComponent(saleType)}` : '');
      app.actions.common.goToRoute(route);
    }

    if (locationQuery) {
      window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({
        lat: Lat,
        lon: Lon,
        radius,
        locationQuery,
      }));
    }
  };

  return Landing;
};
