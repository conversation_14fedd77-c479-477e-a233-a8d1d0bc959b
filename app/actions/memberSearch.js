module.exports = function (app) {
  /** *
   * Static methods for MemberSearch
   * */
  function MemberSearch() {
  }

  MemberSearch.layoutCursor = app.cursor.select('layout');

  MemberSearch.route = function (route) {
    MemberSearch.currentRoute = route;

    // this is kind of kludgy but there isn't a great way to get the total document height
    /*
    let resizeHeightInterval;
    let lastHeight = 0;
    let settingSameHeightCount = 0;
    const setFbIframeHeight = function setFbIframeHeight(height, cssSelector, buffer) {
      try {
        if (settingSameHeightCount === 0 && height) {
          window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height }, '*');
          FB && FB.Canvas.setSize({ height });
        }

        if (cssSelector) {
          const element = document.querySelector(cssSelector);
          if (!element) {
            return;
          }
          let newHeight = Math.ceil((window.pageYOffset || document.documentElement.scrollTop) + element.getBoundingClientRect().bottom + (buffer || 0));
          newHeight = Math.max(height || 0, newHeight);
          if (lastHeight === newHeight) {
            settingSameHeightCount += 1;
            window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height: newHeight }, '*');
            FB && FB.Canvas.setSize({ height: newHeight });
          } else {
            window.parent && window.parent.postMessage({ type: 'HOMEASAP_FRAME_HEIGHT', height: newHeight }, '*');
            FB && FB.Canvas.setSize({ height: newHeight });
            settingSameHeightCount = 0;
            lastHeight = newHeight;
          }

          if (settingSameHeightCount > 4) {
            clearInterval(resizeHeightInterval);
          }
        } else {
          clearInterval(resizeHeightInterval);
        }

        // if this can ever be improved, the final goal really is to set the FB canvas height to the rendered document height of the page
      } catch (e) {
        console.log(e, 'FB not initialized yet, retrying to set height');
      }
    };
    */

    // Layout
    const layout = {
      readHeader: 'member-search',
      memberSearch: true,
    };

    // Set the layout
    app.utils.updateCursor({
      cursor: MemberSearch.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    app.cursor.commit();

    app.actions.common.fetchAgentCount();
  };

  MemberSearch.onNav = function () {
    app.router.go('/member-search');
  };

  return MemberSearch;
};
