const async = require('async');
const _ = require('lodash');

module.exports = function (app) {
  /** *
   * Static methods for Panels
   * */
  function Login() {
    app.actions.common.setLoginState(Login.LOGIN_STATE.Unknown);
  }

  Login.LOGIN_STATE = Object.freeze(
    {
      Unknown: -1,
      NotLoggedIn: 0,
      LoggedIn: 1,
    },
  );

  Login.layoutCursor = app.cursor.select('layout', 'login');
  Login.sharedCursor = app.cursor.select('shared');

  // Accepts 1 parameter with a return object
  // Object properties:
  //    status = String containing a status message
  //    data = A copy of the buyer data object
  Login.callbacks = [];

  Login.registerCallback = function (cb) {
    if (typeof cb === 'function') {
      Login.callbacks.push(cb);
    }
  };

  Login.doCallbacks = function (status) {
    const newCallbacks = [];
    for (let i = 0; i < Login.callbacks.length; i++) {
      const cb = Login.callbacks[i];
      if (typeof cb === 'function') {
        const result = cb(status);
        if (result === false) {
          newCallbacks.push(cb);
        }
      }
    }
    delete Login.callbacks;
    Login.callbacks = newCallbacks;
  };

  Login.startAction = '';
  Login.start = function (opts = {}, cb = () => {}) {
    // Still don't know yet
    Login.registerCallback(cb);

    let buyerData = app.actions.common.getBuyerData();
    if (buyerData && buyerData.Id && !opts.showLogin) {
      // Logged In
      app.actions.common.setLoginState(Login.LOGIN_STATE.LoggedIn);
      Login.doCallbacks({ state: Login.LOGIN_STATE.LoggedIn, data: buyerData });
    } else if (opts.direct) {
      Login.promptDirectLogin(opts);
    } else {
      // var currentLoginState = app.actions.common.getLoginState();
      // if (currentLoginState !== Login.LOGIN_STATE.Unknown || opts.showLogin) {
      // Not logged in

      app.events.emit(app.events.PROMPT_FOR_LOGIN, {
        loginButtonText: opts.loginButtonText || 'Login to continue',
        modalType: opts.action || 'default',
      });
      // } else if (opts && opts.dontBlock) {
      //  Still don't know yet, but you might want to know that
      // Login.doCallbacks({state: Login.LOGIN_STATE.Unknown});
      // }
    }
    buyerData = null;
  };

  Login.show = function (/* facebookDirect */) {
    // TODO app.actions.analytics.sendEvent('login modal show', Login.startAction, '', app.actions.common.getLoginModalCount())
  };

  Login.hide = function () {
    // app.actions.analytics.sendEvent('login', 'no thanks')
    Login.layoutCursor.set(false);
    app.cursor.commit();
  };

  Login.cancel = function () {
    Login.hide();

    const loginState = app.actions.common.getLoginState();
    Login.doCallbacks({ state: loginState, status: 'Login cancelled' });
  };

  // Facebook auth succeeded
  Login.success = function (fbAuthResponse, fbUserInfoResponse) {
    if (fbAuthResponse && fbUserInfoResponse) {
      Login.authApi(fbAuthResponse.authResponse.accessToken);
    }
  };

  Login.populateBuyerData = function (buyerData) {
    for (const el of [...document.querySelectorAll('[data-populate-buyer]')]) {
      const buyerDataKey = el.dataset.populateBuyer;
      if (buyerDataKey in buyerData) {
        el.value = buyerData[buyerDataKey];
      }
    }
  };

  Login.authApi = function (token) {
    const buyerAuth = (t) => {
      app.api.buyerAuth(t,
        (statusCode, res) => {
          if (statusCode === 200) {
            // Set buyer ID and data
            app.actions.common.setBuyerId(res);

            app.api.buyer((statusCode1, buyerData) => {
              if (statusCode1 === 200) {
                app.actions.common.setBuyerData(buyerData);
                Login.populateBuyerData(buyerData);
                Login.onLoginSuccess();
                Login.doCallbacks({ state: Login.LOGIN_STATE.LoggedIn, status: 'Login successful', data: buyerData });
              } else {
                Login.onLoginFailed();

                const statusMsg = 'Error logging in to the buyer app';
                Login.doCallbacks({ state: Login.LOGIN_STATE.NotLoggedIn, status: statusMsg });
              }
            });

            Login.hide();
          } else {
            Login.onLoginFailed();

            const statusMsg = 'Error logging in to the buyer app';
            Login.doCallbacks({ state: Login.LOGIN_STATE.NotLoggedIn, status: statusMsg });
          }
          app.actions.common.hideBlankModal();
        });
    };

    if (token.match(/^ha_/)) {
      buyerAuth(token);
    } else if (window.FB && window.FB.getLoginStatus) {
      window.FB.getLoginStatus((response = {}) => {
        buyerAuth(response.authResponse && response.authResponse.accessToken || token);
      });
    } else {
      buyerAuth(token);
    }
  };

  // Facebook auth failed
  Login.failed = function (statusMsg) {
    // Signals that the cursor has at least received the auth response
    Login.onLoginFailed();
    Login.doCallbacks({ state: Login.LOGIN_STATE.NotLoggedIn, status: statusMsg });
    app.actions.common.hideBlankModal();
  };

  Login.getBuyerId = function () {
    return app.actions.common.getBuyerId();
  };

  Login.onLoginSuccess = function () {
    app.actions.tagging.getAllTags();
    app.actions.tagging.getListingTagsByRadius();
    app.actions.tagging.getListingTagsAll();

    app.actions.leads.pushLocalLeadsToServer();
    app.actions.common.setLoginState(Login.LOGIN_STATE.LoggedIn);

    app.actions.analytics.sendEvent('login', 'success');

    // Trigger Google Ad Conversion Event
    // eslint-disable-next-line no-undef
    gtag('event', 'conversion', { send_to: 'AW-11414257268/HXuJCLe4rvcYEPT83sIq' });
    app.actions.analytics.sendEvent('custom_conversion', 'login_success');
  };

  Login.onLoginFailed = function () {
    app.actions.common.setLoginState(Login.LOGIN_STATE.NotLoggedIn);
    app.actions.common.resetBuyerData();
    app.actions.common.hideBlankModal();
    app.actions.analytics.sendEvent('login', 'failed');
  };

  Login.logout = function () {
    app.api.logOut(() => {});
    app.actions.common.resetBuyerData();
    app.actions.common.goToRoute('/logout');

    app.actions.analytics.sendEvent('login', 'logout');
  };

  Login.logOutFromApp = function () {
    app.api.logOut(() => {});
  };

  // This function is called when someone finishes with the Login
  // Button.  See the onlogin handler attached to it in the sample
  // code below.
  Login.attemptSignUp = function (opts /* , callback */) {
    // window.registerFbAsyncFunc( () => {
    //  var permissions = "email";
    //  let extraPermissions = app.cursor.get( ['shared', 'login', 'extraPermissions']);
    //  if (extraPermissions) {
    //    permissions += "," + extraPermissions;
    //  }
    //  // callback is optional, and should just wrap Login.statusChangeCallback
    //  FB.login( callback || Login.statusChangeCallback, {
    //    scope: permissions,
    //    auth_type: 'rerequest',
    //    return_scopes: true
    //  });
    //  Login.hide();
    // })

    // extraPermissions: ['shared', 'login', 'extraPermissions'],

    window.HomeASAPAccountsAddInitCallback(
      () => {
        window.HomeASAPAccounts.buyerSignUp(opts);
        app.actions.common.disableBodyScroll();
        Login.hide();
        Login.sharedCursor.set(['login', 'homeASAPModal'], true);
        app.cursor.commit();
      },
    );
  };

  Login.attemptLogin = function (opts /* , callback */) {
    window.HomeASAPAccountsAddInitCallback(
      () => {
        window.HomeASAPAccounts.buyerLogin(opts);
        app.actions.common.disableBodyScroll();
        Login.hide();
        Login.sharedCursor.set(['login', 'homeASAPModal'], true);
        app.cursor.commit();
      },
    );
  };

  window.HomeASAPAccountsStatusChangeCallback = function (data) {
    // const isFacebook = data && data.status && data.status.match(/FACEBOOK/);

    app.actions.analytics.sendEvent('login', data && data.status);

    app.actions.common.enableBodyScroll();
    if (data && data.status) {
      if (data.status.match(/_SUCCESS$/) && typeof data.token === 'string') {
        // Login Successful
        Login.authApi(data.token);
      }

      Login.sharedCursor.set(['login', 'homeASAPModal'], false);
      app.cursor.commit();
    }
  };

  // This is called with the results from from FB.getLoginStatus().
  Login.statusChangeCallback = function (response) {
    console.log('statusChangeCallback');
    console.log(response);
    // The response object is returned with a status field that lets the
    // app know the current login status of the person.
    // Full docs on the response object can be found in the documentation
    // for FB.getLoginStatus().
    if (response.status === 'connected') {
      // A New user just accepted permission and joined.
      if (Login.isNewUser) {
        Login.isNewUser = false;
        // Do stuff with new user
        console.log('New User Accepted Permission');

        Login.show();
      }

      // Logged into your app and Facebook.
      Login.getFBUserInfo(response);
      Login.getPermissions();
    } else if (response.status === 'not_authorized') {
      // The person is logged into Facebook, but not your app.
      Login.failed('Please log into this app');
      Login.isNewUser = true;
    } else {
      // The person is not logged into Facebook, so we're not sure if
      // they are logged into this app or not.
      Login.failed('Please log into Facebook');
    }

    app.actions.common.hideBlankModal();
  };

  // Here we run a very simple test of the Graph API after login is
  // successful.  See statusChangeCallback() for when this call is made.
  Login.getFBUserInfo = function (fbAuthResponse) {
    window.registerFbAsyncFunc(() => {
      FB.api('/me?fields=id,name,email', (fbUserInfoResponse) => {
        Login.success(fbAuthResponse, fbUserInfoResponse);
        app.actions.analytics.sendEvent('login', 'fb success');
      });
    });
  };

  // gets the permissions set
  Login.getPermissions = function () {
    window.registerFbAsyncFunc(() => {
      FB.api('/me/permissions', this.reviewPermissions);
    });
  };

  // checks for the friends permission and sets the value in the tree
  Login.reviewPermissions = function (response) {
    if (!response || !response.data) {
      return;
    }
    response.data.forEach((item) => {
      if (item.permission == 'user_friends' && item.status == 'granted') {
        app.actions.common.setFriendsPermissions();
      }
    });
  };

  Login.checkLoginStatus = function () {
    if (!window.GUID) {
      window.HomeASAPAccounts.checkBuyerLoginStatus(
        (buyerData) => {
          if (buyerData && buyerData.Id) {
            app.actions.common.setBuyerId(buyerData.Id);
            app.actions.common.setBuyerData(buyerData);
            window.registerFbAsyncFunc(() => {
              FB.getLoginStatus(() => {
                Login.onLoginSuccess();
                Login.doCallbacks({ state: Login.LOGIN_STATE.LoggedIn, status: 'Login successful', data: buyerData });
              });
            });
          } else {
            window.registerFbAsyncFunc(() => {
              FB.getLoginStatus((response) => {
                Login.statusChangeCallback(response);
                app.events.emit(app.events.CHECK_FACEBOOK_LOGIN_STATUS_RESPONSE, response);
              });
            });
          }
        },
      );
    }
  };

  Login.handleClick = function () {
    app.actions.analytics.sendEvent('login', 'clicked fb login');
  };

  Login.createLoginPromptViewRecord = function (data, callback = () => {}) {
    const queryParameters = app.actions.common.getQueryParameters();
    const { source } = queryParameters;

    const agentId = _.get(app.actions.common.getCurrentAgent(), 'Id');

    // if no agent ID, wait a bit and try later
    if (!agentId) {
      setTimeout(() => {
        Login.createLoginPromptViewRecord(data, callback);
      }, 100);
      return;
    }

    const dataWithContext = _.assign({}, data, {
      agentId,
      sessionCount: app.actions.common.getSessionCount(),
      source,
      device: app.utils.useMobileSite() ? 'mobile' : 'desktop',
    });

    app.api.createLoginPromptView(dataWithContext, (err, result) => {
      if (err) {
        console.error(err);
        return false;
      }
      callback(err, result.ID);
    });
  };

  Login.saveLoginPromptLoginButtonClicked = function (id) {
    const data = {
      id,
      clickedLogin: true,
    };

    app.api.updateLoginPromptView(data, () => {

    });
  };

  Login.saveLoginPromptCloseClicked = function (id) {
    const data = {
      id,
      closedModal: true,
    };
    app.actions.analytics.sendEvent('login', 'prompter close button clicked');

    app.api.updateLoginPromptView(data, () => {

    });
  };

  Login.saveLoginPromptFacebookResult = function (id, facebookResult) {
    const data = {
      id,
      facebookLoginResult: facebookResult,
    };

    app.api.updateLoginPromptView(data, () => {

    });
  };

  Login.flagLoginPromptAutoLoggedIn = function (id) {
    const data = {
      id,
      autoLoggedIn: true,
    };

    app.api.updateLoginPromptView(data, () => {

    });
  };

  Login.promptDirectLogin = function (opts, callback = () => {}) {
    async.parallel({
      createPromptRecord: (asyncCb) => {
        Login.createLoginPromptViewRecord({
          modalType: opts.action || 'direct',
        }, asyncCb);
      },
      attemptSignUp: (asyncCb) => {
        Login.attemptSignUp({}, (response) => {
          Login.statusChangeCallback(response);
          asyncCb(null, response);
        });
      },
    },
    (err, results) => {
      if (err) {
        console.error('Facebook direct login failed');
      }
      const fbResult = _.get(results, 'attemptSignUp.status');
      const modalId = results.createPromptRecord;

      Login.saveLoginPromptFacebookResult(modalId, fbResult);
      callback(null, fbResult);
    });
  };

  Login.createWindowCloseRecord = function (data) {
    const queryParameters = app.actions.common.getQueryParameters();
    const { source } = queryParameters;

    const agentId = _.get(app.actions.common.getCurrentAgent(), 'Id');

    const dataWithContext = _.assign({}, data, {
      agentId,
      sessionCount: app.actions.common.getSessionCount(),
      source,
      device: app.utils.useMobileSite() ? 'mobile' : 'desktop',
    });

    app.api.createWindowCloseRecord(dataWithContext);
  };

  return Login;
};
