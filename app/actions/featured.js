const Promise = require('promise');

module.exports = function (app) {
  /** *
   * Static methods for Featured
   * */
  function Featured() {
  }

  Featured.layoutCursor = app.cursor.select('layout');
  Featured.cursor = app.cursor.select(['screens', 'featured']);
  Featured.sharedCursor = app.cursor.select(['shared']);
  Featured.userCursor = app.cursor.select(['user']);

  Featured.route = function (route) {
    app.actions.common.setPageTitle('Featured Homes');

    Featured.currentRoute = route;

    // Clean Up
    app.leaflet.clearSchoolMarkersLayer();

    // Layout
    const layout = {
      // leftbar: true,
      header: true,
      searchBar: true,
      featured: {},
    };

    // Account for id:
    if (route.params.id) {
      layout.featured.detail = true;
    } else {
      layout.featured.grid = true;
    }

    Featured.cursor.set('activeId', route.params.id);
    app.actions.panels.reset(['sub', 'photos', 'map']);

    // Set the layout
    app.utils.updateCursor({
      cursor: Featured.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    app.utils.updateCursor({
      cursor: Featured.cursor.select('detail'),
      defaults: app.cursorDefaults.screens.featured.detail,
      finalState: {},
    });

    if (route.params.id) {
      Featured.getListing(route.params.id).then((res) => {
        if (Featured.showPhotos) {
          Featured.showPhotos = false;
          app.actions.common.setPhotoSliderIndex(0);
          app.actions.panels.toggle('photos', res);
        } else {
          app.cursor.commit();
        }
      });

      if ((Featured.cursor.get(['data']) || []).length === 0) {
        Featured.getFeaturedListings();
      }
    } else {
      Featured.showPhotos = false;
      Featured.cursor.set(['detail'], null);
      Featured.getFeaturedListings();
    }

    app.cursor.commit();
  };

  Featured.onNav = function (id) {
    app.actions.common.goToRoute('/featured'.concat(id ? `/${id}` : ''));

    app.actions.analytics.sendEvent('navigation', 'featured', id);
  };

  Featured.onAlert = function (msg) {
    if (msg) {
      Featured.cursor.set(['ref', 'alert'], msg);
    } else {
      Featured.cursor.set(['ref', 'alert'], false);
    }

    app.cursor.commit();
  };

  Featured.onPhotos = function (id) {
    if (id) {
      Featured.showPhotos = true;
    }
    Featured.onNav(id);
  };

  Featured.onMoreInfo = function (id) {
    id ? Featured.onNav(id) : '';
  };

  Featured.getListing = function (id) {
    const p = new Promise(((resolve, reject) => {
      app.actions.panels.getListing(id, (res) => {
        if (res === 404) {
          Featured.cursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          Featured.cursor.set(['detail'], res);

          return resolve(res);
        } else {
          Featured.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
        }

        return reject(null);
      });
    }));
    return p;
  };

  Featured.getFeaturedListings = function () {
    const p = new Promise(((resolve, reject) => {
      let ct = 1000; // Assume all listings
      if (app.utils.isMobile()) {
        if (app.leaflet.opts.numListingsMobile) {
          ct = app.leaflet.opts.numListingsMobile;
        }
      } else if (app.leaflet.opts.numListingsDesktop) {
        ct = app.leaflet.opts.numListingsDesktop;
      }

      const agentData = Featured.sharedCursor.get(['agent', 'data']);

      if (!agentData) {
        return reject(null);
      }

      if (!agentData.MlsId || (!agentData.MlsOfficeId && !agentData.MlsAgentId)) {
        // Not enough data to retrieve featured listing. Therefore showing no results.
        Featured.cursor.set(['data'], []);
        app.cursor.commit();
        resolve([]);
      } else {
        app.api.featuredListings({
          mo: agentData.MlsOfficeId,
          ma: agentData.MlsAgentId,
          ml: agentData.MlsId,
          ct,
        }, (res) => {
          if (res && typeof res === 'object') {
            Featured.cursor.set(['data'], res);
          } else {
            Featured.cursor.set(['data'], false);
          }
          app.cursor.commit();
          resolve(res);
        });
      }
    }));

    return p;
  };

  return Featured;
};
