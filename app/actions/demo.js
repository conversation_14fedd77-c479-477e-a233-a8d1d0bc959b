import agentPickerContext from '../models/agentPickerContext';

const _ = require('lodash');

module.exports = function (app) {
  /** *
   * Static methods for Demo
   * */
  function Demo() {
  }

  Demo.layoutCursor = app.cursor.select('layout');
  Demo.cursor = app.cursor.select(['shared', 'demo']);
  Demo.sharedCursor = app.cursor.select(['shared']);
  Demo.demoAgentDataCursor = app.cursor.select(['shared', 'demoAgent', 'data']);
  Demo.AGENT_PICKER_SOURCE = 'demo';

  Demo.cursor.on('update', (e) => {
    if (e && e.data && e.data.data) {
      if (!_.isEqual(e.data.data, e.data.previousData)) {
        if (e.data.data === false || e.data.data == null) {
          agentPickerContext.removeSource(Demo.AGENT_PICKER_SOURCE);
        } else {
          agentPickerContext.addSource(Demo.AGENT_PICKER_SOURCE);
        }
      }
    }
  });

  Demo.CID_LC_KEY = 'DEMO_CID';
  Demo.getCid = function () {
    return window.localStorageAlias.getItem(Demo.CID_LC_KEY);
  };
  Demo.setCid = function (cid) {
    window.localStorageAlias.setItem(Demo.CID_LC_KEY, cid);
  };
  Demo.removeCid = function () {
    window.localStorageAlias.removeItem(Demo.CID_LC_KEY);
  };

  Demo.INFO_LC_KEY = 'DEMO_INFO';
  Demo.getInfo = function () {
    return JSON.parse(window.localStorageAlias.getItem(Demo.INFO_LC_KEY));
  };
  Demo.setInfo = function (info) {
    window.localStorageAlias.setItem(Demo.INFO_LC_KEY, JSON.stringify(info));

    let updated = false;
    if (info.mlsId && info.mlsId !== Demo.sharedCursor.get(['agent', 'data', 'MlsId'])) {
      updated = true;
      Demo.sharedCursor.set(['agent', 'data', 'MlsId'], info.mlsId);
      Demo.sharedCursor.set(['demoAgent', 'data', 'MlsId'], info.mlsId);
    }
    if (info.mlsOfficeId && info.mlsOfficeId !== Demo.sharedCursor.get(['agent', 'data', 'MlsOfficeId'])) {
      updated = true;
      Demo.sharedCursor.set(['agent', 'data', 'MlsOfficeId'], info.mlsOfficeId);
      Demo.sharedCursor.set(['demoAgent', 'data', 'MlsOfficeId'], info.mlsOfficeId);
    }

    if (updated) {
      app.cursor.commit();

      Demo.demoAgentData = Demo.sharedCursor.get(['agent', 'data']);
      app.actions.common.setOnboardingAgent(Demo.demoAgentData);
    }
  };
  Demo.removeInfo = function () {
    window.localStorageAlias.removeItem(Demo.INFO_LC_KEY);
  };

  Demo.inDemo = function () {
    return Demo.cursor.get();
  };

  Demo.expiredRoute = function () {
    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map', 'listings']);

    // Set the layout
    app.utils.updateCursor({
      cursor: Demo.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { demo: 'expired', header: false, searchBar: false },
    });

    app.cursor.commit();

    const appContainer = document.getElementById('app-container');
    appContainer.classList.add('demo');
  };

  Demo.setDemoLayout = function (layoutValue) {
    Demo.layoutCursor.set('demo', layoutValue);

    app.cursor.commit();
  };

  Demo.checkDemo = function (inComingCid, agentData, cb) {
    if (agentData.HomeSearchRegisteredDateTime && agentData.HomeSearchRegisteredDateTime !== 'demo') {
      if (inComingCid) {
        Demo.setDemoLayout('purchased');

        if (!agentData.AsapStatus) {
          const appContainer = document.getElementById('app-container');
          appContainer.classList.add('demo');
        }
      }
      if (cb) {
        cb(true);
      }
      return;
    }

    // Skip if done once
    if (Demo.cursor.get() !== null) {
      if (cb) {
        cb(Demo.cursor.get());
      }
      return;
    }

    let cidLocal = !!Demo.getCid();

    if (inComingCid) {
      if (Demo.getCid() !== inComingCid) {
        Demo.setCid(inComingCid);
        cidLocal = false;
      }
    }

    const cid = Demo.getCid();

    if (!cid) {
      Demo.cursor.set(false);
      app.cursor.commit();
      if (cb) {
        cb(false);
      }
      return;
    }

    // Check Cid Validity
    app.api.checkCampaignView(cid, Demo.getViewMode(), (res) => {
      if (res && typeof res === 'object') {
        if (res.MembershipId !== app.actions.common.getAgentId()) {
          if (cb) {
            cb(false);
          }
          return;
        }

        app.api.setAgentCookie(agentData.Id);

        if (cidLocal || res.ViewCount === 0) {
          Demo.startDemo(res);
          if (cb) {
            cb(res);
          }
          return;
        } if (res.ViewCount > 0) {
          Demo.stopDemo();

          Demo.cursor.set(res);
          app.router.go('/demo-expired');
          return;
        }
      }

      Demo.stopDemo();
      if (cb) {
        cb(false);
      }
    });
  };

  Demo.stopDemo = function () {
    Demo.removeCid();
    Demo.removeInfo();
    Demo.cursor.set(false);
    Demo.demoAgentData = null;
    Demo.sharedCursor.set(['demoAgent', 'data'], Demo.demoAgentData);
    app.cursor.commit();

    Demo.hideDemoBar();
  };

  Demo.hideDemoBar = function () {
    const appContainer = document.getElementById('app-container');
    appContainer.classList.remove('demo');
  };

  Demo.demoAgentData = null;

  Demo.startDemo = function (res) {
    Demo.cursor.set(res);
    Demo.layoutCursor.set('demo', true);
    Demo.sharedCursor.set(['agent', 'data', 'HomeSearchRegisteredDateTime'], 'demo');

    app.cursor.commit();

    Demo.demoAgentData = Demo.sharedCursor.get(['agent', 'data']);
    Demo.sharedCursor.set(['demoAgent', 'data'], Demo.demoAgentData);

    const appContainer = document.getElementById('app-container');
    appContainer.classList.add('demo');

    app.api.getCampaignValues(Demo.getCid(), (campaignValues) => {
      const info = campaignValues.DEMO_INFO;

      if (info) {
        const infoObj = JSON.parse(info);
        Demo.setInfo(infoObj);

        app.api.postCampaignView(Demo.getCid(), Demo.getViewMode());
      } else {
        Demo.layoutCursor.set('demo', 'pending-build');
        app.cursor.commit();
      }
    });
  };

  Demo.checkCredentials = function (credentials, cb, opts = {}) {
    app.api.checkCampaignValidate(credentials, (res) => {
      if (res && typeof res === 'object'
        && res.DisablePreview !== true
        && (res.Address || (res.CentralLatitude && res.CentralLongitude))) {
        const validated = (res.IsValidOfficeId || res.IsValidAgentId);

        // If Office address was returned, geocode that address for the agent
        if (res.Address) {
          const fullAddress = `${res.Address.FullStreetAddress}, ${
            res.Address.CityName}, ${res.Address.State}, ${res.Address.ZipCode}`;

          app.api.geocodeAddress(fullAddress, (res1) => {
            if (res1 && res1.geometry && res1.geometry.location) {
              if (cb) {
                cb(res);
              }

              const infoObj = {
                location: res1.geometry.location,
                address: res.Address,
                mlsId: credentials.mlsId,
                mlsOfficeId: credentials.officeId,
                agentId: credentials.agentId,
              };
              Demo.setInfo(infoObj);

              app.api.postCampaignValues(Demo.getCid(),
                {
                  DEMO_INFO: JSON.stringify(infoObj),
                  DEMO_VALIDATED_MLSID: credentials.mlsId,
                  DEMO_VALIDATED_OFFICEID: credentials.officeId,
                  DEMO_VALIDATED_AGENTID: credentials.agentId,
                });
            } else {
              const infoObj = {
                location: {
                  lat: res.CentralLatitude,
                  lng: res.CentralLongitude,
                },
                mlsId: credentials.mlsId,
              };
              if (cb) {
                cb(res);
              }

              Demo.setInfo(infoObj);

              app.api.postCampaignValues(Demo.getCid(),
                {
                  DEMO_INFO: JSON.stringify(infoObj),
                  DEMO_UNVALIDATED_MLSID: credentials.mlsId,
                });
            }

            if (validated || !opts.dontSetUnlessValidated) {
              Demo.siteBuilt(credentials.mlsId);
            }
          });
        } else {
          const infoObj = {
            location: {
              lat: res.CentralLatitude,
              lng: res.CentralLongitude,
            },
            mlsId: credentials.mlsId,
          };

          if (cb) {
            cb(res);
          }

          Demo.setInfo(infoObj);

          app.api.postCampaignValues(Demo.getCid(),
            {
              DEMO_INFO: JSON.stringify(infoObj),
              DEMO_UNVALIDATED_MLSID: credentials.mlsId,
            });

          if (validated || !opts.dontSetUnlessValidated) {
            Demo.siteBuilt(credentials.mlsId);
          }
        }
      } else if (res && typeof res === 'object' && res.DisablePreview === true) {
        Demo.setDemoLayout('mls-disable');
      } else if (res && typeof res === 'object' && res.IsActiveMLS === false) {
        Demo.setDemoLayout('inactive');
      } else if (cb) {
        cb(false);
      }
    });
  };

  Demo.siteBuilt = function (mlsId) {
    Demo.setDemoLayout('built');
    app.actions.analytics.sendEvent('Demo Site', 'build my site success', mlsId);
    app.api.postCampaignView(Demo.getCid(), Demo.getViewMode());
  };

  Demo.pointInCircle = function (point, center, radius) {
    return (google.maps.geometry.spherical.computeDistanceBetween(point, center) <= radius);
  };

  Demo.mlsByState = function (stateCode, cb) {
    // Check Cid Validity
    app.api.mlsByState(stateCode, (res) => {
      if (res && typeof res === 'object') {
        cb(res);
        return;
      }

      if (cb) {
        cb(false);
      }
    });
  };

  Demo.getMlsState = function (mlsId, cb) {
    // Get MLS
    if (!mlsId) {
      return cb(null);
    }

    app.api.mls(mlsId, (res) => {
      if (res && typeof res === 'object') {
        // Get State Code
        app.api.state(res.StateId, (res2) => {
          if (res2 && typeof res2 === 'object') {
            return cb(res2.code);
          }
          return cb(null);
        });
      } else {
        return cb(null);
      }
    });
  };

  Demo.getViewMode = function () {
    if (app.utils.useMobileSite()) {
      return 'mobile';
    }
    if (app.utils.isMobile()) {
      return 'tablet';
    }
    return 'desktop';
  };

  return Demo;
};
