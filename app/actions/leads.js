module.exports = function (app) {
  function Leads() {
  }

  Leads.LEADS_IDX_LC_KEY = 'LI';

  Leads.addToLocalStorage = function (lcKey, item) {
    const localStore = JSON.parse(window.localStorageAlias.getItem(lcKey)) || [];
    localStore.push(item);

    window.localStorageAlias.setItem(lcKey, JSON.stringify(localStore));

    return localStore;
  };

  Leads.replaceLocalStorage = function (lcKey, items) {
    window.localStorageAlias.setItem(lcKey, JSON.stringify(items));
  };

  Leads.pushLocalLeadsToServer = function () {
    Leads.pushLeadsToServer(Leads.LEADS_IDX_LC_KEY, JSON.parse(window.localStorageAlias.getItem(Leads.LEADS_IDX_LC_KEY)) || []);
  };

  Leads.pushLeadsToServer = function (lc<PERSON><PERSON>, leads) {
    // Don't push to server unless we have a buyer ID
    const buyerId = app.actions.common.getBuyerId() || 0;
    for (let i = 0; i < leads.length; i++) {
      leads[i].buyerId = buyerId;

      app.api.addLead(
        leads[i],
        // eslint-disable-next-line no-loop-func
        (statusCode, res) => {
          if (statusCode == 200 || statusCode == 400) {
            leads = leads.splice(i, 1);
            Leads.replaceLocalStorage(lcKey, leads);
          } else {
            console.log(`Error adding IDX lead: ${res}`);
          }
        },
      );
    }
  };

  Leads.addLead = function (lcKey, lead) {
    // Don't generate a lead unless we have an agent ID
    const agentId = app.actions.common.getAgentId();
    if (agentId) {
      lead.agentId = agentId;
      Leads.pushLeadsToServer(lcKey, Leads.addToLocalStorage(lcKey, lead));
    }
  };

  Leads.addIDXLead = function (listingId, MlsId) {
    Leads.addLead(Leads.LEADS_IDX_LC_KEY,
      {
        listingId,
        mlsId: MlsId,
        leadActivityType: app.api.LEAD_ACTIVITY_TYPE_ENUM.IDXPin,
        productType: app.api.PRODUCT_TYPE_ENUM.IDX,
        pinType: app.api.LEAD_PIN_TYPE.Automatic,
        partner:
          document.body.dataset.theme === 'more' ? app.api.LEAD_PARTNER.MORE
            : document.body.dataset.theme === 'afordal' ? app.api.LEAD_PARTNER.AFORDAL
              : document.body.dataset.theme === 'fairway' ? app.api.LEAD_PARTNER.FAIRWAY : undefined,
        platform: app.actions.common.forceLoginFirstInteraction() === 'ppc' ? app.api.LEAD_PLATFORM.GOOGLE_ADS : undefined,
      });
  };

  return Leads;
};
