const forEach = require('lodash.foreach');
const filter = require('lodash.filter');
const clone = require('clone');
const Promise = require('promise');

module.exports = function (app) {
  /** *
   * Static methods for Tagging
   * */
  function Tagging() {
  }

  Tagging.TAGGING_LC_KEY = 'T';

  Tagging.userTagsCursor = app.cursor.select('user', 'tags');
  Tagging.taggingCursor = app.cursor.select('shared', 'tagging');
  Tagging.layoutCursor = app.cursor.select('layout');
  Tagging.cursor = app.cursor.select('screens', 'tagging');

  Tagging.route = function (route) {
    app.actions.common.setPageTitle('Tagged Homes');

    Tagging.currentRoute = route;

    // Clean Up
    app.leaflet.clearSchoolMarkersLayer();

    // Layout
    const layout = {
      // leftbar: true,
      tagging: {},
      header: true,
      searchBar: true,
    };

    // Account for id:
    if (route.params.id) {
      layout.tagging.detail = route.qs.tag ? route.qs.tag : true;
    } else {
      layout.tagging.grid = route.qs.tag ? route.qs.tag : true;
    }

    Tagging.cursor.set('activeId', route.params.id);

    app.actions.panels.reset(['sub', 'photos', 'map']);

    // Set the layout
    app.utils.updateCursor({
      cursor: Tagging.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    if (route.params.id) {
      Tagging.getListing(route.params.id).then((res) => {
        if (Tagging.showPhotos) {
          Tagging.showPhotos = false;
          app.actions.common.setPhotoSliderIndex(0);
          app.actions.panels.toggle('photos', res);
        } else {
          app.cursor.commit();
        }
      });
    } else {
      app.utils.updateCursor({
        cursor: Tagging.cursor,
        defaults: app.cursorDefaults.screens.tagging,
        finalState: {},
      });
      Tagging.showPhotos = false;
    }

    app.cursor.commit();
    const loginStatus = app.actions.common.getLoginState();
    // force login if user is not already logged in

    const getTagging = function () {
      if (route.params.id && Tagging.cursor.get('data')) {
        // Don't need to load previous data again

      } else {
        Tagging.getListingsByTag(route.qs.tag || null).then(() => {
          Tagging.getListingTagsAll();
        });
      }
    };

    if (loginStatus === app.actions.login.LOGIN_STATE.LoggedIn) {

    } else if (loginStatus == app.actions.login.LOGIN_STATE.Unknown) {
      app.actions.login.registerCallback((res) => {
        if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
          app.actions.login.startAction = 'tagging';
          app.actions.login.start({ action: 'tagging_force', loginButtonText: 'Login to see tagged homes' }, () => {
            if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
              return false;
            }
            console.log('Logged in');
            Tagging.pushLocalTaggingToServer(() => {
              getTagging();
            });
          });
        } else if (!Tagging.cursor.get(['data'])) {
          getTagging();
        }
      });
    } else {
      app.actions.login.startAction = 'tagging';
      app.actions.login.start({ action: 'tagging_force', loginButtonText: 'Login to see tagged homes' }, (res) => {
        if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
          return false;
        }
        console.log('Logged in');
        getTagging();
      });
    }

    Tagging.forceLogin();
    getTagging();
  };

  Tagging.forceLogin = function () {
    if (!app.actions.demo.inDemo()) {
      app.utils.removeQueryString();
      const loginStatus = app.actions.common.getLoginState();
      // force login if user is not already logged in
      if (loginStatus !== app.actions.login.LOGIN_STATE.LoggedIn) {
        // if (loginStatus == app.actions.login.LOGIN_STATE.Unknown) {
        //  app.actions.login.registerCallback(function (res) {
        //    if (res.status !== "Login successful") {
        //      app.actions.login.startAction = "map"
        //      app.actions.login.start(function (res) {
        //        console.log("Logged in")
        //      })
        //    }
        //  })
        // }
        // else{
        app.actions.login.startAction = 'map';
        app.actions.login.start({ action: 'tagging_force', loginButtonText: 'Login to see tagged homes' }, () => {
          console.log('Logged in');
        });
        // }
      }
    }
  };

  Tagging.getListingsByTag = function (tagName) {
    Tagging.cursor.set(['data'], null);
    Tagging.cursor.set(['ref', 'alert'], false);

    const p = new Promise(((resolve, reject) => {
      app.api.listingsByTags(tagName, (res) => {
        if (res && typeof res === 'object') {
          Tagging.cursor.set(['data'], res);

          app.cursor.commit();

          return resolve(res);
        }

        if (res === 404) {
          Tagging.cursor.set(['ref', 'alert'], 'Listing was not found.');
          Tagging.cursor.set(['data'], false);
        } else if (res === 400) {
          Tagging.cursor.set(['ref', 'alert'], 'Please login.');
          Tagging.cursor.set(['data'], 0);
        } else {
          Tagging.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
          Tagging.cursor.set(['data'], false);
        }

        app.cursor.commit();
        return reject(null);
      });
    }));
    return p;
  };

  Tagging.onNav = function (id) {
    app.actions.common.goToRoute('/tagging'.concat(id ? `/${id}` : ''));

    app.actions.analytics.sendEvent('navigation', 'my homes', id);
  };

  Tagging.onAlert = function (msg) {
    if (msg) {
      Tagging.cursor.set(['ref', 'alert'], msg);
    } else {
      Tagging.cursor.set(['ref', 'alert'], false);
    }

    app.cursor.commit();
  };

  Tagging.onPhotos = function (id) {
    if (id) {
      Tagging.showPhotos = true;
    }
    Tagging.onNav(id);
  };

  Tagging.onMoreInfo = function (id) {
    id ? Tagging.onNav(id) : '';
  };

  Tagging.getListing = function (id) {
    const p = new Promise(((resolve, reject) => {
      app.actions.panels.getListing(id, (res) => {
        if (res === 404) {
          Tagging.cursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          Tagging.cursor.set(['detail'], res);

          return resolve(res);
        } else {
          Tagging.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
        }

        return reject(null);
      });
    }));
    return p;
  };

  Tagging.onNavWithTagName = function (tagName) {
    app.actions.common.goToRoute('/tagging'.concat(tagName ? `?tag=${tagName}` : ''));
  };

  Tagging.isLoggedIn = function () {
    return app.actions.login.getBuyerId();
  };

  Tagging.getTags = function () {
    return Tagging.userTagsCursor.get();
  };

  Tagging.addTag = function (tagName) {
    if (typeof (tagName) === 'string' && tagName) {
      if (['favorite', 'dislike'].indexOf(tagName) !== -1) {
        return;
      }
      const currTagCnt = Tagging.userTagsCursor.select(tagName).get();
      if (!currTagCnt) {
        Tagging.userTagsCursor.set(tagName, 1);
      } else {
        Tagging.userTagsCursor.set(tagName, currTagCnt + 1);
      }
      app.cursor.commit();
    }
  };

  Tagging.localTagging = JSON.parse(window.localStorageAlias.getItem(Tagging.TAGGING_LC_KEY)) || [];

  Tagging.getLocalTaggingCount = function () {
    return filter(Tagging.localTagging, (t) => t.Tags && t.Tags.length > 0).length;
  };

  Tagging.saveToLocalStorage = function () {
    window.localStorageAlias.setItem(Tagging.TAGGING_LC_KEY, JSON.stringify(Tagging.localTagging));

    app.actions.common.setNotificationHeader('Tagged Homes', Tagging.getLocalTaggingCount());
  };

  Tagging.pushLocalTaggingToServer = function (cb) {
    if (Tagging.isLoggedIn()) {
      if (Tagging.localTagging && Tagging.localTagging.length > 0) {
        // Send Local tags to Server
        app.api.addTags(Tagging.localTagging, (res) => {
          if (res && typeof res === 'object') {
            Tagging.localTagging = [];
            Tagging.saveToLocalStorage();
            cb ? cb() : '';
          }
        });
      }
    }
  };

  // [{
  //  ListingId: listingId,
  //  Latitude: Lat,
  //  Longitude: Lon,
  //  Tags: tags,
  //  AgentId: agentId
  // }]
  Tagging.setTagsForListing = function (listing, tagsObject) {
    Tagging.taggingCursor.set(listing.Id, clone(tagsObject));
    app.cursor.commit();

    app.leaflet.modifyIconForTaggingById(listing.Id);

    const tags = Tagging._tagsObjectToArray(tagsObject);

    const listingTagObject = {
      ListingId: listing.Id,
      Latitude: listing.Location.Lat,
      Longitude: listing.Location.Lon,
      Tags: tags,
      AgentId: 0,
    };

    // Check if we've already saved agent Id to this listing
    const taggingData = Tagging.cursor.get('data') || {};
    const listings = taggingData.Listings || [];
    for (let listingIdx = 0; listingIdx < listings.length; listingIdx++) {
      const listingWrapper = listings[listingIdx];
      if (listingWrapper.Listing && listingWrapper.Listing.Id == listing.Id) {
        listingTagObject.AgentId = listingWrapper.AgentId;
        break;
      }
    }

    if (!listingTagObject.AgentId && listing.MlsIds.indexOf((app.actions.common.getAgentData() || {}).MlsId) != -1) {
      listingTagObject.AgentId = app.actions.common.getAgentId();
    } else if (!listingTagObject.AgentId && listing.MlsIds.indexOf(app.actions.common.getOnboardingAgentMlsId()) != -1) {
      listingTagObject.AgentId = app.actions.common.getOnboardingAgentId();
    }

    let replaced = false;
    for (let i = 0; i < Tagging.localTagging.length; i++) {
      if (Tagging.localTagging[i].ListingId === listingTagObject.ListingId) {
        replaced = true;
        Tagging.localTagging[i] = listingTagObject;
        break;
      }
    }
    if (!replaced) {
      Tagging.localTagging.push(listingTagObject);
    }
    Tagging.saveToLocalStorage();

    Tagging.pushLocalTaggingToServer(() => {
      if (Tagging.layoutCursor.get('tagging')) {
        Tagging.getAllTags();
      }
    });
  };

  Tagging.getAllTags = function (localTags, cb) {
    const getLocalTags = function () {
      const allTagsWithCount = {};
      for (let i = 0; i < Tagging.localTagging.length; i++) {
        const tags = Tagging.localTagging[i].Tags;
        for (let j = 0; j < tags.length; j++) {
          const tag = tags[j];
          if (!allTagsWithCount[tag]) {
            allTagsWithCount[tag] = 1;
          } else {
            allTagsWithCount[tag]++;
          }
        }
      }
      return allTagsWithCount;
    };

    if (localTags || !Tagging.isLoggedIn()) {
      // Local Tags
      Tagging.userTagsCursor.set(getLocalTags());
      app.cursor.commit();
      cb ? cb() : '';
    } else {
      app.api.listingTagsWithCount((res) => {
        if (res && typeof res === 'object') {
          const allTagsWithCount = {}; // clone(Tagging.userTagsCursor.get())
          for (let i = 0; i < res.length; i++) {
            allTagsWithCount[res[i].Tag] = /* (allTagsWithCount[res[i].Tag] || 0) +*/ res[i].ListingCount;
          }
          Tagging.userTagsCursor.set(allTagsWithCount);
          app.cursor.commit();
          cb ? cb() : '';

          Tagging.pushLocalTaggingToServer();
        }
      });
    }
  };

  Tagging.moveLocalTagsToTree = function () {
    const t = {};

    forEach(Tagging.localTagging, (tag) => {
      t[tag.ListingId] = Tagging._tagsArrayToObject(tag.Tags);
    });

    Tagging.taggingCursor.set(t);
  };

  Tagging.getTagsForListing = function (listingId, cb) {
    if (Tagging.isLoggedIn()) {
      app.api.listingTagsByListing(listingId, (res) => {
        if (res && typeof res === 'object') {
          cb(res);
        } else {
          cb({});
        }
      });
    } else {
      for (let i = 0; i < Tagging.localTagging.length; i++) {
        if (Tagging.localTagging[i].Id == listingId) {
          cb(Tagging.localTagging[i]);
          return;
        }
      }
      cb({});
    }
  };

  Tagging.getListingTagsByRadius = function () {
    if (Tagging.isLoggedIn()) {
      const locationStr = `${app.cursor.get(['panels', 'listings', 'meta', 'locationStr'])}`;
      const pos = app.utils.validateLocationStr(locationStr);
      if (!pos) {
        return true;
      }

      app.api.listingTagsByRadius(pos.Lat, pos.Lon, pos.radius, (res) => {
        if (res && typeof res === 'object') {
          // console.log("Listing Tags from radius search")
          // console.log(res)

          forEach(res, (taggingObj) => {
            const tagsObj = Tagging._tagsArrayToObject(taggingObj.Tags);
            // Don't overwrite local tags
            const localTagging = Tagging.taggingCursor.get(taggingObj.ListingId);
            if (localTagging) {
              forEach(localTagging, (value, tag) => {
                tagsObj[tag] = true;
              });
            }
            Tagging.taggingCursor.set(taggingObj.ListingId, tagsObj);
          });
          app.cursor.commit();

          if (app.leaflet.markerLayerGroup) {
            app.leaflet.modifyIconsForTagging();
          }
        } else {

        }
      });
    }
  };

  Tagging.getListingTagsAll = function () {
    if (Tagging.isLoggedIn()) {
      app.api.listingTagsAll((res) => {
        if (res && typeof res === 'object') {
          // console.log("Listing Tags from radius search")
          // console.log(res)

          forEach(res, (taggingObj) => {
            const tagsObj = Tagging._tagsArrayToObject(taggingObj.Tags);
            // Don't overwrite local tags
            const localTagging = Tagging.taggingCursor.get(taggingObj.ListingId);
            if (localTagging) {
              forEach(localTagging, (value, tag) => {
                tagsObj[tag] = true;
              });
            }
            Tagging.taggingCursor.set(taggingObj.ListingId, tagsObj);
          });
          app.cursor.commit();
        } else {

        }
      });
    }
  };

  // Helpers

  Tagging._tagsArrayToObject = function (tagsArr) {
    const tags = {};
    forEach(tagsArr, (value) => {
      tags[value] = true;
    });
    return tags;
  };

  Tagging._tagsObjectToArray = function (tagsObject) {
    const tags = [];
    forEach(tagsObject, (value, key) => {
      if (value === true) {
        tags.push(key);
      }
    });
    return tags;
  };

  Tagging.getUserTags = function () {
    return Tagging.cursor.get('data');
  };

  return Tagging;
};
