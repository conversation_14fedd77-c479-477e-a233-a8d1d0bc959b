const _ = require('lodash');

module.exports = function (app) {
  /** *
   * Static methods for Logout
   * */
  function Logout() {
  }

  Logout.cursor = app.cursor.select();
  Logout.layoutCursor = app.cursor.select('layout');

  Logout.route = function () {
    // Reset the tree
    app.utils.updateCursor({
      cursor: Logout.cursor,
      defaults: app.cursorDefaults,
      finalState: { layout: _.assign({}, app.cursorDefaults.layout, { logout: true, header: false }) },
    });

    app.cursor.commit();

    window.localStorageAlias.clear();
  };

  return Logout;
};
