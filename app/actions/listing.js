const Promise = require('promise');
const _ = require('lodash');

module.exports = function (app) {
  /** *
   * Static methods for Listing
   * */
  function Listing() {
  }

  Listing.sharedCursor = app.cursor.select('shared');
  Listing.layoutCursor = app.cursor.select('layout');
  Listing.cursor = app.cursor.select(['screens', 'listing']);
  Listing.mapCursor = app.cursor.select(['panels', 'map']);

  Listing.route = function (route) {
    app.actions.common.resetPageTitle();

    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map']);

    // Get the Listing data
    const p1 = Listing.getListing(route.params.id);

    // Set the layout
    app.utils.updateCursor({
      cursor: Listing.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { listing: true, header: true, searchBar: true },
    });

    // Set the screen
    app.utils.updateCursor({
      cursor: Listing.cursor,
      defaults: app.cursorDefaults.screens.listing,
      finalState: {
        header: true,
        searchBar: true,
        ref: { spinner: true },
      },
    });

    // GET complete
    p1.then((/* res */) => {
      if (app.utils.useMobileSite()) {
        // Map.getStreetViewAvailability(res[1])
        //  .then(function (isStreetViewAvailable) {
        //    if (isStreetViewAvailable) {
        //      Map.cursor.set(['streetViewAvailable'], isStreetViewAvailable)
        //      app.cursor.commit();
        //    }
        //  });
        Map.cursor.set(['streetViewAvailable'], true);
        app.cursor.commit();
      }
      app.cursor.commit();
    });

    app.cursor.commit();
  };

  Listing.onMouseOver = function (listingId) {
    app.leaflet.modifyIconByID('mouseover', listingId);
    if (!app.utils.isMobile()) {
      app.leaflet.showPopup(listingId);
    }
  };

  Listing.onMouseOut = function (listingId) {
    app.leaflet.modifyIconByID('mouseout', listingId);
    if (!app.utils.isMobile()) {
      app.leaflet.hidePopup(listingId);
    }
  };

  Listing.setMap = function (res) {
    app.utils.updateCursor({
      cursor: Listing.mapCursor.select('ref'),
      defaults: app.cursorDefaults.panels.map.ref,
      finalState: { className: 'layout--2s-5' },
    });

    // Set the map
    app.leaflet.clearAllLayers(true);
    app.leaflet.setMap(res.Location, { zoom: 13 });
    app.leaflet.setMarkers([res], res, {
      dontResize: true, dontBindMarker: true,
    });
  };

  Listing.onNavToMap = function () {
    const listing = Listing.cursor.get('data');

    if (listing && listing.Location && listing.Location.Lat && listing.Location.Lon) {
      const nextRoute = `/search/map/${listing.Location.Lat},${listing.Location.Lon},${app.leaflet.opts.circleDefaultRadius}`;
      app.actions.common.goToRoute(nextRoute);
    }
  };

  Listing.getListing = function (id) {
    const p = new Promise(((resolve) => {
      app.actions.panels.getListing(id, (res) => {
        Listing.cursor.set(['ref', 'spinner'], false);

        if (res === 404) {
          Listing.cursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          Listing.cursor.set('data', res);
          Listing.setMap(res);
        } else {
          Listing.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
          // Listing.mapCursor.set('pos', null);
        }

        resolve(res);
      });
    }));

    return p;
  };

  Listing.onAlert = function (msg) {
    if (msg) {
      Listing.cursor.set(['ref', 'alert'], msg);
    } else {
      Listing.cursor.set(['ref', 'alert'], false);
    }

    app.cursor.commit();
  };

  Listing.getPublicSchoolData = function (listing, cb) {
    app.api.publicSchoolsByGeo(
      listing.Location.Lat,
      listing.Location.Lon,
      listing.Address.State,
      (res) => {
        cb(res);
        if (res && typeof res === 'object') {
          app.leaflet.addSchoolMarkers(res);
        }
      },
    );
  };

  Listing.getPrivateSchoolData = function (listing, cb) {
    app.api.privateSchoolsByGeo(
      listing.Location.Lat,
      listing.Location.Lon,
      listing.Address.State,
      (res) => {
        cb(res);
        if (res && typeof res === 'object') {
          app.leaflet.addSchoolMarkers(res);
        }
      },
    );
  };

  Listing.hoverOverSchool = function (school) {
    app.leaflet.showSchoolMarkerPopup(school.gsId);

    if (school.geometry) {
      app.leaflet.showSchoolAttendanceBoundary(school.name, school.geometry);
      app.leaflet.resizeMapToSchool();
    }
  };

  Listing.clearSchool = function () {
    app.leaflet.clearSchoolAttendanceBoundary();
  };

  Listing.getMlsData = function (mlsId, cb) {
    app.api.mls(mlsId, (res) => {
      if (res && typeof res === 'object') {
        Listing.sharedCursor.set(['mlsData', mlsId], res);
        if (cb) {
          cb(res);
        }
      } else {
        Listing.sharedCursor.set(['mlsData', mlsId], false);
        if (cb) {
          cb(false);
        }
      }

      app.cursor.commit();
    }, `Api.Mls-${mlsId}`);
  };

  Listing.getDemographics = function (listing, cb) {
    app.api.demographics(
      listing.Location.Lat,
      listing.Location.Lon,
      (res) => {
        cb(res);
      },
    );
  };

  Listing.getYelpSearchResults = function (listing, category, callback = () => {}) {
    if (!(listing && _.get(listing, 'Location.Lat') && _.get(listing, 'Location.Lon'))) {
      return console.error('Can\'t get yelp search results with invalid location');
    }

    // passing undefined will use the default values that are specified in the function declaration
    app.api.fetchYelpSearchResultsRadius(category, listing.Location.Lat, listing.Location.Lon, undefined, 0, (err, results) => {
      if (err) {
        return console.error(err);
      }

      app.leaflet.addYelpSearchResults(results);

      Listing.cursor.set('yelpSearchResults', results);
      app.cursor.commit();

      if (app.actions.listing.cursor.get('showYelpMarkers')) {
        app.leaflet.fitViewToYelpMarkers();
      }

      callback();
    });
  };

  Listing.removeYelpResults = function () {
    app.leaflet.clearYelpMarkers();
    Listing.cursor.set('yelpSearchResults', []);
    Listing.cursor.set('showYelpMarkers', false);
    app.cursor.commit();
  };

  Listing.setYelpMarkerActive = function (index) {
    app.leaflet.setYelpMarkerActive(index);
  };

  Listing.clearActiveYelpMarker = function () {
    app.leaflet.clearActiveYelpMarker();
  };

  Listing.hideYelpMarkers = function () {
    Listing.cursor.set('showYelpMarkers', false);
    app.cursor.commit();
  };

  Listing.showYelpMarkers = function () {
    Listing.cursor.set('showYelpMarkers', true);
    app.cursor.commit();
    app.leaflet.fitViewToYelpMarkers();
  };

  Listing.getHomeInspectionQuote = function (listingId, cb) {
    app.api.getHomeInspectionQuote(listingId, cb);
  };

  Listing.submitHomeInspectionRequest = function (listingId, initialQuote, discountedRate, buyer, callback) {
    const data = {
      agentId: app.actions.common.getAgentId(),
      buyer,
      listingId,
      initialQuote,
      discountedRate,
    };
    (new Promise((resolve) => {
      if (app.actions.common.getBuyerId()) {
        return resolve();
      }

      app.api.emailRegister(buyer, (statusCode, result) => {
        if (statusCode !== 200) {
          return resolve();
        }
        app.actions.login.registerCallback(() => {
          resolve();
        });
        app.actions.login.authApi(result);
      });
    })).finally(() => {
      app.api.submitHomeInspectionRequest(data, (err, result) => {
        callback(err, result);
      });
    });
  };

  Listing.checkRatePlugRates = app.api.checkRatePlugRates;

  return Listing;
};
