module.exports = function (app) {
  /** *
   * Static methods for Onboarding
   * */
  function Onboarding() {
  }

  Onboarding.layoutCursor = app.cursor.select('layout');
  Onboarding.cursor = app.cursor.select(['screens', 'onboarding']);

  Onboarding.route = function (route) {
    app.actions.common.resetPageTitle();

    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map', 'listings']);

    // nob = no onboarding. we shorten it so it is less obvious in the URL
    if (!('nob' in route.qs)) {
      app.actions.common.setOnboardingAgent(app.cursor.get(['shared', 'agent', 'data']));
    }

    // Set the layout
    app.utils.updateCursor({
      cursor: Onboarding.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { onboarding: true, header: false, searchBar: false },
    });

    app.models.agentPicker.deliverFreeLeadTrafficForCurrentAgent();

    app.cursor.commit();
  };

  Onboarding.onAutosuggest = {

    showWhen(val) {
      return val && val.trim().length > 2;
    },

    suggestionValue(data) {
      return data.value;
    },

    api(val, cb) {
      app.api.autosuggest(val, (res) => (typeof res === 'object'
        ? cb(null, res)
        : cb(null, null)));
    },
  };

  Onboarding.onNav = function (Lat, Lon, radius, locationQuery, saleType, opts) {
    console.log('-- Suggestion value --', Lat, Lon);

    if (Lat && Number(Lat) && Lon && Number(Lon)) {
      app.actions.map.resetToInitialZoomOnRoute();

      let route = '/search/map/'.concat(Lat, ',', Lon, ',', (radius || app.leaflet.opts.circleDefaultRadius),
        (opts && opts.listingId) ? `/${opts.listingId}` : '');
      route = route.concat('?fr=1',
        locationQuery ? `&q=${encodeURIComponent(locationQuery)}` : '',
        saleType ? `&st=${encodeURIComponent(saleType)}` : '');

      app.actions.common.setLocationType(opts);

      app.actions.common.goToRoute(route);
    }

    if (locationQuery) {
      window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({
        lat: Lat,
        lon: Lon,
        radius,
        locationQuery,
      }));
    }
  };

  Onboarding.geocodeAddress = function (address, cb) {
    if (!address || address.length < 4) {
      return;
    }
    app.api.geocodeAddress(address, (res) => {
      if (res && typeof res === 'object') {
        cb(res);
      }
    });
  };

  return Onboarding;
};
