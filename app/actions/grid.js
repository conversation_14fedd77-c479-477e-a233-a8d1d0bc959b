const Promise = require('promise');
const forEach = require('lodash.foreach');

module.exports = function (app) {
  /** *
   * Static methods for Grid
   * */
  function Grid() {
  }

  Grid.layoutCursor = app.cursor.select('layout');
  Grid.cursor = app.cursor.select(['screens', 'grid']);
  Grid.listingsCursor = app.cursor.select(['panels', 'listings']);
  Grid.sharedCursor = app.cursor.select(['shared']);
  Grid.userCursor = app.cursor.select(['user']);

  Grid.setPageTitleWithCount = function (listingCount) {
    if (listingCount) {
      app.actions.common.setPageTitle(''.concat(listingCount, ' Search Results'));
    } else {
      app.actions.common.resetPageTitle();
    }
  };

  Grid.route = function (route) {
    Grid.currentRoute = route;
    app.actions.common.setLocation(route);

    app.actions.common.restoreSearchParams();
    app.actions.menu.updateUrlWithSearchParams();

    // Clean Up
    app.leaflet.clearSchoolMarkersLayer();

    // Are we coming from a previous gird/map route
    const meta = Grid.listingsCursor.get('meta');
    const previousListingsData = Grid.listingsCursor.get('data');
    const canUseListingsData = !!(route.lastUrl // We've previously loaded a url
    && meta.locationStr // There is a location saved
    && meta.locationStr === route.params.location // The location in the new route is the same as the last one we saved
    // route.lastParams.location === route.params.location &&  // The location is last route is the same as the location in the new route
    // Commented out to no restrict canUseListingsData to map grid toggles. Now it will also work with agent page toggle.
    && previousListingsData // A current data set exists
    );

    // Layout
    const layout = {
      // leftbar: true,
      header: true,
      searchBar: true,
      grid: {},
    };

    let p1; let committed = false;
    const p2 = canUseListingsData === false
        && app.actions.panels.getListings(route.params.location);

    // Account for id:
    if (route.params.id) {
      layout.grid.detail = true;
      const lastListingData = Grid.cursor.get(['data', route.params.id]);
      if (lastListingData) {
        p1 = true;
      } else {
        p1 = Grid.getListing(route.params.id);
      }

      app.actions.common.incrementSessionViewedPropertyCount(route.params.id);
    } else {
      layout.grid.grid = true;
      let listingCount = Grid.listingsCursor.get('lastFullListingCount');
      if (!listingCount) {
        listingCount = app.cursor.facets.listingCount.get();
      }
      Grid.setPageTitleWithCount(listingCount);
    }

    layout.menu = Grid.layoutCursor.get('menu') === 'should-stay';

    app.actions.panels.setActiveListing(route.params.id);

    const panelsToReset = ['sub', 'map'];

    // if photos were open, keep them open
    if (Grid.layoutCursor.get('photos')) {
      layout.photos = true;
    } else {
      panelsToReset.push('photos');
    }

    app.actions.panels.reset(panelsToReset);

    // Set the layout
    app.utils.updateCursor({
      cursor: Grid.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    // Set the screen
    app.utils.updateCursor({
      cursor: Grid.cursor.select('ref'),
      defaults: app.cursorDefaults.screens.grid.ref,
      finalState: {
        ref: { spinner: true },
      },
    });

    if (Grid.cursor.get(['ref', 'activeId']) === 'show-tagging') {
      Grid.cursor.set(['ref', 'activeId'], 'Tagged Properties');
    }

    // Save current location to the tree
    Grid.listingsCursor.set(['meta', 'locationStr'], route.params.location);

    // GET complete
    Promise.all([p2, p1]).then((res) => {
      Grid.cursor.set(['ref', 'spinner'], false);

      // Handle response depending on situation
      // And account for id:
      if (typeof res[0] !== 'undefined'
        && res[0] === null) {
        Grid.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
      }

      committed = true;
      app.cursor.commit();

      Grid.showPhotosIfNeeded();

      if (!route.params.id) {
        // const data = Grid.listingsCursor.get('data');
        let listingCount = Grid.listingsCursor.get('lastFullListingCount');
        if (!listingCount) {
          listingCount = app.cursor.facets.listingCount.get();
        }
        Grid.setPageTitleWithCount(listingCount);
      }

      // check if agent id matches
      if (route.params.id) {
        const listing = app.cursor.get(['screens', 'grid', 'data', app.cursor.get(['panels', 'listings', 'activeId'])]);
        app.cursor.set(['screens', 'grid', 'data', 'activeListing'], listing);
      }
    });

    if (committed === false) {
      app.cursor.commit();

      Grid.showPhotosIfNeeded();
    }
  };

  Grid.onNav = function (id) {
    const locationStr = Grid.listingsCursor.get(['meta', 'locationStr']);
    if (!locationStr) {
      return;
    }
    if (!id || (Grid.currentRoute && Grid.currentRoute.params.id == id)) {
      app.actions.common.goToRoute('/search/grid/'.concat(locationStr));
    } else {
      app.actions.common.goToRoute('/search/grid/'.concat(locationStr, '/', (id || '')));
    }
  };

  Grid.onAlert = function (msg) {
    if (msg) {
      Grid.cursor.set(['ref', 'alert'], msg);
    } else {
      Grid.cursor.set(['ref', 'alert'], false);
    }

    app.cursor.commit();
  };

  Grid.getListing = function (id) {
    forEach(Grid.cursor.get(['data']), (n, key) => {
      Grid.cursor.set(['data', key], null);
    });
    app.cursor.commit();
    const p = new Promise(((resolve, reject) => {
      app.actions.panels.getListing(id, (res) => {
        if (res === 404) {
          Grid.cursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          // Grid.cursor.set(['data'], res);
          Grid.cursor.set(['data', id], res);
          app.cursor.commit();
          return resolve(res);
        } else {
          Grid.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
        }

        return reject(null);
      });
    }));
    return p;
  };

  Grid.toggleLights = function () {
    const isOn = Grid.cursor.get(['lightsOut']);
    Grid.cursor.set(['lightsOut'], !isOn);
    app.cursor.commit();

    app.actions.analytics.sendEvent('navigation', 'lights out', !isOn);
  };

  Grid.setActiveTab = function (item) {
    console.log(`active is${item}`);
    Grid.cursor.set(['ref', 'activeId'], item);
    app.cursor.commit();
  };

  Grid.onSortClick = function (type) {
    Grid.cursor.set(['ref', 'sortType'], type);
    app.cursor.commit();
    app.actions.menu.updateUrlWithSearchParams();
  };

  Grid.getCardData = function (id, callback = () => {}) {
    console.log(`get card data for ${id}is${Grid.cursor.get(['data', id])}`);
    if (Grid.cursor.get(['data', id])) {
      callback(null, Grid.cursor.get(['data', id]));
    } else {
      const p = Grid.getListing(id);
      p.then((value) => {
        app.actions.analytics.sendEvent('property cards', 'get data', value.ZipCode);
        callback(null, value);
        app.cursor.commit();
      });
    }

    /*
     var p = Grid.getListing(id);
     p.then(function(value){
     if( Grid.cursor.get(['ref','showingBackId']) === id)
     Grid.cursor.set(['ref','showingBackId'],null);
     else
     Grid.cursor.set(['ref','showingBackId'],id);
     app.cursor.commit();
     })
     */
  };

  Grid.onPhotos = function (id) {
    const locationStr = Grid.listingsCursor.get(['meta', 'locationStr']);
    if (!locationStr) {
      return;
    }
    if (id) {
      Grid.onPhotosFlag = id;
    }
    app.actions.common.goToRoute('/search/grid/'.concat(locationStr, '/', (id || '')));
  };

  Grid.showPhotosIfNeeded = function () {
    if (Grid.onPhotosFlag) {
      app.actions.panels.toggle('photos', Grid.onPhotosFlag);
      Grid.onPhotosFlag = null;
    }
  };

  Grid.onMoreInfo = function (id) {
    const locationStr = Grid.listingsCursor.get(['meta', 'locationStr']);
    if (!locationStr) {
      return;
    }
    app.actions.common.goToRoute('/search/grid/'.concat(locationStr, '/', (id || '')));
  };

  Grid.setTag = function (text) {
    Grid.cursor.set(['ref', 'tag'], text);
    app.cursor.commit();
    console.log(`${text}is the text`);
    app.api.listingTagsByTag(text, () => {

    });
  };

  // THIS APPEARS TO BE EITHER BROKEN OR UNUSED.  panels.getFeaturedListings does not exist.
  Grid.getFeaturedListings = function () {
    app.actions.panels.getFeaturedListings().then(() => {
      app.cursor.commit();
    });
  };

  Grid.onNavToTag = function () {
    if (Grid.layoutCursor.get(['grid', 'grid'])) {
      Grid.setActiveTab('Tagged Properties');
    } else {
      Grid.setActiveTab('show-tagging');
      Grid.onNav();
    }
  };

  Grid.showMenu = function () {
    Grid.cursor.set(['showMenu'], true);
    app.cursor.commit();
  };

  Grid.hideMenu = function () {
    Grid.cursor.set(['showMenu'], false);
    app.cursor.commit();
  };

  return Grid;
};
