module.exports = function (app) {
  function Help() {
  }

  Help.toggleHelp = function () {
    const isActive = app.cursor.get(['layout', 'help']);
    app.cursor.set(['layout', 'help'], (!isActive));
    app.cursor.commit();

    if (!isActive) {
      app.actions.analytics.sendEvent('navigation', 'help');
    }
  };

  Help.showHelpOverlay = function () {
    app.cursor.set(['panels', 'help', 'overlay'], true);
    app.cursor.commit();
  };

  Help.hideHelpOverlay = function () {
    app.cursor.set(['panels', 'help', 'overlay'], false);
    app.cursor.commit();
  };

  Help.setOverlayTargets = function (targets) {
    if (!targets) {
      targets = [];
    }
    if (!(Array.isArray(targets))) {
      console.warn('Attempt to set tree.panels.help.targets to', targets, 'which is not an array. Only an array of 0 or more CSS selectors is valid.');
    }
    app.cursor.set(['panels', 'help', 'targets'], targets);
    app.cursor.commit();
  };

  return Help;
};
