import agentPickerContext from '../models/agentPickerContext';

module.exports = function (app) {
  /** *
   * Static methods for Home
   * */
  function Home() {
  }

  Home.layoutCursor = app.cursor.select('layout');
  Home.cursor = app.cursor.select(['screens', 'onboarding']);

  Home.route = function () {
    app.actions.common.resetPageTitle();

    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map', 'listings']);
    app.actions.common.clearOnboardingAgent();

    // Set the layout1
    app.utils.updateCursor({
      cursor: Home.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { home: window.location.pathname === '/more' ? 'more' : true, header: false, searchBar: false },
    });

    app.cursor.commit();
  };

  Home.onAgentSelected = function (agent, href) {
    app.router.go(href);
  };

  Home.onNav = function (Lat, Lon, radius, locationQuery, saleType, opts) {
    console.log('-- Suggestion value --', Lat, Lon);

    if (Lat && Number(Lat) && Lon && Number(Lon)) {
      app.actions.map.resetToInitialZoomOnRoute();

      const onboardingAgentId = app.actions.common.getOnboardingAgentId();
      const context = {
        lat: Lat,
        lng: Lon,
        onboardingAgentId,
      };

      const cb = (agent) => {
        if (agent && (agent.CustomURL || agent.Id)) {
          let route = '/'.concat(agent.CustomURL || agent.Id, '/search/map/',
            Lat, ',', Lon, ',', (radius || app.leaflet.opts.circleDefaultRadius),
            (opts && opts.listingId) ? `/${opts.listingId}` : '');

          if (opts && opts.currentLocation) {
            if (app.utils.useMobileSite()) {
              // Go to List view in mobile
              route = route.replace('/search/map/', '/search/grid/');
            }
            route = route.replace(/,\d+$/, ',1000');
            app.actions.menu.sharedMenuCursor.set('sortType', 'distance_asc');
          }

          app.actions.common.setLocationType(opts);

          route = route.concat('?fr=1',
            locationQuery ? `&q=${encodeURIComponent(locationQuery)}` : '',
            saleType ? `&sa=${encodeURIComponent(saleType)}` : '');

          app.router.go(route);

          if (locationQuery) {
            window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({
              lat: Lat,
              lon: Lon,
              radius,
              locationQuery,
            }));
          }
        } else {
          console.log('No agent was picked');
        }
      };

      app.models.agentPicker.setRouteCallback(cb);
      agentPickerContext.set(context);
    }
  };

  Home.performAgentSearch = function (lat, lng, callback = () => {}) {
    Home.fetchBestAgentsByPoint(lat, lng, callback);

    // async.parallel(
    //   {
    //     pickedAgent: cb => {
    //       app.models.agentPicker.pickAgentWithoutCharging( { lat, lng }, ( err, agent ) => {
    //         cb( err, agent );
    //       });
    //     },
    //     bestAgents: cb => {
    //       Home.fetchBestAgentsByPoint( lat, lng, cb );
    //     }
    //   },
    //   (err, results) => {
    //     if( err ){
    //       console.error(err);
    //       return callback( err );
    //     }
    //
    //     app.cursor.set( ['screens','agentSearch', 'pickedAgent'], results.pickedAgent );
    //     app.cursor.commit();
    //     callback();
    //   }
    // )
  };

  Home.fetchBestAgentsByPoint = function (lat, lng, callback = () => {}) {
    const data = {
      lat,
      lng,
    };
    app.api.getBestAgentsByPoint(data, (err, results) => {
      if (err) {
        callback(err);
        return console.error(err);
      }

      app.cursor.set(['screens', 'agentSearch', 'agents'], results);
      app.cursor.commit();
      callback();
    });
  };

  Home.submitContestEntry = function (id, callback = () => {}) {
    app.api.submitContestEntry(id, (err, response) => {
      if (err) {
        callback(err);
        return console.error(err);
      }
      callback(null, response);
    });
  };

  Home.trackSelectAgentActivity = function (id) {
    app.api.trackSelectAgentActivity(id);
  };

  Home.simulatePickAgent = app.api.simulatePickAgent;

  return Home;
};
