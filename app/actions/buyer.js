const Promise = require('promise');

module.exports = function (app) {
  /** *
   * Static methods for Buyer
   * */
  function Buyer() {
  }

  Buyer.layoutCursor = app.cursor.select(['layout']);
  Buyer.screenCursor = app.cursor.select(['screens', 'buyer']);
  Buyer.buyerData = app.cursor.select(['shared', 'buyer', 'data']);

  Buyer.route = function (route) {
    app.actions.menu.setBackRoute(route);

    const buyerData = Buyer.buyerData.get();
    app.actions.common.setPageTitle(
      ''.concat(buyerData.FirstName ? `${buyerData.FirstName} ${buyerData.LastName}` : 'Dashboard'),
    );

    // Set the layout
    app.utils.updateCursor({
      cursor: Buyer.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { buyer: (app.actions.menu.backRoute ? app.actions.menu.backRoute : true), header: true, searchBar: true },
    });

    let getListingPromise;
    let committed = false;

    if (route.params.id) {
      Buyer.screenCursor.set(['activeListing'], null);
      getListingPromise = Buyer.getListing(route.params.id);
    } else {
      Buyer.screenCursor.set(['activeListing'], 0);
    }

    if (getListingPromise) {
      getListingPromise.then(() => {
        committed = true;
        app.cursor.commit();

      // try to find the agent that was originally tagged
      // const tags = app.actions.tagging.getUserTags();
      // const listingTags = _.get(tags, 'Listings') || [];
      // const listingEntry = _.find(listingTags, (entry) => entry.Listing.Id == route.params.id);
      });
    }

    if (committed === false) {
      app.cursor.commit();
    }

    const loginStatus = app.actions.common.getLoginState();
    // force login if user is not already logged in

    const getTagging = function () {
      if (route.params.id && app.actions.tagging.cursor.get('data')) {
        // Don't need to load previous data again

      } else {
        app.actions.tagging.getListingsByTag(route.qs.tag || null).then(() => {
          app.actions.tagging.getListingTagsAll();
        });
      }
    };

    if (loginStatus === app.actions.login.LOGIN_STATE.LoggedIn) {

    } else if (loginStatus == app.actions.login.LOGIN_STATE.Unknown) {
      app.actions.login.registerCallback((res) => {
        if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
          app.actions.login.startAction = 'tagging';
          app.actions.login.start({ action: 'buyer_force' }, () => {
            if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
              return false;
            }
            console.log('Logged in');
            app.actions.tagging.pushLocalTaggingToServer(() => {
              getTagging();
            });
          });
        } else if (!app.actions.tagging.cursor.get(['data'])) {
          getTagging();
        }
      });
    } else {
      app.actions.login.start({ action: 'buyer_force' }, (res) => {
        if (res.state !== app.actions.login.LOGIN_STATE.LoggedIn) {
          return false;
        }
        console.log('Logged in');
        getTagging();
      });
    }

    getTagging();
  };

  Buyer.onClose = function () {
    if (app.actions.menu.backRoute) {
      app.router.go(app.actions.menu.backRoute);
    }
  };

  Buyer.onNav = function (listingId) {
    app.actions.common.goToRoute('/buyer'.concat(listingId ? `/${listingId}` : ''));

    app.actions.analytics.sendEvent('navigation', 'buyer profile', listingId);
  };

  Buyer.getListing = function (id) {
    const p = new Promise(((resolve) => {
      app.actions.panels.getListing(id, (res) => {
        if (res === 404) {
          Buyer.screenCursor.set(['ref', 'alert'], 'Listing was not found.');
        } else if (res !== null) {
          Buyer.screenCursor.set(['activeListing'], res);
        } else {
          //  Grid.cursor.set(['ref','alert'], 'There was a problem connecting to the server')
        }

        resolve(res);
      });
    }));
    return p;
  };

  Buyer.dsRoute = function () {
    if (app.actions.common.getAgentHasIDX()) {
      app.actions.common.goToRoute('/');
    } else {
      app.actions.common.goToRoute('/agent');
    }
    if (app.actions.common.getAgentHasDS()) {
      app.actions.common.showDreamsweepsModal();
    }
  };

  Buyer.getDreamsweepsEntry = function (callback) {
    const buyerId = app.actions.common.getBuyerId();
    const agentId = app.actions.common.getAgentId();
    const dsId = app.cursor.get(['shared', 'dreamsweeps', 'Id']);

    if (!buyerId || !agentId || !dsId) {
      console.error('Buyer.getDreamsweepsEntry Missing parameters');
      callback(400);
    } else {
      app.api.getDreamsweepsEntry({
        AgentId: agentId,
        BuyerId: buyerId,
        SweepstakesId: dsId,
      }, callback);
    }
  };

  Buyer.createLendingTreeLead = function ({
    firstName, lastName, email, phone,
  }, callback) {
    const agentId = app.actions.common.getAgentId();

    if (!firstName || !email) {
      console.error('Buyer.createLendingTreeLead Missing parameters');
      callback(400);
    } else {
      app.api.createLendingTreeLead({
        agentId,
        firstName,
        lastName,
        email,
        phone,
        sourceUrl: window.location.href,
      }, callback);
    }
  };

  Buyer.createDreamsweepsLead = function ({
    firstName, lastName, email, phone,
  }, successCallback, errorCallback) {
    const buyerId = app.actions.common.getBuyerId();
    const agentId = app.actions.common.getAgentId();
    const dsId = app.cursor.get(['shared', 'dreamsweeps', 'Id']);

    if (!firstName || !email) {
      console.error('Buyer.createDreamsweepsLead Missing parameters');
      errorCallback('Please enter your details.');
    } else {
      app.api.createDreamsweepsLead({
        agentId,
        buyerId,
        bonus: false,
        sweepstakesId: dsId,
        firstName,
        lastName,
        email,
        phone,
      }, (statusCode, res) => {
        if (!statusCode && res) {
          successCallback(res.totalEntries);
          app.actions.common.showDreamsweepsSuccessModal();
        } else {
          const errorMessageMatch = (res && res.Message || '').match(/errorMessage":"([^"]+)"/);
          errorCallback(errorMessageMatch && errorMessageMatch[1] || 'Error occured, please try again later.');
        }
      });
    }
  };

  Buyer.ppcBuyerRegister = function (buyer, callback) {
    const {
      firstName, lastName, email, phoneNumber,
    } = buyer || {};
    app.api.emailRegister({ firstName, lastName, email }, (statusCode, result) => {
      if (statusCode !== 200) {
        callback(statusCode, result);

        alert('Please login to your existing account.');
        app.actions.login.attemptLogin();
      } else {
        app.actions.login.registerCallback((res) => {
          if (res && res.state === app.actions.login.LOGIN_STATE.LoggedIn) {
            app.api.putBuyer({ Phone: phoneNumber }, () => {});
          } else {

          }
        });
        app.actions.login.authApi(result);
        callback();
      }
    });
  };

  return Buyer;
};
