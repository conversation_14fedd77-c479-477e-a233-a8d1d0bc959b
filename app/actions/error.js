module.exports = function (app) {
  /** *
   * Static methods for Error
   * */
  function Error() {
  }

  Error.layoutCursor = app.cursor.select('layout');
  Error.cursor = app.cursor.select(['screens', 'error']);

  Error.route = function (route) {
    app.actions.common.resetPageTitle();

    if (route.errorUrl) {
      window.history.replaceState({}, document.title, route.errorUrl);
    }

    Error.currentRoute = route;

    // Reset things
    app.actions.panels.reset(['sub', 'photos', 'map', 'listings']);

    // const agentData = app.cursor.get(['shared', 'agent', 'data']);

    // Set the layout
    app.utils.updateCursor({
      cursor: Error.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { error: app.cursor.get(['layout', 'error']) || true, header: true, searchBar: true },
    });

    app.utils.updateCursor({
      cursor: Error.cursor,
      defaults: app.cursorDefaults.screens.error,
      finalState: { ref: { hasBack: !!Error.currentRoute.lastUrl } },
    });

    app.cursor.commit();
  };

  return Error;
};
