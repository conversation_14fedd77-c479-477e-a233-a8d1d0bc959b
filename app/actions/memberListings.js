module.exports = function (app) {
  /** *
   * Static methods for MemberListings
   * */
  function MemberListings() {
  }

  MemberListings.layoutCursor = app.cursor.select('layout');
  MemberListings.listingsCursor = app.cursor.select(['panels', 'listings']);

  MemberListings.route = function (route) {
    MemberListings.currentRoute = route;

    // Layout
    const layout = {
      readHeader: 'member-listings',
      memberListings: true,
    };

    // Set the layout
    app.utils.updateCursor({
      cursor: MemberListings.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: layout,
    });

    app.cursor.commit();

    app.actions.common.fetchAgentCount();

    const location = app.utils.validateLocationStr(route.params.location);
    if (location) {
      MemberListings.listingsCursor.set(['meta', 'locationStr'], route.params.location);
      app.actions.panels.getListings(route.params.location, { rm: true });
    } else if (!route.lastUrl && window.self === window.top) {
      // Pop up location permission on first load
      app.actions.menu.searchCurrentLocation(1, (err) => {
        if (err) {
          // Cannot search location
        }
      });
    }
  };

  MemberListings.onNav = function () {
    app.router.go('/member-listings');
  };

  return MemberListings;
};
