module.exports = function (app) {
  /** *
   * Static methods for Home
   * */
  function HomeWorth() {
  }

  HomeWorth.layoutCursor = app.cursor.select('layout');
  HomeWorth.cursor = app.cursor.select(['screens', 'homeWorth']);

  HomeWorth.route = function (route) {
    if (!app.actions.common.getAgentHasHV()) {
      return app.actions.common.goToRoute('/');
    }

    app.actions.common.setPageTitle('My Home Value');

    // Set the layout
    app.utils.updateCursor({
      cursor: HomeWorth.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: { homeWorth: true },
    });

    let address = route.qs.address && decodeURIComponent(route.qs.address);
    if (!address) {
      address = route.qs.address_encoded && Buffer.from(route.qs.address_encoded, 'base64');
    }
    if (address) {
      app.actions.analytics.sendEvent('navigation', 'home worth', 'has address');
      app.actions.analytics.sendEvent('home value', 'loaded with address');
      if (address !== HomeWorth.cursor.get(['searchResult', 'formatted_address'])) {
        HomeWorth.cursor.set('searchResult', null);
        HomeWorth.fetchSearchResult(address);
      }
    } else {
      app.actions.analytics.sendEvent('navigation', 'home worth', 'initial');
      app.actions.analytics.sendEvent('home value', 'loaded');
      HomeWorth.cursor.set('searchResult', null);
    }

    app.cursor.commit();
    window.scrollTo(0, 0);
  };

  HomeWorth.submitHomeWorthSearch = function (value, result) {
    const url = app.router.generateUrl(app.router.ROUTE_NAMES.HOME_WORTH, {
      agentId: app.router.currentRoute.params.agentId,
    },
    {
      // address: value,
      address_encoded: Buffer.from(value).toString('base64'),
    });
    if (result && result.formatted_address) {
      HomeWorth.cursor.set('searchResult', JSON.parse(JSON.stringify(result)));
      app.cursor.commit();
    }
    app.router.go(url);
  };

  HomeWorth.fetchSearchResult = function (address, callback = () => {}) {
    app.api.geocodeAddress(decodeURI(address), (result) => {
      HomeWorth.cursor.set('searchResult', result);
      app.cursor.commit();
      callback(result);
    });
  };

  HomeWorth.submitHomeWorthForm = function (formData, callback) {
    formData.agentId = app.actions.common.getAgentId();

    (new Promise((resolve) => {
      if (app.actions.common.getBuyerId()) {
        return resolve();
      }

      const fullName = formData.name.trim();
      let firstName = fullName.substr(0, fullName.lastIndexOf(' '));
      let lastName = fullName.replace(firstName, '').trim();
      if (!firstName && lastName) {
        firstName = lastName;
        lastName = '';
      }
      const params = {
        firstName,
        lastName,
        email: formData.email,
      };
      const formattedPhone = formData.phone.replace(/[^\d]/g, '');
      if (formattedPhone.length === 10) {
        params.phoneNumber = formattedPhone;
      }

      app.api.emailRegister(params, (statusCode, result) => {
        if (statusCode !== 200) {
          return resolve();
        }
        app.actions.login.registerCallback(() => {
          resolve();
        });
        app.actions.login.authApi(result);
      });
    })).finally(() => {
      app.api.submitHomeWorthForm(formData, (err, result) => {
        callback(err, result);
      });
    });
  };

  HomeWorth.onNav = function () {
    app.actions.common.goToRoute('/homevalue');

    app.actions.analytics.sendEvent('navigation', 'home worth');
  };

  return HomeWorth;
};
