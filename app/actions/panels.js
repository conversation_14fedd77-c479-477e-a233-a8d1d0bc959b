import agentPickerContext from '../models/agentPickerContext';

const forEach = require('lodash.foreach');
const Promise = require('promise');

module.exports = function (app) {
  /** *
   * Static methods for Panels
   * */
  function Panels() {
  }

  Panels.cursor = app.cursor.select('panels');
  Panels.layoutCursor = app.cursor.select('layout');
  Panels.mapCursor = app.cursor.select(['panels', 'map']);
  Panels.headerCursor = app.cursor.select(['panels', 'header']);
  Panels.sharedCursor = app.cursor.select(['shared']);
  Panels.gridCursor = app.cursor.select(['screens', 'grid']);
  Panels.mapScreenCursor = app.cursor.select(['screens', 'map']);

  // Reset to tree state - type = map,header,sub,photos,leftbar
  Panels.reset = function (type, commit) {
    // If type array is passed, then just iterate
    if (Array.isArray(type)) {
      forEach(type, (t) => {
        Panels.reset(t);
      });

      if (commit) {
        app.cursor.commit();
      }
      return;
    }

    // For map, go to map method
    if (type === 'map') {
      Panels.resetMap(true);
      return;
    }

    if (Panels.layoutCursor.get(type)) {
      Panels.layoutCursor.set(type, false);
    }

    app.utils.updateCursor({
      cursor: Panels.cursor.select(type),
      defaults: app.cursorDefaults.panels[type],
    });

    if (commit) {
      app.cursor.commit();
    }
  };

  // Reset to tree state - type = sub,photos
  Panels.toggle = function (type, data, layoutData) {
    if (type && data) {
      // Check if toggle
      if (Panels.cursor.get([type, 'activeId']) === data) {
        Panels.reset(type, true);
        return;
      }

      // Determine if data is type string
      // If type is string, then fetch the data from listings
      if (typeof data === 'string') {
        const listings = Panels.cursor.get(['listings', 'data']);
        let d = null;

        forEach(listings, (i) => {
          if (i.Id === data) {
            d = i;
            return false;
          }
        });
        if (d) {
          data = d;
        }
      }

      // Another check coz coz:
      // For some reason, data is not in the listings
      if (typeof data === 'object') {
        Panels.layoutCursor.set(type, layoutData || true);
        Panels.cursor.set([type, 'activeId'], String(data.Id));

        // If type == photos - build images
        if (type === 'photos') {
          if (!data.REALData || data.Images) {
            data.ImageUrls = app.utils.getImageUrls(data);
            Panels.cursor.set([type, 'data'], data);
          } else {
            // REALData images are in full listing object
            app.api.listing(data.Id, (res) => {
              if (res && typeof res === 'object') {
                const listingData = res;
                listingData.ImageUrls = app.utils.getImageUrls(listingData);
                Panels.cursor.set([type, 'data'], listingData);
                app.cursor.commit();
              } else {

              }
            }, true);
          }

          window.fbq('track', 'ViewContent', {
            ...app.utils.getListingPixelAttributes(data),
            content_type: 'home_listing_photos',
          });
        } else {
          Panels.cursor.set([type, 'data'], data);
        }
      } else {
        Panels.reset(type);
      }
    } else {
      Panels.reset(type);
    }
    app.cursor.commit();
  };

  Panels.resetMap = function (commit) {
    app.utils.updateCursor({
      cursor: Panels.mapCursor.select('ref'),
      defaults: app.cursorDefaults.panels.map.ref,
    });

    if (commit) {
      app.cursor.commit();
    }
  };

  Panels.startApiMS = null;

  Panels.getListings = function (locationStr, opts = {}) {
    // boundingBox is a Leaflet LatLngBounds object

    if (!locationStr) {
      locationStr = `${Panels.cursor.get(['listings', 'meta', 'locationStr'])}`;
    }

    const pos = app.utils.validateLocationStr(locationStr);
    if (!pos) {
      return new Promise(((resolve) => {
        resolve();
      }));
    }

    // For a scenaio where you do not want data to be set
    // Or any mutations to the listings cursor here
    if (opts.dontSetData !== true) {
      Panels.cursor.set(['listings', 'meta', 'spinner'], true);
      app.cursor.commit();
    }

    const p = new Promise(((resolve) => {
      let ct = 1000; // Assume we will *ask for *all listings

      if (app.utils.useMobileSite()) {
        // Phones use the standard mobile setting always
        ct = app.leaflet.opts.numListingsMobile;
      } else if (!opts.boundingBox) {
        // Don't restrict listings on a boundingBox query on desktop.
        //   Bounding box (zoomed listing) queries are intersected with the circle and filtered on the client
        if (app.utils.isMobile()) {
          if (app.leaflet.opts.numListingsMobile) {
            ct = app.leaflet.opts.numListingsMobile;
          }
        } else if (app.leaflet.opts.numListingsDesktop) {
          ct = app.leaflet.opts.numListingsDesktop;
        }
      }

      const queryOpts = {
        Lat: pos.Lat,
        Lon: pos.Lon,
        radius: pos.radius,
      };

      const queryNarrowingOpts = {
        ct,
        BoundingBox: opts.boundingBox, // Leaflet LatLngBounds
      };

      // Check the opts that defined the last query, to check if this is a new query
      //   This determines whether we use the "original" (last full search) number of search results or the real number of search results
      const lastSearchOptsJSON = Panels.cursor.get(['listings', 'lastSearchOpts']);

      // Use the search query opts and selection opts for this comparison, but ignore sort type
      const newSearchOptsObj = app.actions.menu.getSearchSelectionsOpts();
      for (const attrname in queryOpts) {
        newSearchOptsObj[attrname] = queryOpts[attrname];
      }
      newSearchOptsObj.sortType = null;

      // Save new query for comparison next time
      const newSearchOptsJSON = JSON.stringify(newSearchOptsObj);
      Panels.cursor.set(['listings', 'lastSearchOpts'], newSearchOptsJSON);

      const searchOpts = app.actions.menu.getSearchSelectionsOpts();
      for (const attrname in queryOpts) {
        searchOpts[attrname] = queryOpts[attrname];
      }
      for (const attrname in queryNarrowingOpts) {
        searchOpts[attrname] = queryNarrowingOpts[attrname];
      }
      searchOpts.circleUnchanged = opts.circleUnchanged;

      if (opts.rm) {
        searchOpts.rm = true;
      }

      // Check for parameter to limit results to 1 MLS
      const mapCurrentRoute = app.actions.map.currentRoute;
      if (mapCurrentRoute && mapCurrentRoute.qs && mapCurrentRoute.qs.mlsid) {
        searchOpts.mlsId = mapCurrentRoute.qs.mlsid;
      }

      app.api.listings(searchOpts, (res, numResults) => {
        if (opts.dontSetData !== true) {
          Panels.cursor.set(['listings', 'meta', 'spinner'], false);

          if (res && typeof res === 'object') {
            // Filter the listing array
            // #1 Potentially filter by the circle (logic for whether or not this happens is in the Leaflet function)
            res = app.leaflet.filterListingsByCircle(res);

            const specialFinancingFilter = (searchOpts.specialFinancing || '').toString();
            // #1.5 Sort the listings by special financing highlighter if supplied
            if (specialFinancingFilter) {
              // res is an array with an optional key SpecialFinancePrograms which is array of strings containing special financing program names, e.g. ['FHA', 'VA']
              // sort res so items containing searchOpts.specialFinancing are first
              res = res.sort((a, b) => {
                const aHasSpecialFinancing = (a.SpecialFinancePrograms || []).includes(specialFinancingFilter);
                const bHasSpecialFinancing = (b.SpecialFinancePrograms || []).includes(specialFinancingFilter);
                if (aHasSpecialFinancing && !bHasSpecialFinancing) {
                  return -1;
                }
                if (!aHasSpecialFinancing && bHasSpecialFinancing) {
                  return 1;
                }
                return 0;
              });

              const hasSpecialFinancingResults = res.some((listing) => listing.SpecialFinancePrograms && listing.SpecialFinancePrograms.includes(specialFinancingFilter));
              if (hasSpecialFinancingResults) {
                app.actions.common.hideRateplugSpecialFinancingMissingModal();
              } else {
                app.actions.common.showRateplugSpecialFinancingMissingModal();
              }
            }

            // #2 Regardless, filter down to the correct number of listings for the environment
            let filteredListingCount = app.leaflet.opts.numListingsDesktop;
            if (app.utils.useMobileSite()) {
              filteredListingCount = app.leaflet.opts.numListingsMobile;
            }
            res.splice(filteredListingCount, res.length - filteredListingCount);

            // Set the listing data in the cursor here, but only if the map is NOT visible
            const mapVisible = app.cursor.get(['layout', 'map']);
            if (!mapVisible) {
              Panels.cursor.set(['listings', 'data'], res);
            }

            // Regardless, set the full number of listings in the cursor for display
            Panels.cursor.set(['listings', 'numResults'], numResults);
            if (app.utils.useMobileSite() || (lastSearchOptsJSON != newSearchOptsJSON) || (numResults > res)) {
              // If this is a new search, save the number of results that just came back as the last full listing count
              Panels.cursor.set(['listings', 'lastFullListingCount'], numResults);
            }
          } else {
            // On error, null out the listing cursor
            Panels.cursor.set(['listings', 'data'], null);
          }
        } else {
          console.log("!!! Listings API: Didn't update cursor");
        }

        app.actions.tagging.getListingTagsByRadius();

        app.cursor.commit();

        resolve(res);
      });
    }));

    return p;
  };

  Panels.refreshListings = function (opts = {}, locationStr) {
    console.warn('Refreshing listings');

    // If map is visible, immediately refresh pins
    // First, Check if we need to do a bounding box query
    let p1;

    const boundingBox = app.leaflet.getBoundingBox();
    if (boundingBox
      && (
        boundingBox.getNorth() !== boundingBox.getSouth()
        || boundingBox.getWest() !== boundingBox.getEast()
      )
    ) {
      p1 = app.actions.panels.getListings(locationStr, { circleUnchanged: opts.circleUnchanged, boundingBox });
    } else {
      p1 = app.actions.panels.getListings(locationStr, { circleUnchanged: opts.circleUnchanged });
    }

    Promise.all([p1]).then((res) => {
      const selectedListing = Panels.getSelectedListingId();
      console.log(`Setting listing: ${selectedListing}`);

      // Set the new listings on the map, but don't mess with the selected listing
      opts.dontResetSelected = true;

      app.actions.map.setListings(res, selectedListing, opts);
      app.cursor.commit();

      // For a bounding box query, we use the last full listing count in the page title
      //   Otherwise, use the number of results just returned
      let numTotalResults = 0;
      numTotalResults = app.actions.map.listingsCursor.get('lastFullListingCount');
      if (!numTotalResults) {
        numTotalResults = res[0].length;
      }

      app.actions.map.setPageTitleWithCount(numTotalResults);
    });
  };

  Panels.getSelectedListingId = function () {
    let selectedListingId = null;
    const mapCurrentRoute = app.actions.map.currentRoute;
    const mapVisible = app.cursor.get(['layout', 'map']);
    if (mapVisible && mapCurrentRoute && mapCurrentRoute.params && mapCurrentRoute.params.id) {
      selectedListingId = mapCurrentRoute.params.id;
      // p2 = app.actions.map.getListing(selectedListingId);
    }

    return selectedListingId;
  };

  Panels.setActiveListing = function (id, commit) {
    Panels.cursor.set(['listings', 'activeId'],
      (typeof id === 'string' ? id : null));

    if (commit) {
      app.cursor.commit();
    }
  };

  Panels.showSortRail = function () {
    Panels.mapScreenCursor.set(['sort'], true);
    app.cursor.commit();
  };

  Panels.hideSortRail = function () {
    Panels.mapScreenCursor.set(['sort'], false);
    app.cursor.commit();
  };

  Panels.showHeaders = function () {
    if (!Panels.headerCursor.get(['showHeader'])) {
      Panels.headerCursor.set(['showHeader'], true);
      app.cursor.commit();
    }
  };

  Panels.hideHeaders = function () {
    if (Panels.headerCursor.get(['showHeader'])) {
      Panels.headerCursor.set(['showHeader'], false);
      app.cursor.commit();
    }
  };

  Panels.getListing = function (id, cb) {
    app.api.listing(id, (res) => {
      // set agent picker context mlsids
      if (res && res !== 404 && res.MlsIds) {
        agentPickerContext.setMlsIds(res.MlsIds);
      }

      window.fbq('track', 'ViewContent', {
        ...app.utils.getListingPixelAttributes(res),
      });

      cb(res);
    }, true);
  };

  return Panels;
};
