const Promise = require('promise');
const _ = require('lodash');
const forEach = require('lodash.foreach');

module.exports = function (app) {
  /** *
   * Static methods for Map
   * */
  function Map() {
  }

  Map.layoutCursor = app.cursor.select('layout');
  Map.cursor = app.cursor.select(['screens', 'map']);
  Map.sharedCursor = app.cursor.select(['shared']);
  Map.listingsCursor = app.cursor.select(['panels', 'listings']);
  Map.mapCursor = app.cursor.select(['panels', 'map']);

  Map.setPageTitleWithCount = function (listingCount) {
    if (listingCount) {
      app.actions.common.setPageTitle(''.concat(listingCount, ' Search Results'));
    } else {
      app.actions.common.resetPageTitle();
    }
  };

  // Viewport reset status
  Map.canResetViewport = function () {
    return !Map.mapCursor.get('dontResetViewport');
  };
  Map.allowResetViewport = function () {
    Map.mapCursor.set('dontResetViewport', false);
    app.cursor.commit();
  };
  Map.dontResetViewport = function () {
    Map.mapCursor.set('dontResetViewport', true);
    app.cursor.commit();
  };

  // Map pan to route status
  Map.canPanToRoute = function () {
    return !Map.mapCursor.get('dontPanToRoute');
  };
  Map.allowPanToRoute = function () {
    Map.mapCursor.set('dontPanToRoute', false);
    app.cursor.commit();
  };
  Map.dontPanToRoute = function () {
    Map.mapCursor.set('dontPanToRoute', true);
    app.cursor.commit();
  };

  Map.hasBoundingBox = function () {
    return app.leaflet.hasBoundingBox();
  };

  Map.canShowListings = function () {
    return app.leaflet.canShowListings();
  };

  Map.zoomToLevel = null;
  Map.resetToInitialZoomOnRoute = function () {
    Map.zoomToLevel = app.leaflet.opts.initialZoom;
  };

  Map.route = function (route, opts = {}) {
    Map.currentRoute = route;
    app.actions.common.setLocation(route);

    app.actions.common.restoreSearchParams();
    app.actions.menu.updateUrlWithSearchParams();

    // Check for parameter to limit results to 1 MLS
    const mapCurrentRoute = app.actions.map.currentRoute;
    if (mapCurrentRoute && mapCurrentRoute.qs && mapCurrentRoute.qs.mlsid) {
      app.actions.menu.setMlsFilter(mapCurrentRoute.qs.mlsid);
    }

    // If there is currently a listing selected and there was not previously a listing selected, pan to listing
    if (Map.currentRoute.params.id && !Map.currentRoute.lastParams.id) {
      app.leaflet.panToListing(Map.currentRoute.params.id);
    }

    // Are we coming from a previous gird/map route
    const meta = Map.listingsCursor.get('meta');

    const previousListingsData = Map.listingsCursor.get('data');

    // Reuse current data set if all of the following are true...
    let canUseListingsData = !!(route.lastUrl // We've previously loaded a url
    && meta.locationStr // There is a location saved
    && meta.locationStr === route.params.location // The location in the new route is the same as the last one we saved
    // route.lastParams.location === route.params.location &&  // The location is last route is the same as the location in the new route
    // Commented out to no restrict canUseListingsData to map grid toggles. Now it will also work with agent page toggle.
    && previousListingsData // A current data set exists
    );

    if (opts && opts.reload) {
      canUseListingsData = false; // Always reload data if requested
    }

    // Save current location to the tree
    Map.listingsCursor.set(['meta', 'locationStr'], route.params.location);

    const savedSearch = Map.sharedCursor.get('savedSearch');
    if (savedSearch && savedSearch.searchLocationStr !== route.params.location) {
      Map.sharedCursor.set('savedSearch', null);
    }

    // Initialize detail view, if necessary
    const content = {
      ref: {},
    };

    if (route.params.id) {
      content.ref.display = true;
    }

    // Set up map layout
    Map.setMapLayout(route, canUseListingsData, opts);
    Map.setViews(route, content);

    if (Map.showZoomMsg()) {
      Map.listingsCursor.set(['meta', 'zoomMsg'], true);
      return;
    }

    // Set map alert for listings GET
    if (canUseListingsData === false) {
      Map.mapCursor.set(['ref', 'alert'], 'Loading listings...');
    }

    let p1;
    if (canUseListingsData) {
      // We are reusing the existing data set
      if (route.params.id) {
        // If a property is selected, load its details
        p1 = Map.getListing(route.params.id);
        Promise.all([p1]).then((listingRes) => {
          // Set the existing search data and the new selected listing in the map
          const res = new Array(2);
          res[0] = null;
          res[1] = listingRes[0];

          Map.setListings(res, route.params.id, opts);
          app.cursor.commit();
        });

        app.actions.common.incrementSessionViewedPropertyCount(route.params.id);
      } else {
        // Otherwise just declutter
        // app.leaflet.declutter();

        app.leaflet.cleanSelected();
        app.leaflet.modifyIconsForTagging(); // Local Tags

        // Map.setMapLayout removes markers if coming from grid,
        // need to add them back if that is the case.
        if (route.lastUrl.match(/\/search\/grid\//g)) {
          Map.setListings([previousListingsData], route.params.id, opts);
          app.cursor.commit();
        }
      }
    } else {
      if (route.params.id && !app.cursor.get(['screens', 'map', 'data'])) {
        // If a property is selected, load its details
        p1 = Map.getListing(route.params.id);
        Promise.all([p1]).then((listingRes) => {
          // Set the existing search data and the new selected listing in the map
          const res = new Array(2);
          res[0] = null;
          res[1] = listingRes[0];

          Map.setListings(res, route.params.id, opts);
          app.cursor.commit();
        });

        app.actions.common.incrementSessionViewedPropertyCount(route.params.id);
      }
      // Do a full listing refresh
      app.leaflet.m.whenReady(() => {
        // setBoundingBox does not work correctly in this context on initial load unless there is a  callback
        setTimeout(() => {
          app.leaflet.setBoundingBox();
          app.actions.panels.refreshListings({}, route.params.location);
        }, 60);
      }, this);
    }

    // Regardless, refresh tiny markers
    app.leaflet.refreshTinyMarkers();
  };

  Map.agentSearchRoute = function (route, opts) {
    Map.currentRoute = route;
    Map.setMapLayout(route, false, opts);
    Map.setViews(route, { ref: {} });

    app.actions.common.restoreSearchParams();
    app.actions.menu.updateUrlWithSearchParams();

    // Check for parameter to limit results to 1 MLS
    const mapCurrentRoute = app.actions.map.currentRoute;
    if (mapCurrentRoute && mapCurrentRoute.qs && mapCurrentRoute.qs.mlsid) {
      app.actions.menu.setMlsFilter(mapCurrentRoute.qs.mlsid);
    }

    app.utils.updateCursor({
      cursor: Map.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: {
        map: true,
        header: true,
        searchBar: true,
        menu: Map.layoutCursor.get('menu') === 'should-stay',
        agentSearch: true,
      },
    });

    // Save current location to the tree
    Map.listingsCursor.set(['meta', 'locationStr'], route.params.location);
    const location = app.utils.validateLocationStr(route.params.location);
    const agents = app.cursor.get(['screens', 'agentSearch', 'agents']);
    if (_.isEmpty(agents)) {
      app.actions.home.performAgentSearch(location.Lat, location.Lon);
    }

    app.leaflet.m.whenReady(() => {
      // setBoundingBox does not work correctly in this context on initial load unless there is a  callback
      setTimeout(() => {
        app.leaflet.setBoundingBox();
        app.actions.panels.refreshListings({}, route.params.location);
      }, 100);
    }, this);

    app.leaflet.refreshTinyMarkers();
  };

  Map.setViews = function (route, content) {
    // Set the layout
    app.utils.updateCursor({
      cursor: Map.layoutCursor,
      defaults: app.cursorDefaults.layout,
      finalState: {
        map: true, header: true, searchBar: true, menu: Map.layoutCursor.get('menu') === 'should-stay',
      },
    });

    // Set the screen
    app.utils.updateCursor({
      cursor: Map.cursor,
      defaults: app.cursorDefaults.screens.map,
      finalState: content,
    });

    if (app.leaflet.opts.showListingDetail) {
      app.actions.panels.setActiveListing(route.params.id);
    }
    if (app.leaflet.opts.showListingSummary) {
      app.actions.panels.reset(['sub', 'photos']);
    }

    if (Map.zoomToLevel) {
      app.leaflet.m.setZoom(Map.zoomToLevel);
      Map.zoomToLevel = null;
    }

    Map.listingsCursor.set(['meta', 'zoomMsg'], false);

    app.cursor.commit();
  };

  /// /// Common steps after loading listings ///////////////////////////////////

  Map.setListings = function (res, id, opts = {}) {
    // Hide the spinner
    Map.cursor.set(['ref', 'spinner'], false);

    // Set the listing data, if it exists
    if (res[0]) {
      Map.listingsCursor.set('data', res[0]);

      // Get current radius for the status message
      const pos = app.utils.validateLocationStr(Map.listingsCursor.get(['meta', 'locationStr']));

      // For a bounding box query, we use the last full listing count in the page title
      //   Otherwise, use the number of results just returned
      let numTotalResults = Map.listingsCursor.get('lastFullListingCount');
      if (!numTotalResults) {
        numTotalResults = res[0].length;
      }

      app.actions.map.updateStatusMessage(pos.radius, numTotalResults, res[0].length);
      app.actions.map.setPageTitleWithCount(numTotalResults);
    }

    // Only set markers if the map is visible.  The markers will get reset when the map is selected again
    const mapVisible = app.cursor.get(['layout', 'map']);
    if (mapVisible) {
      // Don't resize the map if we're in bounding box mode, or if it was requested
      let dontResize = app.leaflet.hasBoundingBox();
      if (!Map.canResetViewport() || (opts && opts.dontResetViewport)) {
        dontResize = true;
        Map.allowResetViewport(); // For next time
      }

      // Set the selected property
      // If the selected property is a tax property, don't set it as "selected" for the purposes of the map Markers
      let selectedProperty = res[1];
      if (selectedProperty && selectedProperty.isTaxProperty) {
        selectedProperty = null;
      }

      // Setup the map markers.
      opts.commit = true;
      opts.dontResize = dontResize;
      app.leaflet.setMarkers(
        res[0],
        selectedProperty,
        opts,
      );

      // Do map outlines (if applicable)
      Map.showMapOverlay();
    } else {
      // If the map isn't visible, just commit the data
      app.cursor.commit();
    }

    // Set street view button
    // if (typeof res[1] !== 'undefined' && res[1] !== null) {
    //  Map.getStreetViewAvailability(res[1])
    //    .then(function (isStreetViewAvailable) {
    //      Map.setStreetViewAvailability( isStreetViewAvailable );
    //    });
    // }
    Map.setStreetViewAvailability(true);

    app.leaflet.modifyIconsForTagging(); // Local Tags
  };

  Map.showMapOverlay = function () {
    // NPLAY-5240 Remove Policy Map Data

    /*
    const loc = app.actions.common.getLocationType();
    if (loc) {
      switch (loc.locType) {
        case 'S': // School
          app.api.schoolGeometryByNCESId(loc.locId, (schoolName, geometry) => {
            const pos = app.utils.validateLocationStr(Map.listingsCursor.get(['meta', 'locationStr']));

            app.leaflet.addSchoolMarker(
              {
                gsId: loc.locId,
                lat: pos.Lat,
                lon: pos.Lon,
                name: schoolName,
                geometry,
              },
            );

            if (geometry) {
              app.leaflet.showSchoolAttendanceBoundary(schoolName, geometry);
            }
          });
          break;

        case 'N': // Neighborhood
          var pos = app.utils.validateLocationStr(Map.listingsCursor.get(['meta', 'locationStr']));
          if (pos) {
            app.api.neighborhoodGeometryByGeo(pos.Lat, pos.Lon, (neighborhoodName, geometry) => {
              if (geometry) {
                app.leaflet.showNeighborhoodBoundary(neighborhoodName, geometry);
              } else {
                app.leaflet.clearNeighborhoodBoundary();
              }
            });
          }
          break;

        default:
          // app.leaflet.clearSchoolMarkersLayer();
          // app.leaflet.clearNeighborhoodBoundary();
          break;
      }
    }
    */
  };

  Map.onAlert = function (msg) {
    if (msg) {
      Map.cursor.set(['ref', 'alert'], msg);
    } else {
      Map.cursor.set(['ref', 'alert'], false);
    }

    app.cursor.commit();
  };

  Map.onNav = function (id, opts) {
    const locationStr = Map.listingsCursor.get(['meta', 'locationStr']);
    if (!locationStr) {
      return;
    }

    const mapRoute = '/search/map/'.concat(locationStr);

    if (!id || (Map.currentRoute && Map.currentRoute.params.id == id)) {
      if (opts && opts.dontResetViewport) {
        /// // Used for switching from grid to map view without resetting the zoom level
        // TODO: figure out a better way to route without resetting the viewport
        app.actions.common.goToRoute(mapRoute, opts);
      } else if (!opts || (opts && !opts.dontToggle)) {
        // Normal case (unsure of use for dontToggle...)
        app.actions.common.goToRoute(mapRoute);
      }
    } else {
      app.actions.common.goToRoute(mapRoute.concat('/', (id || '')));
    }
  };

  Map.setMobileCardDataWithId = function (id) {
    if (id) {
      const listings = Map.listingsCursor.get('data');
      let d = null;

      forEach(listings, (i) => {
        if (i.Id === id) {
          d = i;
          return false;
        }
      });

      Map.cursor.set('mobilePreviewData', d);
      app.cursor.commit();
    } else if (Map.cursor.get('mobilePreviewData') !== null) {
      Map.cursor.set('mobilePreviewData', null);
      app.cursor.commit();
    }
  };

  Map.setActiveMarker = function (id) {
    const markers = app.leaflet.listingsCursor.get('data');
    app.leaflet.setActiveMarker(
      markers,
      app.leaflet.getMarkerByID(markers, id),
    );
  };

  Map.updateZoomInRoute = function (zoom) {
    let newLocationStr;
    const pos = app.utils.validateLocationStr(Map.listingsCursor.get(['meta', 'locationStr']));

    if (pos) {
      newLocationStr = app.utils.toLocationStr(pos.Lat, pos.Lon, pos.radius, zoom);

      // Validate if radius is larger than allowed
      if (app.utils.validateLocationStr(newLocationStr)) {
        // Update the browser url
        const activeId = Map.listingsCursor.get(['activeId']);
        const nextRoute = '/search/map/'.concat(newLocationStr, (activeId ? `/${activeId}` : ''), window.location.search);
        app.actions.common.goToRoute(nextRoute, { skip: true });
        Map.listingsCursor.set(['meta', 'locationStr'], newLocationStr);
      }
    }
  };

  // Zoom display logic
  Map.showZoomMsg = function () {
    // Show zoom message only when
    //  1.  We cannot otherwise show listingsCursor, and...
    //  2.  We are not on the agentless listing page
    return !Map.canShowListings() && Map.currentRoute && !Map.currentRoute.url.match(/(\/listing)/);
  };

  // Zoom States
  Map.onZoomIn = function () {
    app.actions.analytics.sendEvent('map actions', 'zoom in');
    Map.listingsCursor.set(['meta', 'zoomMsg'], Map.showZoomMsg());
    Map.mapCursor.set(['ref', 'alert'], null);
    app.cursor.commit();
  };

  Map.onZoomOut = function () {
    app.actions.analytics.sendEvent('map actions', 'zoom out');
    Map.listingsCursor.set(['meta', 'zoomMsg'], Map.showZoomMsg());
    Map.mapCursor.set(['ref', 'alert'], null);
    app.cursor.commit();
  };

  Map.onCenterChange = function (Lat, Lon) {
    const pos = app.utils.validateLocationStr(
      Map.listingsCursor.get(['meta', 'locationStr']),
    );

    app.actions.common.saveLocationQuery('Current location based on map coordinates');

    app.actions.common.goToRoute('/search/map/'.concat(app.utils.toLocationStr(Lat, Lon, pos.radius, pos.zoom)), {
      // replace: true
    });
  };

  Map.onRadiusChange = function (radius, onDrag) {
    let newLocationStr;
    radius = Number(radius);

    const pos = app.utils.validateLocationStr(Map.listingsCursor.get(['meta', 'locationStr']));

    if (pos) {
      newLocationStr = app.utils.toLocationStr(pos.Lat, pos.Lon, radius, pos.zoom);

      // Validate if radius is larger than allowed
      if (app.utils.validateLocationStr(newLocationStr) === false) {
        Map.mapCursor.set(['ref', 'alert'],
          'The search radius you selected is too large.  Please narrow your search.');

        app.cursor.commit();
        return;
      }
    }

    if (onDrag) {
      // Update the map alert for better UX
      Map.mapCursor.set(['ref', 'alert'], `Your search radius is ${
        app.utils.convertToMiles(radius)} miles.`);

      app.cursor.commit();

      app.actions.analytics.sendEvent('map actions', 'move radius');
      return;
    }

    app.actions.analytics.sendEvent('map actions', 'change radius');
    if (pos.radius === radius) {
      // Radius didn't change (zooming outside of max radius)
      return null;
    }

    // Update the browser url
    const activeId = Map.listingsCursor.get(['activeId']);
    const nextRoute = '/search/map/'.concat(newLocationStr, (activeId ? `/${activeId}` : ''));

    app.actions.common.goToRoute(nextRoute, {
      // skip: true
    });
  };

  Map.updateStatusMessage = function (radius, numTotalResults, numFilteredResults) {
    // Make sure they're not null or undefined
    if (!numTotalResults) {
      numTotalResults = 0;
    }
    if (!numFilteredResults) {
      numFilteredResults = 0;
    }

    if (numFilteredResults > numTotalResults) {
      numTotalResults = numFilteredResults;
    }
    //  Otherwise, use the saved value from the last circle search
    let resultsStr = `${numTotalResults} results (showing ${numFilteredResults})`;

    if (radius && !app.utils.useMobileSite()) {
      resultsStr += ` within a ${app.utils.convertToMiles(radius)} mile radius`; // and a 24 min average commute';
    }

    // Set the page title to match
    Map.setPageTitleWithCount(numTotalResults);

    Map.mapCursor.set(['ref', 'alert'], resultsStr);

    app.leaflet.setSearchResults(numFilteredResults);
  };

  Map.getListing = function (id) {
    Map.cursor.set(['ref', 'spinner'], true);

    const p = new Promise(((resolve, reject) => {
      app.actions.panels.getListing(id, (res) => {
        Map.cursor.set(['ref', 'spinner'], false);

        if (res === 404) {
          Map.cursor.set(['ref', 'alert'], 'Listing was not found.');
          reject(null);
        } else if (res !== null) {
          Map.cursor.set(['data'], res);
          resolve(res);
        } else {
          Map.cursor.set(['ref', 'alert'], 'There was a problem connecting to the server');
          reject(null);
        }
      }, true);
    }));

    return p;
  };

  Map.getStreetViewAvailability = function (/* listingData */) {
    const p = new Promise(((resolve) => {
      // new google.maps.StreetViewService()
      //  .getPanoramaByLocation(
      //  new google.maps.LatLng(listingData.Location.Lat, listingData.Location.Lon),
      //  50, // Check if StreetView Image is available within 50 Meters
      //  function (data, status) {
      //    if (status == google.maps.StreetViewStatus.OK) {
      //      resolve(true)
      //    } else {
      //      console.log("Street View Not Available")
      //      resolve(false)
      //    }
      //  });
      resolve(true);
    }));

    return p;
  };

  Map.setStreetViewAvailability = function (available) {
    Map.cursor.set(['streetViewAvailable'], available);
    app.cursor.commit();
  };

  Map.setMapLayout = function (route, canUseListingsData, opts) {
    const pos = app.utils.validateLocationStr(route.params.location);

    // Render the layout first
    //  A state with a "selected" property (route.params.id is set), requires a 3-column layout (2f-4) instead of a 2-column layout (2f-3)
    app.utils.updateCursor({
      cursor: Map.mapCursor.select('ref'),
      defaults: app.cursorDefaults.panels.map.ref,
      finalState: {
        className: (route.params.id ? 'layout--2f-4' : 'layout--2f-3'),
        header: true,
        searchBar: true,
      },
    });

    let dontReset = false;
    if (!Map.canResetViewport() || (opts && opts.dontResetViewport)) {
      dontReset = true;
    }

    // Remove Pins not in circle only on desktop
    if (!app.leaflet.useMobileSite() && pos) {
      app.leaflet.clearUnneededMarkers(pos);
    }

    // Case #1:
    // A. Fresh listings data
    // B. or Came from grid view
    // == Render all markers again & circle
    // == Reset everything
    if (canUseListingsData === false || route.lastUrl.match(/\/search\/grid\//g)) {
      // Setup the map
      if (dontReset) {
        app.leaflet.clearMarkersLayer();
      } else {
        /*   NOT SURE IF THIS IS NECESSARY AFTER A MERGE CONFLICT
        app.leaflet.clearAllLayers(true);
        app.leaflet.setMap(pos);
        app.leaflet.setCircle(pos);
      }
    }

    if (Map.zoomToLevel) {
      app.leaflet.setZoom(Map.zoomToLevel);
      Map.zoomToLevel = null;
    } else if (app.leaflet.useMobileSite()) {
      app.leaflet.resizeMap()
    }
  */
        app.leaflet.clearAllLayers({ skipMarkerLayer: true });

        if (Map.zoomToLevel) {
          pos.zoom = Map.zoomToLevel;
        }

        // Fix NPLAY-3758 where sections of the map is white due to container resizing
        setTimeout(() => {
          app.leaflet.setCircle(pos, { maxRadius: 16093 });
          if (Map.canPanToRoute()) {
            app.leaflet.setMap(pos);
          }
        }, 50);

        Map.zoomToLevel = null;
      }
    }
  };

  Map.getTinyListings = function (bounds, cb) {
    const locationStr = Map.listingsCursor.get(['meta', 'locationStr']);
    const pos = app.utils.validateLocationStr(locationStr);

    if (!pos) {
      return cb([]);
    }

    let ct = 1000; // Assume all listings
    if (app.utils.isMobile()) {
      if (app.leaflet.opts.numTinyListingsMobile) {
        ct = app.leaflet.opts.numTinyListingsMobile;
      }
    } else if (app.leaflet.opts.numTinyListingsDesktop) {
      ct = app.leaflet.opts.numTinyListingsDesktop;
    }

    pos.bounds = bounds;
    pos.ct = ct;

    const opts = app.actions.menu.getSearchSelectionsOpts();

    for (const attrname in pos) {
      opts[attrname] = pos[attrname];
    }

    app.api.tinyListings(opts, (res) => cb(typeof res === 'object' ? res : []));
  };

  Map.moveMapToCurrentLocationIfNeeded = function (latLng) {
    if (!window.location.href.match(/\/map/)) {
      return;
    }

    const mapLocationString = Map.listingsCursor.get(['meta', 'locationStr']);
    const pos = app.utils.validateLocationStr(mapLocationString);

    if (!pos) {
      return;
    }

    const mapPoint = new google.maps.LatLng(pos.Lat, pos.Lon);
    const currentLoc = new google.maps.LatLng(latLng[0], latLng[1]);
    const distance = google.maps.geometry.spherical.computeDistanceBetween(mapPoint, currentLoc);

    if (distance > 500) {
      const newLocationStrArr = mapLocationString.split(',');
      newLocationStrArr[0] = latLng[0];
      newLocationStrArr[1] = latLng[1];
      const newLocationStr = newLocationStrArr.join(',');
      app.router.go(Map.currentRoute.url.replace(mapLocationString, newLocationStr), { replace: true });
    }
  };

  Map.legend = document.createElement('div');
  Map.legend.id = 'pin-legend-container';
  Map.legend.innerHTML = '<h4>What different pins mean</h4>'
  + '<p class="pin-legend-tagline">Homes with pins are the most relevant to your search. Zoom in or click small pins to see more</p>'
  + '<table>'
  + '<tr><td>'
  + '<div class="leaflet-marker-icon map-icon num-3 general" style="position: relative; top: auto; left: auto; text-align: center;"><div class="pin"></div><div class="dot"></div></div>'
  + '</td><td><p>Homes</p><p class="desc">Number inside the pin is the number of bedrooms</p></td></tr>'
  + '<tr><td>'
  + '<div class="leaflet-marker-icon map-icon num-3 general active" style="position: relative; top: auto; left: auto; text-align: center;"><div class="pin"></div><div class="dot"></div></div>'
  + '</td><td><p>Active Home</p><p class="desc">Currently selected home</p></td></tr>'
  + '<tr><td>'
  + '<div class="leaflet-marker-icon map-icon num-3 general viewed" style="position: relative; top: auto; left: auto; text-align: center;"><div class="pin"></div><div class="dot"></div></div>'
  + '</td><td><p>Viewed Homes</p><p class="desc">These are homes you have already viewed in this browser</p></td></tr>'
  + '<tr><td>'
  + '<div class="leaflet-marker-icon map-icon general favorite" style="position: absolute; top: auto; left: auto; margin-top: -12px; margin-left: -6px;"><div class="pin"></div><div class="dot"></div></div>'
  + '<div class="leaflet-marker-icon map-icon general tagged" style="position: absolute; top: auto; left: auto; margin-top: -12px; margin-left: 6px;"><div class="pin"></div><div class="dot"></div></div>'
  + '</td><td><p>Liked Homes</p><p class="desc">When you like a property it is also saved for you</p></td></tr>'
  + '<tr><td>'
  + '<div class="leaflet-marker-icon map-icon small" style="position: relative; top: auto; left: auto; text-align: center;"><div class="pin"></div><div class="dot"></div></div>'
  + '</td><td><p>Small Pin</p><p class="desc">Additional homes, zoom in or click</p></td></tr>'
  + '</table>';
  Map.legend.style.display = 'none';
  document.body.appendChild(Map.legend);

  Map.showLegend = function () {
    const contextMenu = (window.document.getElementsByClassName('leaflet-contextmenu') || [])[0];
    if (contextMenu) {
      const contextMenuRect = contextMenu.getBoundingClientRect();
      if (contextMenuRect) {
        Map.legend.style.top = `${contextMenuRect.top}px`;
        Map.legend.style.left = `${contextMenuRect.right}px`;
        Map.legend.style.display = 'block';
        const mapLegendRect = Map.legend.getBoundingClientRect();
        if (mapLegendRect.bottom > window.innerHeight) {
          Map.legend.style.top = `${contextMenuRect.top - (mapLegendRect.bottom - window.innerHeight) - 10}px`;
        }
      }
    }
  };

  Map.hideLegend = function () {
    Map.legend.style.display = 'none';
  };

  return Map;
};
