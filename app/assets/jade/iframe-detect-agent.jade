doctype
html
  head
    meta(charset='utf-8')
    meta(http-equiv='X-UA-Compatible', content='IE=edge')
    meta(name='description', content='')
    meta(name='viewport', content='width=device-width, initial-scale=1')
    script(src="https://cdnjs.cloudflare.com/ajax/libs/promiz/1.0.6/promiz.min.js")
  body
    - var facebookAppId = __Config.NAME === 'production' ? '253956901293839' : '895759733780216'

    script.
      var apiRoot = '#{__Config.API_URL}'
      var origin = getUrlParameter('origin') || window.location.origin;

      function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
      };

      function readLogin(accessToken, isGuid, callback, errorCallback) {
        // Logged into your app and Facebook.
        var request = new XMLHttpRequest();
        request.open('POST', apiRoot + "agentauth/", true);
        request.withCredentials = true;
        request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
        request.onreadystatechange = function () {
          if(request.readyState === XMLHttpRequest.DONE) {
            if (request.status === 200) {
              callback(request.responseText)
            } else {
              errorCallback()
            }
          }
        };
        request.send(JSON.stringify((isGuid ? 'ha-aa_' : '') + accessToken));
      }

      function getAgent(callback, errorCallback) {
        var request2 = new XMLHttpRequest();
        request2.open('GET', apiRoot + "agentautoauth", true);
        request2.withCredentials = true;
        request2.onreadystatechange = function () {
          if(request2.readyState === XMLHttpRequest.DONE) {
            if (request2.status === 200) {
              callback(request2.responseText)
            } else {
              errorCallback()
            }
          }
        };
        request2.send();
      }

      var directGetPromise = function() {
        return new Promise(function (resolve, reject) {
          getAgent(function (response) {
            if (response) {
              resolve(response)
            } else {
              reject(new Error("directGetPromise failed"))
            }
          }, function () {
            reject(new Error("directGetPromise failed"))
          })
        });
      }

      var getGuidPromise = function() {
        return new Promise(function (resolve, reject) {
          var guid = getUrlParameter('guid');
          if (guid) {
            readLogin(guid, true, function (response) {
              if (response) {
                getAgent(function (response) {
                  if (response) {
                    resolve(response)
                  } else {
                    reject(new Error("getGuidPromise failed"))
                  }
                }, function () {
                  reject(new Error("getGuidPromise failed"))
                })
              } else {
                reject(new Error("getGuidPromise failed"))
              }
            }, function () {
              reject(new Error("getGuidPromise failed"))
            })
          } else {
            reject(new Error("getGuidPromise failed"))
          }
        });
      }

      var facebookDetectionPromise = function() {
        return new Promise(function (resolve, reject) {
          var statusChangeCallback = function (response) {
            // The response object is returned with a status field that lets the
            // app know the current login status of the person.
            // Full docs on the response object can be found in the documentation
            // for FB.getLoginStatus().
            if (response.status === 'connected') {
              readLogin(response.authResponse.accessToken, false, function (response) {
                if (response) {
                  getAgent(function (response) {
                    if (response) {
                      resolve(response)
                    } else {
                      reject(new Error("facebookDetectionPromise failed"))
                    }
                  }, function () {
                    reject(new Error("facebookDetectionPromise failed"))
                  })
                } else {
                  reject(new Error("facebookDetectionPromise failed"))
                }
              }, function () {
                reject(new Error("facebookDetectionPromise failed"))
              })
            } else if (response.status === 'not_authorized') {
              reject(new Error("facebookDetectionPromise failed"))
            } else {
              reject(new Error("facebookDetectionPromise failed"))
            }
          }

          var checkLoginState = function () {
            FB.getLoginStatus(function (response) {
              statusChangeCallback(response);
            });
          }

          window.fbAsyncInit = function () {
            FB.init({
              appId: '#{facebookAppId}',
              cookie: true,
              xfbml: true,
              version: 'v2.10'
            });
            checkLoginState()
          };
          (function (d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {
              return;
            }
            js = d.createElement(s);
            js.id = id;
            js.src = "//connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'facebook-jssdk'));
        });
      }

      function homeASAPDetectionPromise() {
        var successPromise = function () {
          return successRace([directGetPromise(), getGuidPromise(), facebookDetectionPromise()])
            .then(
            function (token) {
              return Promise.resolve(token);
            },
            function () {
              return Promise.resolve("failed");
            }
          )
        }

        var timeoutPromise = function () {
          return new Promise(function(resolve, reject) {
            setTimeout(function() {
              resolve("timeout");
            }, 5000);
          })
        }

        successRace([successPromise(), timeoutPromise()])
        .then(
          function (token) {
            if (["failed", "timeout"].indexOf(token) > -1) {
              console.log("HomeASAP Agent Detection Failed");
              if (window.parent) {
                window.parent.postMessage("HOMEASAP_DETECT_AGENT:FAILED::", origin)
              }
              return
            }
            console.log("HomeASAP Agent Detection Success:" + token);
            if (window.parent) {
              window.parent.postMessage("HOMEASAP_DETECT_AGENT:SUCCESS::" + token, origin)
            }
          },
          function () {
            console.log("HomeASAP Agent Detection Failed");
            if (window.parent) {
              window.parent.postMessage("HOMEASAP_DETECT_AGENT:FAILED::", origin)
            }
          }
        )
      }

      homeASAPDetectionPromise();

      function successRace(promises) {
        return Promise.all(promises.map(function (p) {
            return p.then(function (val) {
              return Promise.reject(val);
            }, function (err) {
              return Promise.resolve(err);
            });
          }
        )).then(
          function (errors) {
            return Promise.reject(errors);
          },
          function (val) {
            return Promise.resolve(val);
          }
        );
      }
//
  === Build Version ===
  Hash: #{__Config.GIT_HASH}
  BuildDate: #{__Config.BUILD_DATE.toUTCString()}