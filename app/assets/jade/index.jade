extends layout

block css

  - var styleCssPath = "css/index.css"
  - var styleCss = __Config.SITE_ASSETS_CDN_URL + "/" + (__rev[styleCssPath] || styleCssPath)

  link(href='//fonts.googleapis.com/css?family=Open+Sans:400,300,600,700,800', rel='stylesheet')
  link(href='//fonts.googleapis.com/css?family=Poppins:400,300,600,700,800', rel='stylesheet')
  link(href='//fonts.googleapis.com/css?family=Montserrat:400,300,600,700,800', rel='stylesheet')
  link(href='//fonts.googleapis.com/css?family=Roboto+Serif:400,300,600,700,800', rel='stylesheet')
  link(href='//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css', rel='stylesheet')
  link(href='//api.tiles.mapbox.com/mapbox.js/v2.2.1/mapbox.css', rel='stylesheet')
  link(href='https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.1/mapbox-gl.min.css', rel='stylesheet')
  script(src='https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.1/mapbox-gl.min.js')
  script(src='https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.0/clipboard.min.js')
  link(href='#{styleCss}', rel='stylesheet', id='ha-css')
  style
    include ../../thirdparty/react-slick/slick.css

block favicons

  - var faviconPath = __Config.SITE_ASSETS_CDN_URL + "/" + "images/favicons"

  meta(name='theme-color', content='#ffffff')
  meta(name='msapplication-TileColor', content='#ffffff')
  meta(name='msapplication-TileImage', content='#{faviconPath}/ms-icon-144x144.png')
  meta(name='msapplication-square70x70logo', content='#{faviconPath}/mstile-70x70.png')
  meta(name='msapplication-square150x150logo', content='#{faviconPath}/mstile-150x150.png')
  meta(name='msapplication-wide310x150logo', content='#{faviconPath}/mstile-310x150.png')

  <!--[if IE]>
  link(rel='shortcut icon', href='/favicon.ico')
  <![endif]-->
  link(rel='apple-touch-icon', sizes='57x57', href='#{faviconPath}/apple-icon-57x57.png')
  link(rel='apple-touch-icon', sizes='60x60', href='#{faviconPath}/apple-icon-60x60.png')
  link(rel='apple-touch-icon', sizes='72x72', href='#{faviconPath}/apple-icon-72x72.png')
  link(rel='apple-touch-icon', sizes='76x76', href='#{faviconPath}/apple-icon-76x76.png')
  link(rel='apple-touch-icon', sizes='114x114', href='#{faviconPath}/apple-icon-114x114.png')
  link(rel='apple-touch-icon', sizes='120x120', href='#{faviconPath}/apple-icon-120x120.png')
  link(rel='apple-touch-icon', sizes='144x144', href='#{faviconPath}/apple-icon-144x144.png')
  link(rel='apple-touch-icon', sizes='152x152', href='#{faviconPath}/apple-icon-152x152.png')
  link(rel='apple-touch-icon', sizes='180x180', href='#{faviconPath}/apple-icon-180x180.png')
  link(rel='apple-touch-icon-precomposed', sizes='180x180', href='#{faviconPath}/apple-icon-precomposed.png')
  link(rel='icon', type='image/png', sizes='16x16', href='#{faviconPath}/favicon-16x16.png')
  link(rel='icon', type='image/png', sizes='32x32', href='#{faviconPath}/favicon-32x32.png')
  link(rel='icon', type='image/png', sizes='96x96', href='#{faviconPath}/favicon-96x96.png')

block scripts

  - var appScriptPath = "js/app.js"
  - var appScript = __Config.SITE_ASSETS_CDN_URL + "/" + (__rev[appScriptPath] || appScriptPath)
  - var Config = __Config

  if (__LIVE)
    //- Polling for CSS changes in LIVE mode
    script.
      !function(){var e={Etag:1,"Last-Modified":1,"Content-Length":1,"Content-Type":1},t={},o={},a={},n={},s=100,r=!1,c={html:0,css:1,js:0},i={heartbeat:function(){document.body&&(r||i.loadresources(),i.checkForChanges()),setTimeout(i.heartbeat,s)},loadresources:function(){function e(e){var t=document.location,o=new RegExp("^\\.|^/(?!/)|^[\\w]((?!://).)*$|"+t.protocol+"//"+t.host);return e.match(o)}for(var o=document.getElementsByTagName("script"),n=document.getElementsByTagName("link"),s=[],l=0;l<o.length;l++){var d=o[l],u=d.getAttribute("src");if(u&&e(u)&&s.push(u),u&&u.match(/\blive.js#/)){for(var h in c)c[h]=null!=u.match("[#,|]"+h);u.match("notify")&&alert("Live.js is loaded.")}}c.js||(s=[]),c.html&&s.push(document.location.href);for(var l=0;l<n.length&&c.css;l++){var m=n[l],p=m.getAttribute("rel"),v=m.getAttribute("href",2);v&&p&&p.match(new RegExp("stylesheet","i"))&&e(v)&&(s.push(v),a[v]=m)}for(var l=0;l<s.length;l++){var f=s[l];i.getHead(f,function(e,o){t[e]=o})}var g=document.getElementsByTagName("head")[0],w=document.createElement("style"),y="transition: all .3s ease-out;";css=[".livejs-loading * { ",y," -webkit-",y,"-moz-",y,"-o-",y,"}"].join(""),w.setAttribute("type","text/css"),g.appendChild(w),w.styleSheet?w.styleSheet.cssText=css:w.appendChild(document.createTextNode(css)),r=!0},checkForChanges:function(){for(var e in t)o[e]||i.getHead(e,function(e,o){var a=t[e],n=!1;t[e]=o;for(var s in a){var r=a[s],c=o[s],l=o["Content-Type"];switch(s.toLowerCase()){case"etag":if(!c)break;default:n=r!=c}if(n){i.refreshResource(e,l);break}}})},refreshResource:function(e,t){switch(t.toLowerCase()){case"text/css":var o=a[e],s=document.body.parentNode,r=o.parentNode,c=o.nextSibling,l=document.createElement("link");s.className=s.className.replace(/\s*livejs\-loading/gi,"")+" livejs-loading",l.setAttribute("type","text/css"),l.setAttribute("rel","stylesheet"),l.setAttribute("href",e+"?now="+1*new Date),c?r.insertBefore(l,c):r.appendChild(l),a[e]=l,n[e]=o,i.removeoldLinkElements();break;case"text/html":if(e!=document.location.href)return;case"text/javascript":case"application/javascript":case"application/x-javascript":document.location.reload()}},removeoldLinkElements:function(){var e=0;for(var t in n){try{var o=a[t],s=n[t],r=document.body.parentNode,c=o.sheet||o.styleSheet,l=c.rules||c.cssRules;l.length>=0&&(s.parentNode.removeChild(s),delete n[t],setTimeout(function(){r.className=r.className.replace(/\s*livejs\-loading/gi,"")},100))}catch(d){e++}e&&setTimeout(i.removeoldLinkElements,50)}},getHead:function(t,a){o[t]=!0;var n=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XmlHttp");n.open("HEAD",t,!0),n.onreadystatechange=function(){if(delete o[t],4==n.readyState&&304!=n.status){n.getAllResponseHeaders();var s={};for(var r in e){var c=n.getResponseHeader(r);"etag"==r.toLowerCase()&&c&&(c=c.replace(/^W\//,"")),"content-type"==r.toLowerCase()&&c&&(c=c.replace(/^(.*?);.*?$/i,"$1")),s[r]=c}a(t,s)}},n.send()}};"file:"!=document.location.protocol?(window.liveJsLoaded||i.heartbeat(),window.liveJsLoaded=!0):window.console&&console.log("Live.js doesn't support the file protocol. It needs http.")}();

  if (__PROD)
    script.
      if (window.location.protocol != "https:" || window.location.href.match(/\/\/www\./))
        window.location.href = "https:" + window.location.href.substring(window.location.protocol.length).replace(/^\/\/www\./, '//');

  script.
    window.START_TIME = Date.now();
    window.BASE_URL = '#{__ROOT || "/"}';
    window.CONFIG = {
      IN_PROD: #{Config.NAME === 'production'},
      GA_KEY: "#{Config.GA_KEY}",
      GOOGLE_KEY: "#{Config.GOOGLE_KEY}",
      GEOCODIO_KEY: "#{Config.GEOCODIO_KEY}",
      MAPBOX_KEY: "#{Config.MAPBOX_KEY}",
      FACEBOOK_TAB_APPID: "#{Config.FACEBOOK_TAB_APPID}",
      GATEWAY_URL: "#{Config.GATEWAY_URL}",
      CDN_URL: "#{Config.CDN_URL}",
      HA_ANALYTICS_APP: "#{Config.HA_ANALYTICS_APP}",
      SITE_ASSETS_CDN_URL: "#{Config.SITE_ASSETS_CDN_URL}",
      READ_URL: "#{Config.READ_URL}",
      AGENT_SEARCH_URL: "#{Config.AGENT_SEARCH_URL}",
    }
    window.CURRENT_YEAR = (new Date()).getFullYear();

    var BASE_API_URL = '#{Config.API_URL}';
    var SESSION_URL = 'map/initsession/';

    //- window.gaQ = []
    window.sendPageView = function(url, title) {
      //- window.gaQ.push({
      //-   url: url || (window.location.pathname + window.location.search),
      //-   title: title
      //- })
      // GA4 should be configured to track page views on history change automatically
      //- if (window.ga) {
      //-   window.gaQ.forEach(function(item, idx) {

      //-   })
      //-   window.gaQ = []
      //- }
      window.fbq('track', "PageView");
    }
    window.gaEventQ = []
    window.sendEvent = function (category, action, label, value) {
      //- console.log({category: category, action: action, label: label, value: value})
      if (category && action) {
        window.gaEventQ.push({category: category, action: action, label: label, value: value})
      }
      if (window.gtag) {
        window.gaEventQ.forEach(function (item, idx) {
          gtag('event', item.category, {
            event_category: item.category,
            event_action: item.action,
            event_label: item.label,
            event_value: item.value,
          });
        })
        window.gaEventQ = []
      }
    }
    window.fbAsyncFuncs = []
    window.fbAsyncInit = function () {
      FB.init({
        appId: window.CONFIG.FACEBOOK_TAB_APPID,     // DEBUG: '480034382043336', TEST: '854417991251776'
        cookie: true,  // enable cookies to allow the server to access the session
        xfbml: true,  // parse social plugins on this page
        version: 'v2.10'
      });
      window.fbAsyncFuncs.forEach(function (func, idx) {
        if (typeof func === 'function') {
          func()
        }
      });
      window.fbAsyncFuncs = []
    }
    window.registerFbAsyncFunc = function (func) {
      if (window.FB) {
        func()
      } else {
        window.fbAsyncFuncs.push(func)
      }
    }

  //- Facebook Pixel Code
  script.
    //- <!-- Meta Pixel Code -->
    //- !function(f,b,e,v,n,t,s)
    //- {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    //- n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    //- if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    //- n.queue=[];t=b.createElement(e);t.async=!0;
    //- t.src=v;s=b.getElementsByTagName(e)[0];
    //- s.parentNode.insertBefore(t,s)}(window, document,'script',
    //- 'https://connect.facebook.net/en_US/fbevents.js');
    //- <!-- End Meta Pixel Code -->
    window.fbq = function(){};  // NPLAY-7301 Disable Facebook Pixel
    fbq('init', '#{Config.FACEBOOK_PIXEL_CODE}');
  //- End Facebook Pixel Code

  if (Config.NAME === 'test')
    //- Convert Experiences code
    script(type='text/javascript', src='//cdn-3.convertexperiments.com/js/10004227-10005232.js')

  link(rel='manifest', href='/manifest.json')
  script(src='https://cdn.onesignal.com/sdks/OneSignalSDK.js', async)
  script(src='https://custom-scripts.homeasapcontent.com/global-scripts.js', defer)

block bodyScripts

  script.
    var guidMatches = window.location.search.match(/guid=(?:ha-aa_)?([^&]+)(&|$)/i);
    if (guidMatches && guidMatches[1]) {
      window.GUID = 'ha-aa_' + guidMatches[1];
      window.history.replaceState({}, document.title, window.location.href.replace(guidMatches[0], ''))
    }

    if (window.location.search.match('fbp=1')) {
      window.history.replaceState({}, document.title, window.location.href.replace('&fbp=1', '').replace('fbp=1&', '').replace('fbp=1', ''))

      var facebookInAppBrowser = function () {
        var ua = navigator.userAgent || navigator.vendor || window.opera;
        return (ua.indexOf("FBAN") > -1) || (ua.indexOf("FBAV") > -1);
      }
      if (facebookInAppBrowser()) {
        window.document.body.style.visibility = "hidden"
        window.document.body.style.background = "#fff"

        var showBody = function () {
          window.document.body.style.visibility = "visible"
          window.document.body.style.background = "#223949"
        }

        setTimeout(function () {
          // Timeout Fallback
          window.document.body.style.visibility = "visible"
          window.document.body.style.background = "#223949"
        }, 5000)

        window.sendEvent('login', 'fbp detected')

        window.registerFbAsyncFunc(
          function () {
            var isNewUser = false
            var notAuthorizedCount = 0
            var statusChangeCallback = function (response) {
              // console.log('statusChangeCallback');
              // console.log(response);
              if (response.status === 'connected') {
                window.sendEvent('login', 'fb success')
                if (isNewUser) {
                  isNewUser = false
                  // window.sendEvent('login', 'fb success')  // Will be fired after refresh
                }
                // Refresh Page let regular login process kick in
                window.location.reload()
                // showBody()
              } else if (response.status === 'not_authorized') {
                isNewUser = true
                notAuthorizedCount++
                if (notAuthorizedCount > 1) {
                  // User hit cancel, do not ask them to login again
                  window.sendEvent('login', 'failed')
                  showBody()
                  return
                }
                window.sendEvent('login', 'fbp show')
                FB.login(statusChangeCallback, {
                  scope: 'email', auth_type: 'rerequest',
                  return_scopes: true
                });
              } else {
                showBody()
              }
            }
            FB.getLoginStatus(function (response) {
              statusChangeCallback(response);
            }.bind(this));
          }.bind(this)
        )
      }
    }

    let qs = new URLSearchParams(document.location.search);
    let theme = qs.get("theme");

    if (window.location.pathname === '/more') {
      theme = 'more'
    }

    if (theme) {
      qs.delete("theme");
      try {
        window.sessionStorage.setItem('HA_THEME', theme);
      } catch (ex) { }
    } else {
      try {
        theme = window.sessionStorage.getItem('HA_THEME');
      } catch (ex) { }
    }

    if (theme) {
      document.body.dataset.theme = theme;
    }

    // Hard code a rate for IDX Plus for now
    window.idx_rate = 7.61;

    // Rateplug qs extraction
    window.rateplug = {}
    for (const q of ['rp_rate', 'rp_apr', 'rp_program', 'rp_payment', 'rp_downpayment', 'rp_buyer', 'rp_lo', 'rp_onboarding', 'rp_veteran', 'rp_va_loan_limit', 'rp_welcome']) {
      if (qs.get(q)) {
        window.rateplug[q] = qs.get(q);
        qs.delete(q);
      }
    }

    if (window.rateplug.rp_buyer /* Config.NAME === 'production' */) {
      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:3476265,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    }

    if (qs.get('home_pin')) {
      const [lat, lng] = qs.get('home_pin').split(',');
      if (!isNaN(lat) && !isNaN(lng)) {
        window.home_pin = {lat: Number(lat), lng: Number(lng)};
      }
    }

    //- window.history.replaceState({}, document.title, `${location.protocol}//${location.host}${location.pathname}?${qs.toString()}`);

    $LAB.setGlobalDefaults({AllowDuplicates: false})
      .script('#{appScript}')
      .wait()
      .script('https://maps.googleapis.com/maps/api/js?libraries=geometry,places&callback=__noop&key=' + window.CONFIG.GOOGLE_KEY)
      // NPLAY-5240 Remove Policy Map Data
      //- .script('https://parcelstream.com/api/DmpApi.aspx?map=Google&services=Utility&host=parcelstream.com/&v=3')
      //- .wait(function () {
      //-   var xhr = new XMLHttpRequest()
      //-   xhr.open("GET", BASE_API_URL.concat(SESSION_URL), true);
      //-   xhr.setRequestHeader("Content-Type", "application/json");
      //-   xhr.setRequestHeader("Accept", "application/json");
      //-   xhr.onreadystatechange = function () {
      //-     if (xhr.readyState == 4 && xhr.status == 200) {
      //-       try {
      //-         res = JSON.parse(xhr.responseText)
      //-         Dmp.HostName = 'https://parcelstream.com/'
      //-         Dmp.Env.Connections["SS"] = new Dmp.Conn.Connection("https://parcelstream.com/InitSession.aspx");
      //-         Dmp.Env.Connections["SS"].init(res.SIK);
      //-       } catch (e) {
      //-         console.log('Error initializing parcelStream:', e);
      //-       }
      //-     }
      //-   }
      //-   xhr.send();
      //- })
      .script('https://www.googletagmanager.com/gtag/js?id=' + window.CONFIG.GA_KEY)

    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', window.CONFIG.GA_KEY);

  - var assetsSvgPath = "svg/assets.svg"
  - var assetsSvg = __Config.SITE_ASSETS_CDN_URL + "/" + (__rev[assetsSvgPath] || assetsSvgPath)
  //- Load raw SVG into dom via XHR which can be cached
  script.
    (function (doc) {
      var scripts = doc.getElementsByTagName('script')
      var script = scripts[scripts.length - 1]
      var xhr = new XMLHttpRequest()
      xhr.onload = function () {
        var div = doc.createElement('div')
        div.innerHTML = this.responseText
        div.style.display = 'none'
        script.parentNode.insertBefore(div, script)
      }
      xhr.open('get', '#{assetsSvg}', true)
      xhr.send()
    })(document)

block content
  #app-container
    center.mt30: .spinner-round-component.lg

block footerScripts

  - var Config = __Config.ACCOUNTS_URL
  script.
    (function(w, d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      w.HomeASAPAccountsOrigin = w.location.origin;
      w.HomeASAPAccountsInitCallbacks = w.HomeASAPAccountsInitCallbacks || [];
      w.HomeASAPAccountsAddInitCallback = function(func) {
        if (w.HomeASAPAccounts) { func() } else { w.HomeASAPAccountsInitCallbacks.push(func); }
      }
      if (d.getElementById(id)) { return; }
    js = d.createElement(s);
      js.id = id;
      js.src = "#{Config}sdk/index.js";
      fjs.parentNode.insertBefore(js, fjs);
    }(window, document, 'script', 'homeasap-accounts-js'));

    (function (d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = "//connect.facebook.net/en_US/sdk.js";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

  script(type = "text/javascript", async, src = "//platform.twitter.com/widgets.js")

block version
  //
    === Build Version ===
    Hash: #{__Config.GIT_HASH}
    BuildDate: #{__Config.BUILD_DATE.toUTCString()}
