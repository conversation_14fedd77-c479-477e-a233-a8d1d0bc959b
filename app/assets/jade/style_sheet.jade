extends layout

block css
  link(href='css/index.css?v=#{__appVersion}', rel='stylesheet')

block content

  nav.nav-scrollable
    a.brand(href='/')
      img(src='data:image/png;base64,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')

    a.item.active(href='/buttons') Orders
    a.item(href='/tables') Customers
    a.item(href='/tables') Customers
    a.item(href='/tables') Customers
    a.item(href='/forms') Meals Settings



  .alert-container
    span.close ×
    p Some error Some errorSome error


  h1 H1 text
    small Hello
  h2 H2 text
    small Hello

  h3 H3 text
    small Hello

  h4 H4 text
    small Hello

  h5 H5 text
    small Hello

  h6 H6 text
    small Hello

  p Paragraph Paragraph Paragraph Paragraph Paragraph Paragraph

  a(href='#') Link Link

  br

  button.btn.btn-outline(type='button') Default
  button.btn.btn-primary(type='button') Primary
  button.btn.btn-success(type='button') Success
  button.btn.btn-info(type='button') Info
  button.btn.btn-danger(type='button') Danger
  button.btn.btn-secondary(type='button') Secondary

  .btn-group(role='group', aria-label='...')
    button.btn.btn-primary(type='button') Left
    button.btn.btn-primary(type='button') Middle
    button.btn.btn-primary(type='button') Right

  br
  hr
  br
  form
    textarea.form-control(rows='3')
    br

    select.form-control
      option 1
      option 2
      option 3
      option 4
      option 5

    br

    .has-success
      label(for='exampleInputEmail1') Email address
      input#exampleInputEmail1.form-control(type='email', placeholder='Enter email')
      p.feedback-text Hello World

    .has-error.has-feedback
      label(for='exampleInputEmail1') Email address
      input.form-control(type='email', placeholder='Enter email')

    div
      label(for='exampleInputPassword1') Password
      input#exampleInputPassword1.form-control(type='password', placeholder='Password')


    div.has-feedback.has-error
      label(for='exampleInputFile') File input
      input#exampleInputFile(type='file')
      p.help-block Example block-level help text here.

    .checkbox
      label
        input(type='checkbox')
        | Check me out

  .has-feedback.has-error
    .input-group
      span.input-group-addon @
      input.form-control(type='text', placeholder='Username', aria-describedby='basic-addon1')

  br

  .input-group
    input.form-control(type='text', placeholder="Recipient's username", aria-describedby='basic-addon2')
    span#basic-addon2.input-group-addon @example.com

  br
  .input-group
    span.input-group-addon $
    input.form-control(type='text', aria-label='Amount (to the nearest dollar)')
    span.input-group-addon .00

  br
  .input-group
    span.input-group-addon
      input(type='checkbox', aria-label='...')
    input.form-control(type='text', aria-label='...')

  br
  .input-group
    input.form-control(type='text', placeholder='Search for...')
    span.input-group-btn
      button.btn.btn-primary(type='button') Go!

  br
  br
