<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 19.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 91.2 74.8" enable-background="new 0 0 91.2 74.8" xml:space="preserve">
<g>
	<path fill="none" d="M45.5,63.9c1.7,0,3.1-1.4,3.2-3.1c0-1.7-1.4-3.1-3.1-3.1c-0.4,0-0.7,0.1-1,0.2c-0.9,0.2-1.5,0.8-1.9,1.6
		c-0.2,0.3-0.3,0.6-0.3,0.9c0,0.1,0,0.3,0,0.4c0,0.7,0.2,1.3,0.6,1.8C43.6,63.4,44.5,63.9,45.5,63.9z"/>
	<path fill="none" d="M37.7,43.7c0.8,0,1.5-0.7,1.6-1.6c0-0.9-0.7-1.6-1.6-1.6c-0.9,0-1.6,0.7-1.6,1.6C36.1,43,36.8,43.7,37.7,43.7z
		"/>
	<path fill="none" d="M53.3,43.7C53.3,43.7,53.3,43.7,53.3,43.7C53.3,43.7,53.3,43.7,53.3,43.7c-0.9,0-1.6-0.7-1.6-1.6
		C51.7,43,52.4,43.7,53.3,43.7z"/>
	<path fill="none" d="M36.5,32.8c-0.4,0.2-0.8,0.4-1.2,0.5c0,0,0.1,0.1,0.1,0.1c-4.2,2-7,5.3-7,9.5c0,0.4,0,0.7,0.1,1.1l-0.2,0.2
		c0.1,0.6,0.3,1,0.6,1.5l-1.1,1.1L27.6,47l3,3c0.1,0.2,0.2,0.4,0.2,0.6c0,0.5-0.4,0.8-0.8,0.8c-0.3,0-0.5-0.1-0.6-0.2l-3-3L23.6,51
		c-0.3,0.3-0.8,0.3-1.1,0c-0.3-0.3-0.3-0.8,0-1.1l2.8-2.8l-2.8-2.8l-2.8,2.8c-0.3,0.3-0.8,0.3-1.1,0c-0.3-0.3-0.3-0.8,0-1.1l2.7-2.6
		l-2.8-2.8l-2.8,2.8c-0.3,0.3-0.8,0.3-1.1,0c-0.3-0.3-0.3-0.8,0-1.1l2.8-2.8l-3-3c-0.1-0.2-0.2-0.4-0.2-0.6c0-0.5,0.4-0.8,0.8-0.8
		c0.3,0,0.5,0.1,0.6,0.2l3,3l2.8-2.8c0.3-0.3,0.8-0.3,1.1,0s0.3,0.8,0,1.1l-2.8,2.8l2.8,2.8l2.8-2.8c0.3-0.3,0.8-0.3,1.1,0
		c0.3,0.3,0.3,0.8,0,1.1l-2.8,2.8l2.8,2.8l0.7-0.8c0,0,0,0,0,0l0.1-0.1c-0.3-0.7-0.4-1.5-0.4-2.3c0-4.5,2.8-8.4,7.5-10.7
		c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0.1,0c-0.1-0.1-0.2-0.2-0.3-0.3c-3.9-3.9-8.6-6.2-13.1-7.6L4.3,40.8c1.4,4.6,3.8,9.3,7.6,13.1
		c3.8,3.9,8.5,6.3,13,7.7l10.3-10.3c-0.5-0.3-1.2-0.7-2.3-1.1c-1.7-0.7-3.8-1.6-5-3.4l1.1-1.1c1.5,2.6,5.3,2.9,7.3,4.5
		c0.4,0.3,0.8,0.7,1,1.2c0.3,0.4,0.4,1,0.4,1.6c0,5.5-4.7,4.7-4.7,9.3c0,0.2,0,0.5,0,0.7c-0.5,0-1.1,0-1.6-0.1c0,0,0,0.1,0,0.1
		c0.6,0,1.1,0.1,1.6,0.1c0.1,0.5,0.2,1,0.2,1.5c0,0,0.1,0,0.1,0c0.9,3.8,4.4,7.1,12.2,7.1c7,0,20.3-5.5,20.3-19.5
		c0-3.1-0.6-5.9-1.6-8.4c-0.3-0.9-0.7-1.7-1.1-2.4c-0.8-1.4-1.8-2.7-2.9-3.9c-0.5-0.6-1-1-1.5-1.5c-1.7-1.4-3.6-2.5-5.6-3.3
		c-0.5-0.2-1-0.3-1.5-0.5c-2-0.7-4.1-1-6.1-1c-2.7,0-5.2,0.4-7.4,1.1c-0.5,0.1-0.9,0.2-1.2,0.4c-0.4-0.4-0.7-0.8-1.1-1.2
		c0,0,0,0-0.1,0c0.4,0.4,0.7,0.8,1,1.1C36.7,32.7,36.6,32.8,36.5,32.8z M61.1,49.9c0,1.7-1.4,3.1-3.1,3.1s-3.1-1.4-3.1-3.1
		c0-1.7,1.4-3.1,3.1-3.1S61.1,48.2,61.1,49.9z M53.3,39c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1s-3.1-1.4-3.1-3.1S51.6,39,53.3,39z
		 M43.5,36.5c0.5-0.4,1.2-0.6,1.9-0.6c0.7,0,1.4,0.2,1.9,0.6c0.8,0.6,1.3,1.5,1.2,2.5c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1
		C42.3,38,42.8,37.1,43.5,36.5z M43.1,56.8c0.5-0.3,1-0.5,1.6-0.6c0.3-0.1,0.5-0.1,0.8-0.1c2.6,0,4.7,2.1,4.7,4.7s-2.1,4.7-4.7,4.7
		c-1.6,0-2.9-0.7-3.8-1.9c-0.3-0.4-0.5-0.9-0.7-1.4c-0.1-0.5-0.2-0.9-0.2-1.4C40.8,59.1,41.7,57.6,43.1,56.8z M37.7,39
		c0.6,0,1.2,0.2,1.7,0.5c0.8,0.6,1.4,1.5,1.4,2.6c0,0.2,0,0.3,0,0.5c-0.3,1.4-1.5,2.6-3.1,2.6c-1.7,0-3.1-1.4-3.1-3.1S36,39,37.7,39
		z"/>
	<path fill="none" d="M3.8,23.8c-0.4,0.3-1.2,2.4-1.2,6.4c0,2.6,0.3,5.7,1.2,8.9l15.3-15.4C11.6,21.9,4.9,22.7,3.8,23.8z"/>
	<path fill="none" d="M58,51.5c0.8,0,1.5-0.7,1.6-1.6c0-0.9-0.7-1.6-1.6-1.6s-1.6,0.7-1.6,1.6C56.4,50.8,57.1,51.5,58,51.5z"/>
	<path fill="none" d="M31.4,63.2c0,0,0,0.1,0,0.1C31.4,63.3,31.4,63.2,31.4,63.2z"/>
	<path fill="none" d="M34.1,56.9c1.2-1,2-1.8,2-3.9c0-0.1,0-0.3,0-0.4l-9.5,9.5c1.7,0.4,3.3,0.7,4.8,0.8c0-0.2,0-0.4,0-0.6
		C31.4,59.4,32.9,58,34.1,56.9z"/>
	<path fill="none" d="M59.2,34.5c-0.1,0.2-0.2,0.4-0.3,0.5C59,34.8,59.1,34.6,59.2,34.5C59.2,34.5,59.2,34.5,59.2,34.5z"/>
	<path fill="none" d="M54.9,42.1c0-0.9-0.7-1.6-1.6-1.6C54.2,40.5,54.9,41.2,54.9,42.1z"/>
	<path fill="none" d="M53.3,40.5c-0.9,0-1.6,0.7-1.6,1.6C51.7,41.2,52.4,40.5,53.3,40.5z"/>
	<path fill="none" d="M65.4,42.6c-0.8-0.3-1.6-0.7-2.3-1.2C63.8,41.9,64.6,42.3,65.4,42.6z"/>
	<path fill="none" d="M45.5,40.5c0.8,0,1.5-0.6,1.6-1.6c0-0.9-0.7-1.6-1.6-1.6s-1.6,0.7-1.6,1.6C43.9,39.8,44.6,40.5,45.5,40.5z"/>
	<path fill="none" d="M37.1,31c0.2,0.5,0.6,0.9,1,1.3C37.7,31.9,37.4,31.5,37.1,31z"/>
	<path fill="none" d="M60.4,35.5c-0.1,0.2-0.2,0.4-0.2,0.7C60.2,36,60.3,35.7,60.4,35.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M58.7,36C58.7,36,58.6,35.9,58.7,36c0-0.4,0.1-0.7,0.2-1
		c0.1-0.2,0.2-0.4,0.3-0.5c0,0,0,0-0.1-0.1c0,0,0,0,0-0.1c-1.7-1.3-3.5-2.4-5.3-3l-0.7,1.4C55.1,33.5,57,34.6,58.7,36z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M45.5,31.2c2,0,4.1,0.3,6.1,1l0.7-1.4
		c-2.2-0.7-4.5-1.1-6.8-1.1c-3.1,0-5.9,0.5-8.4,1.3c0.3,0.5,0.6,0.9,1,1.3C40.3,31.6,42.8,31.2,45.5,31.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M64.2,43.8c1,2.5,1.6,5.3,1.6,8.4c0,14-13.3,19.5-20.3,19.5
		c-7.8,0-11.3-3.3-12.2-7.1c0,0-0.1,0-0.1,0c0,0,0,0.1,0,0.1c-0.5,0-1,0-1.5-0.1c0.7,3.5,3.5,8.7,13.8,8.7c3.5,0,9-1.3,13.7-4.8
		c3.7-2.8,8.1-7.8,8.1-16.3c0-2.9-0.4-5.5-1.2-7.8h0l0,0C65.4,44.3,64.8,44.1,64.2,43.8z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M33,62.3c0-4.6,4.7-3.8,4.7-9.3c0-0.6-0.1-1.2-0.4-1.6
		l-1.2,1.2c0,0.1,0,0.3,0,0.4c0,2.1-0.8,2.9-2,3.9c-1.2,1.1-2.7,2.5-2.7,5.4c0,0.2,0,0.4,0,0.6C31.9,63,32.5,63,33,63
		C33,62.8,33,62.5,33,62.3z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M29,45.7l-1.1,1.1c1.2,1.8,3.3,2.7,5,3.4
		c1.1,0.4,1.8,0.8,2.3,1.1l1.1-1.1C34.3,48.6,30.5,48.3,29,45.7z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M34.3,32.2c-4.7,2.3-7.5,6.2-7.5,10.7c0,0.9,0.1,1.6,0.4,2.3
		l0.1-0.1l1-1c0,0,0,0.1,0,0.1l0.2-0.2c-0.1-0.4-0.1-0.7-0.1-1.1c0-4.2,2.8-7.5,7-9.5c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0
		C34.9,32.9,34.6,32.6,34.3,32.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M60.2,37.5c1.1,1.2,2.1,2.5,2.9,3.9c0.7,0.5,1.5,0.9,2.3,1.2
		c-1.2-2.8-2.9-5.2-5-7.1c-0.1,0.2-0.2,0.5-0.2,0.7C60.1,36.6,60.1,37.1,60.2,37.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M53.3,45.2c1.7,0,3.1-1.4,3.1-3.1S55,39,53.3,39
		s-3.1,1.4-3.1,3.1S51.6,45.2,53.3,45.2z M53.3,40.5c0.9,0,1.6,0.7,1.6,1.6c0,0.9-0.7,1.6-1.6,1.6c0,0,0,0,0,0
		c-0.9,0-1.6-0.7-1.6-1.6C51.7,41.2,52.4,40.5,53.3,40.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M53.3,43.7c0.9,0,1.6-0.7,1.6-1.6
		C54.8,43,54.1,43.7,53.3,43.7z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M54.9,49.9c0,1.7,1.4,3.1,3.1,3.1s3.1-1.4,3.1-3.1
		c0-1.7-1.4-3.1-3.1-3.1S54.9,48.2,54.9,49.9z M58,48.3c0.9,0,1.6,0.7,1.6,1.6c-0.1,0.9-0.8,1.6-1.6,1.6c-0.9,0-1.6-0.7-1.6-1.6
		C56.4,49,57.1,48.3,58,48.3z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M59.2,34.4C59.2,34.4,59.2,34.4,59.2,34.4
		C59.2,34.5,59.2,34.5,59.2,34.4C59.2,34.4,59.2,34.4,59.2,34.4z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M60.3,35.4c1-2.4,4.6-3.6,8.5-2.8c2.3,0.5,4.2,1.6,5.4,2.9
		L75,34l0.6-1.2L83.8,16c0.2-0.4,0-0.9-0.4-1h-0.1L64.1,8.2c-0.4-0.1-0.8,0-1,0.4l-8.6,17.5l-1.4,2.8l-0.9,1.8
		c0.5,0.2,1,0.3,1.5,0.5L64.2,9.9L82,16.2L73.7,33c-1.3-0.9-2.8-1.6-4.6-1.9c-4.4-0.9-8.4,0.5-10,3.2c0,0,0,0,0.1,0.1
		C59.6,34.8,60,35.1,60.3,35.4z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M59.2,34.5C59.2,34.4,59.2,34.4,59.2,34.5c0-0.1,0-0.1,0-0.1
		C59.1,34.4,59.1,34.4,59.2,34.5C59.1,34.4,59.1,34.4,59.2,34.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M90.4,11.5C90.4,11.5,90.3,11.5,90.4,11.5
		C90.3,11.5,90.4,11.5,90.4,11.5L62.3,1.2c-0.4-0.1-0.8,0-1,0.4L50,24.9c-1.3-0.9-2.8-1.6-4.6-1.9c-5.1-1.1-9.8,1.1-10.5,4.8
		c-0.2,1,0,2,0.3,2.9c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.1,0.3,0.2,0.4c0,0,0,0,0.1,0.1c0,0,0,0,0.1,0c0,0,0,0,0,0
		c0.4-0.2,0.7-0.3,1.2-0.5c-0.5-1.1-0.8-2.1-0.6-3c0.5-2.9,4.4-4.5,8.7-3.6c2.3,0.5,4.2,1.6,5.4,2.9l0.7-1.5l0.6-1.2L62.4,2.9
		l26.5,9.7L78.4,34.3L77,37.1l-2.1,4.2l-0.1-0.1c-1.5,1.7-4.5,2.5-7.8,1.8c-0.5-0.1-1-0.3-1.5-0.4c0.3,0.6,0.5,1.2,0.7,1.8
		c0.2,0,0.4,0.1,0.6,0.1c4,0.8,7.8-0.3,9.6-2.7h0.1l0.4-0.8c0-0.1,0.1-0.2,0.1-0.3l13.9-28.4C90.9,12.1,90.8,11.7,90.4,11.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M64.2,43.8c0.6,0.2,1.2,0.4,1.9,0.6c-0.2-0.6-0.4-1.2-0.7-1.8
		c-0.8-0.3-1.6-0.7-2.3-1.2C63.5,42.1,63.9,42.9,64.2,43.8z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M66.1,44.4L66.1,44.4c-0.7-0.2-1.3-0.4-1.9-0.6
		C64.8,44.1,65.4,44.3,66.1,44.4z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M59.2,34.5c-0.1,0.2-0.2,0.4-0.3,0.5
		c-0.1,0.3-0.2,0.6-0.2,0.9V36c0,0,0,0,0,0c0.5,0.6,1,0.9,1.5,1.5c-0.1-0.4-0.1-0.9,0-1.3c0-0.3,0.1-0.5,0.2-0.7c0,0,0,0-0.1-0.1
		c0,0,0,0,0,0.1C59.9,35.1,59.5,34.8,59.2,34.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M58.7,35.9c0-0.3,0.1-0.6,0.2-0.9c-0.1,0.3-0.2,0.6-0.3,0.9
		C58.6,35.9,58.7,36,58.7,35.9c0,0.1,0,0.1,0,0.1V35.9z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M59.2,34.5c0.4,0.3,0.8,0.7,1.1,1c0,0,0,0,0-0.1
		C60,35.1,59.6,34.8,59.2,34.5C59.2,34.4,59.2,34.4,59.2,34.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M53.1,32.7l0.7-1.4c-0.5-0.2-1-0.4-1.5-0.5l-0.7,1.4
		C52.1,32.4,52.6,32.5,53.1,32.7z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M45.4,42.1c1.7,0,3.1-1.4,3.1-3.1c0.1-1-0.4-1.9-1.2-2.5
		c-0.5-0.4-1.2-0.6-1.9-0.6c-0.7,0-1.4,0.2-1.9,0.6c-0.7,0.6-1.2,1.5-1.2,2.5C42.3,40.7,43.7,42.1,45.4,42.1z M45.5,37.3
		c0.9,0,1.6,0.7,1.6,1.6c-0.1,1-0.8,1.6-1.6,1.6c-0.9,0-1.6-0.7-1.6-1.6C43.9,38,44.6,37.3,45.5,37.3z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.8,31.5c0.4,0.4,0.7,0.8,1.1,1.2c0.3-0.2,0.7-0.3,1.2-0.4
		c-0.4-0.4-0.8-0.8-1-1.3c0,0-0.1,0-0.1,0c0,0,0,0,0,0.1C36.6,31.2,36.2,31.4,35.8,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.8,31.5c0.4-0.2,0.8-0.4,1.2-0.4c0,0,0,0,0-0.1
		C36.5,31.2,36.2,31.3,35.8,31.5C35.8,31.5,35.8,31.5,35.8,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M31.4,63.2c0-0.1,0-0.1,0-0.2c0,0,0,0,0,0c0,0,0-0.1,0-0.1
		c-1.5-0.1-3.1-0.4-4.8-0.8l9.5-9.5c-0.1-0.5-0.4-0.9-0.9-1.3L24.9,61.6c-4.5-1.4-9.2-3.8-13-7.7c-3.8-3.8-6.2-8.5-7.6-13.1
		l16.6-16.6c4.5,1.4,9.2,3.7,13.1,7.6c0.1,0.1,0.2,0.2,0.3,0.3c0.5-0.2,0.9-0.4,1.3-0.6h0c-0.1-0.1-0.2-0.2-0.2-0.3
		c-0.1-0.1-0.1-0.3-0.2-0.5c0,0,0,0,0,0C23.8,19.5,5.5,20,2.7,22.7c-1,0.9-1.7,3.8-1.7,7.5c0,7.1,2.4,17.5,9.7,24.8
		c6.2,6.2,14.3,8.9,20.9,9.5c-0.1-0.4-0.1-0.8-0.2-1.2C31.4,63.3,31.4,63.2,31.4,63.2z M2.6,30.2c0-4,0.8-6.1,1.2-6.4
		c1.1-1.1,7.8-1.9,15.3-0.1L3.8,39.1C2.9,35.9,2.6,32.8,2.6,30.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.6,31.5L35.6,31.5C35.7,31.5,35.7,31.5,35.6,31.5
		c-0.1-0.1-0.1-0.2-0.2-0.3c0,0,0-0.1-0.1-0.1c0,0,0.1,0.1,0.1,0.1C35.5,31.3,35.6,31.4,35.6,31.5c0-0.1,0-0.3-0.1-0.4
		c-0.1-0.1-0.2-0.2-0.3-0.4c0.1,0.2,0.1,0.3,0.2,0.5C35.5,31.3,35.6,31.4,35.6,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M26.4,46.1l-2.8-2.8l2.8-2.8c0.3-0.3,0.3-0.8,0-1.1
		c-0.3-0.3-0.8-0.3-1.1,0l-2.8,2.8l-2.8-2.8l2.8-2.8c0.3-0.3,0.3-0.8,0-1.1s-0.8-0.3-1.1,0l-2.8,2.8l-3-3c-0.1-0.1-0.3-0.2-0.6-0.2
		c-0.4,0-0.8,0.3-0.8,0.8c0,0.2,0.1,0.4,0.2,0.6l3,3l-2.8,2.8c-0.3,0.3-0.3,0.8,0,1.1c0.3,0.3,0.8,0.3,1.1,0l2.8-2.8l2.8,2.8
		L18.6,46c-0.3,0.3-0.3,0.8,0,1.1c0.3,0.3,0.8,0.3,1.1,0l2.8-2.8l2.8,2.8l-2.8,2.8c-0.3,0.3-0.3,0.8,0,1.1c0.3,0.3,0.8,0.3,1.1,0
		l2.8-2.8l3,3c0.1,0.1,0.3,0.2,0.6,0.2c0.4,0,0.8-0.3,0.8-0.8c0-0.2-0.1-0.4-0.2-0.6l-3-3l0.2-0.2c-0.3-0.5-0.5-0.9-0.7-1.5
		L26.4,46.1z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M41,62.2c0.2,0.5,0.4,1,0.7,1.4c0.9,1.2,2.2,1.9,3.8,1.9
		c2.6,0,4.7-2.1,4.7-4.7s-2.1-4.7-4.7-4.7c-0.3,0-0.5,0-0.8,0.1c-0.6,0.1-1.1,0.3-1.6,0.6c-1.4,0.8-2.3,2.3-2.3,4
		C40.8,61.3,40.9,61.7,41,62.2z M42.4,60.4c0-0.3,0.1-0.6,0.3-0.9c0.4-0.8,1-1.4,1.9-1.6c0.3-0.1,0.6-0.2,1-0.2
		c1.7,0,3.1,1.4,3.1,3.1c-0.1,1.7-1.5,3.1-3.2,3.1c-1,0-1.9-0.5-2.5-1.3c-0.4-0.5-0.6-1.1-0.6-1.8C42.4,60.7,42.4,60.5,42.4,60.4z"
		/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M34.6,42.1c0,1.7,1.4,3.1,3.1,3.1c1.6,0,2.8-1.2,3.1-2.6
		c0-0.2,0-0.3,0-0.5c0-1.1-0.6-2-1.4-2.6c-0.5-0.3-1.1-0.5-1.7-0.5C36,39,34.6,40.4,34.6,42.1z M37.7,40.5c0.9,0,1.6,0.7,1.6,1.6
		c-0.1,0.9-0.8,1.6-1.6,1.6c-0.9,0-1.6-0.7-1.6-1.6C36.1,41.2,36.8,40.5,37.7,40.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M36.3,50.2l-1.1,1.1c0.5,0.4,0.8,0.8,0.9,1.3l1.2-1.2
		C37.1,50.9,36.7,50.5,36.3,50.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M33.2,64.6c0-0.5-0.1-1-0.2-1.5c-0.5,0-1-0.1-1.6-0.1
		c0,0.1,0,0.1,0,0.2c0,0,0,0.1,0,0.1c0,0.4,0.1,0.8,0.2,1.2c0,0,0,0,0,0c0,0,0,0.1,0,0.1c0,0,0.1,0,0.1,0c0,0,0-0.1,0-0.1
		C32.2,64.5,32.7,64.6,33.2,64.6z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M33.2,64.6c-0.5,0-1-0.1-1.5-0.1c0,0,0,0.1,0,0.1
		c0.5,0.1,1,0.1,1.5,0.1C33.2,64.7,33.2,64.6,33.2,64.6z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M31.4,63.2c0-0.1,0-0.1,0-0.2c0,0,0,0,0,0
		C31.4,63.1,31.4,63.1,31.4,63.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M31.4,63.3c0,0.4,0.1,0.8,0.2,1.2c0,0,0,0,0,0
		C31.5,64.1,31.5,63.6,31.4,63.3z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M34.3,32.2C34.3,32.2,34.3,32.2,34.3,32.2
		c0.4,0.4,0.7,0.7,1,1.1c0.4-0.1,0.8-0.3,1.2-0.5c-0.4-0.4-0.7-0.8-0.9-1.3c-0.5,0.2-0.9,0.4-1.3,0.6c0,0,0,0,0,0c0,0,0,0-0.1,0
		C34.2,32.1,34.3,32.2,34.3,32.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M34.3,32.2c0.4,0.4,0.6,0.7,1,1.1c0,0,0,0,0,0
		C35,32.9,34.7,32.6,34.3,32.2C34.3,32.2,34.3,32.2,34.3,32.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.6,31.5C35.6,31.5,35.6,31.5,35.6,31.5
		c-0.5,0.2-0.9,0.3-1.3,0.5c0,0,0,0,0,0C34.7,31.9,35.1,31.7,35.6,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M27.2,45.2L27.2,45.2C27.1,45.3,27.1,45.3,27.2,45.2
		L27.2,45.2C27.2,45.2,27.2,45.2,27.2,45.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M28,46.6l-0.2,0.2l1.1-1.1c-0.3-0.5-0.5-1-0.6-1.5l-1,1
		C27.5,45.7,27.7,46.2,28,46.6z"/>
	<polygon stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" points="27.3,45.1 27.2,45.2 27.2,45.2 	"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M27.3,45.1C27.3,45.1,27.3,45.1,27.3,45.1l1-0.9
		c0,0,0-0.1,0-0.1L27.3,45.1z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M27.3,45.2l-0.1,0.1c0,0,0,0,0-0.1l-0.1,0.1
		c0.2,0.6,0.4,1,0.7,1.5l0.2-0.2C27.7,46.2,27.5,45.7,27.3,45.2z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M27.2,45.3l0.1-0.1c0,0,0,0,0-0.1L27.2,45.3
		C27.2,45.3,27.2,45.3,27.2,45.3z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.5,31.2c0,0,0-0.1-0.1-0.1C35.4,31.1,35.4,31.2,35.5,31.2z
		"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.7,31.5c-0.1-0.1-0.2-0.2-0.2-0.3
		C35.5,31.3,35.6,31.4,35.7,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.6,31.5C35.6,31.5,35.6,31.5,35.6,31.5
		c0.2,0.5,0.5,0.9,0.9,1.3c0.1,0,0.2-0.1,0.3-0.1c-0.4-0.4-0.7-0.8-1-1.1C35.7,31.6,35.7,31.6,35.6,31.5
		C35.7,31.6,35.7,31.5,35.6,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.7,31.6C35.7,31.6,35.7,31.6,35.7,31.6
		C35.7,31.5,35.7,31.5,35.7,31.6C35.7,31.5,35.7,31.5,35.7,31.6C35.7,31.5,35.7,31.6,35.7,31.6z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.6,31.5C35.6,31.5,35.6,31.5,35.6,31.5L35.6,31.5
		C35.6,31.5,35.6,31.5,35.6,31.5C35.6,31.5,35.6,31.5,35.6,31.5z"/>
	<path stroke="#000000" stroke-width="0.1" stroke-miterlimit="10" d="M35.6,31.5C35.6,31.5,35.6,31.5,35.6,31.5
		C35.7,31.5,35.7,31.5,35.6,31.5C35.7,31.5,35.7,31.5,35.6,31.5L35.6,31.5z"/>
</g>
</svg>
