@image-root: '/images/';
// use as follows - @url: url("@{image-root}image.png")

/** Colors **/
@brand-primary: @brand-red;
@brand-primary-dark-lighter: #405062;
@brand-primary-dark: @brand-blue;
@brand-primary-darker: @hover-blue;
@brand-primary-light: #A9AEB5;
@brand-primary-lighter: #EDEDED;
@brand-secondary: lighten(#1B2433, 10%);

/** Branding **/
@hover-blue: #13232F;
@brand-blue: #223949;
@icon-blue: #5D9ED6;
@brand-red: #F15F4C;
@accent-gray: #EBEBEB;
@icon-gray: #999999;
@card-back: #182c38;
@font-light: #223949;
@lighter-gray: #f5f5f5;
@search-blue: #2f4d67;

@link-color: #3872a3;
@text-color: #121212;
@text-muted: #626262;

@detail-blue: #5d9ed6;
@brand-darkblue: #223949;

@brand-success: #00b67d;
@brand-info: #1976D2;
@brand-danger: #f55a4e;
@brand-specialfinancing: @brand-info;
//#f55a4e
@brand-viewed: #D0A6A6;
@brand-fav: #EBC305;
@brand-tag: #3FCA81;

@body-bg: #fff;
//#ECF2F6; //lighten(#e9eaed, 3%);
@hr-color: darken(#fff, 10%);
//#dfdfdf;
@leftbar-color: @brand-secondary;
@tooltip-max-width: 250px;

/** Text **/
@font-family: "Open Sans", sans-serif;
@base-font-size: 14px;
@base-line-height: 1.428571429;

/** Navbar **/
@nav-height: 40px;
@nav-color: @brand-primary;

/** Mobile Searchbar **/
@search-bar-height: 5.5em;
@search-bar-results-height: 2em;

/** Buttons **/
@btn-top-padding: 6px;
@btn-side-padding: 12px;
@btn-font-weight: 600;
@btn-color: #fff;

@btn-obHover: darken(@brand-primary, 3%);

/** Forms **/
@input-color: @text-muted;
@label-color: @text-muted;
@input-border: darken(#fff, 15%);
@input-border-focus: @brand-primary;

@onboarding-trans: darken(#fff, 80%);
@onboarding-bg: fade(#000, 60%);

/** Form Feedback **/
@state-success-text: @brand-success;
@state-success-border: @brand-success;

@state-danger-text: @brand-danger;
@state-danger-border: @brand-danger;

/** General Layout **/
@content-bg: #f5f5f5;
@content-bg-fade: fade(@content-bg, 30%);

/** Grid Variables **/
@mobile: 40em;
// 640px
@tablet: 48em;
// 768px
@desktop: 64em;
// 992px
@largeDesktop: 80em;
// 1280px
@xlargeDesktop: 90em;
// 1440px

/** Layout **/

// ==== Z-indexes Layout ====
@layout-content-z: 1;
@layout-leftbar-z: 3;
@layout-header-z: 12;

// === Width Layout ===
@leftbar-width: 0;
//3.75em;
@header-height: 4.4em;
//3.5em;

// ==== Content Widths ====
@content-right-width: 20em;
@content-gutter-width: 4em;
@content-gutter-width-lg: 6em;
@content-photo-z: 9;
@content-photo-container-z: 10;
@content-left-max-width: 1160px;
