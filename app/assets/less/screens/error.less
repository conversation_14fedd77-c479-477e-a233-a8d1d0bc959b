.oh-shucks-container {
  min-width: 300px;

  position: absolute;
  top: @header-height;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20%;

  margin: auto;

  div.white-bg {
    display: block;
    position: fixed;
    top: @header-height;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: -1;
  }

  svg {
    pointer-events: all;

    .style0 {
      fill: #A57A50;
    }
    .style1 {
      fill: #DEDDDD;
    }
    .style10 {
      fill: #233949;
    }
    .style11 {
      fill: #F1604C;
    }
    .style12 {
      fill: #88C3EA;
    }
    .style13 {
      fill: #FFFFFF;
    }
    .style14 {
      fill: none;
    }
    .style15 {
      font-size: 60px;
      font-family: "Open Sans", sans-serif;
      font-style: italic;
      font-weight: 800;
      letter-spacing: 5px;
      fill: #5D9ED5;
    }
    .style16 {
      font-size: 55px;
      font-family: "Open Sans", sans-serif;
      font-weight: 600;
      letter-spacing: 2px;
      fill: #5D9ED5;
    }
    .style17 {
      font-size: 30px;
      font-family: "Open Sans", sans-serif;
      fill: #F15F4C;
    }
    .style18 {
      font-size: 30px;
      font-family: "Open Sans", sans-serif;
      letter-spacing: 1px;
      fill: #5D9ED5;
    }
    .style19 {
      font-size: 30px;
      font-family: "Open Sans", sans-serif;
      fill: #5D9ED5;
      letter-spacing: 1px;
    }
    .style2 {
      fill: #284554;
    }
    .style3 {
      fill: #5D9ED5;
    }
    .style4 {
      fill: #F26D73;
    }
    .style5 {
      fill: #C1C1C0;
    }
    .style6 {
      fill: #13232F;
    }
    .style7 {
      fill: #223949;
    }
    .style8 {
      fill: #B0AFB0;
    }
    .style9 {
      fill: #7B7B7B;
    }
    .cursor-pointer {
      pointer-events: all;

      &:hover {
        font-weight: 600;
      }
    }
  }

  @grass-height: 160px;
  div.grass {
    position: absolute;
    left: 0;
    right: 0;

    &.left-grass {
      margin-top: -112px;
      height: @grass-height;
      border-right: 100vw solid transparent;
      border-bottom: @grass-height solid #48a875;
    }

    &.right-grass {
      margin-top: @grass-height/2 - 112px;
      height: @grass-height/2;
      border-left: 100vw solid transparent;
      border-bottom: @grass-height/2 solid #65c090;
    }
  }
}