.login-prompter-modal{
  z-index: 10000;

  .modal-dialog{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate( -50%, -50% ) !important;
    margin: 0px !important;

    @media (max-width: (@tablet)) {
      width: 90vw;
    }

    @media (max-height: 530px){
      transform: none !important;
      top: auto;
      left: auto;
      position: relative;
      margin: 30px auto !important;
    }
  }

  .modal-content{
    background: transparent;

    .modal-body{
      padding: 0;
    }
  }
}

.login-prompt{
  background-color: white;

  .slick-slider{
    margin-bottom: 0px;
  }

  .slick-track{
    align-items: stretch;
    align-content: stretch;
    display: flex;

    .slick-slide{
      float: none;
      height: auto;
      display: flex;
    }
  }

  .slick-dots{
    bottom: 0;

    li{
      margin: 0;
    }

    li button:before{
      font-size: 24px;
    }

    li.slick-active button:before{
      color: #fff;
    }
  }

  .close-button{
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate( 50%, -50% );
    padding: 12px;
    border-radius: 50%;
    background-color: @brand-red;
    z-index: 1;

    @media (max-width: (@tablet)) {
      transform: translate( 25%, -50% );
    }

    &:hover{
      border: 5px solid rgba(255,255,255,0.5);
      background-clip: padding-box;
    }

    svg{
      width: 24px;
      height: 24px;
      fill: white;
    }
  }

  .login-quote-container{
    display: flex;
    align-items: center;
    padding: 24px 48px;

    color: white;
    font-style: italic;
    font-size: 16px;
    line-height: 24px;

    img{
      margin-right: 12px;
      border-radius: 50%;
      flex-shrink: 0;
      width: 100px;
      height: 100px;
    }

    @media (max-width: (@tablet)) {
      padding: 32px 16px 32px 16px;
      font-size: 14px;
      line-height: 20px;

      img{
        width: 80px;
        height: 80px;
      }
    }
  }

  .common-login{

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    padding: 32px 32px 48px 32px;
    text-align: center;

    @media (max-width: (@tablet)) {
      padding: 32px 16px;
    }

    .title{
      font-size: 30px;
      color: @brand-red;
      font-family: @font-family;
      margin-bottom: 24px;
      font-weight: 400;
    }

    .login-button{
      background-color: @brand-success;
      color: white;
      font-size: 18px;
      margin-bottom: 4px;
      padding: 10px;
      border-radius: 3px;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover{
        background-color: darken(@brand-success, 5%);
      }

      div:last-child{
        text-align: center;
        flex: 1 0 auto;
        margin: 0px 24px;
        letter-spacing: 1px;

        @media (max-width: (@tablet)) {
          margin: 0px 10px;
          font-size: 16px;
        }

        @media (max-width: (320px)) {
          margin: 0px 0px 0px 10px;
          font-size: 14px;
        }
      }
    }

    .prompt-login-note{
      color: #666;
      font-style: italic;
      font-size: 12px;
      max-width: 320px;
    }

    .are-you-an-agent{
      position: absolute;
      bottom: 16px;
      right: 16px;
      font-size: 11px;
      line-height: 11px;
    }
  }

  &.hardwall-prompt{

    .close-button{
      background-color: #ccc;
    }
  }
}

.hardwall{
  background-color: #223949;
  text-align: center;
  color: white;
  font-size: 18px;
  padding: 24px;
  font-weight: 400;

  div:first-child{
    font-size: 22px;
    margin-bottom: 4px;

    @media (max-width: (@tablet)) {
      font-size: 16px;
    }
  }

  div:last-child{
    margin-top: 4px;
  }

  .hardwall-more{
    display: flex;
    align-items: center;
    justify-content: center;

    ul{
      text-align: left;
      font-size: 16px;
    }

    svg{
      width: 80px;
      height: 80px;
      fill: @brand-red;
    }
  }

}

.progress-modal-content{
  padding: 24px 0px 0px 0px;
  background-color: white;

  h3{
    text-align: center;
    margin: 16px 0px 32px 0px;
  }


  .progress-track{
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;

    > div{
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      flex: 1 0 33.3%;

      @media (max-width: 530px){
        margin: 15px 0px 0px 0px;
      }
    }
  }

  .label{
    color: @brand-info;
    font-size: 16px;
    font-weight: 400;
    margin-top: 12px;
  }

  svg{
    width: 50px;
    height: 50px;
    fill: @brand-red;
  }
}

.contest-modal-content{
  display: flex;
  flex-direction: column;
  padding: 0px;
  justify-content: center;
  align-items: center;
  position: relative;

  @media (max-width: (@tablet)) {
    padding: 0px;
  }

  div{
    font-size: 16px;
  }

  img{
    width: 100%;
  }

  .contest-draw-date{
    position: absolute;
    text-align: center;
    width: 100%;
    color: white;
    text-transform: uppercase;
    bottom: 14px;
    left: 0;
    font-size: 16px;
    max-width: 100%;
    margin: 0 auto;

    @media (max-width: (@tablet)) {
      padding: 0px;
      background-color: transparent;
      box-shadow: none;
      font-size: 12px;
    }
  }
}

.contest-modal-content ~ .common-login{
  .login-button{
    div:last-child{
      font-size: 12px;
    }
  }
}
