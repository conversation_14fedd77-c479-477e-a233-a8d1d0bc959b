.agent-screen-container {
  .menu-top {
    max-width: 1440px;
    margin: 0 auto;
    div.text-left {
      width: 80%;
      &.buyer{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    div.text-right {
      @right-rail:20em;
    }
  }

  .menu-left {
    background: @accent-gray;

    > div.scroll-container {
      margin: 0 auto;
      padding: 0;
      padding-left: 50px;
      background-color:transparent;
      max-width: @content-left-max-width;

      .listing-full-content-container{
        background-color:#fff;
      }
    }

    .n-play-footer {
      margin:0 0 1em 0;
      /*margin: 1.5em -1.5em;*/
      font-size: 11px;
    }

    .listing-full + .n-play-footer {
      display: none;
    }
  }
}

.agent-profile-outer-container{
  background: @accent-gray;

  .details-holder{
    max-width:1160px;
    margin:0 auto;
    /*background-color: #fff;*/
    .agent-name-container,.broker-name-container{
      white-space: nowrap;
      display: inline-block;
      max-width: 40%;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 2em;
    }
    .broker-logo{
     /* vertical-align:top;
      font-size: 2em;
      padding: 0 6px;*/

    }
  }
  div.text-left {
    @temp: 20em;
    width:calc(~"(99.999999% - @{temp})");

  }
  div.text-right {
    width: 20em;
    float:right;
    background-color: @brand-blue;
    svg{
      height: 3em;
      fill:#fff;
      padding: .5em 0;
    }
  }
}

.menu-top.fixed {
  background: #fff;
  position: fixed;
  top: @header-height;
  left: 0;
  right: 0;
  height: @header-height;
  z-index: 10;

  padding:0;
  padding-top: 1em;
  border-left: 50px solid @accent-gray;
  border-bottom: 0.1em solid @hr-color;
  @right-rail: 20em;

  > div {
    display: inline-block;

    &.text-center {
      width: 60%;

      p {
        .match-count {
          font-size: 1.5em;
        }

        svg {
          fill: #000;
          stroke: #000;
          width: 1.5em;
          height: 1em;
          margin-left: 0.5em;
          margin-right: 0.25em;
        }
      }
    }
  }


  .broker-logo {
    height: 1.2em;
  }
  .broker-name{
    cursor:pointer;
  }
  .right-container{
    text-align:right;
  }
  .headline {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    color:@icon-blue;
    font-size: 1.05em;
    font-style: italic;
    font-weight: 500;
    margin-left: 3em;
    margin-top: 1em;
    width: 380px;
    a {
      text-decoration: none;
      cursor: pointer;
    }

  }
  .contact-button{
    height:28px;
    display:inline-block;
    background-color:@icon-blue;
    color:#fff;
    margin-bottom: 2em;
    margin-right:5px;
    padding:3px 6px;
    font-size:10px;
    svg{
      width:15px;
      height:20px;
      vertical-align:middle;
      fill:#fff;
    }
    &.sm{

    }
  }
  .social-container {
    display: inline-block;
    /*float: right;*/
    text-align: left;
    padding: 0;
    margin-top: .5em;
    div{
      display:inline-block;
      cursor:pointer;
    }
  }
  .close-button-cross {
    width: 46px;
    height: 46px;
    padding: 5px;
    background-color: @brand-primary;
    svg {
      fill: #fff;
      stroke: #fff;
    }
  }
  .social-icon{
    width:20px;
    height:30px;
    fill: @brand-red;

    &.twitter-icon{
      fill: #598DCA;
    }

    &.facebook-icon{
      fill: #3A589B;
    }

    &.instagram-icon{
      fill: #833AB4;
    }

    &.youtube-icon{
      fill: #c4302b;
    }

    &.google-icon{
      fill: #4285F4;
    }

    &.lg{
      width:22px;
    }
  }
  .social {
    margin-right: 6px;
  }
}

.featured-listings {
  border-top: 1em solid #ebebeb;
  position: relative;
  z-index: 9;
}

.brokerage-modal {
  .modal-content{
    max-width:400px;
    padding:20px;
    .close-button{
      top:10px;
      position: absolute;
      right: 10px;
      cursor: pointer;
      text-decoration: none;
    }
  }

  .brokerage {
    .logo{
      max-width:30%;
      margin-right:20px;
      display:inline-block;
      vertical-align:top;
      img{
        height:5em;
        width:100%;
      }
    }
    .details{
      width:48%;
      display:inline-block;
    }
  }

}

@media (min-width: @screen-sm-min) {

  .brokerage-modal {
    .modal-dialog {
      .modal-content {
        width: 100%;
      }

    }
  }

}

@media (min-width: 767px) and (max-width: 970px) {

  .m-hide{
    display:none;
  }
}

.title-show{
  &.show-headline-inside{
    display:block !important;
  }
}
@media (max-width: 860px){

  .title-hide{
    display:none;
  }
  .title-show{
    display:block !important;
  }
}
