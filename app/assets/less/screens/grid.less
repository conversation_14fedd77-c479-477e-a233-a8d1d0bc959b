@dark: #13232a;
@light: #fff;
@bg-light: #eae8ed;
@outline: @accent-gray;
@data: #5d99e1;
@menu-font: #353e4d;
@active: @brand-red;
@bg-dark: #505050;
@glow: #e6b800;
@one-card-width: 298px;
@two-card-screen: 627px;
@three-card-screen: 925px;
@four-card-screen: 1223px;
@five-card-screen: 1521px;
@six-card-screen: 1819px;
@seven-card-screen: 2117px;

.grid-container {
  position: relative;
  min-height: 100vh;
  .mn {
    margin: 0 !important;
  }
  &.lights-out {
    background-color: @dark;
    color: #fff !important;

    .listings-no-results{
      background-color: @dark;
    }

    .courtesy, .bottom {
      background-image: none;
    }

    .card-front {
      &:hover {
        .bottom {
          #gradient.vertical(rgba(0, 0, 0, 0.0); rgba(0, 0, 0, .0); 0%; 100%);
          border-top: 3px solid @detail-blue !important;
        }
      }
    }

    .card {
      a{
        color: #fff !important;
      }

      .card-front {
        background-color: @card-back;
        border-bottom: none;
        .bottom {
          #gradient.vertical(rgba(0, 0, 0, 0.0); rgba(0, 0, 0, .0); 0%; 100%);

          border-bottom: 1px solid #ccc;
          border-top: 3px solid #ccc !important;
          height: auto;
          .address {
            display: block;
          }
        }
        &:hover {
          .bottom {
            #gradient.vertical(rgba(0, 0, 0, 0.0); rgba(0, 0, 0, .0); 0%; 100%);
            border-top: 3px solid @detail-blue !important;
          }
        }
      }
      .card-back {
        background-color: @card-back;
        color: inherit;

        .box-long, .box {
          color: inherit;
        }
        .box-icon,
        .box-long-icon {
          fill: #fff;
          &.calendar {
            stroke: #fff;
          }
        }
      }
    }
    .grid-menu {

      .nav-scrollable {

        a.item,div.item {
          color: #FFF;

          svg {
            stroke: #fff;
            fill: #fff;
          }
          &:hover,&:focus,&.active{
            color: @brand-red;
            text-decoration: none;
            letter-spacing: .5px;

            svg {
              stroke: @brand-red;
              fill: @brand-red;
            }
          }
        }
        .dropdown-menu {
          border: 1px solid rgba(255,255,255,.5);
          border-bottom:none;
          background-color: @dark !important;
          li {
            border-bottom: 1px solid rgba(255,255,255,.5);
            &:hover,
            &:focus {
              background-color: @card-back;
            }
            a {
              color: #FFF;
              &:hover,
              &focus {
                background-color: @card-back;
              }
            }
          }
        }

        > a.item:first-child {
          margin-left: 0;
        }
        > div.item:last-child {
          margin-right: 0;
        }
      }
      .sort-container,
      .grid-container,
      .tags-container {
        background-color: @accent-gray;
        .square-icon-btn {
          color: inherit;
          background-color: @accent-gray;
          &.active {
            background-color: @dark;
          }
          svg {
            fill: @light;
            stroke: @light;
          }
        }
      }
    }

    div.results-count {
      a.lights-toggle {
        background-color: transparent;
        border-color: transparent;
//        color: #fff;
        cursor: pointer;

        svg {
          fill: #fff;
          stroke: #fff;
        }
      }
    }
  }
  &.lights-on {

    background-color: @accent-gray;
    color: #000 !important;
    .courtesy, .bottom {
      background-image: none;
    }
    .bottom {
      border-bottom: 1px solid @accent-gray;
      background-color: #fff !important;
      border-top: 3px solid transparent !important;
      height: auto;
      .address {
        display: block;
      }
    }

    .card-front {
      &:hover {
        .bottom {
          #gradient.vertical(rgba(0, 0, 0, 0.0); rgba(0, 0, 0, .0); 0%; 100%);
          border-top: 4px solid @detail-blue !important;
          background-color: #fdfdfd;
        }
      }
    }

    .card-back {
      background-color: @body-bg !important;
      .box-long, .box {
        background-color: @light;
      }
      .box-icon, .box-long-icon {
        fill: #acacac;
        &.calendar {
          stroke: #acacac;
        }
      }
    }
    .grid-menu {
      .nav-scrollable {
       /* a,div{
          color: @font-light;
          svg{
            fill:@font-light;
          }
        }*/
        .dropdown-menu {
          border: 1px solid @accent-gray;
          border-bottom:none;
          background-color: #fff;
         li {
           border-bottom: 1px solid @accent-gray;
           &:hover,
           &:focus {
             background-color: @accent-gray;
           }
           a {
             color: @font-light;
             &:hover,
             &focus {
               background-color: @accent-gray;
             }
           }
         }
        }
      }
    }

    div.results-count {
      a.lights-toggle {
        background-color: transparent;
        border-color: transparent;
//        color: #000;
        cursor: pointer;

        svg {
          fill: #000;
          stroke: #000;
        }
      }
    }
  }
  .grid-cards-container {
    box-shadow: none;
    margin-top: 100px;
    padding-top: 0;
    background-color: inherit !important;
    border: none;

    @media screen and (max-width: @desktop) {
      margin-top: 135px;
    }

    .cards-holder {
      text-align: left;
      margin: 15px auto;
    }
    .card-holder {
      padding-left: 9px;
      padding-right: 9px;
      display: inline-block;
    }
    .card {
      width: 280px;
      margin-bottom: .9em;
      margin-left: 0px;
      height: 16em;
      color: inherit !important;

      a{
        color: #333;
      }

      &.card-tag {
        background-color: lightblue;
      }
      .bottom {
        border-top: 0.2em solid #999999;
      }
      .card-image {
        height: 12.6em;
        background-size: cover;
      }
      .card-front {
        .photo-button {
          color: #fff !important;
        }
        .card-broker-logo{
          bottom: 72px;
        }
        .courtesy {
          text-align: left;
          background-color: transparent;
          color: @body-bg;
          bottom: 3.2em;
          &.courtesy-large{
            height: 2em;
          }
        }
        .bottom {
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;

          p {
            overflow: hidden;
          }

          .bottom-left {
            font-weight: 600;
          }
          .bottom-right {
            > p:after {
              margin-left: 0;
              margin-right: 0.1em;
            }
          }
          .bottom-left, .bottom-right {
            margin-bottom: 0;
            line-height: 30px;
            height: 30px;
          }
          .address {
            font-size:10px;
            display: block !important;
            clear: left;
            margin-top: 1px;
          }

          &.land-tenure {
            .bottom-left, .bottom-right {
              line-height: 1.2em;
              height: 1.2em;
            }
          }
        }
      }
      .card-back {
        border: 1px solid lighten(@dark, 70%);
        color: inherit;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        .data {
          color: @data;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: 400;
        }
        .flip-icon{
          width: 16px;
          height: 16px;
          position: absolute;
          top: 5px;
          right: 10px;
          fill: @icon-gray;
        }
        .feature {
          color: @data;
          font-weight: 400;
          font-size: 0.8em;
          display: block;
          display: -webkit-box;
          max-width: 100%;
          height: 43px;
          margin: 0 auto;
          line-height: 1;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          .feature-item {
            display: inline;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
          }
        }
        .header {
          height: 15%;
          width: 73%;
          font-size: 0.8em;
          font-weight: 400;
          text-align: left;
          padding: 8px 10px;
        }
        .box {
          height: 33%;
          width: 32%;
          margin: 1%;
          margin-right: 0px;
          display: inline-block;
          text-align: center;
          float: left;
          position: relative;
          border-bottom: 1px solid #ebebeb;
          &.mtn {
            margin-top: 7px !important;
            width: 98%;
            height: 21%;
            border: none;
            padding: 3px 10px;
            text-align: left;
            .box-header {
              color: #555;
              font-size: 0.8em;
              font-weight: 400;
            }
            .feature-item {

            }
          }

          .box-header {
            font-size: 0.7em;
            font-weight: 500;
            color: #707070;
            margin: 0;
          }
          .box-icon {
            width: 100%;
            position: absolute;
            bottom: 10px;
            left: 0;
            height: 30%;
            &.large {
              height: 42%;
            }
          }
        }
        .box-side {
          position: absolute;
          margin-left: 0px;
          bottom: 0;
          width: 100%;
          cursor:default;

          &.fullwidth {
            width: 100%;
          }
          > div {
            float: left;
            /* border: 1px solid  @active;
             color: @active;*/
            text-align: center;
            padding: 5px;
            position: relative;
            width: 50%;

           /* &:hover {
              color: @light;
            }*/
            span {
              font-weight: 700;
            }
            .box-long-icon {
              width: 30%;
              position: absolute;
              bottom: 6px;
              left: 3px;
              height: 65%;
              fill: @active;
              width: 20%;

            }
          }
        }

      }
    }
  }
  .menu-fixed {
    position: fixed;
    width: 100%;
    top: 61px;
    background-color: inherit;
    z-index: 10;
  }
  .grid-menu {
    background-color: inherit;
    color: inherit;
    font-weight: 200;
    padding: 10px 10px 0;
    position: relative;
    margin-top: 2em;
    .layout--nav {
      top: 3.5em;
      color: inherit;
      background-color: inherit;
      margin: 0 auto;
      padding: 9px 9px 0;
      .btn-default{
        text-transform: none;
      }
      nav.listing-nav {
        .sort-dropdown{
          text-transform: none;
        }
         border-top:none;
        /*text-transform: none;*/
      }
    }
    .nav-scrollable {
      color: inherit;
      height: 41px;
      background-color: inherit;
      overflow-y: visible;
      overflow-x: visible;

      a.item {
        &.br::after {
          color: inherit;
          padding-right: 10px;
          content: '|';
        }
      }
      .tags-holder{
        .dropdown-menu{
          overflow-y: auto;
          overflow-x: hidden;
          max-height: 236px;
        }
      }
      .btn-group {
        vertical-align: top;

        a {
          color: inherit;
        }
        .btn-default {
          color: inherit;
          border: none;
          fill: #fff;
          stroke: #fff;
          padding-left: 0;
          padding-right: 0;
          &:hover,
          &:focus {
            background: none;
          }
        }

        .dropdown-menu {
          padding: 0px;
          margin: 0px;
          text-transform: none;
        /*  border: 1px solid rgba(255,255,255,.5);*/
          border-bottom: none;

          li {
            margin: 0;
            padding: 6px 0px;
/*        border-bottom: 1px solid rgba(255,255,255,.5);*/

            &.active{
              font-weight: 700;
              background-color:inherit;
              a{
                font-weight: 700;
                background-color:inherit;
              }
            }
          }
        }
      }
    }
    .sort-container,
    .tags-container {
      top: 40px;
      z-index: 3;
      background-color: #fff;
      .square-icon-btn {
        border: none;
        width: 9em;
        height: 11em;
        border-radius: 0;
      }
    }
    .sort-container {
      position: absolute;
      width: 200px;
      top: 40px;
      z-index: 3;
      background-color: #fff;
      right: 100px;
      top: 60px;
      .list-group {
        margin-bottom: 0;
      }
    }
    .tags-container {
      text-align: left;
      .tag-header {
        margin: 0 auto;
        width: 90%;
        padding-top: 20px;
        padding-bottom: 5px;
        border-bottom: 1px solid;
      }
      .tag-holder {
        padding: 20px 40% 20px 10%;
      }
      .tag-content {
        width: 50%;
        display: inline-block;
        text-align: left;
        a {
          color: inherit;
          cursor: pointer;
          &:hover {
            color: inherit;
          }
        }
        span {
          margin: 0;
        }
      }
    }
    .menu-left {
      position: absolute;
      top: 30px;
      left: 20px;
      width: 50%;
      text-align: left;
    }
    .menu-right {
      position: absolute;
      top: 30px;
      right: 20px;
      width: 50%;
      text-align: right;
    }
    .br::after {
      content: '|';
    }
    .menu-icon {
      margin: 0;
      width: 22px;
      height: 22px;
      vertical-align: middle;
      &.tag-icon{
        width: 16px;
        height: 16px;
        margin-top: 7px;
        margin-right: 7px;
      }
    }

    span {
      display: inline;
      margin-left: 10px;
      &.active {
        color: #f5674f !important;
        border-bottom: 1px solid @brand-red;
      }
      a {
        color: inherit;
        &:hover,
        &:focus {
          text-decoration: none !important;
        }
      }
    }
  }

  .grid-info-bar {

    .grid-info-logo {
      width: 11em;
      height: 2em;
      a{
        width: 11em;
        display: block;
        height: 2em;
      }
      svg {
        width: 100%;
        height: 100%;
      }
    }

    .grid-info-exit {
      box-shadow: -2px 1px 3px rgba(0, 0, 0, 0.25);

      svg {
        stroke-width: 0;
        margin-bottom: -2px;
        stroke-width: 2px;
      }
    }

    margin: 0 4em 3em;

    &.fixed {
      margin: 0;
      position: fixed;
      bottom: 3em;
      right: 4em;
      z-index: 1;
    }
  }

  /* To set the menu width relative to the cards*/
  @media screen and (max-width: @two-card-screen) {
    .cards-width,.cards-holder {
      width: calc(@one-card-width);
    }
  }

  @media screen and (min-width: @two-card-screen) and (max-width: (@three-card-screen)) {
    .cards-width,.cards-holder {
      width: calc(2 * @one-card-width);
    }
  }
  @media screen and (min-width: @three-card-screen) and (max-width: (@four-card-screen)) {
    .cards-width,.cards-holder {
      width: calc(3 * @one-card-width);
    }
  }
  @media screen and (min-width: @four-card-screen) and (max-width: (@five-card-screen)) {
    .cards-width,.cards-holder {
      width: calc(4 * @one-card-width);
    }
  }
  @media screen and (min-width: @five-card-screen) and (max-width: @six-card-screen) {
    .cards-width,.cards-holder {
      width: calc(5 * @one-card-width);
    }
  }
  @media screen and (min-width: @six-card-screen) and (max-width: @seven-card-screen) {
    .cards-width,.cards-holder {
      width: calc(6 * @one-card-width);
    }
  }
  @media screen and (min-width: @seven-card-screen) {
    .cards-width,.cards-holder {
      width: calc(7 * @one-card-width);
    }
  }
}

.grid-footer{

  .grid-footer-container{
    margin: 0 auto;
    padding: 2em 0.5em;
    text-align: left;
    > div{
      padding: 8px;
    }

    .last-refreshed{
      margin: 0;
      text-align: left;
    }
  }

  .whyusehomeasap-logo{
    height: 25px;
  }

  .whyusehomeasap-logo svg{
    height: 25px;
    width: 133px;
  }

  .mls-disclosure {
    img {
      height: 25px;
    }
    p {
      margin-top: 8px;

      &.extra-message {
        font-weight: 700;
      }
    }
  }

  .whyusehomeasap{
    p {
      font-size: 80%;
      margin-top: 8px;

      &.extra-message {
        font-weight: 700;
      }
    }
  }

  @media screen and (max-width: @two-card-screen) {
    .grid-footer-container {
      width: calc(@one-card-width);
      text-align: center;
    }

    .grid-footer-container > div{
      width: 100%;
      display: block;
      padding: 0 !important;
    }

    .grid-footer-container > div:first-child{
      margin-bottom: 1.5em;
    }

    .mt-results{
      margin-top:45px;
    }
  }

  @media screen and (min-width: @two-card-screen) and (max-width: (@three-card-screen)) {
    .grid-footer-container {
      width: calc(2 * @one-card-width);
    }
    .mt-results{
      margin-top:40px;
    }
  }
  @media screen and (min-width: @three-card-screen) and (max-width: (@four-card-screen)) {
    .grid-footer-container {
      width: calc(3 * @one-card-width);
    }
  }
  @media screen and (min-width: @four-card-screen) and (max-width: (@five-card-screen)) {
    .grid-footer-container {
      width: calc(4 * @one-card-width);
    }
  }
  @media screen and (min-width: @five-card-screen) and (max-width: @six-card-screen) {
    .grid-footer-container {
      width: calc(5 * @one-card-width);
    }
  }
  @media screen and (min-width: @six-card-screen) and (max-width: @seven-card-screen) {
    .grid-footer-container {
      width: calc(6 * @one-card-width);
    }
  }
  @media screen and (min-width: @seven-card-screen) {
    .grid-footer-container {
      width: calc(7 * @one-card-width);
    }
  }
}

.grid-rateplug-subheader {
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 11;
  background: white;

  span {
    display: inline-block !important;
  }
}