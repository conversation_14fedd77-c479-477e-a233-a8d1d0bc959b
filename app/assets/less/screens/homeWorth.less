.home-worth{
  position: relative;
  text-align: center;
  overflow-y: auto;
  min-height: 100vh;
  width: 100%;
  margin-top: -@header-height;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  z-index: 9;

  header.home-worth-agent-header {
    display: flex;
    width: 100%;
    padding: 16px;
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    background: transparent;
    cursor: pointer;
    flex-wrap: wrap;

    .agent{
      flex: 1 0 auto;
      display: flex;
      text-align: left;

      .agent-image{
        width: 60px;
        height: 60px;
        margin-right: 10px;
        border-radius: 50%;
      }
    }

    .details{
      display: flex;
      justify-content: center;
      flex-direction: column;

      .name{
        font-weight: bold;
      }
    }

    .contact{
      flex: 0 0 auto;
      text-align: right;
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin-left: auto;

      .icons{
        display: none;
      }

      a{
        color: white;
      }
    }
  }


  .content{
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    flex: 1 1 auto;
    color: white;
    background-repeat: no-repeat;
    background-position: center center;
    background-attachment: fixed;
    background-size: cover;

    h1{
      margin-bottom: 40px;
      font-size: 32px;
      font-weight: 600;
      text-shadow: 0px 1px 8px rgba(0, 0, 0, 0.9);

      > span {
        display: inline-block;
      }
    }

    .home-search, .found-home, .success{
      display: none;
    }

    &.show-found{
      background: none !important;

      .found-home{
        display: flex;
      }
    }

    .home-search .disclosures {
      margin-top: 10px;
      text-shadow: 0px 1px 8px rgba(0, 0, 0, 0.9);
      white-space: pre;

      a {
        color: white;
        text-decoration: underline;
      }
    }

    &.show-search{
      .home-search{
        display: block;
      }
    }

    &.show-success{
      .success{
        display: block;
      }
    }

  }

  .found-home{
    flex: 1 1 auto;
    flex-direction: row-reverse;
    width: 100%;
    background: @brand-darkblue;

    #found-home-map{
      width: 50%;
      cursor: default;
      pointer-events: none;

      .mapboxgl-canvas{
        left: 0;
      }

      .leaflet-gl-layer.mapboxgl-map:after {
        display: block;
        content: '';
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,.2);
        position: relative;
      }

      .leaflet-marker-icon.favorite svg {
        transform: scale(1.5) translateY(-8px);
      }

      .leaflet-marker-icon.marker-label {
        cursor: default;

        > p {
          margin: 0;
          display: block;
          transform: translateX(-50%);
          white-space: nowrap;
          background: #fff;
          padding: 10px 18px;
          font-style: italic;
          box-shadow: 1px 1px 9px rgba(0,0,0,.05);
          border-radius: 3px;
        }

        div.arrow-up {
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 12px solid #fff;
          transform: translateX(-50%);
        }
      }
    }

    #found-home-form{
      padding: 16px 8%;
      margin: 0 auto;
      display: flex;
      width: 50%;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      h1, div{
        margin-bottom: 12px;
      }

      address {
        color: #EBD567;
        margin-bottom: 18px;
      }

      #prompt {
        text-align: left;
      }

      form{
        display: block;
        width: 100%;
      }

      .row{
        margin-bottom: 12px;
        width: 100%;

        label {
          color: #fff;
          display: block;
          text-align: left;
        }

        input, select{
          padding: 4px;
          border-radius: 3px;
          border: 1px solid #ddd;
          display: block;
          width: 100%;
          height: 44px;
          color: #333;
          background: #fff;
        }
      }
    }

    button{
      border: none;
      background-color: @brand-danger;
      margin-top: 12px;
      padding: 4px 25px;
      border-radius: 3px;
      height: 44px;
      font-weight: 600;
    }
  }

  .search-area{
    justify-content: center;
    width: 55vw;
    min-width: 320px;
    display: flex;
    margin: 0 auto;
    height: 60px;

    button{
      background-color: @brand-danger;
      width: 80px;
      flex: 0 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      border: none;
    }

    input{
      border: none;
      padding: 4px 16px;
      flex: 1 1 auto;
      font-size: 16px;
      color: #333;
    }

    svg{
      width: 30px;
      height: 30px;
      fill: white;
    }
  }

  .success{
    margin: 16px auto;
    width: 550px;
    max-width: 95vw;
    color: #333;
    background-color: white;
    padding: 30px 45px;
    border-radius: 6px;
    box-shadow: 0px 2px 5px 1px rgba(0,0,0,.5);

    h1{
      text-shadow: none;
      margin-bottom: 18px;
      font-weight: 600;
    }

    div{
      margin-bottom: 12px;
    }

    button{
      border: none;
      color: #fff;
      background-color: @brand-danger;
      margin-top: 12px;
      padding: 4px 50px;
      border-radius: 3px;
      height: 44px;
      font-weight: 600;
    }
  }

  .copyright {
    position: absolute;
    left: 1em;
    bottom: 0.7em;

    p {
      margin: 1.2em 0;
      color: #fff;
      font-weight: 300;
      text-transform: uppercase;
      font-size: 80%;

      a {
        color: #fff;
        font-weight: 300;
      }
    }
  }
}
