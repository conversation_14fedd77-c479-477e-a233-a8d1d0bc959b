.agents, .grid-cards-container {
  .jumbo-container();
}

.grid-detail,
.map-detail,
.agents{
  margin: 1.5em 0 2.5em 0;
}

.grid-cards-container {
  margin: 1.5em 0 0.5em 0;
}

.listing {
  padding: 1.5em;
}

.grid-cards-container {
  padding: 1.5em 0.5em 0.4em 0.5em;

  .card {
    .inner {
      border: 1px solid @hr-color;
      box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
      border-top-color: lighten(@hr-color, 8%);
      border-radius: 3px;
      margin: 0 0.8em 1.3em 0.8em;
      margin-top: 0;
    }

    img {
      border-radius: 3px 3px 0 0;
      width: 100%;
    }

  }
}
