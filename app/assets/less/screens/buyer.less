.logout {
  cursor: pointer;
  padding: 20px;
  color: @icon-blue;
}

.buyer-screen-container {
  background-color: #fff;

  .buyer-info-container {
    background-color: @accent-gray;
    width: 100%;
    position: relative;
    z-index: 10;
    min-height: calc(~"100vh - @{header-height}");

    .buyer-info {
      background-color: #fff;
      margin: 15px auto;
      padding: 80px 4em 100px;
      max-width: 1440px;
      width: 85%;
      position: relative;

      .menu-top {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;

        .buyer{
          overflow: visible;
        }

        h2 {
          color: @icon-blue;
          font-weight: 400;
          letter-spacing: 2px;
        }

        .logout {
          position: absolute;
          top: 24px;
          right: 75px;
          padding: 0;
        }

        .btn-close{
          width: 65px;
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          background-color: @brand-primary;
          border-color: @brand-primary;
          color: #fff;
          font-weight: 100;
          font-size: 45px;
          line-height: 0;
        }
      }

      h3, h4 {
        color: @icon-blue;
        line-height: 1.75em;
        margin-bottom: 1em;

        &.brand-primary {
          color: @brand-primary;
        }
      }

      h3 {
        font-size: 1.5em;
        font-weight: 600;
      }

      h4 {
        font-size: 18px;
      }

      table {
        margin-top: 30px;

        td p {
          margin-top: 10px;
        }
        td.margin {
          width: 20px;
        }
        td.pic {
          width: 25%;
          max-width: 250px;
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
      }

      .n-play-footer {
        margin: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
      }
    }
  }
}
