.onboarding {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 10;
  background: url(//nplayassets.blob.core.windows.net/search2/backgrounds/landing-brown-house.jpg) no-repeat center center fixed;
  background-size: cover;
  transition: background 3s ease-in 1s;
  text-align: center;
  width: 100%;
  overflow: hidden;

  .onboarding-center {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;

    //input container and controls
    .inputs-container {
      form.search-form {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;

        .panel-text {
          position: absolute;
          top: -40px;
          width: 95%;
          height: 56px;
          margin: 0;
          font-size: 28px;
          color: #ffffff;
          text-align: right;
        }

        .homecount-text {
          color: #ffffff;
          padding-top: 5px;
        }

        .dropdown-menu {
          border-radius: 0 0 2px 2px;
          margin-top: -2px;
          min-width: 99px;
          z-index: 2;
          margin-left: -1px;
          border-top: transparent;
          padding: 3px 0;
          font-size: .9em;
          .active {
            a {
              background-color: #dedede;
              color: #000000;

            }
          }
        }

        button {
          background-color: @body-bg;
          border: none;
          border-right: 1px solid #888;
          border-radius: 2px 0 0 2px;
          color: @brand-primary;
          text-transform: uppercase;
          font-weight: 600;

          &[type="submit"] {
            background-color: @brand-primary;
            border-radius: 0 2px 2px 0;
          }
          &.btn-primary {
            border-right: none;
            padding: 0;
            height: 46px;
            width: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            align-content: center;

            svg{
              width: 20px;
              height: 20px;
              margin: 0 auto;
              flex: 1 0 auto;
            }

            border-left: 1px solid #5e5e5e;
            &:hover {
              background-color: @btn-obHover;
              text-decoration: underline;
            }
          }
        }

        .dropdown-toggle {
          padding: 13px 10px;
        }

        .form-control {
          width: 40vw;
          max-width: 400px;
          border-right: 1px solid @brand-primary-lighter !important;
          height: 46px;
          background-color: @body-bg;
          border: 1px solid transparent;

          &:focus {
            border: 1px solid transparent;
          }
        }
        .react-autosuggest__suggestions-container{
          border: 1px solid #dcdcdc;
          margin-top: 12px;
          text-align: left;

          li {
            padding: 7px 5px;
            margin: 0;
            font-size: .9em;
            &:hover {
              background-color: #dedede;
              color: #000;
            }
          }
        }

        p.terms {
          text-align: center;
          color: #fff;
          margin-top: 0.5em;

          > a {
            color: #fff;
            text-decoration: underline;
          }
        }

      }
    }
  }

  //buyer container

  .buyer-container {
    position: absolute;
    top: 1em;
    right: 2em;
    height: 100%;
    float: right;

    > div {
      float: left;
    }

    .buyer-name{
      text-overflow: ellipsis;
      max-width: 145px;
      white-space: nowrap;
      overflow: hidden;
      margin: 22px 0px 0 5px;
      a {
        color: #ffffff !important;
      }
    }

    .image-container {
      @img-ratio: 0.62;
      width: @header-height * @img-ratio;
      margin: @header-height * (1-@img-ratio)/2;
      margin-right: 3px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      label.buyer-num {
        color: #fff;
        margin: 0;
        position: absolute;
        top: 0;
        left: -0.75em;
      }
    }

    p {
      line-height: @header-height;
      color: #fff;
      padding-left: 0.5em;

      a {
        color: #fff;
        .cursor-pointer();
      }
    }
  }

  .implicit-agree{
    color: whiteSmoke;
    text-align: left;
    font-size: 11px;
    margin-top: 8px;
    margin-bottom: 8px;
    width: 100%;
    display: block;
    &:hover{
      color: whiteSmoke;
    }
  }

  .copyright {
    position: absolute;
    left: 1em;
    bottom: 0.7em;

    p {
      margin: 1.2em 0;
      color: #fff;
      font-weight: 300;
      text-transform: uppercase;
      font-size: 80%;

      a {
        color: #fff;
        font-weight: 300;
      }
    }
  }
}

.popover-terms {
  height: 200px;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
}


