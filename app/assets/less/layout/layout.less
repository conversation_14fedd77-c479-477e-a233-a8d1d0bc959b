html {
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased !important;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.004);
}

*, *:after, *:before {
  font-family: @font-family;
}

#app-container {
  z-index: 1;
  overflow: hidden;
  padding-top: @header-height;
  //  font-family: "Open Sans", sans-serif;
  font-weight: 400;
}

// ====== Layout
.layout {

  &--header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: @layout-header-z;
    height: @header-height;
    .clearfix();

    // Styling
    background: @nav-color;
    box-shadow: 0 1px 1px 0px rgba(0, 0, 0, .35);
    // box-shadow: 0 0 4px rgba(0,0,0,.14),0 4px 8px rgba(0,0,0,.28);
  }

  &--content {
    z-index: @layout-content-z;
    height: 100%;
    // overflow: hidden;
    position: relative;
  }

  // &--leftbar {
  //   z-index: @layout-leftbar-z;
  //   width: @leftbar-width;
  //   position: fixed;
  //   top: @header-height;
  //   left: 0;
  //   bottom: 0;

  //   // Styling
  //   background: @laftbar-color;
  //   box-shadow: 0 0 4px rgba(0,0,0,.14),2px 4px 8px rgba(0,0,0,.28);
  //   color: #fff;
  // }
}

.mixin-scroll-container() {
  // position: fixed;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

// ====== Layout Content Variations
.layout {

  &--full {
    top: @header-height;
    left: 0;
    right: 0;
    bottom: 0;
    position: fixed;
  }

  &--front {
    z-index: 1000;
  }

  &--1ns {
    margin-left: @leftbar-width;
    background-color: #fff;
  }

  // Used in Listing Map
  &--2s-5 {
    top: @header-height;
    // left: @leftbar-width;
    left: 0;
    bottom: 0;
    right: 70%; //calc(~"99.999999% * 0.6");
    position: fixed;

    // box-shadow: 0 0 4px rgba(0,0,0,.14),2px 4px 8px rgba(0,0,0,.28);
    box-shadow: 0 0 10px -2px rgba(0, 0, 0, 0.3);
  }

  // Used in Listing Content
  &--3r-5 {
    top: @header-height;
    left: 30%;
    bottom: 0;
    right: 0;
    position: fixed;

    box-shadow: -4px 0 5px rgba(0,0,0,0.1);
  }
  //  &--3ns-5 {
  //    overflow: hidden;
  //    margin-left: 30%; //calc(~"40% - @{leftbar-width}");
  //  }

  // Used in Agent Content
  &--2ns-3 {
    overflow: hidden;
    // margin-left: @leftbar-width;
    margin-right: @content-right-width;

    // styling
    background: #fff;
  }

  // Used in Agent Listings
  &--1s-3 {
    top: @header-height;
    width: @content-right-width;
    bottom: 0;
    right: 0;
    position: fixed;
    .mixin-scroll-container();

    // styling
    background: #03A9F4;
  }

  // Used in Detail Content
  &--2s-3 {
    top: @header-height;
    left: @leftbar-width;
    bottom: 0;
    right: @content-right-width;
    position: fixed;
    .mixin-scroll-container();
    overflow-y: auto;

    // styling
    background: #ebebeb;

    z-index: 5;
  }

  // Used in Grid Detail Listings
  &--1ns-3 {
    overflow: hidden;
    margin-left: calc(~"99.999999% - @{content-right-width}");
    background-color: #fff;
  }

  // Used in Grid Detail Photo
  // Used in map.map w/o Content
  &--2f-3 {
    top: @header-height;
    left: @leftbar-width;
    bottom: 0;
    position: fixed;
    z-index: @content-photo-z;
    @temp-width: @content-right-width + @leftbar-width;
    width: calc(~"(99.999999% - @{temp-width})");

    // styling
    background: #fff;
  }

  // Used in map.map - with content
  &--2f-4 {
    top: @header-height;
    left: @leftbar-width;
    bottom: 0;
    @temp-width: @content-right-width + @leftbar-width;
    width: calc(~"(99.999999% - @{temp-width}) / 2");
    position: fixed;
    z-index: 3;

    @media (min-width: @largeDesktop) {
      @temp-width: @content-right-width + @leftbar-width;
      width: calc(~"((99.999999% - @{temp-width}) / 2) + @{content-gutter-width}");
    }

    @media (min-width: @xlargeDesktop) {
      width: calc(~"((99.999999% - @{temp-width}) / 2) + @{content-gutter-width-lg}");
    }

    // styling
    background: #fff;
  }

  &--2f-3, &--2f-4 {
    display: flex;
    flex-direction: column;

    .map-container {
      flex-grow: 1;
    }
  }

  // Used in map.content
  &--2s-4 {
    top: @header-height;
    right: @content-right-width;
    bottom: 0;

    @temp-width: @content-right-width + @leftbar-width;
    width: calc(~"(99.999999% - @{temp-width}) / 2");

    position: fixed;

    z-index: 5;

    .scroll-container {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      .mixin-scroll-container();
    }

    // ==== Apply Media Queries ====
    @media (max-width: (@desktop)) {
      left: @leftbar-width;
      @temp-width: @content-right-width + @leftbar-width;
      width: calc(~"99.999999% - @{temp-width}");
    }

    @media (min-width: @largeDesktop) {
      @temp-width: @content-right-width + @leftbar-width;
      width: calc(~"((99.999999% - @{temp-width}) / 2) - @{content-gutter-width}");
    }

    @media (min-width: @xlargeDesktop) {
      width: calc(~"((99.999999% - @{temp-width}) / 2) - @{content-gutter-width-lg}");
    }

    // styling
    background: #fff;
    //    padding: 1em;
  }

  // Used in map.subcontent
  &--1f-4 {
    top: @header-height;
    bottom: 0;

    @temp-width: @content-right-width + @leftbar-width;
    width: calc(~"(99.999999% - @{temp-width}) / 2");
    right: calc(~"((99.999999% - @{temp-width}) / 2) + @{content-right-width}");

    position: fixed;
    // .mixin-scroll-container();
    z-index: 6;

    // ==== Apply Media Queries ====
    @media (max-width: (@desktop)) {
      left: @leftbar-width;
      @temp-width: @content-right-width + @leftbar-width;
      width: calc(~"99.999999% - @{temp-width}");
    }

    @media (min-width: @largeDesktop) {
      @temp-width2: @content-right-width - @content-gutter-width;
      width: calc(~"((99.999999% - @{temp-width}) / 2) - @{content-gutter-width}");
      right: calc(~"((99.999999% - @{temp-width}) / 2) + @{temp-width2}");
    }

    @media (min-width: @xlargeDesktop) {
      @temp-width2: @content-right-width - @content-gutter-width-lg;
      width: calc(~"((99.999999% - @{temp-width}) / 2) - @{content-gutter-width-lg}");
      right: calc(~"((99.999999% - @{temp-width}) / 2) + @{temp-width2}");
    }

    // styling
    background: #fff;
  }

  // Used in map.subcontent - hide click container
  &--1f-4-hide {
    top: @header-height;
    bottom: 0;
    left: @leftbar-width;
    position: fixed;
    z-index: 4;
    background: transparent;

    @temp-width: @content-right-width + @leftbar-width + @content-gutter-width-lg;
    width: calc(~"(99.999999% - @{temp-width}) / 2");
  }
}

.hidden- {
  &sm {
    @media screen and (max-width: (@tablet - 0.1)) {
      display: none !important;
    }
  }

  &md {
    @media screen and (min-width: @tablet) and (max-width: (@desktop - 0.1)) {
      display: none !important;
    }
  }

  &lg {
    @media screen and (min-width: @desktop) and (max-width: (@largeDesktop - 0.1)) {
      display: none !important;
    }
  }

  &xl {
    @media screen and (min-width: @largeDesktop) {
      display: none !important;
    }
  }
}

svg {
  pointer-events: none;
}
