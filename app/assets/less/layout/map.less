// Circle
@circle-stroke-color: @brand-darkblue;
@circle-fill-color: #1976d2;

// Small marker
@small-icon-color: #444;

// General marker
@general-icon-color: @brand-primary;
@icon-dot-size: 8px;

// Make sure it is even number && (% 2 = even) as well
@icon-size: 28px;
@icon-size-sm: 24px;
@icon-pulse-size: 38px;

// Active marker
@active-icon-color: @icon-blue;

// Color Variants
@viewed-icon-color: @brand-viewed;
@favorite-icon-color: @brand-fav;
@dislike-icon-color: #AAA;
@tagged-icon-color: @brand-tag;
@hovered-icon-color: #1976d2;

//pop-up colors
@brand-green: #3cb878;
@pop-up-border-color: #f2664d;

// -webkit-tap-highlight-color: transparent;
//   -webkit-font-smoothing: antialiased;
.map-invisible {
  display: none;
}

.map {
  height: 100%;
  width: 100%;

  .leaflet-control-custom {
    position: absolute;
    left: 10px;
  }

  .leaflet-control-recenter {
    top: 74px;
    box-sizing: border-box;

    @keyframes control-pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
        border-color: @brand-primary;
        box-shadow: @brand-primary 0 0 8px;
      }
      100% {
        transform: scale(1);
      }
    }

    &.pulse {
      animation-name: control-pulse;
      animation-duration: 1s;
      animation-fill-mode: both;
      animation-iteration-count: infinite;
    }

    &.pulse:hover {
      animation: none;
    }

    a {
      svg.leaflet-control-svg {
        margin: 3px;
        height: 20px;
        width: 20px;
      }
    }
  }

  .leaflet-control-layers {
    top: 138px;
  }

  .hints {
    z-index: 1;
    position: relative;

    > div {
      position: absolute;

      &.top-hint {
        padding: 5px 10px;
        background: fade(@brand-blue, 85%);
        top: 74px - 45px;
        width: @content-right-width;
        height: 45px + 28px - 2px;
        left: 38px + 16px;
      }

      &.bottom-hint {
        padding: 5px 10px;
        background: fade(@brand-blue, 85%);
        top: 74px + 26px + 2px;
        width: @content-right-width;
        height: 100px;
        left: 38px + 16px;

        div.got-it-container {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;

          a.btn {
            color: #fff;
            font-size: 90%;

            &:hover, &.active {
              color: #fff
            }
          }
        }
      }

      &.top-hint-arrow {
        width: 16px;
        height: 26px;
        top: 74px - 36px + 36px + 28px - 2px - 26px;
        left: 38px;
      }

      &.bottom-hint-arrow {
        width: 16px;
        height: 26px;
        top: 74px + 26px + 2px;
        left: 38px;
      }

      &.top-hint-arrow, &.bottom-hint-arrow {
        border-right: 16px solid fade(@brand-blue, 85%);
        border-top: 13px solid transparent;
        border-bottom: 13px solid transparent;
      }

      p {
        margin: 0;
        padding: 0;
        color: #fff;
        font-weight: normal;
        font-size: 12px;

        &.title {
          text-transform: uppercase;
          font-weight: 600;
        }
      }
    }
  }
}

.map-container {
  position: relative;
    width: 100%;
  height: 100%;

  .map-north {
    position: absolute;
    top: 15px;
    left: 3.25em;
    width: 2em;
    height: 2em;
    pointer-events: none;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  .map-logo {
    position: absolute;
    bottom: 1.25em;
    left: 2em;
    width: 11em;
    height: 2em;
    a{
      width: 11em;
      display: block;
      height: 2em;
    }
    svg {
      width: 100%;
      height: 100%;
    }
  }

  .map-exit {
    position: absolute;
    bottom: 1.5em;
    right: 2.6em;
    box-shadow: -2px 1px 3px rgba(0, 0, 0, 0.25);

    svg {
      stroke-width: 0;
      margin-bottom: -2px;
    }
  }
}

.map-alert {
  position: absolute;
  text-align: center;
  top: 0.6em;
  width: 100%;
  z-index: 1000;

  p {
    margin: 0;
    padding: 0.15em 0 0.21em 0;
    font-size: 13px;
    letter-spacing: 0.07em;
    background: rgba(34, 57, 73, 0.6);  // @brand-blue
    color: #fff;
    display: inline-block;
    width: 70%;
    border-radius: 3px
  }
}

.map-rateplug-subheader {
  text-align: center;
  width: 100%;
  z-index: 1001;
}

.map-zoommsg {
  position: absolute;
  text-align: center;
  bottom: 55px;
  left: 10px;
  right: 10px;
  z-index: 1000;

  p {
    margin: 0;
    padding: 10px 20px;
    font-size: 16px;
    background: rgba(34, 57, 73, 0.6);  // @brand-blue
    color: #fff;
    display: inline-block;
    border-radius: 10px
  }
}

.map-circle-editor {
  stroke: @circle-stroke-color;
  fill: @circle-fill-color;
}

.map-circle-radiusline {
  stroke: @circle-stroke-color;
  stroke-width: 3;
  stroke-opacity: 1.0;
}

.map-circle-center {
  z-index: 10000 !important;
  border-radius: 50%;
  background: #fff;
  cursor: move;
  border: 2px solid @circle-stroke-color;
  color:  @brand-darkblue;

  &:hover {
    z-index: 10001 !important;
  }

  .toolsvg {
    position: absolute;
    height: 16px;
    width: 16px;
    fill: #fff;
  }

  .tooltipsvg {
    z-index: 10001 !important;
    position: absolute;
    top: -60px;
    left: -75px;
    height: 20px;
    width: 20px;
    stroke: #fff;
    stroke-width: 1;
    fill: @brand-darkblue;
  }

  .tooltipspan {
    position: absolute;
    top: -50px;
    left: -65px;
    width: 160px;
    margin: 0;
    padding: 5px 5px;
    text-align: center;
    font-size: 10px;
    letter-spacing: 0.07em;
    line-height: 1.3;
    background: @brand-darkblue;
    color: #fff;
  }
}

.map-circle-center-drag {
  background: @circle-stroke-color;
  border: 2px solid #fff;
  color: #fff;

  .toolsvg {
    fill: @brand-darkblue;
  }
}

.map-circle-radius {
  z-index: 10000 !important;
  width: 200px;
  background: #fff;
  border: 2px solid @circle-stroke-color;
  cursor: move;
  transition: transform .25s ease-out;

  .toolsvg {
    position: absolute;
    top: 1px;
    left: 1px;
    height: 22px;
    width: 22px;
    fill: @brand-darkblue;
  }

  .toolspan {
    position: absolute;
    top: -2px;
    left: 26px;
    width: 100px;
    height: 28px;
    text-align: center;
    margin: 0;
    padding: 3px 5px;
    font-size: 10px;
    letter-spacing: 0.07em;
    background: @brand-darkblue;
    color: #fff;
    border-left: 0px;
  }

  .tooltipsvg {
    z-index: 10001 !important;
    position: absolute;
    top: -56px;
    left: -75px;
    height: 20px;
    width: 20px;
    stroke: #fff;
    stroke-width: 1;
    fill: @brand-darkblue;
  }

  .tooltipspan {
    position: absolute;
    top: -46px;
    left: -65px;
    width: 150px;
    margin: 0;
    padding: 5px 5px;
    text-align: center;
    font-size: 10px;
    letter-spacing: 0.07em;
    line-height: 1.3;
    background: @brand-darkblue;
    color: #fff;
  }

}

.map-circle-radius-drag {
  z-index: 10000 !important;
  background: @circle-stroke-color;
  border: 2px solid @brand-darkblue;
  transition: none;

  .toolsvg {
    fill: #fff;
    stroke: #fff;
  }

  .toolspan,.tooltipspan {
    display: inline-block;
  }
}

.easeout {
  transition: opacity 3s ease;
}

.map-popup {

  pointer-events: none;

  *{
    pointer-events: none;
  }

  .leaflet-popup-content-wrapper {
    border-radius: 2px;
    border: 0px;
    box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
  }
  .popup-broker-logo{
    max-width: 150px;
    max-height: 22px;
  }
  .leaflet-popup-content {
    font-size: .85em;
    width: 275px !important;
    padding: 5px 5px 5px 5px;
    .clearfix();
    .popup-top {
      border-bottom: 1px solid @outline;
      padding-bottom: 3px;
      .clearfix();
      .sale-type {
        padding: 2px 8px;
        color: white;
        border-radius: 40px;
        &.sale {
          background-color: @brand-green;
        }
        &.rent {
          background-color: @icon-blue;
        }

      }
    }

    .popup-content {
      display: flex;
      padding-top: 6px;

      .popup-broker, popup-agent{
        line-height: 12px;
      }

      h4 {
        font-size: 1.6em;
        float: none;
        display: block;
      }
      div {
        font-size: 13px;
        float: none;
        display: block;
        margin-bottom: 6px;
        line-height: 13px;
      }
    }
  }

  .leaflet-popup-tip-container {
    display: none;
  }

  .popup-listing-image {
    width: 100px;
    flex: 0 0 auto;
    height: 76px;
    float: none;
    margin-right: 8px;
  }

  p, h4 {
    margin: 0;
    min-width: 70px;
    float: none;
    font-size: 13px;
    margin-bottom: 6px;
  }

  h4 {
    font-weight: bold;
  }
}

// ===== Icons
.map-icon-svg {
  // Defaults
  color: @brand-primary;
  width: 30px;
  height: 46px;

  .pin {
    display: none;
    width: 30px;
    height: 36px;
    margin-top: 5px;
    path {
      fill: @brand-primary;
    }
  }
  .dot {
    display: none;
    width: 10px;
    height: 10px;
    margin-left: 10px; // (30px (width of icon) - 10px (width of dot)) / 2
  }

  .label {
    pointer-events: none;
    color: #000000;
    background: #fff;
    border-radius: 5px;
    display: none;
    font-weight: bold;
    font-size: 11px;
    text-align: center;
    padding: 2px 5px 2px 5px;
    user-select: none;
    position: absolute;
    top: 41px;
    left: -6px;
  }
  // Each pin is always ONLY of: "general", "small" , or "active"
  //  These will set position and (if necessary) color

  &.general {
    .pin {
      display: block;
      top: 5px;
    }
    .dot {
      display: none;
    }
  }

  &.small {
    .pin {
      display: none;
    }
    .dot {
      position: absolute;
      display: inline-block;
      top: 36px;
    }
    .label {
      display: none;
    }
  }

  &.general, &.tagged, &.favorite {
    .label {
      display: none;
    }

    &.zoom14, &.zoom15, &.zoom16, &.zoom17, &.zoom18, &.zoom19, &.zoom20, &.zoom21, &.zoom22 {
      .label {
        display: inline-block;
      }
    }
  }

  // A pin may OPTIONALLY be ONLY one of these "viewed",  "tagged", "favorite", "dislike"
  // Generally, this will mean only setting a color
  &.viewed {
    color: @viewed-icon-color;
    svg {
      path {
        fill: @viewed-icon-color;
      }
    }
  }

  &.dislike {
    color: @dislike-icon-color;
    svg {
      path {
        fill: @dislike-icon-color;
      }
    }
  }

  &.favorite {
    .pin, .dot {
      display: inherit;
    }
    .dot{
      display: none;
    }
    color: @favorite-icon-color;
    svg {
      path {
        fill: @favorite-icon-color;
      }
    }
  }

  &.home-pin {
    z-index: 10001 !important;
    pointer-events: none;

    .pin, .dot {
      display: inherit;
    }
    .pin {
      width: 22px;
      height: 22px;
      margin: 0;
    }
    .dot{
      display: none;
    }
    color: @brand-info;
    svg {
      path {
        fill: @brand-info;
      }
    }
  }

  &.tagged {
    .pin, .dot {
      display: inherit;
    }
    color: @tagged-icon-color;
    svg {
      path {
        fill: @tagged-icon-color;
      }
    }
  }

  // Active is the 2nd most important pin color (behind "hover")
  &.active {
    // Reason for z-index number
    // https://github.com/leaflet-extras/leaflet-map/blob/master/leaflet-marker.html#L655-L657
    z-index: 999 !important;

    color: @active-icon-color !important;
    svg {
      path {
        fill: @active-icon-color !important;
      }
    }
    .pin {
      display: block;
      margin-top: 5px;
    }
    .dot {
      display: none;
    }
  }

  /// General hover is just a color change, but it overrides all other colors
  &.hover {
    color: @hovered-icon-color !important;
    svg {
      path {
        fill: @hovered-icon-color !important;
      }
    }
  }

  /// Small hover must change color and reposition the pin and dot
  &.small-hover {
    color: @hovered-icon-color !important;
    svg {
      path {
        fill: @hovered-icon-color !important;
      }
    }
    .pin {
      display: block !important;
      margin-top: 0px;
    }
    .dot {
      display: block !important;
      margin-top: 0px;
    }
  }
}

.map-icon-pulse {
  .pulse {
    pointer-events: none;
    display: inline-block;

    &:after {
      content: "";
      border-radius: 50%;
      height: @icon-pulse-size;
      width: @icon-pulse-size;
      position: absolute;
      margin: -((@icon-pulse-size+@icon-dot-size) / 2) 0 0 -(@icon-pulse-size / 2);
      box-shadow: 0 0 1px 2px @active-icon-color;

      animation: pulsate 0.8s ease-out;
      animation-iteration-count: infinite;
      animation-delay: 0;
    }
  }
}

.map-icon {
  // General styles for pin and pulse
  .pin {
    top: 10px;
    pointer-events: none;
    display: none;
    transform: rotate(-45deg);
    border-radius: 50% 50% 50% 0;
    width: @icon-size-sm;
    height: @icon-size-sm;
    background: @general-icon-color;

    &:after {
      width: round(@icon-size-sm * .75);
      height: round(@icon-size-sm * .75);
      line-height: round(@icon-size-sm * .70);
      transform: rotate(45deg) translate(@icon-size-sm * .18);

      text-align: center;
      display: block;
      background: #fff;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
    }
  }

  .dot {
    pointer-events: none;
    display: inline-block;
    line-height: @icon-dot-size;
    width: @icon-dot-size;
    height: @icon-dot-size;
  }

  .label {
    pointer-events: none;
    color: #000000;
    background: #fff;
    border-radius: 5px;
    display: none;
    font-weight: bold;
    font-size: 11px;
    text-align: center;
    padding: 2px 5px 2px 5px;
    user-select: none;
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.num-0 {
    .pin:after {
      content: "–";
    }
  }

  &.num-1 {
    .pin:after {
      content: "1";
    }
  }

  &.num-2 {
    .pin:after {
      content: "2";
    }
  }

  &.num-3 {
    .pin:after {
      content: "3";
    }
  }

  &.num-4 {
    .pin:after {
      content: "4";
    }
  }

  &.num-5 {
    .pin:after {
      content: "5+";
    }
  }

  &.small {
    top: -(@icon-dot-size / 2);
    left: -(@icon-dot-size / 2);
    line-height: @icon-dot-size;

    .pin {
      display: none;
    }

    .dot {
      background: @small-icon-color;
      border-radius: 50%;
      border: 2px solid #fff;
    }

    .label {
      display: none;
    }
  }

  &.general, &.tagged, &.favorite {
    top: -(@icon-size-sm + 2);
    left: -(@icon-size-sm / 2);
    line-height: 1;

    .pin {
      display: inline-block;

      // Animation
      animation-name: bouncepin;
      animation-duration: 0.4s;
    }

    .dot {
      display: none;
    }

    .label {
      display: none;
    }

    &.zoom14, &.zoom15, &.zoom16, &.zoom17, &.zoom18, &.zoom19, &.zoom20, &.zoom21, &.zoom22 {
      .label {
        display: inline-block;
      }
    }
  }

  &.viewed {
    .pin {
      background: @viewed-icon-color;
    }
    .dot {
      color: @viewed-icon-color;
    }
  }

  &.dislike {
    .pin {
      background: @dislike-icon-color;
    }
    .dot {
      color: @dislike-icon-color;
    }
  }

  &.tagged {
    .pin {
      background: @tagged-icon-color;

      &:after {
        content: "\f004";
        color: @tagged-icon-color;
        text-align: center;
        line-height: 20px;
        font-size: 14px;
        font-family: FontAwesome;
        transform: rotate(45deg) translate(@icon-size-sm * .18, 0);
      }
    }
    .dot {
      color: @tagged-icon-color;
    }
  }
  &.tagged.active {
    .pin {
      &:after {
        color: @active-icon-color;
      }
    }
  }
  &.tagged.hover {
    .pin {
      background: @hovered-icon-color !important;
      &:after {
        color: @hovered-icon-color !important;
      }
    }
  }

  &.favorite {
    .pin {
      background: @favorite-icon-color;

      &:after {
        content: "\2605";
        color: @favorite-icon-color;
        text-align: center;
        line-height: 17px;
        font-size: 18px;
        font-family: serif;
        padding-right: 1px;
      }
    }
    .dot {
      color: @favorite-icon-color;
    }
  }
  &.favorite.active {
    .pin {
      &:after {
        color: @active-icon-color;
        line-height: 18px;
        font-size: 20px;
      }
    }
  }
  &.favorite.hover {
    .pin {
      background: @hovered-icon-color !important;
      &:after {
        color: @hovered-icon-color !important;
      }
    }
  }

  // Active is the 2nd most important pin color (behind "hover")
  &.active {
    top: -(@icon-size + 3);
    left: -(@icon-size / 2);
    line-height: 1;

    // Reason for z-index number
    // https://github.com/leaflet-extras/leaflet-map/blob/master/leaflet-marker.html#L655-L657
    z-index: 999 !important;

    .pin {
      display: inline-block;
      width: @icon-size;
      height: @icon-size;
      background: @active-icon-color !important;

      // Animation
      animation-name: bouncepin;
      animation-duration: 0.4s;

      &:after {
        width: round(@icon-size * .75);
        height: round(@icon-size * .75);
        line-height: round(@icon-size * .70);
        transform: rotate(45deg) translate(@icon-size * .18);
      }
    }

    .dot {
      display: none;

      &:after {
        display: inline-block;
      }

    }

    .label {
      display: none;
    }

    &.zoom14, &.zoom15, &.zoom16, &.zoom17, &.zoom18, &.zoom19, &.zoom20, &.zoom21, &.zoom22 {
      .label {
        display: inline-block;
        top: 35px;
      }
    }
  }

  /// General hover is just a color change, but it overrides all other colors
  &.hover {
    .pin {
      background: @hovered-icon-color !important;
    }
    .dot {
      color: @hovered-icon-color !important;
    }
  }

  &.small-hover {
    top: -(@icon-size-sm + @icon-dot-size + 2);
    left: -(@icon-size-sm / 2);
    line-height: 1;
    height: 42px;

    .pin {
      display: inline-block;
      background: @hovered-icon-color !important;
    }

    .dot {
      margin-top: 4px;
      transform: translate(100%, 2px);
      background: @hovered-icon-color !important;
    }
  }
}

.map-icon-current-location {

  .carouselWrapper {
    border-top: none;
    margin: 0;
    padding: 0;
    overflow: visible;
    position: relative;
  }
  .carouselWrapper .photoCarousel {
    border-top: none;
    clear: both;
    margin-top: -30px;
    padding: 10px 10px;
    overflow: scroll;
    text-align: center;
    white-space: nowrap;
  }
  .carouselWrapper .photoCarousel .photoThumb {
    display: inline-block;
    margin-left: 6px;
  }
  .carouselWrapper .photoCarousel .photoThumb:first-child {
    margin-left: 0;
  }

  .dot {
    width: 100%;
    position: relative;
    animation-name: fade-dot;
    animation-fill-mode: both;
    animation-iteration-count: 1;
    animation-timing-function: ease;
    animation-duration: 0.5s;

    margin-left: -7px;
    margin-top: -7px;
  }

  .dot .point {
    width: 14px;
    height: 14px;
    background: #2ba7d9;
    border: 1px solid #0f7baf;
    display: block;
    left: 50%;
    position: relative;
    text-align: center;
    top: 0;
    border-radius: 8px;
  }

  .dot .pulse {
    background: transparent;
    border: 1px solid #60c9e9;
    display: block;
    height: 64px;
    left: -26px;
    position: absolute;
    top: -26px;
    width: 64px;
    z-index: -1;
    animation: pulse-dot 2s ease-in-out infinite;
    border-radius: 32px;
    box-shadow: #60c9e9 2px 2px 40px 0px;
  }


  @keyframes fade-dot {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes pulse-dot {
    0% {
      transform: scale(0.32);
      opacity: .8;
    }
    50% {
      opacity: 1;
    }
    85% {
      transform: scale(1);
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
}

@keyframes grow {
  from {
    opacity: 0;
    transform: scale(0.1);
  }
  50% {
    opacity: .5;
    transform: scale(1.3);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulsate {
  0% {
    transform: rotateX(50deg) scale(0.1, 0.1);
    opacity: 0.0;
  }
  50% {
    opacity: 1.0;
  }
  100% {
    transform: rotateX(50deg) scale(1.2, 1.2);
    opacity: 0;
  }
}

@keyframes bouncepin {
  0% {
    transform: scale(0.1) rotate(-45deg);
  }
  70% {
    transform: scale(1.2) rotate(-45deg);
  }
  100% {
    transform: scale(1) rotate(-45deg);
  }
}

.school-icon {
  //  animation: grow 2s;
  background-color: white;
  border-radius: 50%;
  .cursor-pointer();

  svg {
    width: 100%;
    height: 100%;
  }
}

// === Control button
.custom-control {
  background-color: #fff;
  border: 1px solid #999;
  border-color: rgba(0, 0, 0, .4);
  border-radius: 3px;
  box-shadow: none;
}

.map-center-control {
  padding: 3px;
  display: block;
  height: 26px;
  width: 26px;

  span {
    height: 20px;
    width: 20px;
    display: block;
    position: relative;
    border: 3px solid #404040;
    border-radius: 100%;

    &:before {
      content: '';
      height: 8px;
      width: 8px;
      border: 2px solid #404040;
      border-radius: 100%;
      position: absolute;
      top: 3px;
      left: 3px;
    }
  }

  &:hover, &:focus, &:visited {
    text-decoration: none;
  }
}

// Offset to give agent picture room
@offset-top: 50px;
.map {
  .leaflet-control-recenter {
    top: 74px + @offset-top;
  }
  .leaflet-control-layers {
    top: 138px + @offset-top;
  }
  .leaflet-top {
    top: @offset-top;
  }
}
.map-alert {
  left: 50px;
}

.map{

  .yelp-result{
    background-image: url('//nplayassets.blob.core.windows.net/search2/yelp-pin-thicker-stroke.png');
    color: white;
    width: 31px !important;
    height: 43px !important;
    font-weight: bold;
    text-shadow: 1px 1px black;
    border-radius: 0%;
    margin-left: -16px !important;
    margin-top: -43px !important;
    background-size: 100%;
    display: none;
  }


  .yelp-result-active{
    background-image: url('//nplayassets.blob.core.windows.net/search2/selected-yelp-pin-thicker-stroke.png');
  }

  .yelp-popup{
    min-width: 320px;
  }

  .yelp-business{

    display: flex;
    padding: 0;

    .yelp-picture{
      flex: 0 0 auto;
      display: block;

      img{
        width: 75px;
        height: 75px;
      }
    }

    .yelp-business-content{
      flex: 1 1 auto;
      padding-left: 1em;
    }

    .yelp-reviews{
      display: flex;
      align-items: center;

      .yelp-business-rating{
        display: inline-block;
        margin-bottom: 4px;

        img{
          width: 102px;
        }
      }

      .yelp-business-review-count{
        margin-left: 8px;
        display: inline-block;
        font-style: italic;
      }
    }

    .yelp-name{
      font-size: 18px;
      text-decoration: none;
      color: #000;
      line-height: 18px;
    }

    .yelp-categories{

      line-height: 14px;
      margin: 4px 0px;

      .text-muted{
        color: #999;
      }

    }

    .yelp-price{
      line-height: 14px;

      .text-muted{
        color: #999;
      }
    }
  }
}

.map-container{

  &.show-yelp{

    .leaflet-marker-icon{
      display: none;
    }

    .yelp-result{
      display: block;
    }

    .map-icon.active, .map-icon-pulse{
      display: block !important;
    }
  }

}

#pin-legend-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  border: 1px solid #f7f7f7;
  box-shadow: 0 1px 7px rgba(0, 0, 0, 0.4);
  border-radius: 4px;
  background: #fff;
  padding: 10px;
  max-width: 230px;

  h4 {
    font-size: 14px;
    margin-bottom: 2px;
  }

  p.pin-legend-tagline {
    font-size: 10px;
    line-height: 10px;
    margin-bottom: 5px;
  }

  table {
    tr {
      margin-top: 10px;

      td:first-child {
        padding-right: 10px;

        div.leaflet-marker-icon {
          &.active .pin {
            width: 24px;
            height: 24px;

            &:after {
              width: 18px;
              height: 18px;
              transform: rotate(45deg) translate(@icon-size-sm * .18);
            }
          }
        }
      }
      td:nth-child(2) {
        padding: 4px 0 3px;

        p {
          margin: 0;
          font-size: 13px;
        }

        p.desc {
          font-size: 10px;
          line-height: 10px;
        }
      }
    }
  }
}

#app-container:not(.mobile) {
  .--highlight\:SpecialFinancePrograms__VA .map-container .--tag\:SpecialFinancePrograms__VA,
  .--highlight\:SpecialFinancePrograms__FHA .map-container .--tag\:SpecialFinancePrograms__FHA,
  .--highlight\:SpecialFinancePrograms__FHACondo .map-container .--tag\:SpecialFinancePrograms__FHACondo,
  .--highlight\:SpecialFinancePrograms__USDA .map-container .--tag\:SpecialFinancePrograms__USDA,
  .--highlight\:SpecialFinancePrograms__Assumable .map-container .--tag\:SpecialFinancePrograms__Assumable {
    .pin, &.small .dot {
      background: @brand-specialfinancing !important;
    }
    .dot {
      color: @brand-specialfinancing !important;
    }
  }
}

#app-container.mobile {
  .--highlight\:SpecialFinancePrograms__VA .map-container .--tag\:SpecialFinancePrograms__VA,
  .--highlight\:SpecialFinancePrograms__FHA .map-container .--tag\:SpecialFinancePrograms__FHA,
  .--highlight\:SpecialFinancePrograms__FHACondo .map-container .--tag\:SpecialFinancePrograms__FHACondo,
  .--highlight\:SpecialFinancePrograms__USDA .map-container .--tag\:SpecialFinancePrograms__USDA,
  .--highlight\:SpecialFinancePrograms__Assumable .map-container .--tag\:SpecialFinancePrograms__Assumable {
    color: @brand-specialfinancing;

    &.map-icon-svg.active {
      color: @brand-specialfinancing !important;
    }

    &.small .dot {
      fill: @brand-specialfinancing !important;
    }
  }
}