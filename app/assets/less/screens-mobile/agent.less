.agent-profile-mobile-outer-container{
  position: absolute;
  height: 100vh;
  background: white;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 10;
  right: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  .listing-mobile{
    position: fixed;
  }

  &.listing-layout{
    .menu-top.fixed {
      display: none !important;
    }
  }
  .menu-top.fixed{
    background-color:#fff;
    border:none;
    top: 0;
    height:3em;
    transition: transform 0.2s ease-out 0s;
    transform: translateY(0%);
    z-index: 11;
    padding: 0;
    &.scroll-hide{
      transition: transform 0.5s ease-in 0s;
      transform: translateY(-350%);
    }
    &.no-search-form{
      top: 3em;
      /*padding-left: 2.5em;*/
    }
    .details-holder{
      width:100%;
      height:100%;
      padding-top:.5em;
      border-bottom:.1em solid @accent-gray;
      box-shadow: 0px 3px 3px rgba(0,0,0,.1);

      .social-container {
        margin-top: .3em;
      }

      .close-button-cross {
        width: 38px;
        height: 38px;
        margin-top: -4px;
        background-color: @brand-primary;
        svg {
          fill: #fff;
          stroke: #fff;
        }
      }
      .contact-button{
        padding:4px 6px;
        font-size:11px;
        font-weight: 400;
        margin:0 6px 0 0;
        vertical-align:top;
        margin-left: .6em;
        background-color: @brand-primary;
      }
    }
    .social-icon{
      width:25px;
      height:25px;

      &.twitter-icon{
        fill: #598DCA !important;
      }

      &.facebook-icon{
        fill: #3A589B !important;
      }

      &.instagram-icon{
        fill: #833AB4 !important;
      }

      &.youtube-icon{
        fill: #c4302b !important;
      }

      &.google-icon{
        fill: #4285F4 !important;
      }
    }
  }
  .featured-listings{
    padding-top:0;
    position: initial;
  }
  .featured-header{
    svg{
      height:3em;
      padding:.2em;
    }
  }
}
.agent-screen-container-mobile {
  background: #fff;
}
