.buyer-screen-container {
  position: relative;
  background: #fff;
  z-index: 9;
  border-top: 4em solid @brand-darkblue;

  .logout{
    top: 15px;
    right: 10px;
    position:absolute;
    padding:8px 12px;
  }

  .buyer-info-container {
    .buyer-info {
      position: relative;
      width: 100%;
      padding: 60px 1em 0;
      top: auto;
      left: auto;
      right: auto;
      bottom: auto;
      margin: 0;



      p {
        margin-top: 10px;
      }

      img {
        width: 100%;
        margin-top: 10px;
        margin-bottom: 5px;
      }
    }
  }

  .buyer-footer {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
  }

  .listing-mobile {
    position: fixed;
  }
}
