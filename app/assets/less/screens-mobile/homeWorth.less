.home-worth{
  margin-top: 0;

  header{

    .contact{
      display: flex;

      .icons{
        display: flex;

        a:first-child{
          margin-right: 2vmin;
        }
      }

      svg{
        fill: white;
        width: 8vmin;
        height: 8vmin;
      }

      .phone, .email{
        display: none;
      }

    }
  }

  .content {
    &.show-search {
      display: block;

      .home-search {
        margin-top: 100px;
        
        h1{
          font-size: 38px;
          line-height: 50px;
          margin-bottom: 32px;
        }
      }
    }
  }


  .found-home{
    flex-direction: column;

    #found-home-map{
      width: 100%;
      height: 20vh;

      .leaflet-control-attribution {
        display: none;
      }

      .leaflet-marker-icon.marker-label {
        display: none;
      }
    }

    #found-home-form{
      padding: 30px 16px 65px;
      width: 100%;
    }

    button{
      width: 100%;
    }
  }

  .search-area{
    margin: 0 auto;
    height: 40px;
    width: 90vw;
    min-width: auto;

    button{
      background-color: @brand-danger;
      display: flex;
      justify-content: center;
      align-items: center;
      border: none;
    }

    input{
      border: none;
      padding: 4px 16px;
      flex: 1 1 auto;
      font-size: 16px;
      color: #333;
    }

    button{
      width: 60px;
      flex: 0 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    svg{
      width: 20px;
      height: 20px;
      fill: white;
    }
  }

  .success{
    padding: 25px 15px;
    width: 95vw;

    h1 {
      line-height: 35px;
      margin-bottom: 12px;
    }
  }

  .copyright {
    left: 0;
    right: 0;
  }
}
