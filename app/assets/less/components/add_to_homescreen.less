.ath-container{
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  margin: 0 !important;
  background-color: rgba(0,0,0,0.25) !important;
  width: 100% !important;
  height: 100% !important;
  box-shadow: none !important;
  transform: none !important;

  &:before{
    content: 'X' !important;
    color: #fff !important;
    font-size: 35px !important;
    background-color: transparent !important;
    background-image: none !important;
    left: 0 !important;
    margin: 0 !important;
    position: absolute !important;
    top: 0 !important;
    line-height: 50px !important;
    height: 50px !important;
    width: 50px !important;
    padding: 0 !important;
    font-weight: 200 !important;
  }

  .ath-content{
    position: relative;
    top: 25%;
  }

  &.ath-android{
    .ath-install-icon{
      svg{
        width: 20px;
        height: 20px;
      }
    }
    .ath-install-icon-ios{
      display: none;
    }
    .ath-arrow{
      animation: bounceup 2s infinite;
      top: 10px;
      right: 0;
      transform: translateY(0) rotate(180deg);
    }
  }

  &.ath-ios{

    .ath-install-icon{
      svg{
        width: 35px;
        height: 35px;
      }
    }
    .ath-install-icon-android{
      display: none;
    }

    .ath-arrow{
      animation: bouncedown 2s infinite;
      bottom: 10px;
      left: 50%;
      transform: translate(-50%, 0);
    }

    &:after{
      content: none !important;
    }

    &.ath-tablet{
      .ath-arrow{
        top: 10px;
        animation: bounceup 2s infinite;
        right: 115px;
        left: auto;
        transform: translateY(0) rotate(180deg);
        bottom: auto;
      }
    }
  }

  .ath-app-logo{
    transform: translateX(3%);
    margin-bottom: 25px;
  }

  .ath-install-icon{
    svg{
      fill: #fff;
    }
  }

  .ath-app-title{
    color: #fff;
    font-weight: 300;
  }

  .ath-install-icon{
    display: inline-block;
  }

  .ath-message{
    color: #fff !important;
    text-shadow: none !important;
    font-weight: 300;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 1px;
    padding: 0px 20px;
    margin-top: 35px;
  }

  .ath-arrow{
    svg{
      height: 50px;
      width: 50px;
      fill: #fff;
    }
    position: fixed;

    @keyframes bouncedown {
      0%, 20%, 50%, 80%, 100% {
        transform: translate(-50%, 0);
      }
      40% {
        transform: translate(-50%, -30px);
      }
      60% {
        transform: translate(-50%, -15px);
      }
    }

    @keyframes bounceup {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY( 0) rotate(180deg);
      }
      40% {
        transform: translateY(30px) rotate(180deg);
      }
      60% {
        transform: translateY(15px) rotate(180deg);
      }
    }
  }

  hr{
    width: 60%;
    opacity: 0.5;
    height: 1.5px;
  }
}
