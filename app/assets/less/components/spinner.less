@spinner-dot-size: 8px;

.spinner-component {
  display: inline-block;
  font-size: @spinner-dot-size;
  height: @spinner-dot-size;
  position: relative;
  text-align: center;
  vertical-align: middle;
}

.spinner__dot {
  animation: pulse 1s infinite ease-in-out;
  height: 1em;
  width: 1em;
  border-radius: 50%;
  display: inline-block;
  vertical-align: top;
}

&.spinner__dot--second {
  animation-delay: 160ms;
  margin-left: 1em;
}

&.spinner__dot--third {
  animation-delay: 320ms;
  margin-left: 1em;
}

@keyframes pulse {
  0%, 80%, 100% {
    opacity: 0
  }
  40% {
    opacity: 1
  }
}

// default variant
.spinner--primary .spinner__dot {
  background-color: lighten(@brand-primary, 35%);
}

// secondary variant
.spinner--secondary .spinner__dot {
  background-color: @brand-secondary;
}
