.custom-dropdown {
  display: inline-block;

  button.btn.dropdown-toggle {
    text-overflow: ellipsis;
  }

  &.custom-dropdown-multiselect {

    .dropdown-menu li > a {
      position: relative;
      padding: 3px 20px 3px 40px;

      svg, svg use {
        position: absolute;
        top: 0;
        left: 20px;
        width: 1.5em;
        height: 1.5em;
        bottom: 0;
      }
    }

    .active {
      > a {
        color: #000;
        background-color: #fff;
      }
    }
  }

  > .dropdown.open {
    > .btn {
      background-color: #EBEBEB;
    }
  }
}