div.controls-container.map-grid-toggle {
  height: 100%;

  > div {
    float: right;
    line-height: @header-height;

    a.btn {
      font-size: 80%;
      font-weight: 600;
      border-radius: 0;
      color: #fff;
      background-color: @brand-primary-dark-lighter;
      position: relative;
      text-transform: uppercase;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &:hover, &.active {
        background-color: @brand-primary-darker;
      }

      &.not-available {
        cursor: default;

        &:hover {
          background: none;
        }
      }

      span {
        margin-left: 1em;
        margin-right: 0.25em;

        &.svg-container {
          position: relative;
          margin-left: 0.25em;
          margin-right: 1em;
          line-height: @base-font-size;
          height: @base-font-size * 1;
          width: @base-font-size * 1;

          svg {
            position: absolute;
            height: @base-font-size * 1;
            width: @base-font-size * 1;
            top: 50%;
            margin-top: -@base-font-size * 1 / 2;

            fill: #fff;
          }
        }

        &.space-placeholder {
          margin-right: 0;
          margin-left: 0.5em;
        }
      }
    }
  }
}