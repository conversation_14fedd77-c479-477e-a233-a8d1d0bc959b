@help-indicator-size: 34px;

.help-on{
  position: absolute;
  z-index: 10000;

  &.anchor-top{
    transform: translate3d( -50%, -100%, 0 );
  }
  &.anchor-bottom{
    transform: translate3d( -50%, 0%, 0 );
  }
  &.anchor-left{
    transform: translate3d( -100%, -50%, 0 );
  }
  &.anchor-right{
    transform: translate3d( 0%, -50%, 0 );
  }
  &.anchor-center{
    transform: translate3d( -50%, -50%, 0 );
  }

}

.help-information{
  display: block;
  color: #fff;

  .help-indicator{
    display: block;
    background: @brand-blue;
    border: 1px solid #fff;
    font-size: 20px;
    width: @help-indicator-size;
    text-align: center;
    cursor: pointer;
    &:hover{
      color: @icon-blue;
      border-color: @icon-blue;
    }
    &.pulse {
      animation-name: help-indicator-pulse;
      animation-duration: 2.25s;
      animation-fill-mode: both;
      animation-iteration-count: infinite;
    }
    @keyframes help-indicator-pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
        border-color: @icon-blue;
        box-shadow: @icon-blue 0 0 8px;;
      }
      100% {
        transform: scale(1);
      }
    }
  }
  .help-description{
    display: none;
    width: 250px;
    background: @brand-blue;
    border: 1px solid @icon-blue;
    padding: 8px;
    font-size: 14px;
    position: absolute;
    h4{
      text-transform: uppercase;
      font-size: 16px;
      margin-bottom: 5px;
      font-weight: 600;
    }
    &.right-above{
      left: @help-indicator-size;
      bottom: 0;
    }
    &.right-below{
      left: @help-indicator-size;
      top: 0;
    }
    &.left-above{
      right: @help-indicator-size;
      bottom: 0;
    }
    &.left-below{
      right: @help-indicator-size;
      top: 0;
    }
  }

  &.active{
    .help-description{
      display: block;
    }
  }
}

.portal:hover{
  z-index: 10001;
}
