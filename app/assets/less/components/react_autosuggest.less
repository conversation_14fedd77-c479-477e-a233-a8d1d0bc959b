
.react-autosuggest {

  &__container{
    position: relative;

    &--open{

      .react-autosuggest__suggestions-container {
        display: block;
      }
    }
  }

  // Bootstrap specific
  .form-control {
    border-bottom-left-radius: @input-border-radius !important;
    border-top-left-radius: @input-border-radius !important;
  }

  .form-control[aria-expanded="true"] {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom: none;
  }

  &__suggestions-container {
    max-height: 205px;
    overflow: auto;
    display: none;
    z-index: 2;
    position: absolute;
    top: @line-height-computed + (@padding-base-vertical * 2) + 1;
    left: 0;
    margin: 0;
    padding: 0;
    list-style-type: none;
    width: 100%;

    // syling
    border-radius: 0 0 @input-border-radius @input-border-radius;
    border: 1px solid @brand-primary;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-top: none;
    background: #fff;

    .react-autosuggest__suggestions-list{
      list-style: none;
      margin: 0;
      padding: 0;
    }
  }



  &__suggestion {
    cursor: pointer;
    padding: 0.5em;

    &:first-child {
      border: none;
    }

    span.current-location {
      color: @icon-blue;

      svg{
        fill: @icon-blue;
      }
    }

    svg.autosuggest-icon {
      fill: #000;
    }

    &--focused, &:hover {
      background-color: @brand-primary;

      span {
        color: #fff;
      }

      svg.autosuggest-icon {
        fill: #fff;
      }
    }

    svg.autosuggest-icon {
      width: 1em;
      height: 1em;
      transform: scale(1.5) translateY(10%);
      display: inline-block;
      margin-right: .75em;
      margin-left: .5em;
    }
  }
}
