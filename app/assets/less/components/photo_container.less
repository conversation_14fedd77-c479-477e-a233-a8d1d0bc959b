@max-slider-height: 5.5em;

.photo-component {
  height: calc(~"(99.999999% - @{header-height})");
  position: relative;
  user-select: none;
  background: rgba(0, 0, 0, 0.75) !important;
  z-index: @content-photo-container-z !important;

  // Prevent Google Chrome Hardware Acceleration from Throwing out white boxes, disappearing content
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  // Disable iPad double tap zoom
  touch-action: manipulation;

  &.photo-full {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 15 !important;
    width: 100%;
    height: 100%;
  }

  .photo {

    &--primary {
      text-align: center;
      z-index: 1;

      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      // Loading spinner bg
//      background: url('../images/spinner.gif') no-repeat center center;

      div.photo {
        border-radius: 2px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;

        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;

        img {
          width: 1px;
          height: 1px;
          position: absolute;
          left: -2px;
          top: -2px;
        }

        display: none;
        &.active {
          display: block;

          &:first-child + img ~ img  {
            z-index:-1;
          }
        }

        &.more-container {
          overflow: auto;

          div.more-container-darken {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.5);
          }

          &.active {
            display: flex;
            align-items: center;
            justify-content: space-around;

            div.more {
              width: 80%;
              max-width: 800px;
              margin: auto;
              padding: 2em;
              flex-grow: 1;
              z-index: 1;

              background: @brand-blue;
              box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.35);

              h1 {
                font-weight: 400;
                color: #fff;
                font-size: 1.5em;
                margin-top: 0.5em;
                margin-bottom: 1em;
              }

              p.back-to-last {
                color: @icon-blue;
              }

              > div {
                display: inline-flex;
                flex-direction: column;
                justify-content: center;
                align-content: center;
                align-items: center;
                min-height: 140px;
                height: 140px;
                cursor: pointer;

                &:hover {
                  background: @hover-blue;
                }

                &.disabled {
                  cursor: not-allowed;

                  &:hover {
                    background: none;
                  }

                  > svg {
                    fill: @icon-gray;
                    stroke: @icon-gray;
                  }

                  > p {
                    color: @icon-gray;
                  }
                }

                > svg {
                  width: 6em;
                  height: 3em;
                  fill: @icon-blue;
                  stroke: @icon-blue;
                }

                > p {
                  text-align: center;
                  color: #fff;
                  margin-top: 1em;
                }

                &.card-container {
                  flex: 1;
                  overflow: auto;
                  overflow-x: hidden;

                  > div.card {
                    z-index: 2;
                    width: 100%;
                    height: 140px;

                    .card-front {
                      overflow: hidden;
                    }

//                    overflow: hidden;

//                    &:hover {
//                      overflow: visible;
//                    }

                    .tag-panel {

                      form.add-tag input#new-tag {
                        @add-width: 34px;
                        width: calc(~"99.999% - @{add-width}");
                      }
                    }
                  }
                }

                > div.share-listing{
                  display: flex;

                  >svg {
                    display: none;
                  }

                  .share-listing-popup{
                    z-index: 1;
                    background-color: transparent;
                    justify-content: space-around;
                    min-width: 12em;
                    &>a{
                      text-align: center;
                      flex: 1 0 auto;
                      height: 35px;
                      margin: 0 15px;
                      display: inline-block;
                      svg{
                        height: 35px;
                        width: 35px;
                        fill: @icon-blue;
                      }
                    }
                    &>a:first-child{
                      margin-right: 0px;
                    }
                    &>a:last-child{
                      margin-left: 0px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    &--slider {
      height: calc(~"(99.999999% - @{header-height}) * 0.25");
      max-height: @max-slider-height;
      text-align: center;
      z-index: 3;

      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }

    &--caption {
      color: #fff;
      position: absolute;
      margin-top: -2.6em;
      height: 2.6em;
      width: 100%;
      padding: 1em 2em 0;
      text-align: center;
      background: rgba(0, 0, 0, 0.5);
      background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
      background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));

      p {
        margin: 0;
        width: calc(50% - 2em);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      div.toggle {
        width: 2em;
        height: 2em;
        position: absolute;
        top: .5em;
        left: 50%;
        padding: 0;
        border: none;
        margin: 0;
        margin-left: -1em;

        svg {
          width: 100%;
          height: 100%;
          stroke: #fff;
          fill: #fff;
        }
      }
    }

    &--scrollable-container {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.5);
    }

    &--scrollable {
      width: 100%;
      height: 99.999999%;
      overflow-y: hidden;
      overflow-x: auto;
      -ms-overflow-style: none;
      -webkit-overflow-scrolling: touch;
      list-style: none inside none;
      white-space: nowrap;
      padding: 0.7em 0.4em;
      margin: 0;

      li {
        margin: 0;
        padding: 0 0.3em;
        display: inline-block;
        list-style-type: none;
        height: 100%;
      }

      img {
        cursor: pointer;
        border: 0.25em solid transparent;
        height: 100%;
        min-width: 3.5em;
        max-width: 4.5em;
        border-radius: 2px;

        &.active,
        &:hover, {
          border-color: @brand-primary;
        }
      }
    }
  }

  .close {
    z-index: 3;
    position: absolute;
    right: 1em;
    top: 1em;
    cursor: pointer;
    font-weight: 800;
    padding: 0 0.1em;
    line-height: 1;
    background-color: rgba(0,0,0,.8);
    border-radius:2em;

    svg{
      width: 1.5em;
      height: 1.5em;
      margin: .5em;
      fill:#ffffff;
      stroke: #fff;
      stroke-width: 0.25em;

    }

    &:hover {
      opacity: 0.7;
    }
  }

  .full-screen {
    z-index: 3;
    position: absolute;
    right: 4.5em;
    top: 1em;
    cursor: pointer;
    font-weight: 800;
    padding: 0 0.1em;
    line-height: 1;
    background-color: rgba(0,0,0,.8);
    border-radius:2em;

    svg{
      width: 1.5em;
      height: 1.5em;
      margin: .5em;
      fill:#ffffff;
      stroke: #fff;
      stroke-width: 0.25em;

    }

    &:hover {
      opacity: 0.7;
    }
  }

  //  .next, .prev {
  //    z-index: 2;
  //    cursor: pointer;
  //    line-height: 1;
  //    color: #fff;
  //    position: absolute;
  //    top: 50%;
  //    width: 3em;
  //    height: 3em;
  //    margin-top: -6em;
  //
  //    &:hover {
  //      opacity: 0.7;
  //    }
  //
  //    svg {
  //      fill: #fff;
  //      stroke: #fff;
  //      width: 100%;
  //      height: 100%;
  //    }
  //  }
  //
  //  .next {
  //    right: 0.4em;
  //  }
  //
  //  .prev {
  //    left: 0.4em;
  //  }

  .next-container, .prev-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 5em;
    z-index: 2;
    .cursor-pointer();

    .next, .prev {
      fill: #fff;
      stroke: #fff;
      width: 3em;
      height: 3em;

      svg {
        width: 2em;
        height: 100%;
      }
    }

    &:hover {
      filter: drop-shadow(1px 1px 2px #000);
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .next-container {
    right: 0;
    padding-left: 1em;
    padding-right: 1em;
  }

  .prev-container {
    left: 0;
    padding-left: 1em;
    padding-right: 1em;
  }

  .play-pause-button-container {
    position: absolute;
    bottom: 9em;
    right: 0;
    width: 5em;
    height: 5em;
    background-color: #909090;
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
    fill: #fff;
    stroke: #fff;
    z-index: 3;
    .cursor-pointer();

    svg {
      width: 100%;
      height: 100%;
      padding: 1.5em 1em 1.5em 2em;
    }
  }
}

// Ken Burns
.photo-component.playing {
  overflow: hidden;
  background-color: rgba(0,0,0,0.85) !important;

  .photo--primary {
    div.photo:not(.more-container) {
      transition: opacity 3s, transform 10s;
      background-size: cover;

      display: block;
      opacity: 0;

      &.active.playing {
        display: block;
        opacity: 1;

        &:first-child + img ~ img  {
          z-index:-1;
        }
      }

      transform-origin: top right;
      transform: scale(1);
      &.active.playing {
        transform: scale(1.1);
      }

      &.in {
        opacity: 1;
        transition-duration: 0s, 0s;
        &.active.playing {
          transition-duration: 3s, 10s;
        }
      }

      &:nth-child(3n+1) {
        transform-origin: top right;
        transform: scale(1.1);
        &.active.playing {
          transform: scale(1);
        }
      }

      &:nth-child(5n+1) {
        transform-origin: bottom left;
        transform: scale(1);
        &.active.playing {
          transform: scale(1.1);
        }
      }

      &:nth-child(7n+1) {
        transform-origin: bottom left;
        transform: scale(1.1);
        &.active.playing {
          transform: scale(1);
        }
      }

      &:nth-child(4n+1) {
        transform-origin: top left;
        transform: scale(1);
        &.active.playing {
          transform: scale(1.1);
        }
      }

      &:nth-child(2n+1) {
        transform-origin: top left;
        transform: scale(1.1);
        &.active.playing {
          transform: scale(1);
        }
      }

      &:nth-child(6n+1) {
        transform-origin: bottom right;
        transform: scale(1);
        &.active {
          transform: scale(1.1);
        }
      }

      &:nth-child(8n+1) {
        transform-origin: bottom right;
        transform: scale(1.1);
        &.active.playing {
          transform: scale(1);
        }
      }
    }
  }
}

.photo-component {
  .close, .full-screen, .next-container, .prev-container, .play-pause-button-container {
    opacity: 1;
    transition: opacity .5s;
  }
}

.photo-component.playing.mouse-stopped {
  .close, .full-screen, .next-container, .prev-container, .play-pause-button-container {
    opacity: 0.01;
    transition: opacity .3s;
  }
}