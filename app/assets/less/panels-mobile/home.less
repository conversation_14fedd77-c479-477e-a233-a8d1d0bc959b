div.home {
  z-index: 1;

  header{

    > div:first-child{
      flex-wrap: wrap;

      svg{
        order: 1;
      }

      .about{
        order: 2;
      }

      .contact-us{
        display: none;
      }

      .search-area{
        order: 3;
        margin: 8px 8px 0px 8px;
        max-width: none;
      }
    }
  }

  .homeasap-footer-links{
    text-align: left;
  }

  .homeasap-footer-powered{
    text-align: right;
  }
}

.home-agent-search{

  section{
    .search-area-container{
      width: 90%;

      input{
        height: 40px !important;
        line-height: 40px !important;
        font-size: 15px  !important;
      }

      button{
        height: 40px !important;
        width: 58px !important;

        svg{
          height: 18px !important;
          width: 18px !important;
        }
      }

      .react-autosuggest__suggestions-container{
        top: 40px !important;
      }
    }
  }

  .subtitle{
    padding: 0px 8px 8px 8px;
  }

}
