div.mobile-slideout {
  width: 78vw;
  height: 100%;
  background: #fff;
  position: relative;

  > div.buyer-area {
    background: @brand-darkblue;
    box-shadow: 0 2px 3px rgba(0,0,0,0.25);
    height: 120px;
    z-index: 3;
    position: relative;
    cursor: pointer;

    > div.buyer-area-top {
      height: 60%;
      margin-left: 20px;
      margin-right: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-bottom: -30px;
      }
    }

    > div.buyer-area-bottom {
      height: 40%;
      margin-left: 20px;
      margin-right: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > p {
        margin: 0;
        font-size: 12px;
        color: #fff;
        position: relative;

        span.notification-dot {
          position: absolute;
          top: -3px;
          right: -10px;
          width: 2px;
          height: 8px;
          display: block;
          text-align: center;
          padding: 0 4px;
          border-radius: 100px;
          background: @brand-danger;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
          -webkit-background-clip: padding-box;

          font-size: 70%;
          font-weight: 500;
          color: white;
          text-decoration: none;

          &.total-count {
            display: none
          }
        }
      }

      > div {
        .tag-control {
          height: @header-height;
          width: 30px;
          cursor: pointer;
          position: relative;
          padding: 0;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          fill: #fff;

          &:hover, &:active {
            background-color: @brand-primary-darker !important;
          }

          svg {
            width: 100%;
            height: @header-height / 2 - (@header-height/2 - 1.2em);
          }
        }
      }
    }
  }

  > div.agent-area-top {
    background: @accent-gray;
    box-shadow: 0px 2px 3px rgba(0,0,0,0.25);
    height: 60px;
    z-index: 2;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    > .agent-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      
      div.pro-badge-container {
        top: 0;
        left: -10px;
        width: 20px;
        height: 20px;
      }
    }

    > div.agent-right {
      display: inline-block;
      margin-left: 10px;
      min-width: 50%;
      max-width: calc(~"100% - 70px");

      > p {
        margin: 0;
        font-size: 12px;
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap;
      }
    }
  }

  > div.agent-area-bottom {
    background: @accent-gray;
    z-index: 1;
    position: relative;
    padding: 10px 0;

    > ul {
      list-style: none;
      padding: 5px 0;

      > li {
        padding: 5px 0 5px 18%;

        > svg {
          width: 22px;
          height: 15px;
          margin-right: 12px;
          margin-bottom: -2px;
          fill: @icon-gray;
          stroke: @icon-gray;
        }

        > span {
          color: darken(@icon-gray, 20%);
          font-size: 12px;
        }
      }
    }

    .share-agent{
      display: inline-flex;
      justify-content: flex-start;
      align-items: center;

      span{
        margin-right: 16px;
      }

      a{
        display: inline-flex;
      }

      svg{
        width: 22px;
        height: 22px;
        margin: 0px 3px;
        fill: @icon-gray;
      }
    }
  }

  > div.logo {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 95px;
    text-align: center;

    > svg {
      width: 90px;
      height: 60px;
      display: inline-block;
    }
  }
}
