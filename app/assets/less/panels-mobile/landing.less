@iphoneXWidth: 375;

div.landing {
  z-index: 1;
  background-color: @brand-darkblue;
  background: url(//nplayassets.blob.core.windows.net/search2/backgrounds/landing-brown-house.jpg) no-repeat center center fixed;
  background-size: cover;

  div.landing-wrapper-container{
    padding: 0px;
    background-color: rgba(36,57,73,.4);
  }

  .agent-info{
    margin: 0px !important;
    padding-top: 20px;

    .agent-profile-image{
      width: 38vmin !important;
      height: 38vmin !important;
      max-width: 250px;
      max-height: 250px;

      div.pro-badge-container {
        top: 12px;
        left: -17px;
        width: 50px;
        height: 50px;
      }
    }

    .agent-right {
      .agent-name{
        font-size: unit(26/@iphoneXWidth*100, vmin) !important;
        font-weight: 400 !important;
      }

      .broker-name{
        font-weight: 300 !important;
        text-transform: none !important;
        font-style: italic !important;
        margin-top: 4px !important;
        font-size: unit(16/@iphoneXWidth*100, vmin) !important;
      }

      &.same-size {
        h1 {
          font-size: 2rem !important;
        }

        p.broker-name {
          font-size: 1.75rem !important;
        }
      }
    }
  }

  div.landing-wrapper{
    div.search-area {

      nav.saletype-nav {
        height: auto;
        width: 100%;
        display: flex;

        a {
          line-height: initial !important;
          padding: 2vmin 4vmin;
          flex: 1 1 auto;
          text-decoration: none !important;
          text-align: center;
          font-size: unit(14/@iphoneXWidth*100, vmin) !important;
        }
      }

      form {
        div.input-group {
          div.react-autosuggest__container {
            position: relative;

            input {
              padding: 4vmin;
              font-size: unit(16/@iphoneXWidth*100, vmin) !important;
              height: auto;
              font-weight: 400 !important;
              line-height: initial;
            }

            .react-autosuggest__suggestions-container {
              top: 100%;
            }
          }

          button.btn.btn-primary {
            height: 100%;
            width: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            align-content: center;
            padding: 0px 4vmin !important;

            svg {
              width: 6vmin;
              height: 6vmin;
              margin: 0;
            }
          }
        }
      }

    }
  }

  div.helper-buttons {
    flex-wrap: nowrap;
    margin-top: 0px !important;
    display: block !important;

    > div {
      background-color: @brand-blue;
      text-align: left;
      border-bottom: 1px solid rgba(255,255,255,.5);
      color: white !important;
      padding: 0 !important;
      display: flex;
      align-items: center;
      align-content: center;

      &:last-child{
        border-bottom: none;
      }

      a {
        text-align: left;
        padding: 4vmin !important;
        font-size: unit(16/@iphoneXWidth*100, vmin) !important;
        color: white !important;
        line-height: auto !important;
        font-weight: 600 !important;
        letter-spacing: 0.3px !important;
        display: block;
        flex: 1 1 auto;

        &:after{
          display: none !important;
        }
      }

      svg{
        width: 4vmin;
        height: 4vmin;
        fill: white;
        flex: 0 0 4vmin;
        margin-right: 4vmin;
      }
    }

    .sweepstakes-button{
      background-color: @brand-red;
    }
  }

  .share-agent{
    position: relative;
    top: auto;
    bottom: 0.8em;
    right: auto;
  }

  .contact-links{
    display: flex;
    justify-content: center;
    width: 100%;
    margin: 32px auto;
    padding: 0 24px;
    max-width: 600px;

    > div, > a{

      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-content: center;
      margin-right: 4vmin;

      &:last-child{
        margin-right: 0px;
      }

      svg + div{
        color: white;
        text-transform: uppercase;
        font-weight: 600;
        font-size: unit(14/@iphoneXWidth*100, vmin) !important;
        text-shadow: none;
        letter-spacing: 0.5px;
      }

      svg{
        fill: #ffffff !important;
        width: 10vmin;
        height: 10vmin;
        margin-bottom: 2vmin;
        max-width: 50px;
        max-height: 50px;
      }
    }
  }

  .social-links{
    margin: 24px 0px !important;
    right: auto;
    position: relative !important;
    top: auto;
    bottom: auto;
    left: auto;
    display: flex;
    justify-content: center;
    width: 100%;
    flex-wrap: wrap;

    a{
      margin-right: 16px !important;

      &:last-child{
        margin-right: 0px !important;
      }

      svg{
        width: 48px;
        height: 48px;
        fill: #ffffff !important;
      }
    }
  }

  .mobile-landing-footer{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3vmin;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    background-color: @brand-blue;
    letter-spacing: 0.2px;

    svg{
      height: 24px;
      width: 150px;
    }


  }

  .landing-broker-container{
    padding-bottom: 60px !important;
    flex-direction: column;

    .landing-mls-disclaimer {
      margin: 15px;

      > p {
        max-width: unset;
      }
    }
  }
}
