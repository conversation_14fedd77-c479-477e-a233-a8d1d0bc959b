.read-header-wrapper {
  position: absolute;
}

.read-header {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  height: 4.4em;
  padding: 0 10px;

  > * {
    width: 50%;
  }

  &-left {

  }

  &-middle {
    display: none;
  }

  &-right {
    a {
      &:nth-child(1) {
      }
      &:nth-child(2) {
      }
    }
  }
}

.member-search-header, .member-listings-header {
  flex-direction: column;
  height: auto;
  padding-top: 8px;
  padding-bottom: 8px;

  .toggle-container {
    width: 100%;
    margin-bottom: 0;
  }

  .search-container {
    width: 100%;
    display: block;

    .member-listings-sort {
      display: none;
    }

    .search-button-container {
      flex-direction: column;
      width: 100%;
      margin-top: 8px;

      > div.filter-container {
        margin-bottom: 8px;
        width: 100%;

        a.btn {
          width: 100%;
        }
      }

      > div.search-container {
        .search-form {
          width: 100% !important;
          margin-left: 0;

          .input-container {
            width: 100%;
          }
        }
      }
    }
  }
}