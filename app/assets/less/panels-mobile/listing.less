.listing-mobile {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: #fff;
  z-index: 15;

  div.mobile-picture {
    height: 56.25vw;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
  }

  div.zoom-button {
    cursor: pointer !important;
  }

  .listing-sub-panel{
    padding: 0 0 1em 0;
  }

  .listing-map-subpanel{
    padding: 0;
    top: 0;
    z-index: 99999;
    width: 100% !important;
  }

  .property-highlights-item-container{
    width: 33.3% !important;
  }

  .close-button-cross {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    cursor: pointer;
    background-color: @brand-primary;

    svg {
      fill: #fff;
      stroke: #fff;
    }
  }

  .scroll-container {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    padding: 0;
    background: #fff;

    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  .overlay-container {
    padding: 0;
    z-index: 1;
  }

  #photo-slider {
    padding: 0;

    form.add-tag {
      clear: both;
      margin: 1em 0;
      display: flex;

      .btn-tagsubmit {
        width: auto;
        flex: 0 0 auto;
      }
      input#new-tag {
        width: auto;
        flex: 1 0 auto;
      }
    }
  }
}

.yelp-local{

  .yelp-results{
    max-height: none;
  }

  select{
    margin-right: 0px;
    width: 100%;
  }

  .yelp-list-and-map{
    display: block;

    .yelp-map{
      flex: 1 0 auto;
      height: 300px;
      margin-top: 15px;
    }
  }

  .yelp-business{

    .yelp-reviews{

      .yelp-business-review-count{
        line-height: 10px;
      }

      .yelp-business-rating{

        img{
          width: 60px;
        }
      }
    }
  }

  .yelp-popup{

    .yelp-business{

      .yelp-reviews{
        line-height: 10px;
        margin-bottom: 4px;
      }
    }
  }
}
