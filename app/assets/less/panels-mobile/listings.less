.listing-shadow {
  display: none;
}

.map-sort{
  width: 100%;
  top: @search-bar-height + 1.7em;
  z-index: 9;
}

.body-listings{
  margin-top: 0;
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  padding-top: 4em;
  background-color: @brand-blue;
}

.horizontal-listings-container {
  width: 100vw;
  height: calc(~"100vw/16*9");

  > div > div {
    display: flex;

    > div {
      flex-shrink: 0;
    }
  }
}

div.card {
  height: calc(~"100vw/16*9") !important;

  div.tag-button {
    width: calc(~"100vw/16*9/6") !important;
    height: calc(~"100vw/16*9/6") !important;
    max-width: 30px !important;
    max-height: 30px;
  }
}
