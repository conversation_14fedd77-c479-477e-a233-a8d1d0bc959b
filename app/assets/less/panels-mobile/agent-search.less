.agent-search{
  filter: none !important;
  position: absolute;
  min-height: 100vh;
  width: 100vw;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  align-items: center;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(19,35,47,0.7);
  padding-top: 32px;
  padding-bottom: 32px;

  .container{
    width: 100%;
    padding: 0px 16px;


    .agents-list{
      width: 100%;
    }

    .best-agent{
      width: 100%;
    }

  }

  .agent-search-result{
    width: 100%;

    .top{

      .image{
        flex: 0 0 96px;
        margin: 0px;
        margin-right: 16px;

        img.picture{
          width: 96px;
          height: 96px;
        }

        .agent-since{
          display: block;
          font-size: 12px;
          line-height: 12px;
          text-align: center;
          margin-top: 8px;

          span{
            font-weight: bold;
            margin-left: 5px;
            margin-top: 2px;
            display: block;
          }
        }

        .featured{
          top: 79px;
          bottom: auto;
          font-size: 9px;
          padding: 3px 7px;
          width: 100%;
          line-height: 12px;

          &:before{
            border-right: 0px solid @brand-red;
            border-top: 9px solid @brand-red;
            border-bottom: 9px solid @brand-red;
            border-left: 5px solid white;
          }

          &:after{
            border-left: 0px solid @brand-red;
            border-top: 9px solid @brand-red;
            border-bottom: 9px solid @brand-red;
            border-right: 5px solid white;
          }
        }
      }

      .top-details{

        .specializing{
          font-size: 12px;
        }

        .specializing, .broker-name, .agent-name{
          overflow: visible;
          text-overflow: initial;
          white-space: normal;
        }

        .agent-since{
          display: none;
        }

      }

    }

  }

  .agents-list{

    li{
      flex: 1 1 auto;
      min-width: 200px;
    }

  }

}

.more-agents{
  display: flex;
  flex-direction: column;
}
