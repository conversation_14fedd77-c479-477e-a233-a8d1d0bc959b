.photo-component {
  z-index: 16 !important;
}

.photoslider-mobile {

  div.close {
    width: 1.25em;
    height: 1.25em;
    position: absolute;
    top: 0;
    left: 0;
    margin: 1em;
    box-sizing: content-box;
    cursor: pointer;

    svg {
      width: 100%;
      height: 100%;
      fill: #fff;
    }
  }

  div.next-container, div.prev-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 3em;
    pointer-events: none;

    .next, .prev {
      fill: #fff;
      stroke: #fff;
      width: 1em;
      height: 2em;

      svg {
        width: 1em;
        height: 100%;
      }
    }
  }

  div.next-container {
    right: 0;
    padding-right: .25em;
    text-align: right;
  }

  div.prev-container {
    left: 0;
    padding-left: .25em;
    text-align: left;
  }

  div.photo--caption {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2em;
    line-height: 2em;
    color: #fff;

    background: rgba(0, 0, 0, 0.5);
    background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));

    @caption-left: 5em;

    p.pull-left.text-left {
      width: @caption-left;
      padding-left: 0.5em;
    }

    p.pull-right.text-right {
      width: calc(~"(99.999999% - @{caption-left} - 1em)");
      text-overflow: ellipsis;
      padding-right: 0.5em;
    }
  }
}
