.featured-header, .tagging-header {
  text-align: center;
  color: #223949;
  background-color: @accent-gray;
  padding: 0.5em;
  font-size: 1.25em;
  font-weight: 600;
  text-transform: uppercase;
}

.agent-screen-container-mobile {
  background-color: transparent;
  z-index: 9;

  .btn.contact-button {
    font-weight: 400;
    font-size: 1em;
    display: inline-block;
    min-width: 60%;
    max-width: 90%;
  }

  .center{
    text-align: center;
  }

  .ptn{
    padding-top:0 !important;
  }

  .menu-left, .menu-right {
    margin-top: @header-height;
  }
  .profile-container {
    position:relative;
    margin:0 10px;
    padding: 2em 0 1em 0;
    font-size: 0.8em;
    margin-top: 5em;

    .col-1-1{
    }
    .holder{
      padding-top: .5em;
      padding-bottom: .5em;
    }

    .border-line {
      padding: 8px 0;
    }
    .border-line:nth-child(even){
      background-color: @accent-gray;
    }
    .col-1-1,
    .col-sm-1-2 {
      padding: 0 1em;
    }
    .d-block {
      display: inline-block;
    }

    .header {
      margin: 10px 0;
      font-weight: 400;
      color: @icon-blue;
      font-size: 1.25em;
    }
    .panel-0 {
      margin-bottom: 2em;
      > div {
        padding: 0 10px;

        &.agent-image {
          div.pro-badge-container {
            top: 0;
            left: -8px;
            width: 30px;
            height: 30px;
          }
        }
        &.agent-info {
          p.agent-name {
            margin: 0;
            font-weight: 600;
            font-size: 1.25em;
          }
          p.tagline {
            color: @icon-blue;
            font-style: italic;
          }
          .social-container {
            > div{
              display:inline-block;
              cursor:pointer;
              margin-right: 5px;

              .social-icon {
                border-radius:0;
                width: 30px;
                height: 30px;
                fill:@brand-red;

                &.twitter-icon{
                  fill: #598DCA !important;
                }

                &.facebook-icon{
                  fill: #3A589B !important;
                }

                &.instagram-icon{
                  fill: #833AB4 !important;
                }

                &.youtube-icon{
                  fill: #c4302b !important;
                }

                &.google-icon{
                  fill: #4285F4 !important;
                }
              }
            }
          }
        }
      }
    }
    .panel-1 {
      background-color: @accent-gray;

      .title {
        padding-right:10px;
        font-weight: 600;
      }
      .data {
        width: 50%;
        font-weight: 400;
      }
      .broker-logo{
        max-height: 5em;
        vertical-align:middle;
        margin: 1.5em 0;
      }
      .agent-profile-name {
        padding: 0 1em 0 1em;
        margin-bottom: 1em;
        text-align: center;
        .name{
          font-weight: 600;
          color: @icon-blue;
          font-size: 1.5em;
        }
        .brokerage{
          font-size: 1.2em;
          font-weight: 400;
        }
      }

      .profile-image {
        display: inline-block;
        margin-top:1em;
        .image-holder {
          width: 100%;
          max-width: 14em;
          position: relative;
          margin: 0 auto;
          padding-bottom: 1em;
          img {
            border-radius: 50%;
            width: 100%;
            max-width: 14em;
          }
          .realtor-icon {
            stroke: #fff;
            fill: #007bb6;
            bottom: 10%;
            position: absolute;
            width: 15%;
            height: 15%;
            right: 4%;
          }
        }
      }
      .panel-1-2 {
        display: inline-block;
        padding-left: 1em;
        .license {
          line-height: 2em;
          padding-bottom: 20px;
          font-size: 1.1em;
          width: 100%;
          display: inline-block;
          span {
            padding: 5px;
          }
          span:first-child{
            padding-left: 0px;
          }
          a {
            width: 20%;
            padding-right: 20px;
          }
          .link-container {
            padding: 0;
            font-weight: 400;
            color: @icon-blue;
            display: flex;
            justify-content: space-between;
            max-width: 320px;
            a {
              text-decoration: none;
              cursor: pointer;
              padding: 0;
              color: @icon-blue;
              &.no-hover{
                cursor:not-allowed;
              }
            }
            span {
              padding: 0;
            }
          }
          .link-icon {
            flex: 0 1 auto;
            fill: @icon-blue;
            vertical-align: text-bottom;
            width: 16px;
            height: 16px;
            stroke-width: 5;
            margin-right: 3px;
          }
        }
      }

    }
    .icon-panel {
      padding: 20px 0;
      border-bottom: 1px solid @accent-gray;
      .icon-container {
        vertical-align: top;
        display: inline-block;
        text-align: center;
        padding: 0 !important;
      }
      .icon {
        width: 40px;
        height: 40px;
        fill: @icon-gray;
      }
      .icon-title {
        font-weight: 600;
        color: @icon-blue;
      }
    }
    .video-container {
      padding-top: 6px;
      max-width: 35em;
      margin-top:2em;
    }
    .service-area {
      width: 50%;
      display: inline-block;
    }
    .headline {
      padding: 1em;
      font-size: 1.4em;
      font-style: italic;
      font-weight: 500;
      color:@icon-blue;
      a {
        text-decoration: none;
        cursor: pointer;
      }
      .social-container {
        text-align: right;
        min-width: 140px;
        padding: 0;
        div{
          display:inline-block;
          cursor:pointer;
        }
      }
      .social {
        margin-right: 5px;
      }
    }
    .social-icon {
      border-radius: 0;
      width: 30px;
      height: 30px;

      &.twitter-icon{
        fill: #598DCA !important;
      }

      &.facebook-icon{
        fill: #3A589B !important;
      }

      &.instagram-icon{
        fill: #833AB4 !important;
      }

      &.youtube-icon{
        fill: #c4302b !important;
      }

      &.google-icon{
        fill: #4285F4 !important;
      }
    }
    .about {
      text-align: justify;
    }
    .service {
      @media (max-width: @screen-sm-min) {
        padding-bottom: 15px;
      }
    }
    .expertise {
      .content-holder{
        @offset:80px;
        width: calc(~"(99.9999% - @{offset})");
      }
      .expertise-text {
        width: 100%;
        padding: 0;
        vertical-align: top;
        list-style-type: circle;
        display: inline-block;
        li {
          padding: 0;
          list-style-type: circle;
        }
      }
      .circle {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
        color: #fff;
        margin-right: 10px;
        text-align: center;
        font-size: 0.8em;
        .expertise-icon {
          width: 30px;
          height: 50px;
          fill: @icon-gray;;
          stroke: @icon-gray;
          right: 30px;
          position: absolute;
          top: 0;
        }
        span {
          position: absolute;
          top: 40px;
          left: 18px;
          vertical-align: bottom;
        }
      }
    }
    .service-area-map {
      height: 200px;
      width: 80%;
      background-color: @content-bg;
    }
    .section-icon{
      width: 30px;
      height: 30px;
      fill: @icon-gray;
    }
    .icon-holder{
      margin: 10px 10px 0 0;
      display: inline-block;
      vertical-align: top;
    }
    .h40{
      height:40px;
    }
    .content-holder{
      display:inline-block;
    }
    .publications {
      background-color: @content-bg;
    }
    .organizations {
      background-color: @content-bg;
    }
    .certifications {
      img {
        margin-right: 15px;
        height: 2em;
        margin-bottom: 15px;
      }
    }
    .section-holder{
      padding-top:1em;
    }
    .section-date,.section-desc{
      font-size:.8em;
      color:@icon-gray;
      margin:0;
      display:inline-block;
      padding-right:.5em;
      font-weight:400;
    }

    .section-title{
      font-weight:700;
      &.link{
        color:@icon-blue;
        cursor:pointer;
      }
    }

    .section-details{
      font-style: italic;
      font-size: .9em;
    }
    .shade{
      background-color: @accent-gray;
      padding:1em;
    }

  }
}
