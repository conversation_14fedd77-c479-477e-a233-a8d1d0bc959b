.map-panel-mobile {
  top: 0;
  z-index: 9;

  .leaflet-control-container {
    display: none;
  }

  .leaflet-control-recenter, .leaflet-control-zoom, .leaflet-control-layers {
    display: none;
  }

  .map-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;

    .map-logo {
      left: 0.75em;
      bottom: 0.75em;
      width: 2em;
      height: 2em;

      a {
        width: 100%;
        height: 100%;
      }
    }

    .map-north {
      top: 15px;
      left: 10px;
    }

    .map-zoommsg {
      position: absolute;
      z-index: 1000;
      background: rgba(34, 57, 73, 0.9);  // @brand-blue
      text-align: center;
      bottom: 80px;
      margin: 0px;
      padding: 3px;
      border-radius: 10px;

      svg {
        margin: 0px 20px 0px 0px;
        stroke: #fff;
        fill: #fff;
        width: 28px;
        height: 32px;
        vertical-align: middle;
      }
      span {
        top: 3px;
        padding: 0px ;
        margin: 0px;
        font-size: 14px;
        color: #fff;
        background: rgba(0,0,0,0);
        width: auto;
        border-radius: 0px;
        vertical-align: middle;
      }
    }
  }

}
