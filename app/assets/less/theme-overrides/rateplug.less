.map-icon, .map-icon-svg {
  &.active, &.general, &.tagged, &.favorite {
    &.zoom13, &.zoom14, &.zoom15, &.zoom16, &.zoom17, &.zoom18, &.zoom19, &.zoom20, &.zoom21, &.zoom22 {
      .label {
        display: inline-block;
      }
    }
  }
}

div.rateplug-home {
  display: block !important;
}

.card {
  height: 13em;

  .card-front {
    svg.new-label {
      top: 32px;
      bottom: unset;
    }
  }
}

.mobile {
  div.icon-mobile-toggle {
    bottom: 65px;
  }

  .map-icon, .map-icon-svg {
    .label {
      transform: scale(1.5) translateY(3px);
    }
  }

  .horizontal-listings-container {
    height: calc(~"100vw/16*10");
  }

  div.card {
    height: calc(~"100vw/16*10") !important;


    .card-front {
      .rateplug-monthly-wrapper > .rateplug-monthly-line {
        p {
          font-size: 16px;
        }
      }
    }
  }
}

.search-bar .results{
  animation: hideResults .25s ease-in 2s forwards !important;
}
