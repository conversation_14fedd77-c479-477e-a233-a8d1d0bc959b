@import (multiple) "./rateplug.less";

.layout--header {
  & {
    background-color: #fff;
    color: #000;
  }

  .header-left {
    background-color: #fff;

    div.agent-container:hover {
      background-color: #EAEAEA;
    }

    div.agent-container .agent-header-image {
      border: 3px solid @brand-primary;
    }

    div.agent-container div.header-agent-dropdown {
      background-color: @brand-darkblue;

      ul li:hover {
        background-color: @brand-primary;
      }
    }

    div.agent-container div.dropdown-chevron svg {
      fill: @brand-primary;
      stroke: @brand-primary;
    }

    div.agent-container div.agent-texts.full-name p {
      color: #000;
    }

    div.controls-container.search-button-container > div.search-container .search-form .react-autosuggest__container input {
      background-color: #EAEAEA;
    }
  }

  .header-right {
    .direct-login-button {
      color: #000;

      &:hover {
        color: #fff;
      }
    }

    .tag-control {
      fill: @brand-primary;

      &:hover, &:active {
        fill: #fff;
      }
    }

    .help-control {
      color: @brand-primary;

      .help-icon {
        border-color: @brand-primary;
      }

      &:hover, &:active {
        color: #fff;

        .help-icon {
          border-color: #fff;
        }
      }
    }
  }
}

.rateplug-landing-next, .rateplug-landing-last {
  background-image: url(https://nplayassets.blob.core.windows.net/search2/rateplug/rateplug-onboarding-next-more.png) !important;
}

.n-play-footer > div {
  background: @brand-primary-darker;
}

.grid-container.lights-out {
  background-color: #101010;

  .card .card-front {
    background-color: #003A70;

    .bottom {
      border: none;
    }
  }
}

div.pieSlice3 > div.pie,
#mortgage-calculator-popover .modal-dialog .popover-content p.legend-label span.legend-icon.legend3 {
  background: #F5B93C !important;
}
