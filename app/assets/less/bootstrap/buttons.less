//
// Buttons
// --------------------------------------------------

// Base styles
// --------------------------------------------------

.btn {
  user-select: none;
  background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214
  background: none;
  border: 1px solid transparent;
  touch-action: manipulation;
  white-space: nowrap;
  -webkit-appearance: none;
  overflow: hidden; // contain gradient on IE9
  display: inline-block;
  margin-bottom: 0; // For input.btn
  font-weight: @btn-font-weight;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  .button-size(@padding-base-vertical; @padding-base-horizontal; @font-size-base; @line-height-base; @border-radius-base);
  text-decoration: none;
  outline: 0;

  &:hover,
  &:focus,
  &.focus {
    outline: 0;
    color: @btn-default-color;
    text-decoration: none;
  }

  &:active,
  &.active {
    outline: 0;
    background-image: none;
  }

  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    cursor: @cursor-disabled;
    .opacity(.65);
  }

  a& {
    &.disabled,
    fieldset[disabled] & {
      pointer-events: none; // Future-proof disabling of clicks on `<a>` elements
    }
  }
}

.btn-outline {
  border-color: @btn-primary-bg;
  color: @btn-primary-bg;

  &:focus,
  &.focus {
    color: @btn-primary-color;
    background-color: @btn-primary-bg;
    border-color: @btn-primary-bg;
  }
  &:hover {
    color: @btn-primary-color;
    background-color: @btn-primary-bg;
    border-color: @btn-primary-bg;
  }
}

// Alternate buttons
// --------------------------------------------------

.btn-primary {
  .button-variant(@btn-primary-color; @btn-primary-bg; @btn-primary-color);
}

.btn-secondary {
  .button-variant(#fff, @brand-secondary, @brand-secondary);
}

// Success appears as green
.btn-success {
  .button-variant(@btn-success-color; @btn-success-bg; @btn-success-color);
}

// Info appears as blue-green
.btn-info {
  .button-variant(@btn-info-color; @btn-info-bg; @btn-info-color);
}

// // Warning appears as orange
// .btn-warning {
//   .button-variant(@btn-warning-color; @btn-warning-bg; @btn-warning-color);
// }
// Danger and error appear as red
.btn-danger {
  .button-variant(@btn-danger-color; @btn-danger-bg; @btn-danger-color);
}

// Link buttons
// -------------------------

// Make a button look and behave like a link
// .btn-link {
//   color: @link-color;
//   font-weight: normal;
//   border-radius: 0;

//   &,
//   &:active,
//   &.active,
//   &[disabled],
//   fieldset[disabled] & {
//     background-color: transparent;
//     // .box-shadow(none);
//   }
//   &,
//   &:hover,
//   &:focus,
//   &:active {
//     border-color: transparent;
//   }
//   &:hover,
//   &:focus {
//     color: @link-hover-color;
//     text-decoration: @link-hover-decoration;
//     background-color: transparent;
//   }
//   &[disabled],
//   fieldset[disabled] & {
//     &:hover,
//     &:focus {
//       color: @btn-link-disabled-color;
//       text-decoration: none;
//     }
//   }
// }

// Button Sizes
// --------------------------------------------------

.btn-lg {
  // line-height: ensure even-numbered height of button next to large input
  .button-size(@padding-large-vertical; @padding-large-horizontal; @font-size-large; @line-height-large; @border-radius-large);
}

.btn-sm {
  // line-height: ensure proper height of button next to small input
  .button-size(@padding-small-vertical; @padding-small-horizontal; @font-size-small; @line-height-small; @border-radius-small);
}

.btn-xs {
  .button-size(@padding-xs-vertical; @padding-xs-horizontal; @font-size-small; @line-height-small; @border-radius-small);
}

// Block button
// --------------------------------------------------

.btn-block {
  display: block;
  width: 100%;
}

// Vertically space out multiple block buttons
.btn-block + .btn-block {
  margin-top: 5px;
}

// Specificity overrides
input[type="submit"],
input[type="reset"],
input[type="button"] {
  &.btn-block {
    width: 100%;
  }
}
