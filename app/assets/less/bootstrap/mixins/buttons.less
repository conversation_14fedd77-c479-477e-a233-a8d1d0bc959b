// Button variants
//
// Easily pump out default styles, as well as :hover, :focus, :active,
// and disabled options for all buttons

.button-variant(@color; @background; @border) {
  color: @color;
  fill: @color;
  stroke: @color;
  background-color: @background;
  border-color: @background;

  &:focus,
  &.focus {
    color: @color;
    fill: @color;
    stroke: @color;
    background-color: darken(@background, 5%);
    border-color: darken(@background, 5%);
  }
  &:hover {
    color: @color;
    fill: @color;
    stroke: @color;
    background-color: darken(@background, 5%);
    border-color: darken(@background, 5%);
  }
  &:active,
  &.active,
  .open > .dropdown-toggle& {
    color: @color;
    fill: @color;
    stroke: @color;
    background-color: darken(@background, 5%);
    border-color: darken(@background, 5%);

    &:hover,
    &:focus,
    &.focus {
      color: @color;
      fill: @color;
      stroke: @color;
      background-color: darken(@background, 5%);
      border-color: darken(@background, 5%);
    }
  }
  &:active,
  &.active,
  .open > .dropdown-toggle& {
    background-image: none;
  }
  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    &,
    &:hover,
    &:focus,
    &.focus,
    &:active,
    &.active {
      background-color: @background;
      border-color: @background;
    }
  }

  .badge {
    color: @background;
    fill: @background;
    stroke: @background;
    background-color: @color;
  }
}

// Button sizes
.button-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius) {
  padding: @padding-vertical @padding-horizontal;
  font-size: @font-size;
  line-height: @line-height;
  border-radius: @border-radius;
}
