#app-container {

  div.demo-bar {
    display: none;
  }
}

#app-container.demo {

  @demo-bar-height: 5em;

  padding-top: @header-height + @demo-bar-height;

  // Overrides
  // ====== Layout
  .layout {

    &--header {
      top: @demo-bar-height;
    }

    &--content {
    }
  }

  .layout {

    &--full {
      top: @header-height + @demo-bar-height;
    }

    &--front {
    }

    &--1ns {
    }

    // Used in Listing Map
    &--2s-5 {
      top: @header-height + @demo-bar-height;
    }

    // Used in Listing Content
    &--3r-5 {
      top: @header-height + @demo-bar-height;
    }

    // Used in Agent Content
    &--2ns-3 {
    }

    // Used in Agent Listings
    &--1s-3 {
      top: @header-height + @demo-bar-height;
    }

    // Used in Detail Content
    &--2s-3 {
      top: @header-height + @demo-bar-height;
    }

    // Used in Grid Detail Listings
    &--1ns-3 {
    }

    // Used in Grid Detail Photo
    // Used in map.map w/o Content
    &--2f-3 {
      top: @header-height + @demo-bar-height;
    }

    // Used in map.map - with content
    &--2f-4 {
      top: @header-height + @demo-bar-height;
    }

    // Used in map.content
    &--2s-4 {
      top: @header-height + @demo-bar-height;
    }

    // Used in map.subcontent
    &--1f-4 {
      top: @header-height + @demo-bar-height;
    }

    // Used in map.subcontent - hide click container
    &--1f-4-hide {
      top: @header-height + @demo-bar-height;
    }
  }

  div.home,
  div.home .search-area-background,
  div.home div.search-area.collapsed,
  .help-overlay,
  .listing-shadow,
  div.landing {
    top: @demo-bar-height;
    overflow-y: hidden;
  }

  .map-sort,
  .grid-container .menu-fixed,
  .menu-top.fixed,
  .buyer-screen-container .buyer-info,
  .layout--header .header-left div.controls-container.search-button-container div.editing-filters-container,
  .layout--header .header-left div.controls-container.search-button-container div.editing-filters-overlay {
    top: @header-height + @demo-bar-height;
  }

  .buyer-screen-container .buyer-info {
    margin-top: @header-height;
    margin-bottom: @header-height + 2em;
  }

  // Demo Bar
  div.demo-bar {
    position: fixed;
    z-index: 101;
    top: 0;
    left: 0;
    right: 0;
    height: @demo-bar-height;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,.4);
    overflow: hidden;

    display: flex;
    justify-content: space-between;

    > .demo-logo {
      flex: 1 1 33.333%;
      margin: @demo-bar-height * .125;
      height: @demo-bar-height * .75;
      display: flex;
      justify-content: flex-start;
      > .logo-search-alliance{
        max-width: 280px;
        width: 100%;
      }
    }

    > .demo-title {
      flex: 1 1 33.333%;
      font-size: 1.5em;
      line-height: @demo-bar-height / 1.5;
      font-weight: 500;
    }

    .demo-buttons {
      flex: 1 1 33.333%;
      height: @demo-bar-height - 2em;
      margin: 1em;
      display: flex;
      justify-content: flex-end;

      a.btn-success {
        font-size: 1.25em;
        font-weight: 600;
        padding: 0 1em;
        display: flex;
        align-content: center;
        align-items: center;
      }

      p.btn-link {
        font-size: 1.25em;
        font-weight: 600;
        color: #000;
        cursor: default;
        display: flex;
        align-content: center;
        align-items: center;
      }

//      a.close-demo {
//        width: @demo-bar-height - 2em - 1em;
//        padding: 1em;
//        padding-left: 1.5em;
//
//        svg {
//          width: @demo-bar-height - 2em - 2em;
//          height: @demo-bar-height - 2em - 2em;
//          fill: @icon-gray;
//          stroke: @icon-gray;
//        }
//      }
    }
  }

  ~ div div.modal-backdrop, ~ div div.modal {
    top: @demo-bar-height;
  }

  &.mobile {
    @demo-bar-height: 4em;
    @header-height: 0;  // Header bar removed from new mobile design.

    padding-top: @demo-bar-height;

    // Overrides
    // ====== Layout
    .layout {

      &--header {
        top: @demo-bar-height;
      }

      &--content {
      }
    }

    .layout {

      &--full {
        top: @demo-bar-height;
      }

      &--front {
      }

      &--1ns {
      }

      // Used in Listing Map
      &--2s-5 {
        top: @header-height + @demo-bar-height;
      }

      // Used in Listing Content
      &--3r-5 {
        top: @header-height + @demo-bar-height;
      }

      // Used in Agent Content
      &--2ns-3 {
      }

      // Used in Agent Listings
      &--1s-3 {
        top: @header-height + @demo-bar-height;
      }

      // Used in Detail Content
      &--2s-3 {
        top: @header-height + @demo-bar-height;
      }

      // Used in Grid Detail Listings
      &--1ns-3 {
      }

      // Used in Grid Detail Photo
      // Used in map.map w/o Content
      &--2f-3 {
        top: @header-height + @demo-bar-height;
      }

      // Used in map.map - with content
      &--2f-4 {
        top: @header-height + @demo-bar-height;
      }

      // Used in map.content
      &--2s-4 {
        top: @header-height + @demo-bar-height;
      }

      // Used in map.subcontent
      &--1f-4 {
        top: @header-height + @demo-bar-height;
      }

      // Used in map.subcontent - hide click container
      &--1f-4-hide {
        top: @header-height + @demo-bar-height;
      }
    }

    div.landing {
      top: 0;
    }

    div.home,
    .listing-mobile,
    .photoslider-mobile{
      top: @demo-bar-height;
    }

    .map-sort,
    .grid-container .menu-fixed,
    .menu-top.fixed,
    .buyer-screen-container .buyer-info,
    .search-bar {
      top: @header-height + @demo-bar-height;
    }

    // Demo Bar

    div.demo-bar {
      height: @demo-bar-height;

      > .demo-logo {
        display: none;
      }

      > .demo-title {
        font-size: 1.25em;
        line-height: @demo-bar-height / 1.25;
        margin-left: .5em;
        flex: 1 0 50%;
      }

      h1 {
        font-size: 1.5em;
        line-height: @demo-bar-height * 2 / 3;
        margin: 0;
        margin-left: .5em;
      }

      .demo-buttons {
        height: @demo-bar-height;
        margin: 0;
        flex: 1 0 auto;

        a.btn {
          font-size: 1.15em;
        }

        a.close-demo {
          height: @demo-bar-height;
          width: @demo-bar-height;
          padding: 0.5em;

          svg {
            width: 50%;
            height: 50%;
            margin: 25%;
            padding: 0;
          }
        }
      }
    }

    ~ div div.modal-backdrop, ~ div div.modal {
      top: 0;

      &.pending-build {

        .modal-dialog {

          @keyframes fadeIn {
            0% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
          }
          // Show after 2s
          opacity: 0;
          animation: fadeIn 0s ease-in 2s forwards;

          transition-delay: 2s;
        }
      }
    }

    ~ div.ath-viewport {
      display: none;
    }
  }

  .share-listing{
    display: none;
  }
}

div.modal.demo-modal {
  text-align: center;

  div.modal-dialog {
    max-width: 30em;
    margin-top: 8em;
    margin-left: auto;
    margin-right: auto;

    div.modal-content {
      padding: 5em 1.75em 4em;

      h1 {
        line-height: 1.5em;
      }

      p {
        margin: 1em auto 2em;
      }

      div.header-svg {
        width: 7em;
        height: 7em;
        position: absolute;
        left: 50%;
        top: 0;
        margin-left: -3.5em;
        margin-top: -3.5em;
        border: 2px solid #fff;
        border-radius: 50%;

        &.check {
          background-color: @brand-darkblue;
        }

        &.build {
          background-color: @brand-primary;
        }

        svg {
          width: 56%;
          height: 56%;
          margin: 22%;
          fill: #fff;
        }
      }

      svg.graphics-uh-oh {
        width: 50%;
        height: 7em;
      }

      form {
        text-align: center;

        padding: 0 2em;

        .form-control:focus {
          border-color: #66afe9;
          outline: 0;
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
        }

        div.form-group {
          margin-bottom: 1em;
          text-align: left;

          label {
            font-weight: 600;
          }
        }

        div.optional-label {
          position: relative;

          label {
            position: absolute;
            left: 50%;
            top: 5px;
            width: 70px;
            padding: 0 5px;
            margin-left: -35px;
            background: #fff;
            text-align: center;
            font-style: italic;
          }
        }

        div.optional {
          position: relative;

          div.row-expanded {
            margin: 0 -18px;
          }
        }
      }

      .btn {
        padding-left: 2.5em;
        padding-right: 2.5em;

        svg {
        }
      }

      p.office-id-error {
        color: @brand-danger;
        text-align: left;
        font-size: 85%;
        margin: 0;
      }
    }
  }

  &.built > div.modal-dialog > div.modal-content {

    @keyframes fadeIn {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }

    ul {
      text-align: left;
      margin: 1em auto 2em;

      li {
        margin: 0 auto 1em;
        opacity: 0;
        position: relative;

        animation-name: fadeIn;
        animation-duration: 0.25s;
        animation-timing-function: ease-in;
        animation-fill-mode: forwards;
        list-style-image: url(../images/blue-spinner.gif);

        &:before {
          content: url(../images/green-check.png);
          position: absolute;
          left: -22px;
          opacity: 0;
          animation-name: fadeIn;
          animation-duration: 0s;
          animation-timing-function: ease-in;
          animation-fill-mode: forwards;
          transform: scale(1.1);
        }

        &:nth-child(1) {
          animation-delay: 0.5s;

          &:before {
            animation-delay: 2.5s;
          }
        }

        &:nth-child(2) {
          animation-delay: 2.5s;

          &:before {
            animation-delay: 5.5s;
          }
        }

        &:nth-child(3) {
          animation-delay: 5.5s;

          &:before {
            animation-delay: 9.5s;
          }
        }

        &:nth-child(4) {
          animation-delay: 9.5s;

          &:before {
            animation-delay: 12.5s;
          }
        }

        &:nth-child(5) {
          animation-delay: 12.5s;

          &:before {
            animation-delay: 14s;
          }
        }

        &:nth-child(6) {
          animation-delay: 14s;

          &:before {
            animation-delay: 15.5s;
          }
        }
      }
    }

    h2 {
      opacity: 0;
      animation: fadeIn 0.25s ease-in 15.75s;
      animation-fill-mode: forwards;

      margin: 1em auto 1em;
    }

    .btn {
      opacity: 0;
      animation: fadeIn 0.25s ease-in 15.75s;
      animation-fill-mode: forwards;
    }
  }
}

div#app-container.mobile ~ div div.modal.demo-modal div.modal-dialog {

  margin-top: 5em;

  form {
    padding: 0;
  }
}
