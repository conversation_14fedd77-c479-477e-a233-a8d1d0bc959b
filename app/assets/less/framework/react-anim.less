.fade-enter {
  opacity: 0.01;
}

.fade-enter.fade-enter-active {
  opacity: 1;
  transition: opacity .15s ease-out;
}

.fade-leave {
  opacity: 1;
}

.fade-leave.fade-leave-active {
  opacity: 0.01;
  transition: opacity .15s ease-in;
}

.slide-enter {
  transform: translateY(-100%);
  opacity: 1;
}

.slide-enter.slide-enter-active {
  transform: translateY(0);
  opacity: 1;
  transition: transform .15s ease-out;
}

.slide-leave {
  transform: translateY(0);
  opacity: 1;
}

.slide-leave.slide-leave-active {
  transform: translateY(-100%);
  opacity: 0;
  transition: transform .15s ease-in, opacity .15s ease-in;
}

.slide-up-enter {
  transform: translateY(100%);
}

.slide-up-enter.slide-up-enter-active {
  transform: translateY(0);
  transition: transform .15s ease-out;
}

.slide-up-leave {
  transform: translateY(0);
}

.slide-up-leave.slide-up-leave-active {
  transform: translateY(100%);
  transition: transform .15s ease-in;
}


.slide-left-enter {
  transform: translateX(100%);
}

.slide-left-enter.slide-left-enter-active {
  transform: translateX(0);
  transition: transform .15s ease-out;
}

.slide-left-leave {
  transform: translateX(0);
}

.slide-left-leave.slide-left-leave-active {
  transform: translateX(100%);
  transition: transform .15s ease-in;
}
