
.btn {
  user-select: none;
  background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214
  background: none;
  border: 1px solid transparent;
  touch-action: manipulation;
  white-space: nowrap;
  -webkit-appearance: none;
  overflow: hidden;
  display: inline-block;
  margin-bottom: 0;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  text-decoration: none;
  outline: 0;
  border-radius: 3px;

  // Size
  padding: @btn-top-padding @btn-side-padding;
  line-height: @base-line-height;
  font-size: @base-font-size;
  font-weight: @btn-font-weight;
  color: @btn-color;

  &:hover,
  &:focus,
  &.focus {
    color: @btn-color;
    text-decoration: none;
  }

  &:active,
  &.active {
    background-image: none;
    outline: 0;
  }

  &.disabled,
  &[disabled] {
    opacity: .75;
    pointer-events: none;
    border-color: @icon-gray;
    background-color: @accent-gray;
    color: #fff;
  }
}

.btn-sm {
  padding: (@btn-top-padding - 2) (@btn-side-padding - 2);
}

.btn-lg {
  padding: (@btn-top-padding + 2) (@btn-side-padding + 5);
}

.btn-wide {
  padding-left: (@btn-side-padding + 5);
  padding-right: (@btn-side-padding + 5);
}

// Block button
// ------------------------------

.btn-block {
  display: block;
  width: 100%;
}

// Specificity overrides
input[type="submit"],
input[type="reset"],
input[type="button"] {
  &.btn-block {
    width: 100%;
  }
}

// Button Variants
// ------------------------------

.btn-primary {
  .button-variant(@brand-primary, @brand-primary);
}

.btn-secondary {
  .button-variant(@brand-secondary, @brand-secondary);
}

.btn-success {
  .button-variant(@brand-success, @brand-success);
}

.btn-info {
  .button-variant(@brand-info, @brand-info);
}

.btn-danger {
  .button-variant(@brand-danger, @brand-danger);
}

.btn-outline, .btn-default {
  color: @brand-primary;
  border-color: @brand-primary;
  fill: @brand-primary;
  stroke: @brand-primary;
  text-transform: uppercase;

  &:hover, &:focus, &:active, &.active {
    color: @brand-primary;
    background-color: @accent-gray;
  }
}

.button-variant(@bg, @border) {
  background-color: @bg;
  border-color: @border;
  fill: #fff;
  stroke: #fff;

  &:hover, &:focus, &:active, &.active {
    background-color: darken(@bg, 5%);
    border-color: darken(@border, 5%);
  }

  svg {
    width: 1em;
    height: 1em;
    margin: 0 0.5em 0 0;
  }
}

// Button Goups
// ------------------------------

.btn-group {
  display: inline-block;
  position: relative;
  vertical-align: middle;

  > .btn {
    float: left;
    border-radius: 0;
    // margin-left: -1px;
    position: relative;

    &:not(:first-child):not(:last-child) {
      border-left: 1px solid rgba(255, 255, 255, 0.5);
      border-right: 1px solid rgba(255, 255, 255, 0.5);
    }

    //    &:first-child {
    //      border-radius: 3px 0 0 3px;
    //    }
    //    &:last-child {
    //      border-radius: 0 3px 3px 0;
    //    }
  }
}
