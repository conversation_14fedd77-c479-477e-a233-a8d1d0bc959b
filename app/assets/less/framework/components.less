
.heading {
  margin-bottom: 1em;
}

.jumbo-container {
  padding: 1.5em;
  border-radius: 3px;
  border: 1px solid @hr-color;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-top-color: lighten(@hr-color, 8%);
  background: #fff;
}

.panel {
  border-radius: 3px;

  .panel-item {
    .clearfix();
    padding: 8px;
    border: 1px solid @hr-color;
    border-top: none;

    &:last-child {
      border-radius: 0 0 3px 3px;
    }
  }

  .panel-heading {
    background: @body-bg;
    border-radius: 3px 3px 0 0;
    color: @text-muted;
    font-weight: 500;
    padding: 8px;
    font-size: 13px;
    border: 1px solid @hr-color;
  }
}
