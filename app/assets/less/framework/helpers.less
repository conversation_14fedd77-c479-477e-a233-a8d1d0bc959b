// ================= Margins
.mn, .m0 {
  margin: 0 !important
}

.mt0 {
  margin-top: 0px !important;
}

.mt5 {
  margin-top: 5px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.mt15 {
  margin-top: 15px !important;
}

.mt20 {
  margin-top: 20px !important;
}

.mt30 {
  margin-top: 30px !important;
}

.mt40 {
  margin-top: 40px !important;
}

.mb5 {
  margin-bottom: 5px !important;
}

.mb10 {
  margin-bottom: 10px !important;
}

.mb15 {
  margin-bottom: 15px !important;
}

.mb20 {
  margin-bottom: 20px !important;
}

.mb30 {
  margin-bottom: 30px !important;
}

.ml5 {
  margin-left: 5px !important;
}

.ml10 {
  margin-left: 10px !important;
}

.ml15 {
  margin-left: 15px !important;
}

.ml20 {
  margin-left: 20px !important;
}

.ml30 {
  margin-left: 30px !important;
}

.mr5 {
  margin-right: 5px !important;
}

.mr3{
  margin-right: 3px !important;
}

.mr10 {
  margin-right: 10px !important;
}

.mr15 {
  margin-right: 15px !important;
}

.mr20 {
  margin-right: 20px !important;
}

.mr30 {
  margin-right: 30px !important;
}

// ================= Padding
.pn, .pdn, .p0 {
  padding: 0 !important
}

.pt0 {
  padding-top: 0px !important;
}

.pt5 {
  padding-top: 5px !important;
}

.pt10 {
  padding-top: 10px !important;
}

.pt15 {
  padding-top: 15px !important;
}

.pt20 {
  padding-top: 20px !important;
}

.pt30 {
  padding-top: 30px !important;
}

.pt40 {
  padding-top: 40px !important;
}

.pb0 {
  padding-bottom: 0px !important;
}

.pb5 {
  padding-bottom: 5px !important;
}

.pb10 {
  padding-bottom: 10px !important;
}

.pb15 {
  padding-bottom: 15px !important;
}

.pb20 {
  padding-bottom: 20px !important;
}

.pb30 {
  padding-bottom: 30px !important;
}

.pl0 {
  padding-left: 0px !important;
}

.pl5 {
  padding-left: 5px !important;
}

.pl10 {
  padding-left: 10px !important;
}

.pl15 {
  padding-left: 15px !important;
}

.pl20 {
  padding-left: 20px !important;
}

.pl30 {
  padding-left: 30px !important;
}

.pr0 {
  padding-right: 0px !important;
}

.pr5 {
  padding-right: 5px !important;
}

.pr10 {
  padding-right: 10px !important;
}

.pr15 {
  padding-right: 15px !important;
}

.pr20 {
  padding-right: 20px !important;
}

.pr30 {
  padding-right: 30px !important;
}

// ================= Text

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-nowrap {
  white-space: nowrap;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-uppercase {
  text-transform: uppercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-muted {
  color: @text-muted;
}

.text-transform-none {
  text-transform: none;
}

.text-block {
  display: inline-block;
}

.text-80 {
  font-size: 80% !important;
}
.text-110 {
  font-size: 110%;
}
.text-115 {
  font-size: 115%;
}
.text-125 {
  font-size: 125%;
}

.text-underline {
  text-decoration: underline;
}

// ================= Misc

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default !important;
}

.clear-both {
  clear: both;
}

.visibility-hidden {
  visibility: hidden;
}

// === Flips

.flip-horizontal {
  transform: scaleX(-1);
  filter: "FlipH";
  -ms-filter: "FlipH";
}

.flip-vertical {
  transform: scaleY(-1);
  filter: "FlipV";
  -ms-filter: "FlipV";
}

.lending-tree-bg {
  background: linear-gradient(88.02deg, #088381 0%, #4EAA7E 100%);
}

.lending-tree-txt {
  color: #08C177;
}