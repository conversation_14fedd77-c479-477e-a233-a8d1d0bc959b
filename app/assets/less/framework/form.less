
textarea {
  overflow: auto;
  resize: vertical;
}

.input-container {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }

  &.inline {
    label {
      margin-top: floor(@base-font-size / 2);
    }
  }
}

label {
  display: inline-block;
  max-width: 100%; // Force IE8 to wrap long content (see https://github.com/twbs/bootstrap/issues/13141)
  margin-bottom: 4px;
  font-size: 13px;
  color: @label-color;
  //  font-weight: 500;
}

.btn-uploader {
  position: relative;
  cursor: pointer !important;

  input[type="file"] {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    margin: 0;
    opacity: 0;
    width: 100%;
    cursor: pointer;
  }
}
