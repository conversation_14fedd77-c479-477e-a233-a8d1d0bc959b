
.nav-scrollable {
  white-space: nowrap;
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
  user-select: none;

  background: #fff; //@body-bg;
  border-bottom: 1px solid @hr-color;
  line-height: @nav-height;
  text-align: center;
  width: 100%;
  z-index: 1;
  font-weight: 400;
  font-size: 17px;

  > div {
    &:hover, &.open {
      color: @brand-red;
    }
  }
  div.item{
    margin: 0 0.1em 0 .4em;
    padding: 0.3em 0.4em;
  }

  a.item, div.item {
    display: inline-block;
    color: @text-color;
    margin: 0 15px;
    cursor: pointer;
    letter-spacing: .75px;
    font-size: 17px;
    text-transform: capitalize;

    button.dropdown-toggle {
      letter-spacing: .75px;
      font-size: 17px;
      text-transform: capitalize;
      font-weight: 400;
      padding: 3px;
    }

    svg {
      stroke: @text-color;
      fill: @text-color;
    }

    &:hover,
    &:focus,
    &.active {
      color: @brand-red;
      text-decoration: none;

      svg {
        stroke: @brand-red;
        fill: @brand-red;
      }
    }

    &.active {
      font-weight: 600;

      button.dropdown-toggle {
        text-transform: uppercase;
        font-weight: 600;
      }
    }
  }

  a.brand {
    height: @nav-height;
    display: inline-block;
    margin: 0 10px 0 15px;

    img {
      padding: 5px 0;
      max-height: 100%;
    }
  }
}

.nav-scrollable::-webkit-scrollbar {
  display: none;
}
