.member-listings-panel {
  text-align: center;

  &.with-bg {
    background-image: url(https://nplayassets.blob.core.windows.net/backgrounds/HOME%20SEARCH%20GRAPHIC.svg);
    background-position: bottom center;
    background-size: cover;
    position: relative;
    height: 100%;
    height: calc(~"100vh - 128px");
    min-height: 600px;
    text-align: center;

    > p {
      display: inline-block;
      margin-top: 30px;
      font-weight: bold;
      font-size: 36px;
      line-height: 42px;
      letter-spacing: 0.5px;

      color: #2B4150;
      max-width: 900px;
      padding: 0 30px;
    }
  }
}

.member-listings-card {
  display: inline-block;
  position: relative;
  width: 250px;
  margin: 12px 8px 8px;
  border: 1px solid #C4C4C4;
  border-radius: 8px;
  overflow: hidden;
  text-align: left;
  background: #fff;
  cursor: pointer;

  &-top {
    height: 192px;
    position: relative;
    overflow: hidden;
    color: #fff;
    padding: 6px 10px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-color: rgba(0, 0, 0, 0.6);

    &-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 20.59%, rgba(0, 0, 0, 0) 54.45%, #000000 100%);
    }

    &-content {
      position: relative;
      height: 100%;
      z-index: 1;

      label {
        font-weight: bold;
        font-size: 10px;
        color: #fff;
      }

      &-bottom {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;

        p {
          line-height: 14px;
          margin: 8px 0;
          padding-top: 4px;
          position: relative;

          .price {
            font-weight: bold;
            font-size: 20px;
          }

          .listed-by {
            font-size: 8px;
          }

          .agent-image {
            width: 36px;
            height: 36px;
            position: absolute;
            top: 0;
            right: 0;
            border: 1px solid #FFFFFF;
            filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.08));
            border-radius: 50%;
          }
        }
      }
    }
  }

  &-bottom {
    padding: 8px 10px;

    &-attributes {
      p {
        display: inline-block;
        font-weight: 600;
        font-size: 12px;

        &:nth-child(1) {
          width: 23%;
        }

        &:nth-child(2) {
          width: 27%;
        }

        &:nth-child(3) {
          width: 50%;
        }
      }
    }

    &-address {
      font-size: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}