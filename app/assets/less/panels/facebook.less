.facebook-embedded{
  position: absolute;
  top: 0;
  z-index: 10;
  width: 100%;
  background-color: #fff;

  &.facebook-header-version {
    top: 45px;

    .grid-container {
      min-height: calc(~"100vh - 45px");
    }
  }

  &.facebook-page-admin {
    top: 70px;

    .grid-container {
      min-height: calc(~"100vh - 70px");
    }
  }

  .grid-container {
    min-height: 100vh;
    
    .grid-cards-container {
      margin-top: 0;
      padding-top: 8px;

      .cards-holder {
        width: 100%;
        text-align: center;

        .card {
          width: 248px;
          text-align: left;

          .photo-button, .tag-button {
            display: none;
          }
        }
      }
    }
  }
}

.agent-profile-outer-container{
  &.facebook-header-version{
    z-index: 10;
    position: relative;
    
    .menu-top{
      border-left: none !important;
      border-right: none !important;
      border-top: none !important;
      z-index: 3;
      width: 100%;
      height: 47px;
      top: 45px !important;
      .text-left{
        width: 100%;
        .headline{
          margin-top: 0.9em;
        }
      }
      padding: 0;
    }
    &.facebook-page-admin .menu-top {
      top: 70px !important;
    }
    .agent-screen-container{
      z-index: 2;
      .menu-left{
        margin-top: 25px;
        border: none !important;
        width: 100%;
        &>div.scroll-container{
          bottom: 14px !important;
          padding-left: 0;
        }
      }
      .menu-right{
        display: none;
      }
    }
  }
}

.onboarding{
  top: 0;
  &.facebook-header-version{
    top: 45px;

    &.facebook-page-admin {
      top: 70px;
    }
  }
}
