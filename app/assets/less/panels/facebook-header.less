@facebook-header-height: 45px;
.facebook-header {
  background-color: @brand-blue;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: @facebook-header-height;
  line-height: @facebook-header-height;
  font-size: 14px;
  z-index: 5;

  .agent-profile-bar {
    display: none;
  }

  &.facebook-page-admin {
    top: 25px;

    .agent-profile-bar {
      display: block;
      background-color: @brand-red;
      color: #fff;
      text-decoration: underline;
      height: 25px;
      position: absolute;
      top: -25px;
      left: 0;
      right: 0;
      line-height: 25px;
      font-size: 80%;
      font-weight: 500;
    }
  }

  .facebook-header-left {
    width: auto;
    display: inline-block;
    vertical-align: top;
    position: relative;
    height: 100%;

    div.agent-container {
      height: 100%;
      margin-left: 25px;
      position: relative;
      .cursor-pointer();
      .license-num{
        padding-left:5px;
      }

      div.agent-texts {
        height: 100%;
        width: 100%;

        p {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          span {
            font-size: 115%;
          }
        }

        &.full-name {

          p {
            color: #fff;
            display: inline-block;
            line-height: @facebook-header-height;
            margin: 0;

            &.agent-name {

            }

            &.brokerage {
              &:before{
                content: "|";
                margin-left: 5px;
                margin-right: 5px;
                vertical-align: top;
              }
            }
          }
        }
      }
    }
  }

  .facebook-header-right {
    display: inline-block;
    width: auto;
    position: absolute;
    vertical-align: top;
    min-width: 455px;
    right: 0;
    top: 0;
    background-color: @brand-blue;
    padding-left: 15px;
    >div{
      display: inline-block;
      padding: 0 20px;
      color: #fff;
      cursor: pointer;
      font-weight: 600;
      position: relative;
      text-transform: uppercase;
      &.active{
        background-color: @icon-blue;
        &:after{
          content: "";
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 8px 8px 0 8px;
          border-color: @icon-blue transparent transparent transparent;
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          position: absolute;
        }
      }
    }
  }

}
