.read-header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 11;
  background: #223949;

  .read-header-scroll-up {
    position: fixed;
    top: 0;
    left: 50%;
    margin-left: -39px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 78px;
    height: 48px;
    background: #F15F4C;
    cursor: pointer;

    svg {
      width: 36px;
      height: 36px;
      fill: #fff;
    }

    opacity: 0;
    pointer-events: none;
    transition: opacity 0.1s ease-out;

    &.active {
      opacity: 1;
      pointer-events: all;
      transition: opacity 0.25s ease-in;
    }
  }
}

.read-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4.4em;
  padding: 0 20px;

  &-left {
    width: 25%;

    svg {
      width: 137px;
      height: 30px;
    }
  }

  &-middle {
    width: 50%;
    text-align: center;
    color: #fff;
    font-style: italic;
    font-weight: 600;
    font-size: 13px;
  }

  &-right {
    width: 25%;
    text-align: right;

    a {
      height: 28px;
      border-radius: 16px;
      color: #fff;
      padding: 5px 20px;
      margin-left: 8px;
      text-decoration: none;
      user-select: none;

      &:nth-child(1) {
        background: #1B2831;

      }
      &:nth-child(2) {
        background: #415E73;
      }
    }
  }
}

.member-search-header, .member-listings-header {
  display: none !important;

  height: 64px;
  background: #fff;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;

  .toggle-container {
    display: inline-block;
    border: 1px solid #C9C9C9;
    box-sizing: border-box;
    border-radius: 12px;
    padding: 5px;
    width: 300px;
    height: 40px;
    display: flex;
    align-items: center;

    a {
      font-size: 15px;
      line-height: 20px;
      border-radius: 6px;
      background: #fff;
      color: #223949;
      text-decoration: none;
      line-height: 30px;
      width: 50%;
      text-align: center;
      user-select: none;

      &.active {
        font-weight: 800;
        background: #223949;
        color: #fff;
      }
    }
  }

  .search-container {
    display: flex;
    align-items: center;

    .member-listings-sort {
      display: block;
      margin-right: 10px;

      > div {
        position: relative;

        .sort-header {
          padding: 6px 12px;
          height: 36px;
          border-radius: 6px;

          > span {
            display: none;

            @media (min-width: @screen-md-min) {
              display: inline;
            }
          }
        }

        .sort-container {
          position: absolute;
          width: 230px;
        }
      }
    }

    .search-button-container {
        font-weight: 400;
        display: flex;
        align-items: center;

        > div.filter-container {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          a.btn {
            display: inline-block;
            background: #2175BE;
            border-color: #2175BE;
            font-weight: 600;
            text-transform: uppercase;
            padding: 6px 12px;
            height: 36px;
            border-radius: 6px;

            @media screen and (max-width: @desktop) {
              span.label-search {
                display: none;
              }
            }
          }
        }

        > div.search-container{
          height: 100%;
          position: relative;

          .search-form {
            display: inline-block;
            position: relative;
            margin-left: 10px;
            width: 30em;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            @media (max-width: @largeDesktop) {
              width: 25em;
            }
            @media (max-width: @desktop) {
              width: 20em;
            }
            @media (max-width: @tablet) {
              width: 14em;
            }
            @media (max-width: @mobile) {
              width: 10em;
            }

            .input-container{
              width: 100%;
              display: flex;
              border: 1px solid #CCCCCC;
              box-sizing: border-box;
              border-radius: 6px;
            }

            .react-autosuggest__container {
              position: relative;
              height: 100%;
              flex: 1 1 auto;

              .react-autosuggest__suggestions-container {
                max-width: 100vw;
                position: absolute;
                top: 100%;
                left: 0;
                border: none;
                background-color: fade(#fff, 90%);

                .react-autosuggest__suggestion {
                  color: #333;
                  padding: 1em 1.5em;
                  font-weight: 500;

                  svg.autosuggest-icon {
                    fill: #333;
                  }

                  .current-location{

                    svg{
                      fill: @icon-blue !important
                    }
                  }

                  &:hover {
                    background-color: @accent-gray;
                    color: #000;

                    span{
                      color: #000;
                    }

                    svg.autosuggest-icon {
                      fill: #333;
                    }
                  }
                }
              }

              input {
                background-color: transparent;
                color: #333;
                border: none;
                font-weight: 400;
                font-size: 14px;
                padding-left: 10px;
                padding-right: 10px;
                text-overflow: ellipsis;
                border-radius: 3px 0px 0px 3px;
                box-sizing: border-box;
                height: 2.5em;

                &::-webkit-input-placeholder {
                  color: #ccc;
                }
                &:-moz-placeholder {
                  color: #ccc;
                }
                &::-moz-placeholder {
                  color: #ccc;
                }

                &:-ms-input-placeholder {
                  color: #ccc;
                  line-height: 0px;
                }

                &::-webkit-search-cancel-button {
                  height: 12px;
                  width: 12px;
                  background: url(https://nplayassets.blob.core.windows.net/images/icons/close-button.svg);
                }

                &::-ms-clear {
                  height: 12px;
                  width: 12px;
                  background: url(https://nplayassets.blob.core.windows.net/images/icons/close-button.svg);
                  color: #000;
                }
              }
            }

            .search-field-icon {
              cursor: pointer;
              height: 2.5em;
              width: 2.5em;
              background-color: @brand-red;
              border-radius: 0px 5px 5px 0px;
              flex: 0 0 2.5em;
              justify-content: center;
              display: flex;
              align-items: center;

              svg {
                fill: white;
                width: 100%;
                height: 100%;
                padding: 0.4em;
              }
            }
          }

          .terms-notice{
            position: absolute;
            bottom: 0px;
            background-color: transparent;
            padding-left: 21.5px;
            display: flex;
            align-content: center;
            line-height: 8px;
            height: 16px;
            align-items: center;
            pointer-events: none;

            .implicit-agree{
              margin: 0px;
              font-size: 8px;
              letter-spacing: 0.5px;
              pointer-events: all;
            }
          }
        }

        div.editing-filters-container {
          position: fixed;
          top: @header-height;
          right: 0;
          bottom: 0;
          width: 30em;
          max-width: 100%;
          line-height: 1em;
          background: #fff;
          overflow-y: auto;

          .editing-filters-controls{
            display: flex;
            align-items: center;
            padding: 1em;
            border-bottom: 1px solid #eee;

            > h3, > div.search-result {
              flex: 1 0 auto;
              text-transform: uppercase;
              color: @brand-primary-dark;
              padding-left: 1em;
              font-weight: 600;
              font-size: 16px;
            }

            .editing-filters-reset{
              flex: 0 1 auto;
              color: @icon-blue;
              text-align: center;
              text-transform: uppercase;
              margin-right: 1em;
              cursor: pointer;

              svg{
                fill: @icon-blue;
                height: 16px;
                width: 16px;
                position: relative;
                top: 0.2em;
                margin-right: 5px;
              }
            }

            > button{
              flex: 0 1 auto;
              font-size: 12px;
              margin: 10px;
              line-height: 2em;
              padding: 0 10px;
              margin: 0.5em;
              text-transform: uppercase;
              letter-spacing: 2px;
            }
          }

          .filters{
            position: relative;
            background-color: #fff;
            width: 100%;
            z-index: 1;
            padding-bottom: 3em;

            .custom-dropdown{
              display: block;
              background-color: #fff;
              margin: 0 2em;

              .btn-group{
                width: 100%;
                background-color: #fff;
              }
              .btn{
                color: @brand-primary-dark;
                text-align: left;
                width: 100%;
                border: none;
                white-space:normal !important;
                padding: 1em;
                font-size: 120%;
                text-transform: none;
                font-weight: 400;

                .caret{
                  float: right;
                  margin-top: 0.5em;
                }
              }

              button.dropdown-toggle {
                border-radius: @border-radius-small;
              }
            }

            .dropdown-menu{
              position: relative;
              border: none;
              box-shadow: none;
              width: 100%;

              a {
                border-radius: @border-radius-small;
              }
            }

            .custom-dropdown.not-empty{
              .btn{
                color: @brand-red;
              }
            }

            p {
              margin: 0.5em 2em 0.2em;
            }

            .keywords-select {
              margin: 0.5em 2em;

              .Select-control {
                border-color: @brand-blue;
                border-radius: @border-radius-small;
                cursor: text;

                &:hover, &:focus, &:active, &.active {
                  color: #000;
                  background-color: #f5f5f5;
                }

                .Select-placeholder {
                  height: 100%;
                }

                .Select-item {
                  border-radius: 1em;
                  padding: 0 0.25em;
                  color: @brand-primary;
                  background-color: #fafafa;

                  .Select-item-icon {
                    float: right;
                    border: none;

                    &:hover {
                      color: @brand-primary;
                      font-weight: bold;
                      background-color: transparent;
                    }
                  }
                }

                .Select-arrow-zone, .Select-arrow {
                  display: none;
                }

                input {
                  color: @brand-primary;
                }
              }

              // Hide Dropdown for now
              .Select-menu-outer {
                border-radius: 0;

                .Select-noresults {
                  display: none;
                }

                .Select-option {

                  &.is-focused {
                    background-color: #f5f5f5;
                  }
                }
              }
            }
          }
        }

        div.editing-filters-overlay {
          position: fixed;
          top: @header-height;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0,0,0,.5);
        }
      }
  }
}