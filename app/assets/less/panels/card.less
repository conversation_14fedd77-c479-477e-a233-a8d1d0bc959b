.card {
  clear: both;
  color: #fff;
  height: 12em;
  padding: 0 .5em .5em .5em;
  background-color: rgba(0,0,0,.5);
  position: relative;

  a{
    color: #fff;
  }

  .cursor-pointer();

  .card-front {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ccc;
    //background: url('../images/spinner.gif') no-repeat center center;

    .card-image {
      height: 100%;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      filter: hue-rotate(10deg) saturate(1.10) contrast(1.10);
      transition: background-image 0.1s ease-in-out;
    }
    .photo-button {
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      padding: 0.2em 0.5em;

      .photo-icon {
        fill: #000;
        fill-opacity: 0.4;
        stroke: #fff;
        stroke-width: 0.2em;
        stroke-opacity: 1;
        width: 1.4em;
        height: 1.4em;
        margin-top: 0.3em;
        filter: drop-shadow( 1px 1px 1px #000 );
        float: left;
      }

      .photo-count {
        text-shadow: 1px 1px 1px #000;
        float: left;
        margin-top: 0.3em;
        margin-left: 0.4em;
      }
    }

    .days-in-market {
      position: absolute;
      right: 0;
      bottom: 2.4em;
      background-color: rgba(0, 0, 0, .4);

      small {
        float: right;
        width: 4em;
      }

      p {
        float: right;
        margin: 0;
        font-size: 2em;
        line-height: 1.1em;
        margin-right: 0.15em;
        margin-left: 0.2em;
      }
    }
    .card-broker-logo{
      &>img{
        border-radius: 0px !important;
      }
      position: absolute;
      top: -50px;
      right: 4px;
      img{
        max-width: 200px;
        max-height: 30px;
      }
    }
    .courtesy {
      position: absolute;
      top: -1.1em;
      left: 0;
      right: 0;
      padding: 0px 0.4em;
      height: 1.1em;
      #gradient.vertical(rgba(0, 0, 0, 0.0); rgba(0, 0, 0, .20); 0%; 100%);
      &.courtesy-large{
        height: 1.5em;
        top: -1.5em;
        z-index: 1;
        > p{
          font-size: 100% !important;
        }
      }
      > p {
        font-family: Tahoma, Geneva, sans-serif;
        font-weight: 100;
        font-size: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .rateplug-monthly-wrapper {
      margin-left: -6px;
      margin-right: -6px;

      > .rateplug-monthly-line {
        padding: 1px 6px;
        display: inline-flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        background: @brand-primary;
        color: #fff;

        p {
          margin: 0;
          font-weight: 600;
          font-size: 14px;
        }
      }
    }
    svg.new-label {
      width: 58px;
      height: 26px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 50px;
    }

    .bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0 6px;
      #gradient.vertical(rgba(0, 0, 0, 0.30); rgba(0, 0, 0, .6); 0%; 100%);
      box-sizing: content-box;

      .address {
        display: none;
      }

      .bottom-left {
        float: left;
        letter-spacing: 0.05em;
        font-size: 15px;
        line-height: 30px;
        margin: 0 10px 0 0;
      }
      .bottom-right {
        float: right;
        margin-bottom: 0;
        line-height: 30px;
        > p {
          display: inline-block;
          margin: 0;
          margin-left: 0.2em;

          &:first-child {
            margin-left: 0;
          }

          &:after {
            content: '|';
            margin-left: 0.3em;
            margin-right: 0.1em;
            color: rgba(255, 255, 255, 0.5);
          }

          &:last-child:after {
            content: '';
            margin: 0;
          }
        }
      }

      &.land-tenure {
        .bottom-right {
          float: left;
          clear: left;
        }
        .bottom-left, .bottom-right {
          line-height: 1em;
          margin-bottom: 6px;
        }
      }
    }

    &:hover {
      .bottom {
        border-top: 0.2em solid @detail-blue;
        #gradient.vertical(rgba(0, 0, 0, 0.60); rgba(0, 0, 0, .9); 0%; 100%);
      }
    }

    .card-front-clickable {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;

    }
  }

  &.active {
    .card-front .bottom {
      border-top: 0.2em solid @brand-info;
    }
  }

  .tag-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 1.5em 1.75em;
    background-color: @brand-primary-dark;
    min-height: 100%;
    z-index: 1;
    cursor: default;

    > label {
      color: #fff;
      opacity: 0.59;
    }

    .links-wrapper {
      position: absolute;
      bottom: .7em;
      > button {
        margin: 0 0 10px 80px;
        border: 1px solid #ffffff;
        color: #ffffff;
        padding: 1px 7px;
        &:hover {
          background-color: rgba(0, 0, 0, 0.2)
        }
      }
      > a {
        color: #ffffff !important;
        text-decoration: underline;
        font-size: .9em;
      }
    }

    .tags {
      padding: 0.5em 0;
      float: left;

      label.tag {
        color: #fff;
        border: 1px solid @brand-primary;
        margin-right: 0.75em;
        margin-bottom: 0.5em;
        padding: 0.1em 0.7em 0.2em;
        border-radius: 1em;
        word-break: break-all;
        float: left;
        display: block;

        .cursor-pointer();

        &:hover {

        }

        &.active {
          background-color: @brand-primary;
        }
      }
    }

    form.add-tag {
      clear: both;
      margin-bottom: 2em;

      .btn-tagsubmit {
        background-color: #ffffff;
        color: @brand-primary-dark;
        padding: 4px 2px;
        font-weight: bold;
        font-size: .9em;

        &:hover {
          background-color: rgba(255, 255, 255, 0.8)
        }
      }

      input#new-tag {
        background: none;
        height: 2.2em;
        border: 1px solid @body-bg;
        float: left;
        width: 84%;
        font-size: .9em;
        &:focus {
          color: #fff;
        }
      }
    }
  }

  .tag-button {
    position: absolute;
    top: 0;
    right: 0;
    width: 1.4em;
    height: 1.4em;
    margin: 0.4em 0.5em;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 1.4em;
    box-sizing: content-box;
    .cursor-pointer();

    svg {
      width: 100%;
      height: 100%;

      &.tag-off {
        fill: #fff;
        padding: 0.2em;
      }

      &.tag-close-button {
        fill: #fff;
        stroke: #fff;
        padding: 0.3em;
      }

      &.tag-on {
        fill: @brand-tag;
        padding: 0.2em;
      }

      &.tag-favorite {
        fill: @brand-fav;
        padding: 0.2em;
      }

      &.tag-dislike {
        fill: #fff;
        stroke: #fff;
        stroke-width: 0.25em;
      }
    }
  }

  &.highlightFeatured{
    box-shadow: 0px 0px 5px 2px @brand-red;
    border: 2px solid @brand-red;

  }
}

.--highlight\:SpecialFinancePrograms__VA .card.--tag\:SpecialFinancePrograms__VA,
.--highlight\:SpecialFinancePrograms__FHA .card.--tag\:SpecialFinancePrograms__FHA,
.--highlight\:SpecialFinancePrograms__FHACondo .card.--tag\:SpecialFinancePrograms__FHACondo,
.--highlight\:SpecialFinancePrograms__USDA .card.--tag\:SpecialFinancePrograms__USDA,
.--highlight\:SpecialFinancePrograms__Assumable .card.--tag\:SpecialFinancePrograms__Assumable {
  .rateplug-monthly-wrapper > .rateplug-monthly-line {
    background: @brand-specialfinancing;
  }
}