.agent-search{
  filter: none !important;
  position: absolute;
  min-height: 100vh;
  width: 100%;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  align-items: center;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(19,35,47,0.7);
  padding-top: 32px;
  padding-bottom: 32px;

  .container{
    z-index: 2;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    flex-direction: column;

    .title{
      font-size: 22px;
      color: white;
      text-align: center;
    }

    .location{
      font-size: 22px;
      color: @brand-red;
      font-style: italic;
    }

    .title, .location{
      font-weight: 600;
      margin-bottom: 16px;
    }

    .best-agent, .agents-list{
      margin-top: 8px;
    }

    .agents-list{
      margin-bottom: 8px;
    }


    .show-more{
      padding: 12px;
      font-weight: 400;
      font-size: 16px;
      border: none;
      width: 100%;
      text-transform: uppercase;
      color: rgba(255,255,255,0.8);
      background-color: rgba(19,35,47,.8);
      border-radius: 3px;
      max-width: 450px;
      letter-spacing: 1px;
      margin: 0 auto;
    }
  }

  .agent-search-result{
    padding: 16px;
    display: flex;
    flex-direction: column;
    border-radius: 3px;
    background-color: white;
    color: #333;
    box-shadow: 0px 1px 5px 1px rgba(0,0,0,.3);
    max-width: 450px;
    transition: all 0.2s ease;    
    transform-origin: center;

    .score-holder, .agent-since{
      line-height: 16px;

      div:first-child{
        font-size: 14px;
        font-weight: 400;
      }

      div:last-child{
        font-size: 14px;
        font-weight: 600;
      }
    }


    .content{
      margin-bottom: 16px;
      display: flex;
      flex-direction: column;
      flex: 1 1 auto;
    }

    .top{
      display: flex;

      .image{
        flex: 0 0 128px;
        margin: 4px;
        margin-right: 20px;
        position: relative;

        .picture{
          border-radius: 50%;
          width: 128px;
          height: 128px;
          flex-shrink: 0;
          border: 2px solid #333;
          box-shadow: 0px 1px 4px 0px rgba(0,0,0,.3);

          div.pro-badge-container {
            top: 0px;
            left: -10px;
            width: 40px;
            height: 40px;
          }
        }

        .agent-since{
          display: none;
        }


        .featured{
          position: absolute;
          bottom: -2px;
          text-align: center;
          background-color: @brand-red;
          color: white;
          font-weight: 600;
          font-size: 12px;
          padding: 4px 12px;
          display: none;

          &:before{
            position: absolute;
            content: "";
            left: 0;
            transition: all 0.2s ease;
            top: 0;
            border-right: 0px solid @brand-red;
            border-top: 13px solid @brand-red;
            border-bottom: 12px solid @brand-red;
            border-left: 5px solid white;
          }

          &:after{
            position: absolute;
            content: "";
            right: 0;
            top: 0;
            transition: all 0.2s ease;
            border-left: 0px solid @brand-red;
            border-top: 13px solid @brand-red;
            border-bottom: 12px solid @brand-red;
            border-right: 5px solid white;
          }
        }

      }

      .top-details{
        font-size: 16px;
        color: #222;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-width: 0%;

        .agent-name{
          font-size: 22px;
          font-weight: 500;
          margin-bottom: 5px;
          line-height: 22px;
          padding-bottom: 3px;
        }

        .broker-name{
          color: #999;
          margin-bottom: 14px;
          font-size: 14px;
          font-weight: 500;
          line-height: 14px;
          padding-bottom: 2px;
        }

        .specializing{
          margin-bottom: 16px;
          font-size: 14px;
          font-weight: 500;
          letter-spacing: 0.5px;
          line-height: 16px;
        }

        .specializing, .broker-name, .agent-name{
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agent-since{
          font-size: 12px;
          line-height: 12px;

          span{
            font-weight: bold;
            margin-left: 5px;
          }
        }
      }


    }

    .bottom-button{
      background-color: rgb(90,181,70);
      border-radius: 3px;
      border-bottom: 3px solid rgb(66,158,56);
      color: white;
      font-weight: 500;
      border-left: none;
      border-right: none;
      border-top: none;
      text-shadow: 0px 1px 3px rgb(66,158,56);
      padding: 12px;
      font-size: 17px;
      width: 100%;
      transition: all 0.2s ease;
    }

    &:hover{
      background-color: rgb(235,242,250);
      transform: scale(1.01);

      .featured{

        &:before{
          border-left-color: rgb(235,242,250) !important;
        }

        &:after{
          border-right-color: rgb(235,242,250) !important;
        }
      }

      .bottom-button{
        background-color: rgb(74,165,63);
      }
    }
  }

  .best-agent{

    a{
      text-decoration: none;
    }

    .or-divider{
      text-align: center;
      font-weight: 600;
      font-size: 22px;
      color: white;
      margin: 16px auto;
    }

    .agent-search-result{

      .featured{
        display: block !important;
      }
    }
  }


  .agents-list{
    z-index: 3;
    max-width: 1024px;
    margin: 0 auto;
    padding: 0;
    color: #333;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    li{
      list-style: none;
      flex: 0 0 256px;
      transform-origin: center;
      transition: transform 0.3s ease;
      margin: 0px 8px 16px 8px;
      min-width: 450px;

      &:first-child{

        .agent-search-result{

          .featured{
            display: block !important;
          }
        }
      }
    }

    a{
      color: #333;
      text-decoration: none;
    }

  }
}

.more-agents{
  display: flex;
  flex-direction: column;
}
