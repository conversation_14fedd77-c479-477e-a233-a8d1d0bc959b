.rateplug-landing-modal {

  &.in {
    display: flex !important;
  }

  .modal-dialog {
    width: 920px;
    max-width: 90vw;
    margin: auto;

    .modal-content {
      border: 4px solid @brand-primary;
      box-shadow: 0px 4px 16px -2px rgba(0, 0, 0, 0.8);
      border-radius: 15px;
    }

    .rateplug-landing-step {
      padding: 25px;

      h2 {
        font-weight: 800;
        font-size: 22px;
        line-height: 30px;
        margin-bottom: 40px;

        text-align: center;
        letter-spacing: 0.3em;
        text-transform: uppercase;

        &:before, &:after {
          content: '\2000\2013\2000'; /* en dash */
        }
      }

      > .rateplug-landing-flex-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        > * {
          width: 50%;
        }

        > div {
          padding: 20px;

          h3 {
            font-weight: 700;
            font-size: 28px;
            line-height: 38px;
            margin-bottom: 10px;
          }

          ul {
            padding-left: 16px;
          }

          .btn {
            padding: 10px;
          }

          .btn-primary {
            margin-top: 40px;
          }

          .btn-link {
            color: #000;
            text-decoration: underline;
          }

          h4 {
            font-weight: 700;
            font-size: 26px;
            line-height: 38px;
            margin-bottom: 10px;
          }
        }
      }

      &-1 {

      }

      &-3 {
        .visible-mobile {
          display: none;
        }
        .hidden-mobile {
          display: block;
        }
      }

      .rateplug-landing-pager {
        position: absolute;
        width: 50px;
        bottom: -50px;
        left: calc(~"50% - 25px");
      }

      .rateplug-landing-next, .rateplug-landing-last {
        display: inline-block;
        position: absolute;
        cursor: pointer;
        width: 60px;
        height: 60px;
        top: calc(~"50% - 30px");
        right: -30px;
        font-size: 0;
        background-size: cover;
        background-image: url(https://nplayassets.blob.core.windows.net/search2/rateplug/rateplug-onboarding-next.png);
      }

      .rateplug-landing-last {
        transform: rotate(180deg);
        right: unset;
        left: -30px;
      }
    }
  }


  &.mobile {
    // &.in {
    //   display: block !important;
    // }

    .modal-dialog {
      // margin-top: 25px;

      .rateplug-landing-step {
        padding: 10px;

        h2 {
          font-size: 16px;
          line-height: 22px;
          margin-left: -25px;
          margin-right: -25px;

          &:before, &:after {
            content: '';
          }
        }

        > .rateplug-landing-flex-wrapper {
          flex-direction: column;

          > * {
            width: 100%
          }

          > div {
            padding: 0;
            margin-top: 20px;

            h3 {
              font-size: 28px;
              line-height: 41px;
            }

            h4 {
              font-size: 18px;
              line-height: 22px;
              text-align: center;
              text-transform: uppercase;
            }
          }
        }

        &-1 {

        }

        &-3 {
          .visible-mobile {
            display: block;
          }
          .hidden-mobile {
            display: none;
          }
        }
      }
    }
  }
}