.listing-shadow {
  position: fixed;
  top: 0;
  bottom: 0;
  right: calc(~"@{content-right-width} - 10px");
  width: 10px;
  box-shadow: -3px 0 5px 0px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.listings-container {
  padding: 0;
}

.listings-mls-container{
  .mls-disclosure {
    padding: 10px;
    font-size: 14px;

    p {
      margin-top: 5px;

      &.extra-message {
        font-weight: 700;
      }
    }
  }
}

.last-refreshed{

  margin: 0 10px;
  text-align: center;

  strong {
    font-weight: 600;
  }

  img {
    max-height: 100px;
    max-width: 250px;
  }

  .last-refreshed-broker-logo{
    max-height: 50px;
    margin-left: 8px;
    margin-right: 8px;
    max-width: 120px;
  }
}

.body-listings, .featured-listings, .tagged-listings{
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .listings-container{
    flex: 1 0 auto;
  }

  .mls-disclosure {
    padding: 10px;
    font-size: 14px;

    p {
      margin-top: 5px;

      &.extra-message {
        font-weight: 700;
      }
    }
  }

  .n-play-footer{
    .n-play-footer-powered{
      display: none;
    }
  }
}

.blur .body-listings{
  max-height: ~"calc(100vh - 102px)";
  overflow: hidden;
}

.body-listings{
  min-height: ~"calc(100vh - 102px)";
  margin-top: 40px;
}

.featured-listings{
  min-height: ~"calc( 100vh - 62px )";

  div.svg-featured-listings-container {
    width: 20em;
    background-color: @brand-darkblue;

    svg {
      height: 3em;
      fill: #fff;
      padding: .5em 0;
    }
  }
}

.right-rail-footer{
  .last-refreshed{
    img{
      max-width: 250px;
    }
  }
}

.listings-no-results{
  text-align: center;
  padding: 10px;
  background-color: @accent-gray;
  font-size: 14px;
  padding-bottom: 20px;

  svg{
    fill: @icon-blue;
    width: 30px;
    height: 30px;
    margin: 10px auto;
  }

}

.listings-no-results-zoom{
  text-align: center;
  padding: 100px 20px;
  font-size: 20px;

  svg{
    width: 160px;
    height: 125px;
    margin-left: 25px;
  }

}

.horizontal-listings-container {
  width: 100vw;
  height: 12em;

  .card-container {
    position: relative;
    width: 100vw;
    float: left;
    clear: none;
  }
}
