div.agent-listing-notification-container {

  div.agent-listing-notification-container-small {
    position: fixed;
    z-index: 102;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 0);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    background: #fff;
    box-shadow: 0 0 15px rgba(0,0,0,.25);
    padding: 5px 15px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    img.agent-listing-notification-container-agent-image {
      width: 2em;
      height: 2em;
      margin: 0 10px 0 0;
      border-radius: 50%;
    }

    p.message {
      font-weight: 600;
      margin: 0;
      color: #5d9ed6;
      max-width: 350px;

      a {
        text-decoration: underline;
      }
    }

    .action-button {
      background: #5d9ed6;
      border-color: #5d9ed6;
      margin-left: 5px;
    }
  }
}



// ReactCSSTransition - agent-listing-notification-transition
.agent-listing-notification-transition-appear {
  transform: translateY(120%);

  &-active {
    transform: translateY(0);
    transition: transform 300ms ease;
  }
}
.agent-listing-notification-transition-enter {
  transform: translateY(120%);

  &-active {
    transform: translateY(0);
    transition: transform 300ms ease;
  }
}
.agent-listing-notification-transition-leave {
  transform: translateY(0);
  transition: transform 300ms ease-out;

  &-active {
    transform: translateY(120%);
  }
}


// ReactCSSTransition - agent-listing-notification-sm-transition
.agent-listing-notification-sm-transition-appear {
  margin-bottom: -50px;

  &-active {
    margin-bottom: 0;
    transition: margin-bottom 300ms ease 300ms;
  }
}
.agent-listing-notification-sm-transition-enter {
  margin-bottom: -50px;

  &-active {
    margin-bottom: 0;
    transition: margin-bottom 300ms ease 300ms;
  }
}
.agent-listing-notification-sm-transition-leave {
  margin-bottom: 0;
  transition: transform 300ms ease-out;

  &-active {
    margin-bottom: -50px;
  }
}

.agent-listing-notification-mobile-container {
  position: fixed;
  z-index: 102;

  display: block;
  background: #5d9ed6;
  color: #fff;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 8px 0;

  text-align: center;
  text-decoration: underline;
}