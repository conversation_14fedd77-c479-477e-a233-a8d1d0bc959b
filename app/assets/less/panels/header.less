.layout--header {
  background-color: @brand-primary-darker;

  .header-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: @content-right-width;
    z-index: 1;
    background-color: @brand-primary-darker;

    @media (max-width: @largeDesktop) {
      right: 15em;
    }

    div.agent-container {
      @img-ratio: 1.6;
      height: 100%;
      float: left;
      position: relative;
      .cursor-pointer();
      .license-num{
        padding-left:5px;
      }
      @temp: 42em;
      width:calc(~"(99.999999% - @{temp})");

      @media (max-width: @largeDesktop) {
        @temp:37em;
        width:calc(~"(99.999999% - @{temp})");
      }
      @media (max-width: @desktop) {
        @temp: 28em;
        width:calc(~"(99.999999% - @{temp})");
      }
      @media (max-width: @tablet) {
        @temp: 22em;
        width:calc(~"(99.999999% - @{temp})");
      }
      @media (max-width: @mobile) {
        @temp: 18em;
        width:calc(~"(99.999999% - @{temp})");
      }

      &:hover {
        background-color: @brand-darkblue;
      }

      .agent-header-image {
        width: @header-height * @img-ratio;
        height: @header-height * @img-ratio;
        margin-left: 1em;
        margin-top: @header-height * 0.17;
        border-radius: 50%;
        position: absolute;
        border: 2px solid #fff;
        box-shadow: -3px 3px 6px rgba(0, 0, 0, 0.3);
        z-index: 1;

        div.pro-badge-container {
          top: 0px;
          left: -7px;
          width: 30px;
          height: 30px;
        }
      }

      div.dropdown-chevron {
        position: absolute;
        top: @header-height / 3 + 0.25em;
        right: 0.75em;

         svg {
           width: @header-height / 3;
           height: @header-height / 3;
           fill: #fff;
           stroke: #fff;
         }

         a{
           color: #fff;
           text-decoration: none;
         }
      }

      @agent-image-width: 113px;
      div.header-agent-dropdown{
        display: none;
        position: absolute;
        top: 100%;
        width: calc( ~"100% - @{agent-image-width} + 8px" );
        left: @agent-image-width;
        background-color: fade(@brand-blue, 90%);
        min-width: 275px;
        color: #fff;
        &.visible{
          display: block;
        }
        ul{
          margin: 0;
          padding: 0;
          list-style: none;
          li{
            padding: 8px 0px;
            &:hover{
              background-color: @brand-darkblue;
            }
            &:first-child{
              font-weight: 500;
            }
            &:last-child {
              border-top: 1px solid fade(#fff, 70%);
            }
            span{
              vertical-align: middle;
            }
            svg{
              margin: 0 20px;
              width: 35px;
              height: 35px;
              fill: #fff;
              vertical-align: middle;
            }

          }
        }

        .share-agent{

          span{
            margin-right: 5px;
          }

          a{
            display: inline-block;
          }

          svg{
            width: 28px;
            height: 28px;
            margin: 0px 3px;
            opacity: 0.8;
          }
        }
      }

      div.agent-texts {
        height: 100%;
        width: 100%;

        p {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          span {
            font-size: 115%;
          }
        }

        &.full-name {
          padding-top: @header-height / 3 / 2;
          padding-left: @header-height * @img-ratio + 2.5em;
          padding-right: @header-height / 3 * 2;

          p {
            color: #fff;
            display: block;
            line-height: @header-height / 3;
            margin: 0;

            &.agent-name {
              font-weight: 600;
            }

            &.brokerage {

            }
          }

          @media (max-width: @desktop) {

            display: none;
          }
        }

        &.first-name {
          display: none;
          padding-left: @header-height * @img-ratio + 1.5em;
          padding-right: @header-height / 3 * 2;

          @media (max-width: @desktop) {
            display: block;

            p {
              color: #fff;
              display: block;
              line-height: @header-height;
              margin: 0;

              &.agent-name {
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    div.controls-container {
      height: 100%;
      float: right;
      font-weight: bold;

      &.search-button-container {
        font-weight: 400;

        > div.filter-container {
          float: right;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          a.btn {
            display: inline-block;
            font-weight: 600;
            border-radius: 0;
            text-transform: uppercase;
            border-radius: 3px;
            padding: 6px 12px;
            height: 2.5em;


            @media screen and (max-width: @desktop) {
              span.label-search {
                display: none;
              }
            }
          }
        }

        > div.search-container{
          float: right;
          height: 100%;
          position: relative;

          .search-form {
            display: inline-block;
            position: relative;
            width: 30em;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            @media (max-width: @largeDesktop) {
              width: 25em;
            }
            @media (max-width: @desktop) {
              width: 20em;
            }
            @media (max-width: @tablet) {
              width: 14em;
            }
            @media (max-width: @mobile) {
              width: 10em;
            }

            .input-container{
              width: 90%;
              display: flex;
            }

            .react-autosuggest__container {
              position: relative;
              height: 100%;
              flex: 1 1 auto;

              .react-autosuggest__suggestions-container {
                max-width: 100vw;
                position: absolute;
                top: 100%;
                left: 0;
                border: none;
                background-color: fade(#fff, 90%);

                .react-autosuggest__suggestion {
                  color: #333;
                  padding: 1em 1.5em;
                  font-weight: 500;

                  svg.autosuggest-icon {
                    fill: #333;
                  }

                  .current-location{

                    svg{
                      fill: @icon-blue !important
                    }
                  }

                  &:hover {
                    background-color: @accent-gray;
                    color: #000;

                    span{
                      color: #000;
                    }

                    svg.autosuggest-icon {
                      fill: #333;
                    }
                  }
                }
              }

              input {
                background-color: white;
                color: #333;
                border: none;
                font-weight: 400;
                font-size: 14px;
                padding-left: 20px;
                padding-right: 10px;
                text-overflow: ellipsis;
                border-radius: 3px 0px 0px 3px;
                box-sizing: border-box;
                height: 2.5em;

                &::-webkit-input-placeholder {
                  color: #ccc;
                }
                &:-moz-placeholder {
                  color: #ccc;
                }
                &::-moz-placeholder {
                  color: #ccc;
                }

                &:-ms-input-placeholder {
                  color: #ccc;
                  line-height: 0px;
                }

                &::-webkit-search-cancel-button {
                  height: 12px;
                  width: 12px;
                  background: url(https://nplayassets.blob.core.windows.net/images/icons/close-button.svg);
                }

                &::-ms-clear {
                  height: 12px;
                  width: 12px;
                  background: url(https://nplayassets.blob.core.windows.net/images/icons/close-button.svg);
                  color: #000;
                }
              }
            }

            .search-field-icon {
              cursor: pointer;
              height: 2.5em;
              width: 2.5em;
              background-color: @brand-red;
              border-radius: 0px 3px 3px 0px;
              flex: 0 0 2.5em;
              justify-content: center;
              display: flex;
              align-items: center;

              svg {
                fill: white;
                width: 100%;
                height: 100%;
                padding: 0.4em;
              }
            }
          }

          .terms-notice{
            position: absolute;
            bottom: 0px;
            background-color: transparent;
            padding-left: 21.5px;
            display: flex;
            align-content: center;
            line-height: 8px;
            height: 16px;
            align-items: center;
            pointer-events: none;

            .implicit-agree{
              margin: 0px;
              font-size: 8px;
              letter-spacing: 0.5px;
              pointer-events: all;
            }
          }
        }

        div.editing-filters-container {
          position: fixed;
          top: @header-height;
          right: 0;
          bottom: 0;
          width: 30em;
          line-height: 1em;
          background: #fff;
          overflow-y: auto;

          .editing-filters-controls{
            display: flex;
            align-items: center;
            padding: 1em;
            border-bottom: 1px solid #eee;

            > h3, > div.search-result {
              flex: 1 0 auto;
              text-transform: uppercase;
              color: @brand-primary-dark;
              padding-left: 1em;
              font-weight: 600;
              font-size: 16px;
            }

            .editing-filters-reset{
              flex: 0 1 auto;
              color: @icon-blue;
              text-transform: uppercase;
              margin-right: 0.5em;
              cursor: pointer;

              svg{
                fill: @icon-blue;
                height: 16px;
                width: 16px;
                position: relative;
                top: 0.2em;
                margin-right: 5px;
              }
            }

            > button{
              flex: 0 1 auto;
              font-size: 12px;
              margin: 10px;
              line-height: 2em;
              padding: 0 10px;
              margin: 0.5em;
              text-transform: uppercase;
              letter-spacing: 2px;
            }
          }

          .filters{
            position: relative;
            background-color: #fff;
            width: 100%;
            z-index: 1;
            padding-bottom: 3em;

            .active-toggle {
              padding: 0 2em;

              button {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1em;
                padding-right: 0.5em;
                color: @brand-primary-dark;
                text-align: left;
                width: 100%;
                border: none;
                white-space: normal !important;
                font-size: 120%;
                text-transform: none;
                font-weight: 400;

                svg {
                  width: 2rem;
                  height: 2rem;
                  transform: translateY(-3px);

                  &.icon-checkbox-unchecked {
                    fill: #000;
                  }
                }
              }
            }

            .custom-dropdown{
              display: block;
              background-color: #fff;
              margin: 0 2em;

              .btn-group{
                width: 100%;
                background-color: #fff;
              }
              .btn{
                color: @brand-primary-dark;
                text-align: left;
                width: 100%;
                border: none;
                white-space:normal !important;
                padding: 1em;
                font-size: 120%;
                text-transform: none;
                font-weight: 400;

                .caret{
                  float: right;
                  margin-top: 0.5em;
                }
              }

              button.dropdown-toggle {
                border-radius: @border-radius-small;
              }
            }

            .dropdown-menu{
              position: relative;
              border: none;
              box-shadow: none;
              width: 100%;

              a {
                border-radius: @border-radius-small;
              }
            }

            .custom-dropdown.not-empty{
              .btn{
                color: @brand-red;
              }
            }

            p {
              margin: 0.5em 2em 0.2em;
            }

            .keywords-select {
              margin: 0.5em 2em;

              .Select-control {
                border-color: @brand-blue;
                border-radius: @border-radius-small;
                cursor: text;

                &:hover, &:focus, &:active, &.active {
                  color: #000;
                  background-color: #f5f5f5;
                }

                .Select-placeholder {
                  height: 100%;
                }

                .Select-item {
                  border-radius: 1em;
                  padding: 0 0.25em;
                  color: @brand-primary;
                  background-color: #fafafa;

                  .Select-item-icon {
                    float: right;
                    border: none;

                    &:hover {
                      color: @brand-primary;
                      font-weight: bold;
                      background-color: transparent;
                    }
                  }
                }

                .Select-arrow-zone, .Select-arrow {
                  display: none;
                }

                input {
                  color: @brand-primary;
                }
              }

              // Hide Dropdown for now
              .Select-menu-outer {
                border-radius: 0;

                .Select-noresults {
                  display: none;
                }

                .Select-option {

                  &.is-focused {
                    background-color: #f5f5f5;
                  }
                }
              }
            }
          }
        }

        div.editing-filters-overlay {
          position: fixed;
          top: @header-height;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0,0,0,.5);
        }
      }
    }

    .header-spinner-container{
      @img-ratio: 1.6;

      position: absolute;
      width: @header-height * @img-ratio;
      height: @header-height * @img-ratio;
      margin-left: 1em;
      margin-top: @header-height * 0.17;
      .spinner-round-component{
        border-color: fade(#000, 30%);
        border-right-color: transparent;
        width: @header-height * @img-ratio;
        height: @header-height * @img-ratio;
      }

    }
  }

  .header-right {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: @content-right-width;
    height: @header-height;

    .direct-login-button{
      padding: 8px 12px;
      border: 1px solid @brand-red;
      color: white;
      background-color: transparent;
      border-radius: 3px;
      position: relative;
      top: 50%;
      margin-right: 18px;
      float: right;
      transform: translate(0, -50%);
      outline: none;

      &:hover{
        background-color: @brand-red;

        span.total-count {
          color: @brand-darkblue;
          background: #fff;
        }
      }

      span.total-count {
        position: absolute;
        top: -5px;
        right: -10px;
        padding: 0 4px;
        min-width: 21px;
        text-align: center;
        border-radius: 100px;
        background: @brand-danger;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        -webkit-background-clip: padding-box;

        font-size: 70%;
        font-weight: 500;
        color: white;
        text-decoration: none;
      }
    }

    .buyer-container {
      height: 100%;
      float: right;
      padding-right: 1em;

      > * {
        float: right;
      }

      .buyer-name{
        text-overflow: ellipsis;
        max-width: 145px;
        white-space: nowrap;
        overflow: hidden;
      }

      .image-container {
        @img-ratio: 0.62;
        width: @header-height * @img-ratio;
        margin: @header-height * (1-@img-ratio)/2;
        margin-right: 3px;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }

        label.buyer-num {
          color: #fff;
          margin: 0;
          position: absolute;
          top: 0;
          left: -0.75em;
        }
      }

      p {
        line-height: @header-height;
        color: #fff;
        padding-left: 0.5em;

        a {
          color: #fff;
          .cursor-pointer();
        }
      }
    }

    .tag-control {
      width: @header-height * 2/3;
      height: @header-height;
      float: right;
      padding: 0;
      justify-content: center;
      display: flex;
      align-items: center;

      fill: #fff;

      svg {
        width: @header-height * 0.4;
        height: @header-height * 0.4;
      }
    }

    .help-control {
      width: @header-height * 2/3;
      height: @header-height;
      float: right;
      padding: 0;
      color: #fff;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;

      &:hover, &:active{
        background-color: @brand-primary-darker !important;
      }

      .help-icon {
        width: @header-height * 0.4;;
        height: @header-height * 0.4;;
        border: 2px solid #fff;
        border-radius: 50px;
        font-weight: 600;
        font-size: 14px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }

  .buyer-container, .search-button, .tag-control {
    .cursor-pointer();

    a {
      text-decoration: none;
    }

    &:hover, &.active {
      background-color: @brand-primary-darker !important;

      a {
        text-decoration: none;
      }
    }
  }
}

.rateplug-filters-wrapper, .idx-plus-filters-wrapper {
  .rateplug-filters-container, .idx-plus-filters-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding-left: 120px;

    .filters {
      display: inline-flex;
      gap: 5px;
      // background: #fff;
      padding: 5px;
      border-radius: @border-radius-base;

      .btn {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        // background-color: #EBEBEB;
        background-color: @brand-primary;
        color: #fff;
        border: none;
        border-radius: 3px;
        font-size: 1.25rem;
        text-transform: unset;

        &.label-only {
          cursor: default;
        }
      }

      > div.has-info-button {
        position: relative;

        .btn {
          padding-left: 25px;
        }

        .info-button {
          display: inline-block;
          width: 15px;
          height: 15px;
          cursor: pointer;
          position: absolute;
          left: 7px;
          top: 7px;
          z-index: 1;

          svg {
            width: 100%;
            height: 100%;
            fill: #fff;
            stroke: #fff;
          }
        }
      }

      > div.special-financing-highlight {
        .btn {
          background-color: @brand-specialfinancing;
        }
      }
    }
  }
}
