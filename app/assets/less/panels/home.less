div.home {

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: center;
  -webkit-overflow-scrolling: touch;
  background: url(//nplayassets.blob.core.windows.net/search2/backgrounds/landing-brown-house.jpg) no-repeat center center fixed;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;

  display: flex;
  align-items: stretch;

  .home-wrapper-container{
    z-index: 4;
  }

  .home-background{
    height: 100%;
    width: 100%;
    z-index: 3;
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    transition: background 0s ease-in 0s;
  }

  video#bgVideo {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    z-index: 3;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    background-size: cover;

    &::-webkit-media-controls {
      display:none !important;
    }
  }

  div.search-area {
    letter-spacing: 1px;
    text-align: left;

    > p {
      margin-top: 0.5em;
      font-size: 1.25em;
      color: #fff;
      font-weight: 200;
      text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.5);
      text-align: center;
      opacity: 1;
      transition: opacity 1s ease 0s;
    }

    .search-instruction-tab{
      background-color: rgba(100,158,213,0.6);
      text-shadow: none;
      color: white;
      padding: 6px 18px;
      display: inline-block;
      font-weight: 600;
      border-radius: 3px 3px 0px 0px;
    }

    &.collapsed{
      transform: translate3d(-50%, 0, 0) scale(0.6);
      position: fixed;
      top: 0;
      bottom: auto;
      input{
        -webkit-text-stroke: 0.35px;
        font-size: 22px !important;
      }
      .react-autosuggest__suggestions-container {
        -webkit-text-stroke: 0.35px;
        font-size: 20px;
      }

      > p {
        opacity: 0;
      }
    }

    nav.search-nav {
      height: 38px;
      overflow: hidden;
      text-shadow: none;
      transition: height 1s ease 0s;

      a {
        color: #fff;
        border-radius: 2px 2px 0 0;
        line-height: 38px;
        padding: 0px 15px;
        display: inline-block;
        font-size: inherit;
        font-weight: 600;
        font-size: 18px;
        transition: line-height 1s ease 0s, font 1s ease 0s;

        &.active {
          background-color: fade(@icon-blue, 75%);

        }
      }
    }

    form {
      width: 100%;

      div.input-group {
        width: 100%;

        div.react-autosuggest__container {
          clear: both;
          text-shadow: none;

          input {
            background: #fff;
            border: none !important;
            padding: 0px 16px;
            font-size: 18px;
            display: block;
            height: 58px;
            color: #333;
            line-height: 58px;
            border-radius: 2px 4px 4px 2px !important;
            transition: font 1s ease 0s, padding 1s ease 0s;
            backface-visibility: hidden;
            &::-webkit-input-placeholder {
              color: #aaa;
            }

            &:-moz-placeholder {
              /* Firefox 18- */
              color: #aaa;
            }

            &::-moz-placeholder {
              /* Firefox 19+ */
              color: #aaa;
            }

            &:-ms-input-placeholder {
              color: #aaa;
            }
          }


          .react-autosuggest__suggestions-container {
            max-width: 100vw;
            position: absolute;
            border-color: #c7c7c7;
            border-top: none !important;
            top: 58px;
            left: 0;
            border: none;
            background-color: fade(#fff, 90%);

            .react-autosuggest__suggestion {
              color: #333;
              padding: 1em 1.5em;
              font-weight: 500;

              svg.autosuggest-icon {
                fill: #333;
              }

              .current-location{

                svg{
                  fill: @icon-blue !important
                }
              }

              &:hover {
                background-color: @accent-gray;
                color: #000;

                span{
                  color: #000;
                }

                svg.autosuggest-icon {
                  fill: #333;
                }
              }
            }
          }
        }

        button.btn.btn-primary {
          position: absolute;
          right: 0;
          border: none;
          z-index: 2;
          height: 58px;
          width: 70px;
          border-radius: 0 2px 2px 0;

          transition: width 1s ease 0s, height 1s ease 0s;

          &:disabled{
            background-color: @brand-red;
          }

          svg {
            width: 28px;
            height: 28px;
            margin: 6px 0 0 0;
            transition: width 1s ease 0s, height 1s ease 0s, margin 1s ease 0;
          }
        }
      }
    }
  }

  header{

    svg{
      width: 180px;
      height: 35px;
    }

    > div:first-child{
      display: flex;
      justify-content: space-between;
      padding: 8px;
    }

    > div:last-child{
      color: white;
      padding-top: 8px;
    }
  }
}

.home-agent-search{
  display: flex;
  flex-direction: column;
  width: 100%;
  z-index: 2;

  .have-results-overlay{
    background-color: rgba(37, 52, 72, 0.6);
    width: 100%;
    pointer-events: none;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    display: none;
  }

  header{
    flex: 0 0 auto;
    z-index: 3;

    .search-area{
      flex: 1 1 auto;
      margin: 0px 48px;
      max-width: 62%;
    }

    .subtitle{
      color: white;
      font-weight: 600;
      letter-spacing: 1px;
      font-size: 18px;
    }

    .about{
      margin-left: auto;
    }

    .contact-us{
      margin-left: 12px;
    }

    .about, .contact-us{
      display: flex;
      justify-content: flex-end;
      justify-self: flex-end;
      align-items: center;
      align-content: center;
      padding-right: 8px;

      a{
        color: white;
        text-decoration: none;
        font-size: 18px;
        font-weight: 400;

        &:hover{
          text-decoration: underline;
        }
      }
    }

    svg, .about{
      flex-basis: 150px;
      width: 150px;
    }

    div.input-group{

      div.react-autosuggest__container {

        input {
          font-size: 14px !important;
          height: 38px !important;
          line-height: 38px !important;
        }

        .react-autosuggest__suggestions-container {
          top: 38px !important;
        }
      }

      button.btn.btn-primary {
        height: 38px !important;
        width: 50px !important;

        svg {
          width: 20px !important;
          height: 20px !important;
          margin: 6px 0 0 0;
        }
      }
    }

  }

  &.have-results{

    header{
      background-color: @brand-darkblue;
      box-shadow: 0px 2px 5px 2px rgba(0,0,0,.5);
      padding: 16px;
      margin-bottom: 24px;

    }

    .have-results-overlay{
      display: block;
    }
  }

  section.hero{
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    align-content: center;
    color: white;
    letter-spacing: 0.8px;
    text-shadow: 0px 1px 5px rgba(0,0,0,.8);
    min-height: 100vh;

    h1{
      font-size: 42px;
      font-weight: 700;
    }

    .error-message{
      font-size: 26px;
      margin-top: 12px;
      color: white;
    }

    .subtitle{
      font-size: 26px;
      margin-top: 12px;
      font-weight: 400;
      letter-spacing: 1.6px;
      margin-bottom: 48px;
      font-style: italic;
    }

    .search-area-container{
      width: 62%;
    }
  }

  section.content{
    z-index: 1;
    position: relative;
    background-color: white;
    padding: 40px 15px;
    margin: 0 auto;
    width: 100%;
    box-shadow: 0px 0px 5px 1px rgba(0,0,0,.3);

    h1{
      color: @icon-blue;
      text-transform: uppercase;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .description{
      width: 60%;
      text-align: center;
      margin: 0px auto;

      @media ( max-width: 650px ){
        width: 80%;
      }
    }

    .selling-points{
      margin-top: 40px;
      max-width: 1280px;
      margin-left: auto;
      margin-right: auto;

      .selling-point{
        padding: 15px;
        text-align: center;
        h2{
          color: @icon-blue;
          text-transform: uppercase;
          margin-top: 20px;
          margin-bottom: 5px;
          font-weight: 600;
          font-size: 16px;
          letter-spacing: 1px;
          text-align: left;
        }
        p{
          text-align: left;
        }
        img{
          width: 90%;
          margin: 0 auto;
        }
      }
    }

  }

  footer{
    background-color: @brand-darkblue;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1 0 auto;
    padding: 6px 16px;
    z-index: 1;

    a{
      color: white;
      line-height: 14px;
    }

    svg{
      height: 20px;
      width: 124px;
    }

    .homeasap-footer-powered{
      a{
        p{
          margin-bottom: 0px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
      }
    }
  }

}
