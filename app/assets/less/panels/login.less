
.modal-login {

  &.modal-login-desktop {
    width: 640px;
    max-width: 100%;
    margin: 0 auto;
    margin-top: 6em;
  }

  .modal-content {
    background-color: #fff;
  }

  div.close-svg {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 44px;
    height: 44px;
    padding: 10px;
    cursor: pointer;

    svg {
      fill: @brand-primary;
      width: 24px;
      height: 24px;
    }
  }

  .modal-login-body {
    width: 100%;
    background-color: #fff;
    color: @brand-primary;
    padding: 25px;

    .close-svg {
      svg {
        width: 20px;
        height: 20px;
      }
    }

    .tagline {
      font-size: 24px;
      font-weight: 400;
    }
    .subtagline {
      font-size: 16px;
      font-weight: 200;
    }
    .text-muted {
      padding: 0 20px;
      color: #bebebe;

      a.learn-more {
        color: @link-blue;
        cursor: pointer;
        text-decoration: underline;
      }
    }
    div.bullets {
      position: relative;
      width: 85%;
      margin: 15px auto 30px;

      color: #000;
      font-weight: 400;
      text-align: left;

      svg {
        position: absolute;
        top: 0;
        left: -50px;
        width: 36px;
        height: 36px;
        transform: rotate(-10deg);
        fill: @icon-blue;
        stroke: @icon-blue;
      }
    }
    a {
      color: #fff;
      cursor: pointer;

      &.no-thanks {
        text-decoration: underline;
        color: @brand-primary;
      }

      &.go-now, &.back {
        width: 150px;
      }
    }
  }

  .modal-login-splash {
    width: 100%;
    height: 120px;
    margin-top: 5px;
    margin-bottom: 15px;
  }

  &.second-modal {

    &.modal-login-desktop {

    }

    .modal-content {
      padding: 20px;
      border-radius: 3px;

      .modal-login-body {

      }
    }
  }
}

.popover {
  max-width: 300px;
}

/* -------------------------------------------------
----------------------------------------------------
	MATTBOLDT.COM
	Copyright (c) 2013 Matt Boldt
----------------------------------------------------
----------------------------------------------------
	CODE IS AVAILABLE FOR USE FREE OF CHARGE
	UNDER THE MIT LICENSE
	http://opensource.org/licenses/MIT
----------------------------------------------------
----------------------------------------------------
	If you'd like to credit me, or even donate
	a few bucks that'd be very much appreciated!
	Visit http://www.mattboldt.com/demos/social-buttons/
	for more info.
----------------------------------------------------
-------------------------------------------------- */
.sc-btn {
  display: inline-block;
  position: relative;
  margin: 0 .25em .5em 0;
  padding: 0;
  color: #fff;
  font-family: "Helvetica Neue", "Helvetica", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1em;
  text-decoration: none;
  text-shadow: rgba(0, 0, 0, 0.3) 0px -0.1em 0px;
  border: 0;
  border-radius: 0.4em;
  background-color: #1a1a1a;
  background-image: linear-gradient(to bottom, #595959, #1a1a1a);
  box-shadow: inset rgba(0, 0, 0, 0.1) 0px -0.15em 0px, inset rgba(255, 255, 255, 0.2) 0px 0.15em 0px, rgba(0, 0, 0, 0.3) 0px 0.1em 0.3em;
  text-align: center;
  background-repeat: no-repeat;
  -webkit-transition: background-position .1s ease-in-out;
  -webkit-appearance: none;
  cursor: pointer;
  overflow: hidden;
  transform: scale(0.9);
}

.sc-btn:hover {
  color: #fff;
}

.sc-btn:active {
  box-shadow: rgba(255, 255, 255, 0.2) 0 0.1em 0, inset rgba(0, 0, 0, 0.3) 0px 0.25em 1em;
}

.sc-icon, .sc-text {
  display: block;
  float: left;

}

.sc-icon {
  margin: 0 -.4em 0 0;
  padding: 0.6em .8em .5em;
  border-right: rgba(255, 255, 255, 0.1) 0.1em solid;
  box-shadow: inset rgba(0, 0, 0, 0.1) -0.1em 0px 0px;
}

.sc-text {
  padding: .95em 1em .85em 1em;
  font-size: 1.15em;
  text-align: center;
}

.sc-icon-svg {
  width: 1.8em;
  height: 1.8em;
  fill: #fff;
}

.sc-block {
  display: block;
}

.sc--big {
  font-size: 24px;
}

.sc--small {
  font-size: 12px;
}

.sc--tiny {
  font-size: 9px;
}

.sc--tiny .sc-text {
  padding: .85em .75em .5em .75em;
  text-shadow: rgba(0, 0, 0, 0.3) 0px -1px 0px;
}

.sc--tiny .sc-icon {
  padding: .5em .75em .5em .75em;
  border-right: rgba(255, 255, 255, 0.1) 1px solid;
  box-shadow: inset rgba(0, 0, 0, 0.1) -1px 0px 0px;
}

.sc--short .sc-icon {
  padding: 0.4em .9em .35em;
}

.sc--short .sc-text {
  padding: .75em 1em .75em 1em;
}

.sc--tall {
  font-size: 1.15em;
}

.sc--tall .sc-icon {
  padding: 1em .9em .85em;
}

.sc--tall .sc-text {
  padding: 1.25em 1em 1em 1em;
}

.sc--round {
  border-radius: 5em;
  -webkit-border-radius: 5em;
  -moz-border-radius: 5em;
  -ms-border-radius: 5em;
  -o-border-radius: 5em;
}

.sc--round .sc-icon {
  padding: 0.7em .8em .5em 1em;
}

.sc--flat {
  box-shadow: none;
  background-image: none !important;
}

.sc--flat .sc-icon {
  border-color: transparent;
}

.sc--flat:active {
  box-shadow: inset rgba(0, 0, 0, 0.3) 0px 0.15em 0.25em;
}

.sc--shine {
  box-shadow: inset rgba(0, 0, 0, 0.1) 0px -0.15em 0px, inset rgba(255, 255, 255, 0.1) 0px 0.15em 0px, rgba(0, 0, 0, 0.3) 0px 0.1em 0.3em, inset rgba(255, 255, 255, 0.15) 0px 2.5em 0px -1em;
}

.sc--shine:active {
  box-shadow: rgba(255, 255, 255, 0.2) 0 0.1em 0, inset rgba(0, 0, 0, 0.3) 0px 0.25em 1em, inset rgba(255, 255, 255, 0.1) 0px 2.5em 0px -1em;
}

.sc--shine:before, .sc--shine:after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 0.1em;
}

.sc--shine:before {
  top: 0;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 20%, rgba(255, 255, 255, 0) 100%);
}

.sc--shine:after {
  bottom: .05em;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 80%, rgba(255, 255, 255, 0) 100%);
}

.sc--shine:active:before, .sc--shine:active:after {
  opacity: 0.5;
}

.sc--default {
  color: #222;
  text-shadow: rgba(255, 255, 255, 0.4) 0 0.1em 0;
  background-color: #ebebeb;
  background-image: linear-gradient(to bottom, white, #ebebeb);
}

.sc--default svg {
  fill: #222;
}

.sc--default:hover {
  color: #222;
  background-color: #d2d2d2;
  background-image: linear-gradient(to bottom, white, #d2d2d2);
  background-color: #fdfdfd;
}

.sc--default:active {
  background-color: #dfdfdf;
  background-image: linear-gradient(to bottom, white, #dfdfdf);
}

.sc--facebook {
  background-color: #33477a;
  background-image: linear-gradient(to bottom, #5975ba, #33477a);
}

.sc--facebook:hover {
  background-color: #304373;
  background-image: linear-gradient(to bottom, #6b84c1, #304373);
  background-color: #4560a5;
}

.sc--facebook:active {
  background-color: #33477a;
  background-image: linear-gradient(to bottom, #4a66b0, #33477a);
}
