div.agent-notification-container {

  .close-button-cross {
    width: 35px;
    height: 35px;
  }

  div.agent-notification-container-main {
    position: fixed;
    z-index: 102;
    left: 1em;
    bottom: 1em;
    min-height: 100px;
    width: 25%;
    min-width: 320px;
    max-width: 420px;
    background: #fff;
    box-shadow: 0 0 15px rgba(0,0,0,.25);
    border-radius: 3px;
    padding: 5px;

    p.welcome {
      margin-left: 70px;
      margin-top: 10px;
      font-size: 1.25em;
    }

    &-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      img.agent-notification-container-agent-image {
        width: 50px;
        height: 50px;
        margin: 0 10px 0 10px;
        border-radius: 50%;
      }

      p.message {
        margin-right: 10px;
      }

      a.action-button.btn-link {
        margin: 0;
        padding-left: 0;
        transform: none;
        color: @link-blue;
        display: inline;
        font-weight: 200;
        text-decoration: underline;
      }
    }

    a.action-button {
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  div.agent-notification-container-small {
    position: fixed;
    z-index: 102;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 0);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    background: #fff;
    box-shadow: 0 0 15px rgba(0,0,0,.25);
    padding: 5px 15px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    img.agent-notification-container-agent-image {
      width: 2em;
      height: 2em;
      margin: 0 10px 0 0;
      border-radius: 50%;
    }

    p.message {
      font-weight: 600;
      margin: 0;

      a {
        text-decoration: underline;
      }
    }
  }
}



// ReactCSSTransition - agent-notification-transition
.agent-notification-transition-appear {
  transform: translateY(120%);

  &-active {
    transform: translateY(0);
    transition: transform 300ms ease;
  }
}
.agent-notification-transition-enter {
  transform: translateY(120%);

  &-active {
    transform: translateY(0);
    transition: transform 300ms ease;
  }
}
.agent-notification-transition-leave {
  transform: translateY(0);
  transition: transform 300ms ease-out;

  &-active {
    transform: translateY(120%);
  }
}


// ReactCSSTransition - agent-notification-sm-transition
.agent-notification-sm-transition-appear {
  margin-bottom: -50px;

  &-active {
    margin-bottom: 0;
    transition: margin-bottom 300ms ease 300ms;
  }
}
.agent-notification-sm-transition-enter {
  margin-bottom: -50px;

  &-active {
    margin-bottom: 0;
    transition: margin-bottom 300ms ease 300ms;
  }
}
.agent-notification-sm-transition-leave {
  margin-bottom: 0;
  transition: transform 300ms ease-out;

  &-active {
    margin-bottom: -50px;
  }
}