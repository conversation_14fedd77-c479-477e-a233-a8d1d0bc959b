.menu-container {
  background-color: #fff;

  .menu-top {
    padding: 1em 1.5em;
    border-bottom: 0.1em solid @hr-color;

    > div {
      display: inline-block;
      width: 20%;

      &.text-center {
        width: 60%;

        p {
          .match-count {
            font-size: 1.5em;
          }

          svg {
            fill: #000;
            stroke: #000;
            width: 1.5em;
            height: 1em;
            margin-left: 0.5em;
            margin-right: 0.25em;
          }
        }
      }
    }
  }

  .menu-left {
//    overflow: auto;
//    overflow-x: visible;
    width: calc(~"99.999999% - @{content-right-width}");
    float: left;
    position: relative;
    height: 100%;

    &.adaptive {
      @media screen and (min-width: (@xlargeDesktop + 0.1)) {
        width: calc(~"99.999999% - @{content-right-width} * 1.5");
      }
    }

    &> div.scroll-container {
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      position: absolute;
      top: 0;
      bottom: @header-height;
      left: 0;
      right: 0;
      padding: 1.5em;

      div.menu-panel {
        padding: 0 0.5em 1em 0.5em;

        .row .btn {
          line-height: 1em;
        }

        div.edit-search {
          max-width: @content-left-max-width;
          margin: 0 auto;

          a.reset-search {
            .cursor-pointer();
          }

          .form-control, .form-control:focus {
            border-color: @brand-blue;
            color: @brand-primary;

            &:hover, &:focus, &:active, &.active {
              background-color: #f5f5f5;
            }

          }

          .search-form {
            margin-right: -1px;
            margin-bottom: 10px;
          }

          .btn {
            text-transform: uppercase;
          }

          .btn.btn-default {
            color: #000;
            border-color: @brand-blue;
            fill: #000;
            stroke: #000;

            &:hover, &:focus, &:active, &.active {
              color: #000;
              background-color: #f5f5f5;
            }
          }

          .menu-left-item {
            margin: 1em 0 3em;

            &.menu-left-search {
              form.search-form {
                position: relative;
                min-width: 8em;

                input {
                  color: @brand-primary;
                  height: 36px;
                  padding-left: 18px + 8px * 1.5;
                }

                svg {
                  position: absolute;
                  left: 0;
                  top: 0;
                  bottom: 0;
                  height: 18px;
                  width: 18px;
                  margin: 8px;
                  fill: #AAA;
                }
              }
            }
          }

          .right-drop{
            .btn-group{
              .dropdown-menu{
                right:0 !important;
                left:auto;
              }
            }
          }

          div.custom-dropdown {
            margin-right: -1px;
            margin-bottom: 10px;

            .btn {
              font-weight: 600;
              border-radius: @border-radius-small;
            }

            .btn-group{
              .dropdown-menu{
                min-width:175px;
              }
            }

            &.not-empty {
              .btn {
                color: @brand-primary;
              }
            }

            div.btn-group {
              width: 100%;

              button.dropdown-toggle {
                width: 100%;
                height: 36px;
                border-radius: @border-radius-small;
              }
            }
          }

          .keywords-select {

            .Select-control {
              border-color: @brand-blue;
              border-radius: @border-radius-small;
              cursor: text;

              &:hover, &:focus, &:active, &.active {
                color: #000;
                background-color: #f5f5f5;
              }

              .Select-item {
                border-radius: 1em;
                padding: 0 0.25em;
                color: @brand-primary;
                background-color: #fafafa;

                .Select-item-icon {
                  float: right;
                  border: none;

                  &:hover {
                    color: @brand-primary;
                    font-weight: bold;
                    background-color: transparent;
                  }
                }
              }

              .Select-arrow-zone, .Select-arrow {
                display: none;
              }

              input {
                color: @brand-primary;
              }
            }

            // Hide Dropdown for now
            .Select-menu-outer {
              border-radius: 0;

              .Select-noresults {
                display: none;
              }

              .Select-option {

                &.is-focused {
                  background-color: #f5f5f5;
                }
              }
            }
          }

          .search-result {
            color: #000;
            padding: 0.6em 1em 0.6em 0;
            display: inline-block;

            &.match-count-container {
              padding: 0 1em 0 0;
              color: @brand-primary;
              font-size: 21px;

              .match-count {

              }
            }
          }

          a.clear-search {
            padding: 0.6em 0;
            color: @icon-blue;

            svg {
              width: 1.25em;
              height: 1.4em;
              position: absolute;
              fill: @icon-blue;
              stroke: @icon-blue;
            }

            small {
              margin-left: 1.7em;
            }
          }

          a.btn.view-results {
            font-weight: 600;
          }

          div.controls-container.map-grid-toggle {
            float: right;

            > div {
              line-height: 1em;

              a.btn {
                background-color: @brand-primary-lighter;
                color: @brand-primary-light;

                span {

                  &.svg-container {

                    svg {
                      fill: @brand-primary-light;
                    }
                  }
                }

                &:hover, &.active {
                  background-color: @brand-primary-darker;
                  color: #fff;

                  span {
                    &.svg-container {
                      svg {
                        fill: #fff;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      div.sort-results, div.display-options {

        p.text-muted {
          margin: 0.5em 0 0.75em;
        }
      }
    }
  }

  .menu-right {
    overflow: hidden;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    width: @content-right-width;
    border-left: 0.1em solid @hr-color;
    float: left;
    height: calc(~"(99.999999% - @{header-height})");

    h3.header {
      color: @brand-blue;
      background-color: @accent-gray;
      padding: 0.5em;
      font-size: 1.25em;
      font-weight: 600;
      text-transform: uppercase;
    }

    &.adaptive {
      @media screen and (min-width: (@xlargeDesktop + 0.1)) {
        width: @content-right-width * 1.5;
      }
    }
  }

}

div.prev-search-card {
  clear: both;
  color: #000;
  margin-top: .4em;
  padding: 1.5em .5em;
  border-bottom: 0.1em solid @hr-color;

  h4 {
    font-size: 1em;

    a {
      .cursor-pointer();
      text-decoration: underline;
    }
  }

  p {
    margin-top: .3em;

    > span.text-danger {
      color: @brand-danger;
    }
  }
}
