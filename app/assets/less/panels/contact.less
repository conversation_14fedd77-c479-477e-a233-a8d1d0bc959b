@link-blue : #5d99e1;
@contact-bg: @content-bg;
@font-color: #343d46;

@media (min-width: @screen-sm-min) {

  .agent-contact-modal {
    .modal-dialog {
      width: 480px;
      .modal-content {
        width: 100%;
      }
    }
  }

}

.agent-contact-modal {
  padding: 0px !important;
  .modal-content {
    width: 100%;
  }
  .close-button {
    top:10px;
    position: absolute;
    right: 10px;
    cursor: pointer;
    text-decoration: none;
  }
}

.agent-contact {
  background-color: #fff;
  color: @font-color;
  margin: @border-radius-large;

  .bold {
    font-weight: 500;
  }
  .mb5 {
    margin-bottom: 5px;
  }
  .mrn {
    margin-right: 0px !important;
  }
  .pipe {
    &:after {
      content: '|';
    }
  }

  .login-alert{
    font-size:0.8em;
    margin-top:1em;
  }

  .first-panel {
    height: 30%;
    border-bottom: 1px solid @outline;
    text-align:center;
    .content-holder {
      padding: 3em 1em 2em;
    }
    .broker-info {
      text-align: left;
      display: inline-block;
      padding-left: 25px;
      padding-top: 10px;
      width: fit-content;
      max-width: 70%;
      word-wrap: break-word;
      span{
        word-wrap: break-word;
      }
    }
    .photo {
      vertical-align:top;
      width: 20%;

      div.pro-badge-container {
        top: 0px;
        left: -7px;
        width: 30px;
        height: 30px;
      }
    }
    .name {
      display: inline-block;
      font-size: 1.6em;
      font-weight: 500;
    }
    .directions {
      color: @link-blue;
      text-decoration: none;
      cursor: pointer;
      a {
        text-decoration: none;
      }
    }
  }

  .second-panel {
    position: relative;
    height: 30%;
    border-bottom: 1px solid @outline;
    img {
      width: 240px;
      height: 10em;
    }
    .hide-button{
      font-weight: 700;
      padding: 10px;
    }
    .details {
      font-size: 0.9em;
      vertical-align: middle;
      width: 45%;
      display: inline-block;
      padding-left: 5%;
      .header {
        font-weight: 600;
      }
    }
    .close-button-cross {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .third-panel {
    height: 15%;
    .holder {
      height: 100%;
    }
    .options {
      height: 100%;
      width: 25%;
      display: inline-block;
      border-right: 1px solid @outline;
      border-bottom: 1px solid @outline;
      padding: 5px 0 5px 0;
      text-align: center;
      text-decoration: none;
      cursor: pointer;

      &.two-count{
        width:50%;
      }

      &.active {
        background-color: @contact-bg;
        border-bottom: 1px solid @contact-bg;
        color: @brand-darkblue;
        text-transform: uppercase;

        .options-icon {
          fill: @brand-primary-dark;
          stroke: @brand-primary-dark;
        }
      }

      .options-icon {
        width: 30px;
        height: 30px;
        fill: @brand-primary;
        stroke: @brand-primary;
      }
    }
  }

  .forth-panel {
    font-size: 1.3em;
    color: @link-blue;
    text-align: center;
    min-height: 5em;
    background-color: @contact-bg;
    padding: 20px;
    .title {
      font-size: 0.85em;
      font-weight: 400;
      color: @menu-font;
    }
    .input-group-btn {
      display: inline;
      .btn {
        float: right;
        margin-right: 3px;
      }
    }

  }
  .email-form {
    width: 90%;
    margin: auto;
    .input-group{
      width:100%;
    }
  }
  .showing-form {
    width: 95%;
    margin: 0 auto;
    .input-group {
      width: 100%;
    }
    input {
      margin: 3px;
      width: 48%;
    }

    .error-message{
      color:@brand-red;
      font-size: .7em;
      margin-left:5px;
      text-align:left;
    }

    .form-control {
      &.error{
        outline: 1px solid @brand-danger;
      }
    }
  }
}
