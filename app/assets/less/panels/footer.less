.homeasap-footer {

  .homeasap-footer-brand{
    background-color: #fff;

    a{
      display: inline-block;
      height: 2.2em;
      max-width: 50%;
    }

    svg{
      width: 14em;
      max-height: 2.2em;
    }
  }

  .footer-bar{
    font-size: 11px;
    clear: both;
    background: @brand-primary-dark;
    overflow: hidden;
    height: 36px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    a{
      color: #fff;
      .cursor-pointer();
    }

    .homeasap-footer-links{
      flex: 0 0 330px;
      padding-left: 10px;
      text-align: left;

      @media screen and (max-width: 1000px ){
        flex: 1 0 auto;
      }

      a {
        display: inline-block;
      }
    }

    .homeasap-footer-description{
      flex: 1 0 auto;
      font-weight: 400;
      font-size: 14px;
      font-style: italic;
      padding: 0px 15px;

      @media screen and (max-width: 1000px ){
        display: none;
      }
    }

    .homeasap-footer-powered{
      flex: 0 0 330px;
      text-align: right;

      @media screen and (max-width: 1000px ){
        flex: 1 0 auto;
      }

      span{
        vertical-align: middle;
      }

      svg{
        width: 95px;
        height: 15px;
        vertical-align: middle;
      }
    }
  }

}
