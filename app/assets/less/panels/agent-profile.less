.featured-header, .tagging-header {
  text-align: center;
  color: #223949;
  background-color: @accent-gray;
  padding: 0.5em;
  font-size: 1.25em;
  font-weight: 600;
  text-transform: uppercase;
}

.agent-screen-container {
  background-color: transparent;
  z-index: 9;
  .menu-left, .menu-right {
    margin-top: @header-height;
  }

  .listing-map-subpanel{
    padding-top: @header-height;
    .listing-edge-tabbar{
      top: @header-height;
    }
  }

  .profile-container {
    max-width:1160px;
    position:relative;
    margin:0 auto;
    padding: 1.5em 0;
    background-color:#fff;

    .holder{
      padding-top:4px;
      padding-bottom:4px;
    }

    .border-line{
      padding: 2em 0;
    }
    .border-line:nth-child(even) {
      background-color:@lighter-gray;
      /*border-bottom: 1px solid @accent-gray;*/
    }
    .col-1-1,
    .col-sm-1-2 {
      padding: 0 30px;
    }
    .d-block {
      display: inline-block;
    }

    .header {
      margin: 3px 0;
      font-weight: 600;
      color: @icon-blue;
      padding-top: 6px;
    }
    .panel-1 {
      .title {
        padding-right:10px;
      }
      .data {
        width: 50%;
        font-weight: 600;
      }
      .broker-logo{
        max-width: 100%;
        max-height: 100px;
        vertical-align:middle;
      }
      .profile-image {
        padding: 0 20px 20px 20px;
        display: inline-block;
        .image-holder {
          width: 15vw;
          max-width: 200px;
          min-width: 100px;
          position: relative;
          margin: 0 auto;
          img {
            border-radius: 50%;
            width: 15vw;
            max-width: 200px;
            min-width: 100px;
          }
          .realtor-icon {
            stroke: #fff;
            fill: #007bb6;
            bottom: 10%;
            position: absolute;
            width: 15%;
            height: 15%;
            right: 4%;
          }
        }
      }
      > div{
        padding-top: 0.5em;
        padding-bottom: 0.5em;
      }
      .panel-1-2 {
        padding: 30px 0 20px 0;
        display: inline-block;
        .license {
          line-height: 2em;
          padding-bottom: 20px;
          font-size: 1.1em;
          width: 100%;
          display: inline-block;
          span {
            padding: 5px;
          }
          .title {
            font-weight: 600;
          }
          .data {
            width: 50%;
          }
          a {
            width: 20%;
            padding-right: 20px;
          }
          .link-container {
            padding: 0 30px;
            font-weight: 400;
            color: @icon-blue;
            a {
              text-decoration: none;
              cursor: pointer;
              padding: 0;
              color: @icon-blue;
              &.no-hover{
                cursor:not-allowed;
              }
            }
            span {
              padding: 0;
            }
          }
          .link-icon {
            fill: @icon-blue;
            vertical-align: middle;
            width: 20px;
            height: 20px;
            stroke-width: 5;
            margin-right: 10px;
          }
        }
      }

    }
    .panel-2{
      background-image: url("//nplayassets.blob.core.windows.net/search2/professional-bg.jpg");
      background-repeat: no-repeat;
      background-size: contain;
      background-position: left bottom;
      &.professional{
        background-size: 40%;
      }
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .flex-top{
      align-self:flex-start;
    }
    .icon-panel {
      padding: 20px 0;
      border-bottom: 1px solid @accent-gray;
      .icon-container {
        vertical-align: top;
        display: inline-block;
        text-align: center;
        padding: 0 !important;
      }
      .icon {
        width: 40px;
        height: 40px;
        fill: @icon-gray;
      }
      .icon-title {
        font-weight: 600;
        color: @icon-blue;
      }
    }
    .video-container {
      padding-top: 6px;
      max-width: 35em;
    }
    .service-area {
      width: 50%;
      display: inline-block;
    }
    .headline {
      font-size: 1.4em;
      font-style: italic;
      font-weight: 500;
      color:@icon-blue;
      &.inside{
        display:none;
      }
      a {
        text-decoration: none;
        cursor: pointer;
      }
      .social-container {
        text-align: left;
        min-width: 140px;
        padding: 0;
        div{
          display:inline-block;
          cursor:pointer;
        }
      }
      .social {
        margin-right: 5px;
      }
    }
    .social-icon {
      border-radius:0;
      width: 30px;
      height: 30px;


      &.twitter-icon{
        fill: #598DCA !important;
      }

      &.facebook-icon{
        fill: #3A589B !important;
      }

      &.instagram-icon{
        fill: #833AB4 !important;
      }

      &.youtube-icon{
        fill: #c4302b !important;
      }

      &.google-icon{
        fill: #4285F4 !important;
      }
    }
    .about {
      text-align: justify;
    }
    .service {
      @media (max-width: @screen-sm-min) {
        padding-bottom: 15px;
      }
    }
    .expertise {
      .content-holder{
        @offset:80px;
        width: calc(~"(99.9999% - @{offset})");
      }
      .expertise-text {
        width: 100%;
        padding: 0;
        vertical-align: top;
        list-style-type: circle;
        display: inline-block;
        li {
          padding: 0;
          list-style-type: circle;
        }
      }
      @media (max-width: @screen-sm-min) {
        border-top: 1px solid @accent-gray;
        padding: 15px 30px;
      }
      .circle {
        display: inline-block;
        position: relative;
        width: 80px;
        height: 80px;
        color: #fff;
        margin-right: 10px;
        text-align: center;
        font-size: 0.8em;
        .expertise-icon {
          width: 30px;
          height: 50px;
          fill: @icon-gray;;
          stroke: @icon-gray;
          right: 30px;
          position: absolute;
          top: 0;
        }
        span {
          position: absolute;
          top: 40px;
          left: 18px;
          vertical-align: bottom;
        }
      }
    }
    .service-area-map {
      height: 200px;
      width: 80%;
      background-color: @content-bg;
    }
    .section-icon{
      width: 30px;
      height: 30px;
      fill: @icon-gray;
      &.wider{
        width:35px;
      }
    }
    .icon-holder{
      margin: 10px 10px 0 0;
      display: inline-block;
      vertical-align: top;
    }
    .h40{
      height:40px;
    }
    .content-holder{
      display:inline-block;
    }
    .publications {
      background-color: @content-bg;
    }
    .organizations {
      background-color: @content-bg;
    }
    .certifications {
      img {
        margin-right: 15px;
        height: 2em;
        margin-bottom: 15px;
        margin-top: 5px;
      }
    }
    .section-holder{
      padding-top:1em;
      span{
        padding-right:.2em;
      }
    }
    .section-date,.section-desc{
      font-size:.8em;
      color:@icon-gray;
      margin:0;
      display:inline-block;
      padding-right:.5em;
      font-weight:400;
    }

    .section-title{
      font-weight:700;
      &.link{
        color:@icon-blue;
        cursor:pointer;
      }
    }

    .section-details{
      font-style: italic;
      font-size: .9em;
    }
    .shade{
      background-color: @accent-gray;
      padding:1em;
    }
  }
  .prn{
    padding-right:0;
  }
}
