.home-worth-form-container{
  position: relative;

  img.home-value{
    margin: 24px auto;
    display: block;
  }

  .subtitle{
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .description{
    margin-bottom: 24px;
    text-align: center;
  }

  form{
    display: grid;
    grid-template: ~"auto / 1fr 1fr";
    grid-gap: 12px;
  }

  input{
    padding: 4px 8px;
    border-radius: 3px;
    box-shadow: none;
    border: 1px solid #d7d7d7;
    width: 100%;
  }

  select{
    border-radius: 3px;
    border: 1px solid #d7d7d7;
    background-color: white;
    width: 100%;
  }

  .address-input, select{
    grid-column-end: ~"span 2";
  }

  button{
    grid-column-start: 1;
    justify-self: self-end;
    grid-column-end: ~"span 2";
  }
}

.home-worth-success-container{

  .title{
    font-size: 24px;
    font-weight: 600;
    margin-top: 24px;
    text-align: center;
  }

  .success-checkmark{
    display: block;
    margin: 24px auto;
    width: 150px;
    height: 150px;
  }

  .agent-card{
    display: flex;
    margin: 24px 0px;
    justify-content: center;
    align-items: center;

    .agent-image{
      border-radius: 50%;
      width: 75px;
      height: 75px;
      flex: 0 0 75px;
      margin-right: 12px;
    }

    .agent-details{
      flex: 0 1 auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      font-weight: 400;

      .agent-name{
        font-weight: 600;
      }

      .agent-broker{

      }
    }

  }

  .subtitle{
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 12px;
  }

  .description{
    text-align: center;
    margin-bottom: 24px;
  }

  button{
    margin: 0 auto;
    display: block;
    text-transform: uppercase;
    width: auto;
  }
}

.modal-dialog{
  .close-button-landing{
    svg{
      width: 20px;
      height: 20px;
      fill: @brand-red;
    }
  }
}

.pac-container{
  z-index: 10000;
}

.home-worth-modal:not(.mobile){

  .modal-dialog{
    width: 450px !important;
  }

  .modal-body{
    padding: 24px !important;
  }
}
