#employee-ui-bounds{
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
}

#employee-debug-popup{
  pointer-events: all;
  position: fixed;
  bottom: 0;
  left: 0;
  padding-right: 60px;
  box-shadow: 0px 2px 5px 1px rgba(0,0,0,.5);
  background: white;
  border: 3px solid @brand-red;
  border-radius: 3px;

  .sections{
    display: flex;

    > div{
      margin: 8px;

      img{
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: block;
      }

      svg{
        width: 50px;
        height: 50px;
        fill: #ccc;
        display: block;
      }
    }
  }

  .handle{
    position: absolute;
    top: 0;
    right: 30px;
    padding: 0px;
    cursor: grab;
    background-color: white;

    svg{
      fill: @brand-red;
      width: 27px;
      height: 27px;
    }
  }

  .close{
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px 4px 6px 6px;
    cursor: pointer;
    background-color: @brand-red;
    height: 26px;

    svg{
      fill: white;
      width: 14px;
      height: 14px;
    }

    &:hover{
      svg{
        fill: #ccc;
      }
    }
  }

}

.react-draggable-dragging{
  .handle{
    cursor: grabbing !important;
  }
}
