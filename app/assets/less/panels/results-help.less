
.results-count{
  font-weight: 400;
  margin-bottom: 1em;
  height: 2em;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  letter-spacing: 1px;

  p {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
  }

  > a {
    position: absolute;
    right: 0;
    top: 3px;
    margin-right: 1em;
  }

  .showing-count{
    color: @brand-red;
    font-size: 120%;
    font-style: normal;
    font-weight: 600 !important;
  }

  .total-count{
    color:@brand-red;
    font-weight:700;
    font-size:1.2em;
  }
  .pl{
    padding-left:42px;
  }
  &.left{
    text-align:left;
  }
  &.mt-results{
    margin-top:30px;
  }

  &.condensed {
    margin: 0;
    height: auto;

    p {
      margin: 0;
      text-align: left;
      position: relative;
    }
  }
}

.results-help{
  .modal-content{
    border: 1px solid #000;
    padding: 10px;
    width: 300px;
    font-size: .9em;
  }

}
