#photo-slider {
  width: 100%;
  position: relative;
  padding-bottom: 56.25%;

  .slick-slider{
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

    .slick-list, .slick-track, .slick-slide{
      height: 100%;
    }
  }

  .broker-on-slider{
    background-color: @accent-gray;
    padding: 3px 10px;
    font-weight: 400;
    font-size: 14px;
    overflow: auto;
    text-overflow: ellipsis;
    position: relative;
    z-index: 1;
  }

  .broker-on-slider ~ .tag-button-photo{
    top: 30px;
  }

  .photo-slide {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    cursor: zoom-in;

    &.not-available {
      cursor: default;
    }
  }

  div.zoom-button {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0,0,0,.4);

    display: flex;
    justify-content: center;
    align-items: center;

    opacity: 0;
    cursor: zoom-in;

    transition: opacity .25s ease;

    svg {
      width: 4em;
      height: 4em;
      fill: #fff;
      stroke: #fff;
    }

    &:hover {
      opacity: 1;
    }
  }

  .photo-arrow-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2.5em;
    .cursor-pointer();

    &-left {
      left: 0;
    }
    &-right {
      right: 0;
    }

    fill: #fff;
    stroke: #fff;
    stroke-width: 0.2em;
    filter: drop-shadow(1px 1px 1px #000);

    transition: background-color .25s ease;

    &:hover {
      filter: drop-shadow(1px 1px 2px #000);
      background-color: rgba(0, 0, 0, 0.1);
    }

    .photo-arrow {
      position: absolute;
      top: 50%;
      width: 1em;
      height: 2em;
      cursor: pointer;
      margin-top: -1em;

      &-left {
        left: 0.75em;
      }
      &-right {
        right: 0.75em;
      }

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  .agent-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4em;
    line-height: 3em;
    padding: 0.5em;
    background-color: rgba(0, 0, 0, .4);
    cursor:pointer;
    &:hover{
      background-color: rgba(0, 0, 0, .6);
    }

    .agent-image {
      border-radius: 50%;
      height: 3em;
      width: 3em;
      margin-left: 0.5em;
      display: inline-block;
      vertical-align: top;
    }

    span{
      display: inline-block;
      margin: 0;
      color: #fff;
      font-size: 1.1em;

      &.agent-name {
        padding: 0 0.5em 0 0.9em;
        font-weight: bold;
        text-overflow: ellipsis;
        max-width: ~"calc(100% - 7.25em)";
        overflow: hidden;
        white-space: nowrap;
      }

      &.brokerage {
        padding: 0 0 0 0.5em;
      }
    }

    > svg {
      width: 2.5em;
      height: 2.5em;
      margin-top: 0.25em;
      margin-right: 1em;
      float: right;
      fill: #fff;
      stroke: #fff;
    }
  }

  .tag-panel-photo {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 1.5em 1.75em;
    background-color: @brand-primary-dark;
    min-height: 100%;
    z-index: 1;
    cursor: default;

    > label {
      color: #fff;
      opacity: 0.59;
    }

    .links-wrapper {
      position: absolute;
      bottom: 1em;
      > button {
        margin: 0 0 10px 18em;
        border: 1px solid #ffffff;
        color: #ffffff;
        padding: 1px 7px;
        &:hover {
          background-color: rgba(0, 0, 0, 0.2)
        }
      }
      > a {
        color: #ffffff !important;
        text-decoration: underline;
      }
    }

    .tags {
      padding-top: 0.5em;

      label.tag {
        color: #fff;
        border: 1px solid @brand-primary;
        margin-right: 0.75em;
        margin-bottom: 0.5em;
        padding: 0.1em 0.7em 0.2em;
        border-radius: 1em;
        word-break: break-all;
        .cursor-pointer();

        &:hover {

        }

        &.active {
          background-color: @brand-primary;
        }
      }
    }

    form.add-tag {
      clear: both;
      margin: 1em 0;

      .btn-tagsubmit {
        background-color: #ffffff;
        color: @brand-primary-dark;
        padding: 3px 2px;
        font-weight: bold;
        &:hover {
          background-color: rgba(255, 255, 255, 0.8)
        }
      }
      input#new-tag {
        background: none;
        height: 2em;
        border: 1px solid @body-bg;
        float: left;
        width: 88%;
        &:focus {
          color: #fff;
        }
      }
    }
  }

  .tag-button-photo {
    position: absolute;
    top: 0;
    right: 0;
    width: 2em;
    height: 2em;
    margin: 0.4em 0.5em;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 2em;
    border: 0.25em solid transparent;
    box-sizing: content-box;
    .cursor-pointer();

    svg {
      width: 100%;
      height: 100%;

      &.tag-off {
        fill: #fff;
        padding: 0.2em;
      }

      &.tag-on {
        fill: @brand-tag;
        padding: 0.2em;
      }

      &.tag-favorite {
        fill: @brand-fav;
        padding: 0.2em;
      }

      &.tag-close-button {
        fill: #fff;
        stroke: #fff;
        padding: 0.4em;
      }
      &.tag-dislike {
        fill: #fff;
        stroke: #fff;
        stroke-width: 0.25em;
      }
    }
  }
}

.picture-show {
  overflow: hidden;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.ps-wrap {
  position: relative;
  width: 100%;
  height: 0;
}

.ps-slides {
  position: absolute;
  display: block;
  height: 100%;
  left: 0;
  top: 0;
  transition: all 400ms cubic-bezier(0.365, 0.45, 0.23, 0.95);
}

.ps-slides:hover {
  cursor: pointer;
}

.ps-slide-wrap {
  overflow: hidden;
  height: 100%;
  float: left;
  text-align: center;
  background-color: #FAFAFA;
  -webkit-touch-callout: none;
  user-select: none;
}

/*
 * Centering method from (ghost element)
 * http://css-tricks.com/centering-in-the-unknown/
 */
.ps-slide {
  width: 100%;
  height: 100%;
  text-align: center;
  white-space: nowrap;
}

.ps-slide:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.ps-slide > img {
  display: inline-block;
  vertical-align: middle;
}

.stretch.ps-img {
  width: 100%;
  height: 100%;
}

.tall.ps-img {
  height: 100%;
  width: auto;
}

.wide.ps-img {
  width: 100%;
  height: auto;
}

.stretch.picture-show {
  position: relative;
  width: 100%;
  height: auto;
}

.stretch > .ps-wrap {
  background: none;
  overflow: hidden;
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: auto;
  width: auto;
}

.listing-full{
  .listing-edge-tabbar{
    &.on-map {
      > .listing-edge-border {

        &-top {
          border-right: solid 4.2rem transparent !important;
          border-bottom: solid 2.1rem @brand-primary !important;
          border-left: none !important;
        }

        &-bottom {
          border-right: solid 4.2rem transparent !important;
          border-top: solid 2.1rem @brand-primary !important;
          border-left: none !important;
        }
      }
    }
  }
}
.listing-edge-tabbar {
  width: 3em;
  position: absolute;
  top: 3em;

  &.on-listing-detail {
    left: -3em;
    z-index: 2;

    @media (max-width: @desktop) {
      left: 0;
    }
  }

  &.on-listing-full {
    left: calc(~"50px - 3em");
    z-index: 2;
  }

  &.on-map {
    left: 0;
    z-index: 2;

    @media (min-width: (@largeDesktop)) {
      left: -3em;
    }
  }

  .listing-edge-border {
    width: 4.2rem;
    height: 4.2rem;
    box-sizing: border-box;
    z-index: 0;
    position: relative;
  }

  &.on-listing-full {
    > .listing-edge-border {
      border: none;
      z-index: 1;

      &-top {
        border-left: solid 4.2rem transparent;
        border-bottom: solid 2.1rem @brand-primary;
      }

      &-bottom {
        border-left: solid 4.2rem transparent;
        border-top: solid 2.1rem @brand-primary;
      }
    }
  }

  &.on-listing-detail {
    > .listing-edge-border {
      border: none;
      z-index: 1;

      &-top {
        border-left: solid 4.2rem transparent;
        border-bottom: solid 2.1rem @brand-primary;
      }

      &-bottom {
        border-left: solid 4.2rem transparent;
        border-top: solid 2.1rem @brand-primary;
      }

      @media (max-width: @desktop) {
        border: none;

        &-top {
          border-right: solid 4.2rem transparent;
          border-bottom: solid 2.1rem @brand-primary;
        }

        &-bottom {
          border-right: solid 4.2rem transparent;
          border-top: solid 2.1rem @brand-primary;
        }
      }
    }

    > .listing-edge-items {
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.25);
      @media (max-width: (@desktop)) {
        box-shadow: none;
      }
    }
  }

  &.on-map {
    > .listing-edge-border {
      border: none;
      z-index: 2;

      &-top {
        border-right: solid 4.2rem transparent;
        border-bottom: solid 2.1rem @brand-primary;
      }

      &-bottom {
        border-right: solid 4.2rem transparent;
        border-top: solid 2.1rem @brand-primary;
      }

      @media (min-width: (@largeDesktop)) {
        border: none;

        &-top {
          border-left: solid 4.2rem transparent;
          border-bottom: solid 2.1rem @brand-primary;
        }

        &-bottom {
          border-left: solid 4.2rem transparent;
          border-top: solid 2.1rem @brand-primary;
        }
      }
    }

    > .listing-edge-items {
      @media (min-width: (@largeDesktop)) {
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.25);
      }
    }
  }

  .listing-edge-items {

    .listing-edge-item {
      width: 4.2rem;
      height: 4.2rem;
      background-color: @brand-primary;
      box-sizing: border-box;

      &, & * {
        .cursor-pointer();
      }

      &:hover {
        background-color: lighten(@brand-primary, 10%);
      }

      &.close {
        color: #fff;
        text-align: center;

        svg {
          width: 40%;
          height: 40%;
          margin: 30%;

          fill: #fff;
        }
      }
      &.street, &.aerial, &.parcel, &.map-view {
        position: relative;
        border-top: 1px solid rgba(255, 255, 255, .5);

        svg {
          width: 60%;
          height: 60%;
          margin: 8% 20% 0;

          fill: #fff;
        }

        label {
          margin: 0;
          width: 100%;
          text-align: center;
          font-size: 0.75em;
          color: white;
          position: absolute;
          left: 0;
          bottom: 0;
          right: 0;
        }
      }

      &:first-child {
        border-top: none;
      }

      &.not-available {
        background-color: #d1d1d1;

        &, & * {
          cursor: default;
        }
      }
    }
  }
}

nav.listing-nav {
  text-transform: uppercase;
  border-top: 1px solid #dedede;
  padding: 5px 0px 0px 10px;
  overflow: visible;
  height: 50px;
  margin: 0;

  a.item {
    margin: 0 0.1em 0 .4em;
    padding: 0 0.4em;
  }

  a.btn{
    background-color: @body-bg;
    font-weight:600;
  }

  > .item:first-child {
    margin-left: 0;
  }
  > .item:last-child {
    margin-right: 0;
  }

  .contact-button{
    height: 28px;
    display: inline-block;
    background-color: @icon-blue;
    color: #fff;
    margin-top: 5px;
    margin-right: 10px;
    padding: 3px 6px;
    font-size: 10px;
    float: right;
    svg {
      width: 15px;
      height: 20px;
      vertical-align: middle;
      fill: #fff;
    }
    &.sm {

    }
  }
}

div.icon-back-to-top-container {
  float: right;
  width: 2em;
  height: 2em;
  margin: 3em 1em 0 0;
  .cursor-pointer();

  svg.icon-back-to-top {
    fill: @brand-primary;
    stroke: @brand-primary;
    width: 100%;
    height: 100%;
  }
}

.top-icons {
  width: 30px;
  height: 30px;
  margin-top: 0.75em;
  margin-right: 1em;

  .cursor-pointer();
  fill: @detail-blue;

  svg {
    width: 100%;
    height: 100%;
  }

  &.first {
    margin-right: 1.5em;
  }

  &.share-listing{
    position: relative;
    cursor: pointer;
    box-shadow: none !important;

    &.share-listing-show-popup{
      svg{
        fill: @brand-red;
      }
      .share-listing-popup{
        display: flex;
      }
    }

    .share-listing-popup{
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 1;
      background-color: #fff;
      display: none;
      justify-content: space-around;
      box-shadow: 0 2px 4px 1px rgba(0,0,0,.2);
      min-width: 12em;
      &>a{
        text-align: center;
        flex: 1 0 auto;
        height: 35px;
        margin: 15px 0 15px 15px;
        display: inline-block;
        svg{
          height: 35px;
          width: 35px;
          fill: @icon-blue;
        }
      }
      &>a:last-child{
        margin-right: 15px;
      }
    }
  }
}

&.listing-schools {

  h2 {
    svg.icon-in-header {
      transform: translateY(8px);
    }
  }

  .info-button{
    display: inline-block;
    margin-left: 8px;
    top: 3px;
    position: relative;
  }

  .icon-info{
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  table.school-table {

    thead tr th {
      font-weight: 500;
      color: @brand-primary;
    }

    tbody tr {
      .cursor-pointer();

      &:hover, &.active {
        background-color: #f5f5f5;
      }

      th {
        font-weight: 500;
        color: @brand-primary;

        small {
        }
      }

      td {
        vertical-align: middle;
        font-weight: 500;

//        &.rating {
//          font-weight: bold;
//          vertical-align: top;
//          background-color: #fff;
//
//          div {
//            height: 2.25em;
//            width: 2.25em;
//            border: 1px solid #000;
//            border-radius: 50%;
//            margin: 0 auto;
//
//            p {
//              margin: 0;
//              line-height: 2.1em;
//              text-align: center;
//            }
//          }
//
//          &.rating-1, &.rating-2, &.rating-3,
//          &.rating-4, &.rating-5, &.rating-6,
//          &.rating-7, &.rating-8, &.rating-9, &.rating-10 {
//            div {
//              border-color: @brand-primary;
//            }
//          }
//        }

        &.rating-school-name {

        }

        &.grade-range {
          white-space: nowrap;
        }

        &.distance {
          white-space: nowrap;
        }

        &.arrow {
          div {
            width: 1em;
            height: 1em;

            svg {
              width: 100%;
              height: 100%;
              stroke: @brand-primary;
              stroke-width: 10px;
            }
          }
        }
      }

      &.active {

        td.rating {

        }

        td.arrow div {
          transform: scaleY(-1);
          filter: "FlipH"; // For IE
        }
      }

      &.school-active-row {
        display: none;

        &.active {
          display: table-row;

          div {
            text-align: center;

            &:nth-child(2){
              border-left: 1px solid #DEDDDD;
            }

            &:nth-child(2):not(:last-child){
              border-left: 1px solid #DEDDDD;
              border-right: 1px solid #DEDDDD;
            }

            &:first-child {
              border-left: none;
            }

            p {
              margin: 0;

              &:first-child {
                color: @detail-blue;
                font-size: 2em;
              }
            }
          }
        }

        .school-district{
          display: table;
          width: 100%;

          p{
            font-size: 1em !important;
            font-weight: 600;
            height: 39px;
            display: table-cell;
            width: 100%;
            vertical-align: middle;
            text-align: center;
          }
        }

        .school-website{
          color: @icon-blue;
          text-decoration: underline;
        }

        .school-website-logo{
          height: 31px;
          width: 100%;
          fill: @icon-blue;
          top: 5px;
          position: relative;
        }
      }
    }
  }
}

&.listing-neighborhood {

  div.contact-me-container {
    text-align: center;
    position: relative;
    margin-top: 1em;

    hr {
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      border-color: #ddd;
      margin: 0;
    }

    div.contact-me {
      max-width: 98%;
      background: #fff;
      border-radius: 3px;
      padding: 1em 2em 1.5em;
      display: inline-flex;
      justify-content: center;
      align-items: stretch;
      flex-wrap: wrap-reverse;
      position: relative;
      z-index: 1;

      div.left-side {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        max-width: 225px;
        margin-top: 10px;

        h6 {
          text-align: left;
        }
        a.btn {
          margin-top: 1em;
          font-weight:500;
        }
      }

      div.agent-container {
        text-align: center;
        margin-top: 10px;

        p {
          padding-top: .75em;
          font-weight: 500;
        }

        .agent-image {
          border-radius: 50%;
          height: 3.75em;
          width: 3.75em;
          display: inline-block;
        }
      }
    }
  }
}

div.listing-sub-panel.transportation {

  h3 {
    svg {
      height: 1em;
      width: 4em;
      fill: #aaa;
      float: right;
    }
  }

  table.table {
    height: 100px;

    td.commute-time {
      width: 100px;
      color: #fff;
      fill: #fff;
      position: relative;
      text-align: center;
      padding: 0;

      div.commute-wrapper{
        position: relative;
        height: 100%;
        width: 100%;
        background-color: @brand-darkblue;
        border-radius: @border-radius-small;

        p {
          font-size: 160%;
        }

        svg {
          position: absolute;
          top: 25%;
          left: 10%;
          right: 10%;
          bottom: 10%;
          width: 80%;
          height: 70%;
          padding: 1.2em;
        }

        small {
          position: absolute;
          bottom: 5%;
          left: 0;
          right: 0;
          font-size: 60%;
        }

      }
    }
  }
}

div.listing-sub-panel.weather {

  table {
    border: none;
    margin-top: 10px;
    margin-bottom: 0px;

    thead {
      tr {
        th {
          text-align: center;
          font-weight: 400;
          width: 25%;
          border-top: none;
          border-bottom: none;
          padding-top: 0;

          &:first-child {
            border-left: none;
          }
          &:last-child {
            border-right: none;
          }
        }

        &.seasons {
          th {
            padding: 0;

            svg {
              width: 4em;
              height: 4em;
              fill: @icon-blue;
            }
          }
        }
      }
    }

    tbody {
      tr {
        &:first-child {
          td {
            border-top: none;
            border-bottom: none;
          }
        }

        &.temp td {
          width: 12.5%;
          padding-bottom: 0;

          span.hi-value {
            color: @brand-red;
            font-size: 150%;
          }
          span.low-value {
            color: @icon-blue;
            font-size: 150%;
          }
          span.hi-label {
            color: @brand-red;
          }
          span.low-label {
            color: @icon-blue;
          }
        }

        &.temp-spacing {

          td {
            border-top: none;
            border-bottom: none;
            padding: 4px 0;

            &:first-child {
              border-left: none;
            }
            &:last-child {
              border-right: none;
            }
          }
        }

        td {
          text-align: center;
          position: relative;
          vertical-align: middle;
          padding: 0;

          .cloudy, .sunny{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            &>span{
              margin-top: 8px;
            }
          }

          &:first-child {
            border-left: none;
          }
          &:last-child {
            border-right: none;
          }

          svg {
            width: 4em;
            height: 4em;
          }

          .icon-blue {
            color: @icon-blue;
          }

          &.air-quality-td {
            vertical-align: middle;

            div.air-quality-container {
              position: relative;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;

              display: flex;
              flex-direction: column;

              div.air-quality {
                .gauge-container{
                  position: relative;
                  width: 8em;
                  margin: 0 auto;
                  .color-gauge{
                    width: 100%;
                    height: 4em;
                    vertical-align: middle;
                  }
                  .needle{
                    width: 75%;
                    height: auto;
                    position: absolute;
                    top: 100%;
                    left: 50%;
                  }

                  .air-quality-min{
                    position: absolute;
                    font-size: 11px;
                    left: 4px;
                    top: 100%;
                  }
                  .air-quality-max{
                    position: absolute;
                    font-size: 11px;
                    right: -4px;
                    top: 100%;
                  }
                }
                .air-quality-info{
                  margin-top: 20px;
                  .air-quality-score{
                    text-align: center;
                    font-size: 24px;
                    line-height: 24px;
                  }
                  .air-quality-description{
                    font-size: 13px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.listing-flyout {

  strong {
    font-weight: 600;
  }

  .overlay-container {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
  }

  h2, h3 {
    margin-top: .5em;
    margin-bottom: 0.5em;
    color: @detail-blue;
    font-weight: 600;
    letter-spacing: .75px;

    &.border-bottom {
      border-bottom: 1px solid #ddd;
    }
  }
  h2 {
    font-size: 1.25em;

    svg.icon-in-header {
      width: 2em;
      height: 2em;
      fill: @brand-darkblue;
      margin-right: .5em;
    }
  }
  h3 {
    font-size: 1.25em;
    color: @brand-darkblue;
    font-weight: 200;
    text-transform: uppercase;
  }

  .listing-panels {
    padding: 1em 1.5em 1.5em;

    .listing-footer-info-1 {
      padding-top: 20px;
      font-size: 80%;
    }

    p span.text-primary {
      font-weight: 600;
      letter-spacing: .25px;
    }

    .listing-status{

    }

    &.listing-snapshot {

      p.list-price {
        font-size: 1.75em;
        margin-bottom: 0;

        span.pull-right {
          font-size: 0.6em;
          margin-top: 4px;
        }
      }
    }

    &.listing-detail {
      margin-top: 1em;

      & > .parcel{
        padding-bottom: 1.5em;
      }
      div.description-container,
      div.highlights-container{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width:100% !important;

        span.agent-office-name {
          font-size: 17px;
        }
      }

      div.col-sm-1-1{
        border-right:1px solid transparent !important;
        border-bottom:1px solid #dedede;
        padding-bottom:20px;
      }

      div.listing-agent-section {
        p {
          span {
            &.agent-line {
              display: block;
              .mb5();
            }

            &.sep {
              display: none;
            }
          }

          img {
            .mt5();
          }
        }
      }

      div.property-cta-container{

        width:100%;

        h6{
          text-align: center;
        }

        a.btn{
          margin: 0 auto;
          font-weight:500;
        }

        margin-top:1em;
        padding-left:15px;


        div.agent-container{
          text-align:center;
          padding:15px 0;

          p{
            padding-top:.75em;
            font-weight:500;
          }

          .agent-image{
            border-radius: 50%;
            height: 3.75em;
            width: 3.75em;
            display: inline-block;
          }
        }

      }

      div.property-highlights-item-container {
        width: 90px;
        display: inline-block;
        position: relative;
        text-align: center;

        div.property-highlights-item {
          padding-top: 0.8em;
          padding-bottom: 0.2em;

          p.title {
            color: #999999;
            font-size: 85%;
            font-weight: 500;
          }

          p.content {
            margin: 0.5em 0 0.3em;
            color: @detail-blue;
            font-size: 1.25em;
            line-height: 1em;
            height: 1em;
          }

          svg {
            fill: #999999;
            stroke: #d6d6d6;
            width: 4em;
            height: 3.5em;
          }
        }

        div.property-highlights-item:after {
          content: '';
          position: absolute;
          top: 0.25em;
          right: 0.25em;
          bottom: 0.25em;
          left: 0.25em;
        }
      }

      div.listing-sub-panel.home-details {
        div.home-details-item {
          width: 33.33%;
          float: left;
        }
      }

      div.listing-sub-panel {
        p {
          letter-spacing: .02em;
        }
      }

      div.listing-sub-panel.parcel {
        .parcelContainer {
          position: relative;
        }
        @media screen and (min-width: 1px) {
          .parcelContainer {
            height: 300px;
          }
        }
        @media screen and (min-width: 480px) {
          .parcelContainer {
             height: 300px;
          }
        }
        @media screen and (min-width: 600px) {
          .parcelContainer {
            height: 355px;
          }
        }
      }
    }

    &.listing-financial {

    }

    &.listing-neighborhood {

      .section-gray();

      svg.icon-neighborhood {
        width: 2em;
        height: 2em;
        fill: @brand-darkblue;
        margin-right: .5em;
      }

      a.btn.icon-view-on-map-container {
        display: none;

        color: @brand-primary;

        svg.icon-view-on-map {
          height: 1.5em;
          width: 1.5em;
          margin-right: 0.5em;
          fill: @brand-primary;
        }
      }

      div.listing-sub-panel.weather {

        .col-1-1 {
          width: 100% !important;
        }
      }
    }


    &.listing-footer {

      .mls-disclosure {

        font-size: 14px;

        p {
          margin-top: 5px;

          &.extra-message {
            font-weight: 700;
          }
        }
      }

      .n-play-footer {
        font-size: 80%;
        margin: 0 -17px;

        .n-play-footer-links{
          float:none;
          text-align: center;
          a {
            display: inline-block;
          }
        }

        .n-play-footer-powered{
          display: none;
        }
      }
    }
  }

  .listing-map-subpanel {
    z-index: 11;

    @media screen and (min-width: (@desktop + 0.1em)) {
      z-index: 5;
    }
  }

  .sub-left-enter {
    transform: translateX(100%);
  }

  .sub-left-enter.sub-left-enter-active {
    transform: translateX(0);
    transition: transform .15s ease-out;
  }

  .sub-left-leave {
    transform: translateX(0);
  }

  .sub-left-leave.sub-left-leave-active {
    transform: translateX(100%);
    transition: transform .15s ease-in;
  }
}

.section-gray {
  background-color: #f5f5f5;
}

.listing-panels-container {
  > hr {
    margin: 0;
  }
}

.listing-full, .listing {
  background-color: @accent-gray;

  .listing-sub-panel {
    position: relative;
  }

  .listing-map-subpanel {
    width: ~"calc(100% - 280px)" !important;
    left: 0;
    z-index: 9999;
    .listing-edge-tabbar {
      left: 0;
      z-index: 2;
    }
  }
  .listing-edge-tabbar {
    &.on-listing-detail {
      left: 0 !important;
      z-index: 2;

      @media (min-width: ( @xlargeDesktop + 8em) ) {
        left: -3em !important;
      }
    }
  }

  a.close-button-cross.full-listing-close {
    width: 48px;
    height: 48px;
    margin-top: -5px;
    padding: 6px;
    background: @brand-primary;

    svg {
      fill: #fff;
      stroke: #fff;
    }
  }

  .top-icons {
    margin-top: 0.25em;
    margin-left: 0.5em;
  }

  div.col-sm-1-1 {
    border-right: 1px solid transparent;
    padding-bottom: 20px;
  }

  div.description-container {
    &.col-lg-2-3 {
      @media screen and (min-width: (@desktop+1px)) {
        border-right: 1px solid #dedede;
        padding-right: 2em;
      }
    }
  }

  div.highlights-container {
    &.col-lg-2-3 {
      @media screen and (min-width: (@desktop+1px)) {
        border-right: 1px solid #dedede;
        padding-right: 2em;
      }
    }
  }

  div.property-cta-container {
    margin-top: 1em;
    padding-left: 15px;

    h6 {
      text-align: center;
      font-size: 1em;
    }

    a.btn {
      margin: 0 auto;
      font-weight: 500;
    }

    div.agent-container {
      text-align: center;
      padding: 15px 0;

      p {
        padding-top: .75em;
        font-weight: 500;
      }

      .agent-image {
        border-radius: 50%;
        height: 3.75em;
        width: 3.75em;
        margin-right: .5em;
        display: inline-block;
      }
    }
  }

  div.listing-sub-panel.parcel {
    border-bottom: none;
    .parcelContainer {
      position: relative;
    }
    @media screen and (min-width: 1px) {
      .parcelContainer {
        height: 300px;
      }
    }
    @media screen and (min-width: 480px) {
      .parcelContainer {
        height: 300px;
      }
    }
    @media screen and (min-width: 600px) {
      .parcelContainer {
        height: 355px;
      }
    }
  }

  .scroll-container {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding-top: 10px;
    padding-left: 50px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    max-width: @content-left-max-width;
    margin: 0 auto;

    .listing-full-content-container {
      width: 100%;
      margin: 0 auto;
      background: #fff;

      #photo-slider {
        .broker-on-slider{
          position: absolute;
          bottom: 0;
          width: 100%;
          z-index: 1;
        }
        .broker-on-slider ~ .tag-button-photo{
          top: 0px;
        }
        .agent-container {
          display: none;
        }
      }
    }
  }

  .overlay-container {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    padding-left: 50px;
    max-width: @content-left-max-width;
    margin: 0 auto;
    z-index: 1;

    .listing-full-content-container.stick {
      max-width: @content-left-max-width;
      margin: 0 auto;
      background-color: #fff;

      .listing-header-container {

        #photo-slider {

          .agent-container {
            display: none;
          }
        }
      }
    }
  }

  h2, h3 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    color: @detail-blue;
    font-weight: 600;
    letter-spacing: .75px;

    &.border-bottom {
      padding-bottom: 3px;
      border-bottom: 1px solid #ddd;
    }
  }
  h2 {
    font-size: 1.25em;

    svg.icon-in-header {
      width: 2em;
      height: 2em;
      fill: @brand-darkblue;
      margin-right: .5em;
    }
  }
  h3 {
    font-size: 1.25em;
    color: @brand-darkblue;
    font-weight: 200;
    text-transform: uppercase;
    margin-top: 1em;
  }

  .listing-panels {
    padding: 1em 1em 1.5em;

    p span.text-primary {
      font-weight: 600;
      letter-spacing: .25px;
    }

    .description {
      h3 {
      }

      div.listing-agent-section {

      }
    }

    .listing-sub-panel {
      padding: 0 1em 1em 0em;
      //      border-bottom:1px solid #dedede;
    }

    &.listing-snapshot {

      .snap-container {
        margin-bottom: 5px;
        margin-top: 5px;
      }

      p.list-price {
        font-size: 1.75em;
        margin-bottom: 0;

        span.pull-right {
          font-size: 0.6em;
          margin-top: 4px;
        }
      }
    }

    &.listing-detail {
      margin-top: 1em;

      & > .parcel {
        padding-bottom: 1.5em;
      }

      span.agent-office-name {
        font-size: 17px;
      }

      div.listing-agent-section {
        p {
          span {
            &.agent-line {
              display: block;
              .mb5();
            }

            &.sep {
              display: none;
            }
          }

          img {
            .mt5();
          }
        }
      }


      span.agent-office-name {
        font-size: 17px;
      }

      div.property-highlights-item-container {
        width: 8em;
        display: inline-block;
        position: relative;
        text-align: center;

        div.property-highlights-item {
          padding-top: 0.8em;
          padding-bottom: 0.2em;

          p.title {
            color: #999999;
            font-size: 80%;
          }

          p.content {
            margin: 0.6em 0 0.25em;
            color: @detail-blue;
            font-size: 1.25em;
            line-height: 1em;
            height: 1em;
          }

          svg {
            fill: #999999;
            stroke: #d6d6d6;
            width: 4.5em;
            height: 3.5em;
          }
        }

        div.property-highlights-item:after {
          content: '';
          position: absolute;
          top: 0.25em;
          right: 0.25em;
          bottom: 0.25em;
          left: 0.25em;

        }
      }

      div.listing-sub-panel.home-details {
        div.home-details-item {
          width: 33.33%;
          float: left;
        }
      }

      div.listing-sub-panel.transportation {

      }
    }

    &.listing-financial {

    }

    &.listing-neighborhood {

      .section-gray();

      a.btn.icon-view-on-map-container {
        display: none;

        color: @brand-primary;

        svg.icon-view-on-map {
          height: 1.5em;
          width: 1.5em;
          margin-right: 0.5em;
          fill: @brand-primary;
        }
      }
    }

    &.listing-schools {

      div {
        div {
          &.schools-item {
            border-bottom: 1px solid transparent;
          }
        }
      }

    }

    &.listing-footer {

      .mls-disclosure {
        font-size: 14px;

        p {
          margin-top: 5px;

          &.extra-message {
            font-weight: 700;
          }
        }
      }

      .listing-footer-info-1 {
        padding-top: 20px;
        font-size: 80%;
      }

      .n-play-footer {
        font-size: 80%;
        margin: 0 -14px;
      }
    }
  }
}


ul.parcel-header{
  padding: 0;
  margin-top: 1.25em;

  li{
    font-size: 1em;
    color: #5d9ed6;
    font-weight: 600;
    letter-spacing: .75px;
    display: inline-block;
    padding: 8px 15px 8px 0;
    list-style-type: none;
    cursor: pointer;
    &.disabled{
      cursor: not-allowed;
      color: #ccc;
      &:after{
        border-bottom: none !important;
      }
    }
    &:hover{
      &:after{
        content: "";
        border-bottom: 2px solid @icon-blue;
        display: block;
      }
    }
    &.active{
      cursor: default;
      &:after{
        content: "";
        border-bottom: 2px solid @icon-blue;
        display: block;
      }
    }
  }
}

.yelp-local{
  margin: 12px 0px 16px 0px;

  .yelp-list-and-map{
    display: flex;

    .yelp-list-wrapper{
      min-width: 0px;
      width: 100%;
      flex: 1 1 auto;
    }
  }

  .select-container{
    width: 100%;
    display: flex;
    margin: 8px 0px 0px 0px;

    select{
      font-size: 16px;
      color: @brand-red;
      flex: 1 1 auto;
      margin: 0px 8px 0 0;
      background-image: url('//nplayassets.blob.core.windows.net/search2/chevron-red.png') !important;
      background-position: right 12px top 50% !important;
      font-weight: 400;
      border: 1px solid #ccc;
    }

    button{
      flex: 0 0 auto;
    }

  }

  .yelp-results{
    max-height: 350px;
    overflow-y: auto;
    margin-top: 8px;
  }

  .slick-slider{
    width: 80%;
    margin: 16px auto;
    max-width: 420px;
  }

  .yelp-business{

    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #ddd;
    padding: 0px 8px;

    &:last-child{
      border-bottom: none;
    }

    .header{
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 8px 0px;

      .yelp-name{
        flex: 1 1 auto;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .yelp-business-rating{
        margin: 0 8px;

        img{
          width: 82px;
        }
      }

      .distance{
        margin-right: 8px;
        width: 50px;
        text-align: right;
        font-size: 14px;
        flex: 0 0 auto;
      }


      svg{
        width: 16px;
        height: 16px;
        fill: #999;
        flex: 0 0 auto;
      }

    }

    .content{
      display: flex;
      height: 0px;
      transition: height 0.2s ease;
      overflow: hidden;
    }

    &.expanded{

      .header{
        font-weight: bold;

        img{
          display: none;
        }
      }

      .content{
        padding: 0px 0px 8px 0px;
        height: 108px;

        > a {
          img{
            width: 100px;
            height: 100px;
            object-fit: cover;
          }
        }
      }
    }

    &>img{
      flex: 0 0 auto;
    }

    .yelp-business-content{
      flex: 1 1 auto;
      padding-left: 1em;
    }

    .yelp-reviews{
      display: flex;
      align-items: center;

      .yelp-business-rating{
        display: inline-block;

        img{
          width: 102px;
        }
      }

      .yelp-business-review-count{
        margin-left: 8px;
        display: inline-block;
        font-style: italic;
      }
    }

    .yelp-name{
      font-size: 18px;
      text-decoration: none;
      color: #000;
    }

    .yelp-categories{

      line-height: 16px;
      margin: 4px 0;

      .text-muted{
        color: #999;
      }

    }

    .yelp-price{
      line-height: 16px;

      .text-muted{
        color: #999;
      }
    }

  }

  .slick-prev:before, .slick-next:before{
    content: '' !important;
    border-top: 13px solid #F9B399;
    border-left: 13px solid #F9B399;
    border-bottom: 13px solid transparent;
    border-right: 13px solid transparent;
    width: 0 !important;
    height: 0 !important;
    display: block;
  }

  .slick-prev:before{
    transform: rotateZ(-45deg);
  }

  .slick-next:before{
    transform: rotateZ(135deg);
  }

  .yelp-result{
    background-image: url('//nplayassets.blob.core.windows.net/search2/yelp-pin-thicker-stroke.png');
    color: white;
    width: 31px !important;
    height: 43px !important;
    font-weight: bold;
    text-shadow: 1px 1px black;
    border-radius: 0%;
    margin-left: -16px !important;
    margin-top: -43px !important;
    background-size: 100%;
    display: block;
  }


  .yelp-result-active{
    background-image: url('//nplayassets.blob.core.windows.net/search2/selected-yelp-pin-thicker-stroke.png');
  }

  .yelp-popup{
    min-width: 320px;
  }

  .yelp-map{
    height: 400px;
    flex: 1 0 50%;
    border: 1px solid #ccc;
  }

  .yelp-popup{
    min-width: 320px;

    .yelp-business{

      display: flex;
      padding: 0;
      flex-direction: row;
      border-bottom: none;

      .yelp-picture{
        flex: 0 0 auto;
        display: block;

        img{
          width: 75px;
          height: 75px;
        }
      }

      .yelp-business-content{
        flex: 1 1 auto;
        padding-left: 1em;
      }

      .yelp-reviews{
        display: flex;
        align-items: center;

        .yelp-business-rating{
          display: inline-block;
        }

        .yelp-business-review-count{
          margin-left: 8px;
          display: inline-block;
          font-style: italic;
        }
      }

      .yelp-name{
        font-size: 18px;
        text-decoration: none;
        color: #000;
        line-height: 18px;
      }

      .yelp-categories{

        line-height: 14px;
        margin: 4px 0px;

        .text-muted{
          color: #999;
        }

      }

      .yelp-price{
        line-height: 14px;

        .text-muted{
          color: #999;
        }
      }
    }
  }

}

div.rateplug-special-financing-details {
  .primary-program-button {
    position: relative;
    // border: 1px solid @accent-gray;
    border-radius: 5px;
    padding: 10px 24px 10px 14px;
    min-width: 153px;
    margin-bottom: 10px;
    cursor: pointer;
    float: right;
    clear: right;
    color: white;

    &:after {
      content: '\25B6'; /* right arrow */
      position: absolute;
      top: 50%;
      right: 14px;
      transform: translateY(-50%);
      font-size: 1rem;
    }

    p {
      margin: 0;
    }
  }

  .primary-program-button {
    background-color: @brand-danger;
  }

  > .dropdown {
    position: relative;
    width: 153px;
    margin-bottom: 10px;
    cursor: pointer;
    float: right;
    clear: right;
    color: white;

    > .btn {
      border-radius: 5px;
      padding: 10px 24px 10px 14px;
      background-color: @brand-primary;
      width: 100%;
      font-weight: 400;
      font-size: 80%;
      text-align: left;

      &:after {
        content: '\25BE'; /* down arrow */
        position: absolute;
        top: 50%;
        right: 14px;
        transform: translateY(-50%);
        font-size: 1.5rem;
      }
    }
  }
}

div.mortgage-calculator, div.home-inspection-calculator {
  border: 1px solid @accent-gray;
  border-radius: 5px;
  padding: 6px 14px;
  min-width: 153px;
  margin-bottom: 10px;
  cursor: pointer;
  float: right;
  clear: right;

  &:hover {
    background-color: @accent-gray;
  }

  span.monthly-payment, span.home-inspection-cost {
    font-size: 1.2em;
    font-weight: 500;
  }

  svg {
    width: 18px;
    height: 18px;
    fill: @icon-blue;
    float: right;
    margin-right: 2px;

    &.icon-calculator {
      margin-left: 5px;
    }

    &.icon-inspection {
      width: 22px;
      height: 19px;
      margin-right: 0;
    }
  }

  p.mortgage-calculator-cta, p.home-inspection-cta {
    min-width: 122px;
    font-size: 82%;
    font-weight: 600;
  }
}

.modal#mortgage-calculator-popover .modal-dialog {
  width: 100%;
  max-width: 320px;
  margin-top: 6em;
  margin-left: auto;
  margin-right: auto;
  // max-height: 85vh;
  // overflow-y: scroll;

  .popover-title {
    font-weight: 400;
    text-align: center;
    font-size: 1.25em;
    padding: 18px 12px 6px;
    background: @accent-gray;
    color: @brand-darkblue;
  }

  .close-button-cross {
    position: absolute;
    top: 0;
    right: 0;
    padding: 5px;
    cursor: pointer;
  }

  .popover-content {
    padding: 0;
    border: 5px solid @accent-gray;
    border-bottom-width: 0;

    .form-control {
      padding: 6px;
      border-radius: 2px;
    }

    .input-group-addon {
      padding: 6px;
    }

    div.advanced-toggle a {
      font-weight: 500;
    }

    div.divider {
      margin: 10px -10px;
      height: 5px;
      background: @accent-gray;
    }

    p.estimate {
      font-weight: 400;
      font-size: 2em;

      sub {
        bottom: 0px;
        font-size: 50%;
      }
    }

    div.rate-label > p {
      line-height: 3rem;
    }

    div#pieContainer {
      height: 120px;
      margin: 10px 15px;
      position: relative;

      div.pieBackground {
        background-color: grey;
        position: absolute;
        width: 120px;
        height: 120px;
        border-radius: 60px;
      }

      div.pie {
        position: absolute;
        width: 120px;
        height: 120px;
        border-radius: 60px;
        clip: rect(0px, 60px, 120px, 0px);
      }
      div.hold {
        position: absolute;
        width: 120px;
        height: 120px;
        border-radius: 60px;
        clip: rect(0px, 120px, 120px, 60px);
      }

      div.pieSlice1 > div.pie {
        background: @icon-blue;
      }
      div.pieSlice2 > div.pie {
        background: @brand-darkblue;
      }
      div.pieSlice3 > div.pie {
        background: @brand-primary;
      }

      div.pieCenter {
        background-color: white;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        border: 3px solid #fff;
        position: absolute;
        width: 100px;
        height: 100px;
        margin: 10px;
        border-radius: 50px;
      }
    }

    p.legend-label {
      font-size: 10px;

      span.legend-icon {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 3px;

        &.legend1 {
          background: @icon-blue;
        }
        &.legend2 {
          background: @brand-darkblue;
        }
        &.legend3 {
          background: @brand-primary;
        }
      }
    }

    p.legend-amount {
      font-size: 12px;
    }

    div.mortgage-calculator-lending-tree-cta {
      text-align: center;
      border: 3px solid #4EAA7D;
      border-radius: 2px;
      cursor: pointer;
      padding: 10px;
      margin: -10px;
      width: ~"calc(100% + 20px)";

      h2 {
        font-weight: 700;
        font-size: 17px;
      }

      img {
        margin-top: 10px;
        width: 130px;
        height: 25px;
      }
    }
  }

  p.disclaimer {
    background-color: @accent-gray;
    font-size: 70%;
    padding-bottom: 12px;
  }
}

.modal.mobile-modal#mortgage-calculator-popover .modal-dialog {
  max-width: 310px;
  margin: 10px auto;
}

.modal#home-inspection-popover .modal-dialog .modal-content {
  border-radius: 15px;

  .close-button-cross {
    width: 50px;
    height: 50px;
    position: absolute;
    top: 0;
    right: 0;
  }

  .popover-content {
    padding: 40px;

    h2 {
      text-align: center;
      font-size: 21px;
      font-weight: 600;
      margin: 0 0 30px;
    }

    img {
      width: 100%;
    }

    .snapshot {
      padding-left: 20px;
      margin-top: 5px;
    }

    p {
      font-weight: 200;
    }

    strong {
      font-weight: 400;
    }

    hr {
      margin-top: 30px;
      margin-bottom: 20px;
    }

    .inspection-footer-logo {
      margin-top: 20px;
      width: 300px;
      height: auto;
      max-width: 90%;
    }

    .btn {
      box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.2);
      border-radius: 3px;
      color: #fff;
      max-width: 280px;
      margin: 0 auto;
      font-weight: 600;
      line-height: 30px;
      letter-spacing: 1px;
    }

    &.success {
      text-align: center;

      img.inspection-success-check {
        width: 95px;
        height: 95px;
      }

      h3 {
        margin: 30px 10px;
        font-size: 26px;
        line-height: 35px;
      }

      p {
        font-size: 16px;
        font-weight: normal;
        line-height: 150%;
        margin: 0 10px 40px;
      }
    }
  }
}

.modal.mobile-modal#home-inspection-popover .modal-dialog .modal-content {

  .popover-content {
    padding: 40px 20px;

    .snapshot {
      padding: 0;
      margin-top: 25px;
      text-align: center;
    }
  }
}

.listing-full{
  > .scroll-container{
    position: absolute;
  }
}

div.listing-404-container {
  div.listing-404 {
    svg {
      width: 300px;
      height: 200px;
      margin-bottom: -35px;
    }
    width: 300px;
    margin: 60px auto 30px;

    p.listing-404-mobile {
      display: none;
    }
  }
}

.mobile div.listing-404-container {
  div.listing-404 {
    svg {
      width: 250px;
      height: 165px;
    }
    width: 250px;

    p.listing-404-desktop {
      display: none;
    }
    p.listing-404-mobile {
      display: block;
    }
  }
}

.modal#rateplug-special-financing-modal {
  .modal-content {
    background-color: #ebebeb;
    border-radius: 15px;

    .close-button-cross {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
    }

    .popover-content {
      padding: 5px;


      .dropdown {
        position: relative;
        cursor: pointer;
        color: white;
        margin-bottom: 20px;
        margin-left: auto;
        margin-right: auto;
        display: inline-block;

        > .btn {
          border-radius: 5px;
          padding: 10px 14px;
          background-color: @brand-primary;
          font-weight: 600;
          font-size: 80%;
        }
      }

      h2 {
        text-align: center;
        font-size: 1.75rem;
        font-weight: 600;
        color: @brand-darkblue;
        margin: 10px 0 12px;
      }

      > div {
        padding: 20px 30px;
      }

      .white-divider {
        margin: 10px -5px;
        padding: 0;
        height: 5px;
        background: white;
      }

      .white-section {
        background-color: #fff;
        margin-bottom: 5px;

        .listing-preview {
          img[alt="Listing"] {
            width: 100%;
            border-radius: 5px;
          }

          .snapshot {
            padding-left: 20px;
          }
        }
      }

      h3 {
        text-align: center;
        font-size: 1.5rem;
        font-weight: 600;
        color: #000;
        margin: 20px 0 12px;

        &:first-child {
          margin-top: 0px;
        }
      }

      h4 {
        text-align: center;
        font-size: 1.35rem;
        font-weight: 400;
        color: #000;
        margin: 10px 0;
      }

      tr {
        td:first-child {
          padding-right: 20px;
        }
      }

      .program-description-section {
        margin-bottom: 15px;

        &:nth-child(2) {
          background: #DFF0FF;
          padding: 10px 15px;
          border-radius: 5px;
          margin-left: -5px;
          margin-right: -5px;

          h3 {
            font-weight: 400;
            text-align: left;
            font-size: 1.4rem;
          }
        }
      }

      .contact-loan-offier-methods {
        display: flex;
        gap: 5px;
        margin-top: 10px;

        .btn {
          text-transform: none;
          flex-grow: 1;
          color: #474747;
          border-color: #474747;

          > svg {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 5px;
            fill: #474747;
            stroke: #474747;
          }

          &:hover {
            color: @brand-success;
            border-color: @brand-success;
            fill: @brand-success;
            stroke: @brand-success;

            > svg {
              fill: @brand-success;
              stroke: @brand-success;
            }
          }
        }
      }

      div.contact-me-container {
        text-align: center;
        position: relative;
        margin-top: 1em;

        hr {
          position: absolute;
          left: 0;
          right: 0;
          top: 50%;
          border-color: #ddd;
          margin: 0;
        }

        div.contact-me {
          max-width: 98%;
          background: #fff;
          border-radius: 3px;
          padding: 1em 2em 1.5em;
          display: inline-flex;
          justify-content: center;
          align-items: stretch;
          flex-wrap: wrap-reverse;
          position: relative;
          z-index: 1;

          div.left-side {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            max-width: 250px;
            margin-top: 10px;

            h6 {
              text-align: left;
            }
            a.btn {
              margin-top: 1em;
              font-weight:500;
            }
          }

          div.agent-container {
            text-align: center;
            margin-top: 10px;

            p {
              padding-top: .75em;
              font-weight: 500;
            }

            .agent-image {
              border-radius: 50%;
              height: 3.75em;
              width: 3.75em;
              display: inline-block;
            }
          }
        }
      }

      .disclaimer {
        small {
          font-size: 60%;
        }
      }

      .agent{
        background-color: #6b787c;
        padding: 7px;
      }
      .agent-info{
        margin-top: 15px;
        display: grid;
        grid-template-rows: repeat(6, 1.8rem);
        color: white;
        font-size: 0.90rem;
      }

      .agent-name{
        font-size: 1.2rem;
      }

      .profile-pic{
        width: 75px;
        height: 75px;
        border-radius: 50%;
        object-fit: cover;
      }

      .agent-dark{
        color: #9b9fa1;
        font-size: 0.80rem;
      }

      .col-2-30-70{
        display: grid;
        grid-template-columns: 30% 70%;
      }
    }
  }
}

#rateplug-popover .modal-dialog {

  .modal-content {
    position: relative;
    height: 90vh;

    .close-button-cross {
      position: absolute;
      z-index: 1;
      top: 10px;
      right: 15px;
    }

    iframe {
      width: 100%;
      height: 100%;
      position: absolute;
    }
  }
}

.rateplug-grid-container.rateplug-lender-info {
    /*background-color: #ff7052;*/

  // display: grid;

  // grid-template-rows: 265rem;
  // gap: 25px;

  .center-image{
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }

  .req-img{
    text-align: left;
    margin:4px;
    vertical-align: top;
  }

  .req-img-right{
    text-align: right;
    margin:4px;
    vertical-align: top;
  }


  .logo{
    width: 250px;
  }

  button.text-color-white{
    color: #ffffff;
  }

  .check{
    width: 2rem;
    margin-left: 60px;
    padding-top: 5px;
  }

  .header{
    // background-color: #f8f8f8;
    width: 100%;
    border-bottom-left-radius: 1.2rem;
    border-bottom-right-radius: 1.2rem;
    padding: 20px;
    height: 150px;
    // box-shadow: 2px 3px 5px #8fa6af;
  }

  .footer{
    // background-color: #f8f8f8;
    padding-top: 40px;
  }

  .w100{
    width: 100%;
  }

  .block{
    // margin: 0rem 1.5rem 0rem 1.5rem;
  }

  .w50{
    width: 50%
  }

  .w60{
    width: 60%;
  }

  .t100{
    width: 100%;
  }

  .t60{
    width: 60%;
  }

  .t70{
    width: 70%;
  }

  .t80{
    width: 80%;
  }

  .m0{
    margin: 0;
  }

  .mt0{
    margin-top: 0;
  }

  .mt20{
    margin-top: 20px;
  }
  .mt30{
    margin-top: 30px;
  }
  .mt40{
    margin-top: 40px;
  }
  .ml15{
    margin-left: 15px;
  }

  .mb15{
    margin-bottom: 15px;
  }

  .pr20{
    padding-right: 20px;
  }

  .pr40{
    padding-right: 40px;
  }

  .pr60{
    padding-right: 60px;
  }

  .pr80{
    padding-right: 80px;
  }

  .pr100{
    padding-right: 100px;
  }

  .pr120{
    padding-right: 120px;
  }

  strong{
    font-weight: 600;
    font-size: 1.1rem;
    color: #00b1fd;
  }

  .fw600{
    font-weight: 600;
  }
  .inside-shadow::after{
    // box-shadow: inset 1rem 1rem 2rem rgba(0, 0, 0, 0.5);
  }

  .top-right{
    position: relative;
    top: -120px;
    right: -205px;
    color: #39A9DB;
    width: 120px;
    padding: 5px;
    text-align: center;
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
    // box-shadow: 1px 2px 3px #8fa6af;
  }

  .top-right2{
    position: relative;
    top: -90px;
    right: -195px;
    color: #39A9DB;
    width: 130px;
    padding: 5px;
    text-align: center;
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
    // box-shadow: 1px 2px 3px #8fa6af;
  }

  .common{
    // background-color: #f8f8f8;
    // box-shadow: 2px 3px 5px #8fa6af;
    // padding-left: 2rem;
    // padding-right: 2rem;
    color: #a2a2a2;
    margin-top: 0;
  }

  .darker{
    color: #6c6c6c;
  }

  .larger{
    font-size: 1.1rem;
  }

  .green{
    color: #2eaf3b;
  }

  .fr{
    text-align: right;
  }

  .light-blue{
    color: #00b1fd;
  }

  h2.title{
    font-weight: 300;
    font-size: 2.2rem;
    margin-top: 1rem;
    margin-bottom: 0.50rem;
    color: #696969;
  }

  h2.highlight{
    font-size: 2rem;
    margin-bottom: 0;
    font-weight: 700;
  }

  h2.dark{
    font-weight: 600;
    color: #5e5e5e;
  }

  .col-2{
    display: grid;
    grid-template-columns: 50% 50%;
    color: #858585;
    font-weight: 600;
  }

  .row-2{
    display: grid;
    grid-template-rows: 1rem 1rem;
    gap: 0.4rem;
  }

  .col-2-30-70{
    display: grid;
    grid-template-columns: 30% 70%;
  }

  .col-2-70-30{
    display: grid;
    grid-template-columns: 70% 30%;
  }

  h6.payment{
    font-size: 2rem;
    margin: 0 0 0.3rem 0;
    padding: 0;
    font-weight: 600;
  }

  .sub-title{
    margin: -0.3rem;
    font-weight: 600;
    color: #a2a2a2;
    font-size: 0.9rem;
  }

  h3.no-m{
    margin: 0;
    color: #39A9DB;
    font-size: 1.5rem;
  }

  hr {
    color: #f8f8f8;
  }

  h2.est-value{
    color: #4f4f4f;
    font-weight: 700;
    font-style: normal;
    font-size: 26px;
    line-height: 30px;
  }

  .agent{
    background-color: #6b787c;
    padding: 7px;
  }
  .agent-info{
    margin-top: 15px;
    display: grid;
    grid-template-rows: repeat(6, 1.8rem);
    color: white;
    font-size: 0.90rem;
  }

  .agent-name{
    font-size: 1.2rem;
  }

  .profile-pic{
    width: 75px;
    height: 75px;
    border-radius: 50%;
    object-fit: cover;
  }

  .agent-dark{
    color: #9b9fa1;
    font-size: 0.80rem;
  }

  .card{
    display: grid;
    grid-template-rows: 3rem 6.5rem 3rem;
    width: 100%;
    border-radius: 1.2rem;
    padding: 10px;
    min-height: 190px;
    // box-shadow: 2px 3px 4px #d6d7d9;
  }

  .small-card{
    display: grid;
    border-radius: 5px;
    grid-template-rows: 1.5rem 1.5rem;
    gap: 0.5rem;
    padding: 7px;
    margin: 15px 4px 25px 5px;
    // box-shadow: 1px 2px 3px #d6d7d9;
    background-color: #faf9f9;
  }

  .my-home{
    display: grid;
    grid-template-rows: 9.2rem 4rem 18rem 3rem 3rem 15rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .my-home-price{
    display: grid;
    grid-template-columns: 125px 135px;
    gap: 25px;
  }

  .mhp-gray-box{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 1rem 1rem 1rem 1rem;
    gap: 2px;

    width: 135px;
    height: 40px;

    background: #EEEDEC;
    // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25), 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 5px;

    line-height: 1rem;
  }

  h4.bold{
    font-style: normal;
    font-weight: 700;
    font-size: 1.2rem;
    line-height: 14px;
    color: #495867;
    vertical-align: middle;
    padding-right: 5px;
    padding-top: 10px;
  }

  .blue-box{
    background: @icon-blue;
    // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    color: #ffffff;
  }

  h2.thinking{
    font-weight: 700;
    font-size: 20px;
    line-height: 23px;
  }

  .bb-description{
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    display: flex;
    align-items: center;
  }

  .my-equity{
    display: grid;
    grid-template-rows: 5rem 3rem 10rem 1rem 6rem 18rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .equity-desc{
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    display: flex;
    align-items: center;

    color: #8B8D90;
  }

  .float-left{
    text-align: left;
    width: 100%;
    margin-top: 10px;
  }

  .my-eq-grey-box{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 8px 5px 13px 10px;
    gap: 8px;

    font-weight: 600;
    /* Neutral Cool Light Gray */
    background: #EEEDEC;
    border-radius: 8px;
    color: #495867;
  }

  .breakdown{
    display: grid;
    grid-template-columns: 150px 100px;
    gap: 5px;
  }

  .blue{
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 16px;
    display: flex;
    align-items: center;
    /* Primary Cyan */
    color: #39A9DB;
  }

  .my-next{
    display: grid;
    grid-template-rows: 5rem 3rem 1rem 23rem 5rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .my-mortgage{
    display: grid;
    grid-template-rows: 8rem 4rem 27rem 8rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .my-mortgage-grey{
    background: #EEEDEC;
    border-radius: 8px;
    margin-top: 5px;
    margin-bottom: 30px;
    padding: 10px;
    color: #495867;
  }

  .my-mort-col-2{
    display: grid;
    grid-template-columns: 30px 300px;
    gap: 5px;
  }

  .my-payment{
    display: grid;
    grid-template-rows: 8rem 3rem 1rem 4rem 15rem 15rem 3rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .my-cashOut{
    display: grid;
    grid-template-rows: 5rem 3rem 1rem 3rem 8rem 5rem;
    gap: 1rem;
    background-color: #ffffff;
  }

  .my-lender{
    // display: grid;
    // grid-template-rows: 6rem 1rem 12rem 3rem 26rem 6rem 6rem 12rem 9rem 195rem;
    // gap: 1rem;
    // background-color: #ffffff;
  }

  .disclaimer-container{
    // display: grid;
    // grid-template-rows: 3rem 4rem 3rem 7rem 15rem 4rem 4rem 24rem 3rem 4rem 6rem 27.2rem 3rem 4rem 2rem 24rem 3rem 4rem 2rem 24rem 2rem;
    // gap: 1rem;

    strong, b {
      color: rgb(34, 34, 34);
    }
  }

  .mutual-logo{
    width: 55%;
  }

  @media (max-width: 694px) {


    .my-lender{
      // display: grid;
      // grid-template-rows: 6rem 1rem 12rem 3rem 26rem 6rem 6rem 12rem 9rem 195rem;
      // gap: 1rem;
      // background-color: #ffffff;
    }

    .disclaimer-container{
      // display: grid;
      // grid-template-rows: 3rem 4rem 3rem 7rem 15rem 4rem 4rem 24rem 3rem 4rem 6rem 27.2rem 3rem 4rem 2rem 24rem 3rem 4rem 2rem 24rem 2rem;
      // gap: 1rem;

    }

    .block{
      // margin: 0rem 1rem 0rem 1rem;
    }

  }


  @media (max-width: 512px) {


    .my-lender{
      // display: grid;
      // grid-template-rows: 6rem 1rem 12rem 3rem 26rem 6rem 6rem 12rem 9rem 210rem;
      // gap: 1rem;
      // background-color: #ffffff;
    }

    .disclaimer-container{
      // display: grid;
      // grid-template-rows: 3rem 4rem 3rem 7rem 15rem 4rem 4rem 25rem 3rem 4rem 6rem 33.2rem 3rem 4rem 2rem 25rem 3rem 4rem 2rem 25rem 2rem;
      // gap: 1rem;

    }

  }

  @media (max-width: 390px) {


    .mutual-logo{
      width: 75%;
    }

    .email{
      font-size: 10px;
    }

    .four-box{
      display: flex;

      flex-wrap: wrap;
      flex-direction: row;
      justify-content: center;

      padding: 15px;
    }

    .but-box{
      border-radius: 5px;
      border: 1px solid #39A9DB;
      margin: 6px;
      text-align: center;
      padding-right: 30px;
      padding-left: 30px;
      padding-top: 10px;
      width: 70px;
      height: 70px;
    }



    .rateplug-grid-container{
      /*background-color: #ff7052;*/

      // display: grid;

      // grid-template-rows: 280rem;
      // gap: 25px;

    }

    .block{
      margin: 0;
    }

    .common{
      // // background-color: #f8f8f8;
      // // box-shadow: 2px 3px 5px #8fa6af;
      // padding-left: 1rem;
      // padding-right: 1rem;
      color: #a2a2a2;
      margin-top: 0;
    }

    .my-lender{
      // display: grid;
      // grid-template-rows: 6rem 1rem 12rem 3rem 25rem 6rem 6rem 14rem 6rem 190rem;
      // gap: 1rem;
      // background-color: #ffffff;
    }

    .disclaimer-container{
      // display: grid;
      // grid-template-rows: 3rem 4rem 3rem 7rem 22rem 4rem 4rem 28.2rem 3rem 4rem 6rem 36.5rem 3rem 4rem 2rem 28.2rem 3rem 4rem 2rem 28.2rem 2rem;
      // gap: 1rem;

      font-size: 11px;
    }



  }



  @media (max-width: 352px) {

    .mutual-logo{
      width: 75%;
    }

    .email{
      font-size: 10px;
    }


    .rateplug-grid-container{
      /*background-color: #ff7052;*/

      // display: grid;

      // grid-template-rows: 315rem;
      // gap: 25px;

    }

    .block{
      margin: 0;
    }

    .common{
      // background-color: #f8f8f8;
      // box-shadow: 2px 3px 5px #8fa6af;
      // padding-left: 1rem;
      // padding-right: 1rem;
      color: #a2a2a2;
      margin-top: 0;
    }

    .my-lender{
      // display: grid;
      // grid-template-rows: 6rem 1rem 12rem 3rem 51rem 6rem 6rem 14rem 6rem 200rem;
      // gap: 1rem;
      // background-color: #ffffff;
    }

    .disclaimer-container{
      // display: grid;
      // grid-template-rows: 3rem 4rem 3rem 7rem 26rem 4rem 4rem 29.3rem 3rem 4rem 6rem 40rem 3rem 4rem 2rem 29.4rem 3rem 4rem 2rem 29.6rem 2rem;
      // gap: 1rem;

      font-size: 11px;
    }



  }

  .four-box{
    display: flex;

    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    padding: 15px 0;
  }

  .but-box{
    border-radius: 5px;
    border: 1px solid #39A9DB;
    margin: 6px;
    text-align: center;
    padding: 10px;
    width: 105px;
    height: 105px;
    display: flex;
    align-items: center;
  }

  .cta-btn{
    padding: 0;
    // margin: 10px;
  }

  .button-img{
    margin-left: auto;
    margin-right: auto;
    align-items: center;

    width: 50%;
    display: block;
  }

  /* .four-box{
    display: grid;
    grid-template-rows: 130px 130px;
    grid-template-columns: 50% 50%;
  }

  .but-box{
    border-radius: 3px;
    border: 1px solid #39A9DB;
    margin: 6px;
    text-align: center;
    padding-right: 25px;
    padding-left: 25px;
    padding-top: 10px;
  } */



  .button-green{
    height: auto;
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #53DD6C;
    color: #495867;
  }

  .button-green-stroke{
    height: auto;
    padding-top: 10px;
    padding-bottom: 10px;
    border: 0.15rem solid #66e873;
    color: #7c7c7c;
  }

  /*mat icons*/

  .material-icons {
    vertical-align: middle;
    margin-right: 5px;
    color: #a2d9f1;
  }

   .material-symbols-outlined {
     font-variation-settings:
       'FILL' 1,
       'wght' 100,
       'GRAD' 0,
       'opsz' 20
   }

   /* Brad */
  .est-home-value{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 22px;

    width: 215px;
    height: 30px;
  }

  /* Inside auto layout */
  .est-inside{
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .value-text{
    width: 99px;
    height: 30px;

    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    font-size: 26px;
    line-height: 30px;
    display: flex;
    align-items: center;

    /* Primary Cyan */

    color: #39A9DB;
  }

  button.button-blue{
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #00b1fd;
    color: white;
    z-index: 1000;
  }

  .mt15{
    margin-top: 15px;
  }


  /* TRID */

  .dark {
    color: rgb(34, 34, 34);
  }

  .program{
    font-size: 2rem;
    color: rgb(34, 34, 34);
  }

.rate-box{
  border-radius: 5px;
  border: #00b1fd solid 1px;
  width: 100%;
}

.rate-highlight{
  background-color: #39A9DB;
  padding: 15px;
  color: #fff;
}

.rate-subhighlight{
  background-color: #d1d1d1;
  padding: 10px;
  text-align: center;
  color: #1d1d1d;
}

.rate-col-2{
  display: grid;
  grid-template-columns: 50% 50%;
  color: #858585;
  border-bottom: #00b1fd solid 1px;
}

.rate-col-3{
  display: grid;
  grid-template-columns: 33.33% 33.33% 33.33%;
  color: #858585;
  border-bottom: #00b1fd solid 1px;
}

.rate-col-4{
  display: grid;
  grid-template-columns: 25% 25% 25% 25%;
  color: #858585;
  border-bottom: #00b1fd solid 1px;
}

.rate-col-4-nb{
  display: grid;
  grid-template-columns: 25% 25% 25% 25%;
  color: #858585;
}

.rate-col-3-nb{
  display: grid;
  grid-template-columns: 33.33% 33.33% 33.33%;
  color: #858585;
}

.rate-col-2-nb{
  display: grid;
  grid-template-columns: 50% 50%;
  color: #858585;
}

.border-right{
  border-right: #00b1fd solid 1px;
}

.p10{
  padding: 10px;
}

.m10{
  margin: 10px;
}

}