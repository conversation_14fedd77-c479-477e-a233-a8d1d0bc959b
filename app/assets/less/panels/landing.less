 @agent-font-size: 2.4em;

div.landing {

  position: absolute;
  background: url(//nplayassets.blob.core.windows.net/search2/backgrounds/landing-brown-house.jpg) no-repeat center center fixed;
  background-size: cover;
  transition: background 3s ease-in 1s;
  text-align: center;
  overflow-y: auto;
  height: 100%;
  width: 100%;
  top: 0;
  overflow-x: hidden;

  div.landing-wrapper-container {
    background-color: fade(@brand-blue, 30%);
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  div.landing-wrapper {
    width: 100%;
    text-shadow: 0px 1px 8px rgba(0, 0, 0, 0.9);

    div.agent-info {
      position: relative;
      height: 150px;
      display: inline-block;
      max-width: 80%;
      margin-bottom: 35px;

      .agent-profile-image {
        border-radius: 50%;
        vertical-align: top;
        border: 2px solid #fff;
        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        width: 150px;
        height: 150px;

        div.pro-badge-container {
          top: 12px;
          left: -17px;
          width: 50px;
          height: 50px;
        }
      }

      div.agent-right {
        display: inline-block;
        text-align: left;
        color: #fff;
        margin-left: 1.5em;

        h1 {
          margin-top: .5em;
          letter-spacing: 1px;
          font-weight: 400;
          font-size: @agent-font-size;
        }

        p {
          margin-top: .5em;

          &.broker-name {
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: .5px;

            &.large {
              font-size: 2.4em;
              margin: 0;
              font-weight: 400;
              text-transform: none;
              line-height: 1em;

              small {
                font-size: 100%;
              }
            }
          }

          &.headline {
            letter-spacing: 1px;
          }
        }

        &.same-size {
          h1 {
            font-size: 2.5rem;
          }

          p.broker-name {
            font-size: 2.25rem;
          }
        }
      }
    }

    div.search-area {
      z-index: 100;
      position: relative;
      width: 62%;
      letter-spacing: 1px;
      text-align: left;
      bottom: 12%;
      left: 50%;
      transform: translate3d(-50%, 0, 0);

      nav.saletype-nav {
        height: 38px;
        overflow: hidden;
        text-shadow: none;
        a {
          color: rgba(255,255,255,0.8);
          border-radius: 2px 2px 0 0;
          line-height: 38px;
          padding: 0px 16px;
          text-transform: uppercase;
          display: inline-block;
          font-size: inherit;
          font-weight: 600;
          font-size: 18px;
          background-color: fade(@brand-blue, 80%);
          &:first-child{
            border-radius: 2px 0px 0px 0px;
          }
          &:last-child{
            border-radius: 0px 2px 0px 0px;
          }
          &.active {
            color: #fff;
            background-color: fade(@icon-blue, 95%);
          }
        }
      }

      form {
        width: 100%;

        div.input-group {
          width: 100%;

          #home-worth-landing-input{
            display: none;
            background: #fff;
            border: none !important;
            padding: 0px 16px;
            font-size: 16px;
            height: 58px;
            color: #333;
            line-height: 58px;
            float: none !important;
            border-radius: 0px 4px 0px 0px !important;
            backface-visibility: hidden;
            width: 100%;
          }

          div.react-autosuggest__container {
            clear: both;
            display: block;
            text-shadow: none;

            input {
              background: #fff;
              border: none !important;
              padding: 0px 16px;
              font-size: 16px;
              display: block;
              height: 58px;
              color: #333;
              line-height: 58px;
              float: none !important;
              border-radius: 0px 4px 0px 0px !important;
              backface-visibility: hidden;

              &::-webkit-input-placeholder {
                color: #999;
              }

              &:-moz-placeholder {
                /* Firefox 18- */
                color: #999;
              }

              &::-moz-placeholder {
                /* Firefox 19+ */
                color: #999;
              }

              &:-ms-input-placeholder {
                color: #999;
              }
            }

            .react-autosuggest__suggestions-container {
              border-color: #c7c7c7;
              border-top: none !important;
              top: 100%;
            }
          }

          &.home-worth-search{

            #home-worth-landing-input{
              display: block;
            }

            .react-autosuggest__container{
              display: none;
            }
          }

          button.btn.btn-primary {
            position: absolute;
            right: 0;
            border: none;
            z-index: 2;
            height: 58px;
            width: 70px;
            border-radius: 0 2px 0px 0;
            float: none !important;
            top: 0;

            svg {
              width: 28px;
              height: 28px;
              margin: 6px 0 0 0;
            }
          }
        }
      }

      p.msg {
        color: #fff;
        margin: 2em 0;
        text-align: center;
        font-weight: 400;
      }

      div.helper-buttons {

        text-shadow: none;
        display: flex;
        flex-wrap: wrap;

        div {
          flex: 1 1 auto;
          cursor: pointer;

          a.btn {
            border: none;
            color: rgba(255,255,255,1);
            background-color: fade(@brand-blue, 80%);
            border-radius: 0;
            line-height: 24px;
            position: relative;
            overflow: visible;
            &:after{
              height: 80%;
              content: " ";
              background-color: rgba(255,255,255,0.25);
              width: 1px;
              display: block;
              position: absolute;
              left: 0px;
              top: 10%;
            }
            svg.autosuggest-icon {
              fill: @brand-primary;
            }

            &:hover {
              background-color: @brand-blue;
              color: #fff;

              svg.autosuggest-icon {
                fill: #fff;
              }
            }
          }
        }

        svg.star{
          width: 16px;
          height: 16px;
          fill: yellow;

          &:first-child{
            margin-right: 8px;
          }

          &:last-child{
            margin-left: 8px;
          }
        }

        div:first-child{
          a.btn:after{
            content: none !important;
          }
          a.btn{
            border-radius: 0 0 0 2px;
          }
        }
        div:last-child{
          a.btn{
            border-radius: 0 0 2px 0;
          }
        }
      }
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;

    color: #fff;

    .logo {
      position: absolute;
      left: 1em;
      bottom: 0.5em;
      a{
        display: inline-block;
      }

      svg {
        width: 12em;
        height: 3em;
      }
    }

    .copyright {
      position: absolute;
      left: 2em;
      bottom: 0.7em;

      p {
        margin: 1.2em 0;
        font-weight: 400;
        text-transform: uppercase;
        font-size: 80%;

        a {
          color: #fff;
          font-weight: 400;
        }
      }
    }
  }

  div.links {
    position: absolute;
    right: 1.5em;
    top: 0;

    p {
      margin: 1em 0;
      font-weight: 400;
      text-transform: uppercase;
      color: #fff;

      a {
        color: #fff;
        font-weight: 400;
      }
    }
  }
  .links-mobile {
    display: none;
  }

  @media screen and (max-width: @desktop) {

    div.landing-wrapper {

      div.search-area {
        width: 75%
      }
    }
  }

  @media screen and (max-width: @tablet) {

    div.landing-wrapper-container {
      position: relative;
      padding: 2em 2em 0 2em;
//      margin-top: -1 * @header-height;
      display: block;
      min-height: 100vh;
      height: auto;
    }

    &.landing-mobile {
      position: relative;
      margin-top: 0;
    }

    div.landing-wrapper {
      div.agent-info {
        max-width: 100%;
        width: 100%;
        height: auto;
        margin-top: 0;
        text-align: center;

        .agent-profile-image {
          height: 150px;
          width: 150px;
        }

        div.agent-right {
          display: block;
          width: 100%;
          max-width: 100%;
          text-align: center;
          padding: 0;
          margin: 0;
        }
      }

      div.search-area {
        width: 100%;
        position: relative;
      }
    }

    .footer {
      position: relative;
      margin-top: 1.5em;
      overflow: hidden;

      .logo {
        position: relative;
        width: 100%;
        text-align: center;
        a{
          display: inline-block;
        }
      }

      .copyright {
        position: relative;
        left: 0;
        width: 100%;
        text-align: center;
      }

      .social-links{
        position: relative;
        right: 0;
        margin-bottom: 20px;
        bottom: 0;
      }


    }

    div.links {
      display: none;
    }
    .links-mobile {
      display: block;
    }
  }

  .landing-broker-container{
    padding: 30px 0px;
    display: flex;
    justify-content: flex-start;
    background-color: #fff;

    .landing-broker{
      display: flex;
      padding: 0px 30px;

      .landing-broker-logo{
        &>img{
          max-height: 63px;
          max-width: 100%;
          margin-top: 4px;
        }
        flex: 1 0 63px;
      }

      .landing-broker-details{
        text-align: left;
        padding-left: 15px;
        font-size: 12px;
        letter-spacing: 1px;
        font-weight: 500;
        color: #666;

        &>.broker{
          font-size: @agent-font-size;
          line-height: 28px;
        }

        &>div:nth-child(2){
          margin: 10px 0px;
        }
      }
    }

    .landing-right {
      flex: 1;

      .landing-mls-disclaimer {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 5px;

        .mls-logo {
          max-height: 63px;
          max-width: 100%;
          margin-top: 4px;
        }

        > p {
          max-width: 50vw;
          text-align: left;
          padding: 0 15px;
        }
      }
    }
  }

  @media screen and (max-width: 750px) {
    div.landing-wrapper{

      div.search-area{

        div.helper-buttons {
          flex-wrap: wrap;

          > div{
            flex: 1 1 50%;
            a.btn{
              &:after{
                display: none;
              }
            }
          }
        }
      }
    }

    .landing-broker {
      padding: 0px 15px !important;
    }
    .footer {
      .logo {
        right: 0;
      }
    }
  }

  .social-links{
    position: absolute;
    right: 1.6em;
    bottom: 1.2em;

    a{
      cursor: pointer;
      display: inline-block;
      margin-right: 5px;
    }

    svg{
      width: 30px;
      height: 30px;
      fill: white;
      opacity: 0.8;
    }
  }

  @media screen and (max-width: 550px) {
    div.landing-wrapper{

      div.search-area{

        div.helper-buttons{
          flex-wrap: wrap;

          > div{
            flex: 1 1 100%;

          }
        }
      }
    }

  }
}


#share-agent-popover{

  .share-agent-popup{
    margin-top: 10px;
    margin-bottom: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-gap: 12px;

    > a{
      display: inline-flex;
      justify-content: center;
      align-items: center;
      align-content: center;
      text-decoration: none !important;
      flex-direction: column;

      div{
        color: @brand-blue;
        text-align: center;
      }
    }

    svg{
      width: 12vmin;
      height: 12vmin;
      max-width: 60px;
      max-height: 60px;
      fill: @brand-blue;
    }
  }
}
