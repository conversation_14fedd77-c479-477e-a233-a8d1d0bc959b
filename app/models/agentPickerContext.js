const _ = require('lodash');

class AgentPickerContext {
  constructor() {
    this.app = null;
  }

  setApplication(app) {
    this.app = app;
  }

  set(context) {
    if (context) {
      if (context.lat && context.lng) {
        this.stageLocation(context.lat, context.lng);
      }
      if (context.interacted !== null) {
        this.stageInteracted(context.interacted);
      }
      if (context.forceFree !== null) {
        this.stageForceFree(context.forceFree);
      }
      if (context.isLoggedIn !== null) {
        this.stageIsLoggedIn(context.isLoggedIn);
      }
      if (context.mlsIds) {
        this.stageMlsIds(context.mlsIds);
      }
      if (context.sources) {
        this.stageSources(context.sources);
      }

      this.app.cursor.commit();
    }
  }

  isSignificantChange(context, otherContext) {
    return (context.lat !== otherContext.lat
            || context.lng !== otherContext.lng
            || context.interacted !== otherContext.interacted
            || context.forceFree !== otherContext.forceFree
            || context.isLoggedIn !== otherContext.isLoggedIn
            || !(_.isEqual(_.sortBy(context.mlsIds), _.sortBy(otherContext.mlsIds)))
            || !(_.isEqual(_.sortBy(context.sources), _.sortBy(otherContext.sources)))
    );
  }

  addSource(source) {
    let currentSources = this.app.cursor.get(['shared', 'agentPickerContext', 'sources']);
    if (!currentSources) {
      currentSources = [source];
    } else if (!_.includes(currentSources, source)) {
      currentSources.push(source);
    }
    this.app.cursor.set(['shared', 'agentPickerContext', 'sources'], currentSources);
    this.app.cursor.commit();
  }

  removeSource(source) {
    const currentSources = this.app.cursor.get(['shared', 'agentPickerContext', 'sources']);
    if (!currentSources) {
      return;
    }
    if (_.includes(currentSources, source)) {
      const sourceIndex = currentSources.indexOf(source);
      currentSources.splice(sourceIndex, 1);
    }
    this.app.cursor.set(['shared', 'agentPickerContext', 'sources'], currentSources);
    this.app.cursor.commit();
  }

  stageLocation(lat, lng) {
    let result = false;
    const currentLat = this.app.cursor.get(['shared', 'agentPickerContext', 'lat']);
    const currentLng = this.app.cursor.get(['shared', 'agentPickerContext', 'lng']);
    if (currentLat !== lat) {
      this.app.cursor.set(['shared', 'agentPickerContext', 'lat'], lat);
      result = true;
    }

    if (currentLng !== lng) {
      this.app.cursor.set(['shared', 'agentPickerContext', 'lng'], lng);
      result = true;
    }

    return result;
  }

  setLocation(lat, lng) {
    if (this.stageLocation(lat, lng)) {
      this.app.cursor.commit();
    }
  }

  stageInteracted(val) {
    const currentVal = this.app.cursor.get(['shared', 'agentPickerContext', 'interacted']);
    if (currentVal !== val) {
      window.sessionStorageAlias.setItem(this.app.actions.common.FLAG_USER_HAS_INTERACTED_WITH_APP_KEY, val);
      this.app.cursor.set(['shared', 'agentPickerContext', 'interacted'], val);
      return true;
    }

    return false;
  }

  setInteracted(val) {
    if (this.stageInteracted(val)) {
      this.app.cursor.commit();
    }
  }

  stageForceFree(val) {
    const currentVal = this.app.cursor.get(['shared', 'agentPickerContext', 'forceFree']);
    if (currentVal !== val) {
      window.sessionStorageAlias.setItem(this.app.actions.common.FLAG_NO_CHARGE_FOR_LEADS_KEY, val);
      this.app.cursor.set(['shared', 'agentPickerContext', 'forceFree'], val);
      return true;
    }

    return false;
  }

  setForceFree(val) {
    if (this.stageForceFree(val)) {
      this.app.cursor.commit();
    }
  }

  stageMlsIds(mlsIds) {
    const currentMlsIds = this.app.cursor.get(['shared', 'agentPickerContext', 'mlsIds']);
    if (!(_.isEqual(_.sortBy(currentMlsIds), _.sortBy(mlsIds)))) {
      this.app.cursor.set(['shared', 'agentPickerContext', 'mlsIds'], mlsIds);
      return true;
    }

    return false;
  }

  setMlsIds(mlsIds) {
    if (this.stageMlsIds(mlsIds)) {
      this.app.cursor.commit();
    }
  }

  setMlsIdsFromListings(listings) {
    if (!listings) {
      this.setMlsIds([]);
    } else {
      const mlsIds = this.app.actions.common.getMlsIds(listings);
      this.setMlsIds(mlsIds);
    }
  }

  stageSources(sources) {
    const currentSources = this.app.cursor.get(['shared', 'agentPickerContext', 'sources']);
    if (!(_.isEqual(_.sortBy(currentSources), _.sortBy(sources)))) {
      this.app.cursor.set(['shared', 'agentPickerContext', 'sources'], sources);
      return true;
    }

    return false;
  }

  setsources(sources) {
    if (this.stagesources(sources)) {
      this.app.cursor.commit();
    }
  }

  stageIsLoggedIn(val) {
    const currentVal = this.app.cursor.get('shared', 'agentPickerContext', 'isLoggedIn');
    if (currentVal !== val) {
      this.app.cursor.set(['shared', 'agentPickerContext', 'isLoggedIn'], val);
      return true;
    }

    return false;
  }

  setIsLoggedIn(val) {
    if (this.stageIsLoggedIn(val)) {
      this.app.cursor.commit();
    }
  }
}

// Singleton Instance
export default new AgentPickerContext();
