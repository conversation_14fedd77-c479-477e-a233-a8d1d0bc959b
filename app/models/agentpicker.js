module.exports = function (app) {
  function AgentPicker() {
  }

  AgentPicker.routeCallback = null;

  AgentPicker.setRouteCallback = function (cb) {
    AgentPicker.routeCallback = cb;
  };

  AgentPicker.oneTimeAgentCallbacks = [];

  AgentPicker.oneTimeAgentCallbacksHelper = function (agentId) {
    for (let i = 0; i < AgentPicker.oneTimeAgentCallbacks.length; i++) {
      const cb = AgentPicker.oneTimeAgentCallbacks[i];
      if (typeof cb === 'function') {
        cb(agentId || app.actions.common.getAgentId());
      }
    }
    AgentPicker.oneTimeAgentCallbacks = [];
  };

  AgentPicker.switchAgent = function (agent, trackingId) {
    const currentAgentId = app.cursor.get(['shared', 'agent', 'data', 'Id']);
    const route = app.router.currentRoute;
    app.cursor.set(['shared', 'agentPickerMetadata', 'currentAgentPickTrackingId'], trackingId);
    if (!app.cursor.get(['shared', 'agentPickerMetadata', 'lastAgentSwitchTrackingId'])) {
      app.cursor.set(['shared', 'agentPickerMetadata', 'lastAgentSwitchTrackingId'], trackingId);
    }

    // if we receive an agent and it is different that the current, go there
    if (AgentPicker.routeCallback) {
      AgentPicker.routeCallback(agent);
      AgentPicker.routeCallback = null;
    } else if (agent && (currentAgentId != agent.Id)) {
      app.actions.analytics.sendEvent('agent switch', 'agent switch', agent.CustomURL || agent.Id, app.actions.common.getBuyerId());
      app.router.go(route.url.replace(route.params.agentId, agent.CustomURL || agent.Id) + window.location.search, { replace: true, skip: true });
      app.actions.common.saveAgentId(agent.Id, agent);
      app.cursor.set(['shared', 'agentPickerMetadata', 'lastAgentSwitchTrackingId'], trackingId);
    }

    if (agent && agent.Id) {
      AgentPicker.oneTimeAgentCallbacksHelper(agent.Id);
    }
  };

  // lat lng are optional
  AgentPicker.deliverFreeLeadTrafficForCurrentAgent = function (lat, lng) {
    const currentAgent = app.cursor.get(['shared', 'agent', 'data']);
    // make sure the current agent has IDX, or else we don't deliver it
    if ((!currentAgent) || (!currentAgent.HomeSearchRegisteredDateTime) || (currentAgent.HomeSearchRegisteredDateTime == 'demo') || app.actions.common.haveDeliveredFreeLead()) {
      return;
    }

    app.actions.common.setDeliveredFreeLead();
    app.api.deliverFreeLeadTraffic(currentAgent.Id, lat, lng);
  };

  AgentPicker.deliverChargedLeadForCurrentAgent = function (options = {}, callback = () => {}) {
    const { lat, lng, onboardingAgentId } = options;

    const currentAgent = app.cursor.get(['shared', 'agent', 'data']);
    // make sure the current agent has IDX, or else we don't deliver it
    if ((!currentAgent) || (!currentAgent.HomeSearchRegisteredDateTime) || (currentAgent.HomeSearchRegisteredDateTime == 'demo')) {
      return;
    }

    app.api.deliverChargedLead({
      agentId: currentAgent.Id, onboardingAgentId, lat, lng,
    }, callback);
  };

  AgentPicker.deliverChargedLead = function (options = {}, callback = () => {}) {
    const { lat, lng, agentId } = options;
    app.api.deliverChargedLead({ agentId, lat, lng }, callback);
  };

  return AgentPicker;
};
