const React = require('react');
const mixins = require('../../../lib/mixins/index');
const SVGIcon = require('../../components/svg_icon');

const Card = React.createClass({

  displayName: 'panels.card.new-label',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    savedSearchDate: ['shared', 'savedSearch', 'searchDate'],
  },

  render() {
    if (this.props.daysOnMarket === false || !this.state.savedSearchDate) {
      return <noscript />;
    }

    const daysSinceSearch = Math.floor((new Date() - this.state.savedSearchDate) / (1000 * 60 * 60 * 24));

    if (this.props.daysOnMarket < daysSinceSearch) {
      return <SVGIcon name="icon-new" className="new-label" />;
    }

    return <noscript />;
  },
});

module.exports = Card;
