const React = require('react');
const classNames = require('classnames');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins/index');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.card.back',
  poolPosition: 0,
  fireplacePosition: 0,

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },

  getInitialState() {
    return {
      showFront: false,
    };
  },

  getAddress() {
    let address = '';
    if (this.props.details && this.props.details.Address) {
      address = `${address + this.props.details.Address.FullStreetAddress}, ${this.props.details.Address.CityName}, ${this.props.details.Address.State} ${this.props.details.Address.ZipCode}`;
    }
    return address;
  },

  onPhotos(e) {
    e.stopPropagation();
    !this.props.photoNotAvailable && this.props.onPhotos ? this.props.onPhotos(this.props.listing) : '';
  },

  onMoreInfo(e) {
    e.stopPropagation();
    this.getInfoBlocks();
    this.props.onNav ? this.props.onNav(this.props.listing.Id) : '';
    this.actions.analytics.sendEvent('property cards', 'open detail', this.props.details.ZipCode);
  },

  getFeatureCount() {
    let count = 0;
    count = this.props.details && this.props.details.Exterior && this.props.details.Exterior.HasPool && this.poolPosition > 3 ? 1 : 0 + this.props.details && this.props.details.Interior && this.props.details.Interior.HasFireplace && this.fireplacePosition > 3 ? 1 : 0;
    if (this.props.details && this.props.details.GeneralFeatures) {
      count += this.props.details.GeneralFeatures.length;
    }
    return count;
  },
  getInfoBlocks() {
    const infoBlocks = [];
    if (!this.props.details) {
      return;
    }
    if (this.props.details.DaysOnMarket && this.state.showDaysOnMarket) {
      infoBlocks.push({
        name: 'DaysOnMarket', value: this.props.details.DaysOnMarket, icon: 'icon-calendar',
      });
    }
    if (this.props.details.Exterior && this.props.details.Exterior.CarportSpaces) {
      infoBlocks.push({
        name: 'CarportSpaces', value: this.props.details.Exterior.CarportSpaces, icon: 'icon-car',
      });
    }
    if (this.props.details.Exterior && this.props.details.Exterior.LotSizeAcres) {
      infoBlocks.push({
        name: 'LotSize', value: this.props.details.Exterior.LotSizeAcres, icon: 'icon-lot-size',
      });
    } else if (this.props.details.Exterior && this.props.details.Exterior.LotSizeSquareFeet) {
      infoBlocks.push({
        name: 'LotSize', value: this.props.details.Exterior.LotSizeSquareFeet, icon: 'icon-lot-size',
      });
    } else if (this.props.details.Exterior && this.props.details.Exterior.LotSizeRange) {
      infoBlocks.push({
        name: 'LotSize', value: this.props.details.Exterior.LotSizeRange, icon: 'icon-lot-size',
      });
    }
    if (this.props.details.Exterior && this.props.details.Exterior.HasPool) {
      infoBlocks.push({
        name: 'Pool', value: 'Yes', icon: 'icon-pool',
      });
      this.poolPosition = infoBlocks.length;
    }
    if (this.props.details.Interior && this.props.details.Interior.HasFireplace) {
      infoBlocks.push({
        name: 'Fireplace', value: 'Yes', icon: 'icon-fireplace-in',
      });
      this.fireplacePosition = infoBlocks.length;
    }
    if (this.props.details.YearBuilt) {
      infoBlocks.push({
        name: 'YearBuilt', value: this.props.details.YearBuilt, icon: 'icon-hammer',
      });
    }
    if (this.props.details.Interior && this.props.details.Interior.CoolingFeatures) {
      infoBlocks.push({
        name: 'CoolingFeatures', value: this.props.details.Interior.CoolingFeatures.split(',')[0], icon: 'icon-ac',
      });
    } else if (this.props.details.Interior && this.props.details.Interior.HasCooling) {
      infoBlocks.push({
        name: 'CoolingFeatures', value: 'Yes', icon: 'icon-ac',
      });
    }
    if (this.props.details.Interior && this.props.details.Interior.HeatingFeatures) {
      infoBlocks.push({
        name: 'HeatingFeatures', value: this.props.details.Interior.HeatingFeatures.split(',')[0], icon: 'icon-heating',
      });
    }
    if (this.props.details.Financial && this.props.details.Financial.AssociationDues && this.props.details.Financial.AssociationDues !== '0') {
      infoBlocks.push({
        name: 'HOA',
        value: `${this.utils.addThousandSep(Math.round(this.props.details.Financial.AssociationDues))} ${this.utils.associationPeriodToText(this.props.details.Financial.AssociationPeriod)}`,
        icon: 'icon-hoa-fees',
      });
    }

    console.log(`${this.poolPosition}is pool${this.fireplacePosition} is fireplce`);
    return infoBlocks;
  },

  buildInfoBlocks() {
    const blockCount = 3;
    const infoBlocks = this.getInfoBlocks();
    const maxCount = this.getFeatureCount() > 0 ? blockCount : blockCount * 2;
    console.log(`${this.props.details}is the details`);
    if (!infoBlocks) {
      return;
    }
    return (
      map(infoBlocks.slice(0, maxCount), (item) => (
        <div className="box" key={item.name}>
          <p className="box-header">{item.name}</p>

          <p className="data" title={item.value}>
            {item.value}
          </p>
          <SVGIcon name={item.icon} className="box-icon calendar" />
        </div>
      ), this)
    );
  },

  getFeature() {
    let text = '';
    if (this.props.details && this.props.details.Exterior && this.props.details.Exterior.HasPool && this.poolPosition > 3) {
      text = `${text}Pool, `;
    }
    if (this.props.details && this.props.details.Interior && this.props.details.Interior.HasFireplace && this.fireplacePosition > 3) {
      text = `${text}Fireplace, `;
    }
    if (this.props.details && this.props.details.GeneralFeatures) {
      text += this.props.details.GeneralFeatures;
    }
    return (
      <div className="feature">
        {text ? text.trim(' ').trim(',') : ''}
      </div>
    );
    /*
     var arr = text.split(" ");
     return (
     text.length > 0 ? (
     <div className="feature">{
     arr.map(function (item) {
     return (<p className="feature-item" key={item} title=
     {item}>{item} </p>)
     })
     }
     </div>
     ) : '')
     */
  },

  render() {
    const a = (
      <div className="card">
        <div className="card-back">
          <p className="header">
            { (this.props.details && this.props.details.Status && !this.props.details.Status.match(/^active$/i))
              ? [<strong key="0">Status: </strong>, <em key="1">{this.props.details.Status}</em>]
              : 'Property Highlights'}
          </p>
          <SVGIcon className="flip-icon" name="icon-flip" />
          {this.buildInfoBlocks()}
          { this.getFeatureCount() > 0
            ? (
              <div className="box mtn">
                <span className="box-header">Feature</span>
                {this.getFeature()}
              </div>
            )
            : null}
          <div className={classNames('box-side', { fullwidth: this.getFeatureCount() == 0 })}>
            <div
              role="button"
              tabIndex="-1"
              aria-label="View photo gallery"
              onClick={this.onPhotos}
              title="View photo gallery"
              className={classNames('btn btn-default', { disabled: this.props.photoNotAvailable })}
            >
              <span>PHOTOS</span>
            </div>
            <div
              role="button"
              tabIndex="-1"
              aria-label="View full details"
              onClick={this.onMoreInfo}
              title="View full details"
              className="btn btn-default"
            >
              <span>VIEW FULL</span>
            </div>
          </div>
        </div>
      </div>
    );

    console.log('Card Back Render Func Done');
    return a;
  },
});
