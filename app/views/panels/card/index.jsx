/* eslint-disable jsx-a11y/label-has-associated-control */
const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const classNames = require('classnames');
const _ = require('lodash');
const mixins = require('../../../lib/mixins/index');
const SubmitBtn = require('../../components/submit_btn');
const NewLabel = require('./new-label');
const SVGIcon = require('../../components/svg_icon');

const Card = React.createClass({

  displayName: 'panels.card',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.router, mixins.utils, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    listing: React.PropTypes.object,
    onNav: React.PropTypes.func,
  },

  cursors: {
    cardTags: ['shared', 'tagging', { location: 'props', path: ['listing', 'Id'] }],
    viewed: ['shared', 'viewedProperties', { location: 'props', path: ['listing', 'Id'] }],
    mlsData: ['shared', 'mlsData', { location: 'props', path: ['mlsId'] }],
    agentMlsId: ['shared', 'agent', 'data', 'MlsId'],
    employeeUi: ['layout', 'employeeUi'],
  },

  getInitialState() {
    return {
      tagPanelHidden: true,
      showFront: true,
      showNotAvailable: false,
      slideshowIndex: 0,
    };
  },

  componentWillMount() {
    if (this.props.tagOnly) {
      this.state.allTags = this.actions.tagging.getTags();
    }
  },

  componentDidMount() {
    this.checkStreetViewImage(this.props.listing);

    if (!this.state.mlsData && this.props.mlsId) {
      this.actions.listing.getMlsData(this.props.mlsId);
    }
  },

  componentWillUpdate(nextProps) {
    if (this.props.listing !== nextProps.listing) {
      if (this.state.showNotAvailable && this.isMounted()) {
        this.setState({ showNotAvailable: false });
      }
      this.checkStreetViewImage(nextProps.listing);
    }

    if ((!this.props.images) && nextProps.images && nextProps.images.length > 1) {
      const preloadImage = new Image();
      preloadImage.src = nextProps.images[1].lg || null;
    }
  },

  componentWillUnMount() {
    window.clearTimeout(this.hideTagTimer);
    this.hideTagTimer = null;
  },

  checkStreetViewImage(listingData) {
    if (listingData.Image
      && (listingData.Image || '').indexOf('maps.googleapis.com/maps/api/streetview') !== -1) {
      // this.actions.map.getStreetViewAvailability(listingData).then(function (res) {
      if (/* res === false && */ this.isMounted()) {
        this.setState({ showNotAvailable: true });
        this.props.onPhotoNotAvailable ? this.props.onPhotoNotAvailable() : '';
      }
      // }.bind(this))
    }
  },

  onPhotos(e) {
    e.preventDefault();
    this.props.onPhotos
      ? this.props.onPhotos(this.props.listing)
      : this.actions.common.setPhotoSliderIndex(0)
      && this.actions.panels.toggle('photos', this.props.listing);
    this.actions.analytics.sendEvent('property cards', 'photos', this.props.listing.ZipCode);
  },

  onNav(e) {
    console.log('onNav Clicked');
    e.preventDefault();
    e.stopPropagation();
    this.props.onNav(this.props.listing.Id);

    this.actions.analytics.sendEvent('property cards', 'open detail', this.props.listing.ZipCode);
  },

  onMouseEnterCard() {
    this.actions.listing.onMouseOver(this.props.listing.Id);
    // if gallery enabled, cycle photos
    clearInterval(this.slideshowInterval);
    if (this.props.enableSlideshow) {
      this.startSlideshow();
    }
  },

  onMouseLeaveCard() {
    this.actions.listing.onMouseOut(this.props.listing.Id);
    // stop gallery
    this.stopSlideshow();
  },

  onTouchStart() {
    if (this.props.enableSlideshow) {
      this.startSlideshow();
    }
    if (this.props.onTouchStart) {
      this.props.onTouchStart();
    }
  },

  onTouchEnd() {
    this.stopSlideshow();
    if (this.props.onTouchEnd) {
      this.props.onTouchEnd();
    }
  },

  slideshowInterval: null,

  startSlideshow() {
    this.setState({ slideshowActive: true });
    this.slideshowInterval = setInterval(this.nextSlideshowPhoto, 1500);
  },

  stopSlideshow() {
    this.setState({ slideshowActive: false });
    clearInterval(this.slideshowInterval);
  },

  nextSlideshowPhoto() {
    // only advance if we have images
    if (this.props.images && this.props.images.length) {
      this.setState({
        slideshowIndex: this.state.slideshowIndex + 1,
      });
    }
  },

  toggleTagPanel(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.analytics.sendEvent('property cards', 'tag icon', this.props.listing.ZipCode);
    if (this.hasNothing() || this.hasDislike()) {
      // Add favorite by default
      this.actions.tagging.setTagsForListing(this.props.listing, { favorite: true });
    } else {
      this.actions.tagging.setTagsForListing(this.props.listing, {});
    }
  },

  onTag(tagName) {
    const currCardTags = this.state.cardTags || {};
    // let added = false;
    if (tagName in currCardTags) {
      delete currCardTags[tagName];
    } else {
      currCardTags[tagName] = true;
      // added = true;
    }
    if (tagName === 'favorite' && 'dislike' in currCardTags) {
      delete currCardTags.dislike;
    }
    if (tagName === 'dislike' && 'favorite' in currCardTags) {
      delete currCardTags.favorite;
    }

    this.actions.tagging.setTagsForListing(this.props.listing, currCardTags);

    this.actions.analytics.sendEvent('property cards', 'click tag', this.props.listing.ZipCode);
  },

  onAddTag(e) {
    e.preventDefault();
    const newTagInput = this.refs.newTagInput && this.refs.newTagInput;
    if (!newTagInput) {
      return;
    }
    let tagName = newTagInput.value;
    // NPLAY-1880 Whitelist allowed characters
    tagName = tagName.replace(/[^a-zA-Z0-9-_]/g, '').trim();
    if (tagName) {
      this.actions.tagging.addTag(tagName);
      const currCardTags = this.state.cardTags;
      if (!(tagName in currCardTags)) {
        currCardTags[tagName] = true;
        if (tagName === 'favorite' && 'dislike' in currCardTags) {
          delete currCardTags.dislike;
        }
        if (tagName === 'dislike' && 'favorite' in currCardTags) {
          delete currCardTags.favorite;
        }
        this.state.allTags = this.actions.tagging.getTags();
        this.actions.tagging.setTagsForListing(this.props.listing, currCardTags);
        this.actions.analytics.sendEvent('property cards', 'add new tag', this.props.listing.ZipCode);
      }
    }
    newTagInput.value = '';
  },

  hasNothing() {
    return Object.keys(this.state.cardTags || {}).length === 0 || false;
  },

  hasSomething() {
    return Object.keys(this.state.cardTags || {}).length > 0 || false;
  },

  hasFavorite() {
    return ('favorite' in (this.state.cardTags || {})) || false;
  },

  hasDislike() {
    return ('dislike' in (this.state.cardTags || {})) || false;
  },

  tagButton() {
    if (!this.state.tagPanelHidden) {
      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="Tag"
          className={classNames('tag-button')}
          title="Tag this home"
          onClick={this.toggleTagPanel}
        >
          <SVGIcon name="icon-close-button" title="" className="tag-close-button" />
        </div>
      );
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Tag"
        className={classNames('tag-button')}
        onClick={this.toggleTagPanel}
      >
        {
          this.hasSomething() && !this.hasDislike()
            ? (
              <SVGIcon
                name="icon-bell"
                className={classNames('tag-favorite')}
              />
            )
            : (
              <SVGIcon
                name="icon-bell"
                className={classNames('tag-off')}
              />
            )
        }
      </div>
    );
  },

  hideTaggedTimer() {
    if (!this.state.tagPanelHidden) {
      this.hideTagTimer = window.setTimeout(this.toggleTagPanel.bind(this, true), 400);
    }
  },

  mouseEnterInvalidateHideTagTimer() {
    if (typeof (this.hideTagTimer) !== 'undefined' && this.hideTagTimer) {
      window.clearTimeout(this.hideTagTimer);
      this.hideTagTimer = null;
    }
  },

  tagListingClicked(e) {
    e.preventDefault();
    this.actions.tagging.onNav();
  },

  getImageUrl() {
    let imageUrl;
    if (this.state.showNotAvailable) {
      imageUrl = this.utils.streetViewNotAvailableImage();
    } else if (this.props.useScreenWidthForImage) {
      imageUrl = this.utils.generateMainImageUrl(this.props.listing, window.innerWidth);
    } else if (this.state.slideshowActive && this.props.images && (this.props.images.length > 1) && (this.state.slideshowIndex > 0)) {
      const imagesCount = this.props.images.length;
      const imageIndex = this.state.slideshowIndex % imagesCount;
      imageUrl = this.props.images[imageIndex].lg || null;
      // preload the next image if it is available
      if (imageIndex < imagesCount - 1) {
        const preloadImage = new Image();
        preloadImage.src = this.props.images[imageIndex + 1].lg || null;
      }
    } else {
      imageUrl = this.props.listing.Image;
    }

    return `url('${imageUrl}')`;
  },

  render() {
    const mlsData = this.state.mlsData;

    const cardStyles = {
      backgroundImage: this.getImageUrl(),
    };

    let courtesy;
    const courtesyLabel = (mlsData && mlsData.LabelForIdxListingBroker)
      ? mlsData.LabelForIdxListingBroker
      : 'Courtesy of';
    const listingAgentName = this.props.listing.ListingAgent.Name;
    const brokerName = this.props.listing.ListingAgent.OfficeName;

    if (mlsData && mlsData.ListingAgentOnOff && listingAgentName && brokerName) {
      courtesy = `${courtesyLabel} ${listingAgentName}, ${brokerName}`;
    } else if (brokerName) {
      courtesy = `${courtesyLabel} ${brokerName}`;
    } else {
      courtesy = 'Information not available';
    }

    const squareFootage = this.utils.formatSquareFeet(this.props.listing, mlsData && mlsData.ShowOnlyRangeLivingSquare);

    const price = (this.props.listing.RangePriceFlag == 'Y' && this.props.listing.RangeHighPrice)
      ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.RangeHighPrice)}`
      : this.props.listing.ListPrice
        ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.ListPrice)}`
        : 'Price Not Provided';
    const showRateplugMonthlyPrice = this.props.listing.MortgagePaymentInfo && this.props.listing.MortgagePaymentInfo.TotalPayment;

    const landTenure = mlsData && mlsData.DisplayLandTenure && this.props.listing.LandTenure;

    const currentAgentId = _.get(this.actions.common.getCurrentAgent(), 'MlsAgentId');
    const listingAgentId = _.get(this.props.listing, 'ListingAgent.Id');
    const highlightFeatured = this.state.employeeUi && (listingAgentId && (currentAgentId == listingAgentId));

    return (
      <div
        className={classNames('card',
          `listing-mlsidcount-${(this.props.listing.MlsIds || []).length}`,
          `${(this.props.listing.Tags || []).map(({ Name }) => `--tag:${Name}`).join(' ')}`,
          `${(this.props.listing.SpecialFinancePrograms || []).map((programName) => `--tag:SpecialFinancePrograms__${programName}`).join(' ')}`,
          `${(this.props.listing.MlsIds || []).map((m) => `listing-mlsid-${m}`).join(' ')}`, {
            active: this.props.isActive,
            viewed: this.state.viewed,
            highlightFeatured,
          })}
        onMouseLeave={this.onMouseLeaveCard}
        onTouchStart={this.onTouchStart}
        onTouchEnd={this.onTouchEnd}
      >
        { this.state.showFront ? (
          <div className="card-front">
            <div className="card-image" style={cardStyles} />

            <div className={'bottom'.concat(landTenure ? ' land-tenure' : '')}>

              {mlsData && mlsData.ShowLogoOnPropertyCard && mlsData.LogoUrl && _.includes(this.props.listing.MlsIds, mlsData.Id)
                ? (
                  <div className="card-broker-logo">
                    <img alt="Broker" src={mlsData.LogoUrlForCards || mlsData.LogoUrl} />
                  </div>
                )
                : null}
              <div className={classNames('courtesy', {
                'courtesy-large': (mlsData && mlsData.ShowLargeBrokerOnPropertyCard),
              })}
              >
                <p title={courtesy}>{courtesy}</p>
              </div>

              {
                !showRateplugMonthlyPrice
                  ? (
                    <p className="bottom-left">
                      {price}
                &nbsp;
                      {landTenure ? (
                        <small>
                          |
                          {landTenure}
                        </small>
                      ) : null}
                    </p>
                  ) : (
                    <p className="bottom-left">&nbsp;</p>
                  )
              }
              <div className="bottom-right">
                {
                  this.props.listing.TotalBedrooms
                    ? (
                      <p>
                        {this.props.listing.TotalBedrooms}
                        &nbsp;
                        bd
                      </p>
                    )
                    : null
                }
                {
                  this.props.listing.TotalBaths
                    ? (
                      <p>
                        {this.props.listing.TotalBaths}
                        &nbsp;
                        ba
                      </p>
                    )
                    : null
                }
                {
                  squareFootage
                    ? (
                      <p>
                        {squareFootage}
                        &nbsp;
                        sqft
                      </p>
                    )
                    : null
                }

              </div>
              <div className="address">
                {
                  this.props.listing.FullStreetAddress
                    ? <p>{this.props.listing.FullStreetAddress.concat(', ', this.props.listing.CityName, ', ', this.props.listing.State)}</p>
                    : this.props.listing.Address
                      ? (
                        <p>
                          {''.concat(this.props.listing.Address.FullStreetAddress, ', ',
                            this.props.listing.Address.CityName, ', ',
                            this.props.listing.Address.State)}
                        </p>
                      ) : null
                }
              </div>

              {
                showRateplugMonthlyPrice
                  ? (
                    <div className="rateplug-monthly-wrapper">
                      <div className="rateplug-monthly-line">
                        <p>
                          {price}
                          {landTenure ? (
                            <small>
                              |
                              {landTenure}
                            </small>
                          ) : null}
                        </p>
                        <p>{`$${this.utils.addThousandSep(this.props.listing.MortgagePaymentInfo.TotalPayment.toFixed(0))}/mo `}</p>
                      </div>
                    </div>
                  ) : null
              }
            </div>

            <NewLabel daysOnMarket={this.props.listing && this.props.listing.DaysOnMarket} />

            <div
              role="button"
              tabIndex="-1"
              aria-label="View home"
              className="card-front-clickable"
              onClick={this.onNav}
              title={(`See more about this home, #${this.props.listing.PropertyListingId}`)}
            />

            {
              !this.props.listing.DefaultImageNotAvailable && this.props.listing.ImageCount
                ? (
                  <div
                    role="button"
                    tabIndex="-1"
                    aria-label="View photo gallery"
                    className="photo-button"
                    title="View photo gallery"
                    onClick={this.onPhotos}
                  >
                    <SVGIcon name="icon-gallery" className="photo-icon" />
                    <p className="photo-count">{`${this.props.listing.ImageCount} photo${this.props.listing.ImageCount > 1 ? 's' : ''}`}</p>
                  </div>
                ) : null
            }
            { this.tagButton()}
          </div>
        ) : null}

        <ReactCSSTransitionGroup transitionName="fade" transitionEnter={false} transitionLeaveTimeout={200}>
          {
            this.props.tagOnly || !this.state.tagPanelHidden
              ? (
                <div
                  className="tag-panel"
                  key={`tag-panel-${this.props.listing.Id}`}
                  onMouseLeave={this.hideTaggedTimer}
                  onMouseEnter={this.mouseEnterInvalidateHideTagTimer}
                >
                  <div className="tags">
                    <label
                      className={classNames('tag', { active: (this.state.cardTags || {}).favorite })}
                      onClick={this.onTag.bind(this, 'favorite')}
                    >
                      favorite
                    </label>
                    <label
                      className={classNames('tag', { active: (this.state.cardTags || {}).dislike })}
                      onClick={this.onTag.bind(this, 'dislike')}
                    >
                      dislike
                    </label>

                    {
                      this.state.allTags ? (

                        Object.keys(this.state.allTags).map(function (tagName) {
                          if (['favorite', 'dislike'].indexOf(tagName) !== -1) {
                            return null;
                          }
                          return (
                            <label
                              key={tagName}
                              className={classNames('tag', { active: (this.state.cardTags || {})[tagName] })}
                              onClick={this.onTag.bind(this, tagName)}
                            >
                              {tagName}
                            </label>
                          );
                        }, this)

                      ) : null
                    }

                  </div>
                  <form className="add-tag">
                    <input
                      type="text"
                      placeholder="add new tag?"
                      className="form-control"
                      name="new-tag"
                      id="new-tag"
                      ref="newTagInput"
                    />
                    <SubmitBtn className="btn-tagsubmit" text="ADD" onClick={this.onAddTag} />
                  </form>

                  { this.tagButton()}
                  <div className="links-wrapper">
                    <a href="/tagged" onClick={this.tagListingClicked}>My Homes</a>
                  </div>
                </div>
              )
              : null
          }
        </ReactCSSTransitionGroup>

      </div>
    );
  },

});

module.exports = Card;
