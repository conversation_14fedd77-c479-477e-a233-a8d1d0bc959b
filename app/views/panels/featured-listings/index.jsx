const React = require('react');
const SVGIcon = require('../../components/svg_icon');
const Listings = require('../listings');
const mixins = require('../../../lib/mixins');
const LastRefreshed = require('../last-refreshed');
const MLSDisclosure = require('../mls-disclosure');
const NPlayFooter = require('../n-play-footer');

module.exports = React.createClass({

  displayName: 'panel.featured-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    activeId: ['screens', 'featured', 'activeId'],
    agentData: ['shared', 'agent', 'data'],
  },

  facets: {
    data: ['featuredListings'],
  },

  componentDidMount() {
  },

  onNav(id) {
    this.props.onNav ? this.props.onNav(id) : '';
  },

  render() {
    return (
      <div className={this.props.className}>

        {
        (!this.state.data || this.state.data.length === 0)
        && this.state.agentData.HomeSearchRegisteredDateTime === 'demo'
          ? [
            <img
              alt="Featured"
              src={window.CONFIG.CDN_URL.concat('search2/future-featured-4.png')}
              width="100%"
              key="future-featured"
            />,
            <div key="footer" className="right-rail-footer mt20">
              <NPlayFooter />
            </div>,
          ]
          : [
            <div key="0" className="svg-featured-listings-container"><SVGIcon name="icon-featured-listings" className="" /></div>,
            <Listings key="listings" listings={this.state.data} activeId={this.state.activeId} onNav={this.onNav} />,
            <MLSDisclosure key="mlsDisclosure" mlsId={this.state.agentData && this.state.agentData.MlsId} />,
            <div key="footer" className="right-rail-footer">
              <LastRefreshed className="mb20 mt10" />
              <NPlayFooter />
            </div>,
          ]
      }
      </div>
    );
  },

});
