const React = require('react');
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.footer',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    return (
      <div className="n-play-footer">
        <div>
          <p className="n-play-footer-links details">
            <a href="https://homeasap.com/agent/login" target="_blank">Agent Login</a>
            <span>&nbsp;|&nbsp;</span>
            <span className="text-transform-none text-block">
              &copy;
              {window.CURRENT_YEAR || ''}
              &nbsp;
              HomeASAP LLC
            </span>
            <span>&nbsp;·&nbsp;</span>
            <a role="button" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>Terms</a>
            <span>&nbsp;·&nbsp;</span>
            <a role="button" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}>
              Privacy/DMCA Policy
            </a>
            <span>&nbsp;·&nbsp;</span>
            <a href="/search/ADA" target="_blank">Accessibility</a>
          </p>
        </div>
      </div>
    );
  },
});
