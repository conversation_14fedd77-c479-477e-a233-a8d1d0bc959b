const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.agent-profile',
  empty: '---------',

  mixins: [
    mixins.debug, mixins.actions, mixins.pureRender, mixins.utils,
  ],

  getExpertise() {
    if (!this.props.agentData || !this.props.agentData.ExpertiseDescriptions || this.props.agentData.ExpertiseDescriptions.length == 0) {
      return '';
    }
    return map(this.props.agentData.ExpertiseDescriptions, (item) => (
      <li
        key={item}
        className={
          classNames('item col-1-1 col-md-1-3 col-lg-1-4')
        }
      >
        <b>-</b>
        &nbsp;
        {item}
        &nbsp;

      </li>
    ), this);
  },

  getServiceAreas() {
    if (!this.props.agentData || !this.props.agentData.ZipCodeList || !this.props.agentData.ZipCodes) {
      return;
    }
    const areaList = [];
    this.props.agentData.ZipCodes.forEach((item) => {
      if (areaList.indexOf(item.City) == -1) {
        areaList.push(item.City);
      }
    });

    if (areaList.length === 0) {
      return;
    }

    return areaList;
  },

  getUrl(logo) {
    return (`//nplayassets.blob.core.windows.net/credentials/${logo}`);
  },

  getDisclosures() {

  },

  linkClick(url) {
    if (!url || url == '') {
      return;
    }

    if (url.toLowerCase().indexOf('http://') < 0 && url.toLowerCase().indexOf('https://') < 0) {
      url = `http://${url}`;
    }
    window.open(
      url,
      '_blank',
    );
  },

  awardTemplate() {
    if (!this.props.agentData || !this.props.agentData.Awards || this.props.agentData.Awards && this.props.agentData.Awards.length == 0) {
      return '';
    }
    return map(this.props.agentData.Awards, function (item) {
      return (
        <div className="section-holder">
          -
          <span className="section-title">
            {item.Title}
            ,
          </span>
          <span className="section-details">{item.Description}</span>
          -
          <span className="section-date">{this.formatDate(item.Date)}</span>
        </div>
      );
    }, this);
  },

  publicationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Publications || this.props.agentData.Publications && this.props.agentData.Publications.length == 0) {
      return '';
    }
    return map(this.props.agentData.Publications, function (item) {
      return (
        <div className="section-holder">
          -
          <span
            role="button"
            tabIndex="-1"
            aria-label="Publication"
            className={classNames('section-title', { link: item.Url })}
            onClick={this.linkClick.bind(this, item.Url)}
          >
            {item.Title}
          </span>
          ,
          <span className="section-details">
            &nbsp;
            {item.Publication}
          </span>
          -
          <span className="section-date">{this.formatDate(item.Date)}</span>
          <span className="section-details">{item.Desc}</span>

        </div>
      );
    }, this);
  },

  organizationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Organizations || this.props.agentData.Organizations && this.props.agentData.Organizations.length == 0) {
      return '';
    }
    return map(this.props.agentData.Organizations, (item) => (
      <div className="section-holder">
        -
        <span className="section-title">{item.Title}</span>
        ,
        <span className="section-details">{item.Description}</span>
      </div>
    ), this);
  },

  educationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Education || this.props.agentData.Education && this.props.agentData.Education.length == 0) {
      return '';
    }
    return map(this.props.agentData.Education, (item) => (
      <div className="section-holder">
        -
        <span className="section-title">
          {item.Qualification}
          ,
          &nbsp;
          {item.Major}
        </span>
        -
        <span className="section-details">{item.Name}</span>
      </div>
    ), this);
  },

  formatDate(dateArg) {
    const date = new Date(dateArg);
    return (date.toLocaleDateString('en-US'));
  },

  getIconCount() {
    const count = 0
      + this.props.agentData.InstagramUrl ? 1 : 0
      + this.props.agentData.YouTubeUrl ? 1 : 0
      + this.props.agentData.GoogleUrl ? 1 : 0
      + this.props.agentData.PinterestUrl ? 1 : 0
      + this.props.agentData.LinkedInUrl ? 1 : 0
      + this.props.agentData.TwitterUrl ? 1 : 0
      + this.props.agentData.FacebookUrl ? 1 : 0;
    const titleLength = this.props.agentData.Headline ? this.props.agentData.Headline.length : 0;
    if (titleLength > 60 || titleLength > 40 && count >= 3) {
      this.actions.agent.setHeadlineInside(true);
    }
  },
  render() {
    if (!this.props.agentData) {
      return (<h4>Agent Data Not Found!</h4>);
    }
    return (
      <div>
        <div className="profile-container">
          <div className="row panel-1">
            <div className="col-1-1 col-lg-2-5 prn">
              {this.props.agentData.YearLicensed
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Licensed Since:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.YearLicensed}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.StateLicenseNumber
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">License:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.StateLicenseNumber}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.AssociationName
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Member of:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.AssociationName}
                    </span>
                  </div>
                ) : null}
              {this.getServiceAreas()
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Service Areas:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.getServiceAreas().join(', ')}
                    </span>
                  </div>
                ) : null}
            </div>
            <div className="col-1-1 col-lg-2-5 prn">
              {this.props.agentData.LicenseType
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Licensed Type:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.LicenseType}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.AgentType
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Representing:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.AgentType}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.Languages && this.props.agentData.Languages.length > 0
                ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Languages:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.Languages.join(', ')}
                    </span>
                  </div>
                ) : null}
              {
                this.props.agentData.BrokerName ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Brokerage:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.BrokerName}
                      {
                        this.props.agentData.BrokerPhone ? (
                          <span>
                            <br />
                            {this.utils.formatPhone(this.props.agentData.BrokerPhone)}
                          </span>
                        ) : null
                      }
                      <br />
                      {
                        this.props.agentData.BrokerAddress ? (
                          <span>
                            {this.props.agentData.BrokerAddress}
                            <br />
                          </span>
                        ) : null
                      }
                      {
                        [
                          this.props.agentData.BrokerCity,
                          `${this.props.agentData.BrokerState || ''} ${this.props.agentData.BrokerZip || ''}`.trim(),
                        ].filter((e) => !!e).join(', ')
                      }
                    </span>
                  </div>
                ) : null
              }
            </div>
            {this.props.agentData.BrokerLogo
              ? (
                <div className="col-1-1 col-lg-1-5 logo-container prn">
                  <img alt="Broker" src={this.props.agentData.BrokerLogo} className="broker-logo" />
                </div>
              ) : null}
          </div>
          <div className="row">
            <div className={classNames('headline inside col-1-1 title-show', { 'show-headline-inside': this.props.showHeadlineInside })}>
              {this.props.agentData.Headline}
            </div>
          </div>

          {this.props.agentData.About || this.props.agentData.AgentVideoUrl
            ? (
              <div className={classNames('panel-2 row border-line ptn', { professional: this.props.agentData.AgentVideoUrl })}>
                <div className={classNames('col-1-1 flex-top', { 'col-md-1-2': this.props.agentData.AgentVideoUrl })}>
                  <p className="header">PROFESSIONAL EXPERIENCE</p>
                  <p className={classNames('about')} dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.props.agentData.About) }} />
                </div>
                { this.props.agentData.AgentVideoUrl
                  ? (
                    <div className="col-1-1 col-md-1-2 video-container">
                      <div className="video-holder">
                        <iframe title="Agent video" width="100%" height="240" src={this.props.agentData.AgentVideoUrl.replace('youtube.com/shorts/', 'youtube.com/embed/')} />
                      </div>
                    </div>
                  ) : null}
              </div>
            ) : null}

          {this.props.agentData.Services
            ? (
              <div className="row border-line">
                <div className="col-1-1 service">
                  <p className="header">SERVICES</p>
                  <p dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.props.agentData.Services) }} />
                </div>
              </div>
            ) : null}

          {this.props.agentData.ExpertiseDescriptions && this.props.agentData.ExpertiseDescriptions.length > 0
            ? (
              <div className="row border-line">
                <div className="expertise col-1-1">
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-virtual-tour"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">EXPERTISE</p>
                    <ul className="expertise-text col-17-24">
                      {this.getExpertise()}
                    </ul>
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Awards && this.props.agentData.Awards.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon h40"
                      name="icon-expertise"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">AWARDS</p>
                    {this.awardTemplate()}
                  </div>
                </div>
              </div>
            ) : null}
          {this.props.agentData.Education && this.props.agentData.Education.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-education"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">EDUCATION</p>
                    {this.educationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Publications && this.props.agentData.Publications.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-license"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">PUBLICATIONS</p>
                    {this.publicationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}
          {this.props.agentData.Organizations && this.props.agentData.Organizations.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-organization"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">ORGANIZATIONS AND GROUPS</p>
                    {this.organizationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Credentials && this.props.agentData.Credentials.length > 0
            ? (
              <div className="row border-line">
                <div className="col-1-1">
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-certifications"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">CERTIFICATIONS</p>
                    <div className="certifications">
                      {
                        this.props.agentData.Credentials.map(function (item) {
                          return <img alt="Credential" key={item.Id} src={this.getUrl(item.Logo)} />;
                        }, this)
                      }
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Disclosures
            ? (
              <div className="row border-line">
                <div className="col-1-1">
                  <p className="header">DISCLOSURES</p>
                  <div className="disclosures" dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.props.agentData.Disclosures) }} />
                </div>
              </div>
            ) : null}

          <div className="row text-center mt20">
            <img alt="Equal housing opportunity" height="50" src="https://nplayassets.blob.core.windows.net/images/equal-housing-opportunity-logo.png" />
          </div>
        </div>
      </div>
    );
  },

});
