const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.agent-profile-brokerage',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    showBrokerage: ['screens', 'agent', 'agentProfile', 'showBrokerage'],
  },
  show() {
    this.actions.agent.showBrokerage();
  },
  hide() {
    this.actions.agent.hideBrokerage();
  },
  render() {
    console.log(`showBrokerage${this.state.showBrokerage}`);
    if (!this.state.showBrokerage) {
      return null;
    }
    return (
      <Modal show={this.state.showBrokerage} onHide={this.hide} className="brokerage-modal col-lg-4 col-md-5 col-sm-14 colxs-24" backdrop={false}>
        <button type="button" className="btn btn-default close-button" onClick={this.hide}>Close</button>
        <div className="brokerage ">
          {this.state.agentData.BrokerLogo
            ? (
              <div className="logo">
                <img alt="Broker" src={this.state.agentData.BrokerLogo} />
              </div>
            ) : null}
          <div className="details">
            <p><b>{this.state.agentData.BrokerName}</b></p>
            <p>{this.state.agentData.BrokerAddress}</p>
            <p>
              {this.state.agentData.BrokerCity}
              ,
              &nbsp;
              {this.state.agentData.BrokerState}
              &nbsp;
            </p>
          </div>
        </div>
      </Modal>
    );
  },
});
