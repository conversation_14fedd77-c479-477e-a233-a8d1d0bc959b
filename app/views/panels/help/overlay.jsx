const React = require('react');
const Path = require('paths-js/path');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.help-overlay',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    targets: ['panels', 'help', 'targets'],
  },

  componentDidMount() {
    window.addEventListener('resize', this.resize);
  },

  componentWillUnmount() {
    window.removeEventListener('resize', this.resize);
  },

  resize() {
    this.forceUpdate();
  },

  closeHelp() {
    this.actions.help.toggleHelp();
  },

  isElementVisible(node) {
    return (node.offsetWidth && (node.offsetWidth > 0));
  },

  /*
  Basic idea is we draw an initial rectangle path the size of the screen
  in a clockwise direction, then any subpaths in the opposite direction.
  The subpaths will be cut out, visually.
  See: http://stackoverflow.com/questions/1983256/how-can-i-cut-one-shape-from-another
  */
  renderBackground() {
    const paths = [];

    // first we draw the fullscreen rectangle in a clockwise direction
    const path = Path()
      .moveto(0, 0)
      .lineto(window.innerWidth, 0)
      .lineto(window.innerWidth, window.innerHeight)
      .lineto(0, window.innerHeight)
      .closepath();

    paths.push(path.print());

    // then we iterate over what we want to cut out, and draw in a counter-clockwise direction
    if (this.state.targets) {
      this.state.targets.forEach((selector) => {
        const target = document.querySelector(selector);
        const bounds = target.getBoundingClientRect();

        // let _path;
        /* if( target.classList.contains("map-circle-editor") ){
          var buffer = 20; //we don't want to draw directly on the circle's perimeter or you can't see it
          path = Path()
            .moveto( bounds.left-buffer, bounds.top + (bounds.height/2))
            .arc( bounds.width/2 + buffer, bounds.height/2 +buffer, 0, 1, 0, bounds.left-buffer, bounds.top + (bounds.height/2) - 0.1 )
            .closepath();
        }
        */
        const _path = Path()
          .moveto(bounds.left, bounds.top)
          .lineto(bounds.left, bounds.bottom)
          .lineto(bounds.right, bounds.bottom)
          .lineto(bounds.right, bounds.top)
          .closepath();

        paths.push(_path.print());
      });
    }

    return (
      <svg xmlns="http://www.w3.org/2000/svg" width={window.innerWidth} height={window.innerHeight}>
        <g>
          <path className="help-overlay-background" d={paths.join(' ')} fillRule="evenodd" />
        </g>
      </svg>
    );
  },

  render() {
    return (
      <div role="button" tabIndex="-1" aria-label="Close" className="help-overlay" onClick={this.closeHelp}>
        {this.renderBackground()}
      </div>
    );
  },

});
