const React = require('react');
const Portal = require('react-portal');
const mixins = require('../../../lib/mixins');
const HelpInformation = require('../../components/help_information');
const Overlay = require('./overlay');

module.exports = React.createClass({

  displayName: 'panels.help',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    layout: ['layout'],
  },

  getInitialState() {
    return {
      showInstructions: true,
    };
  },

  componentWillUpdate(nextProps, nextState) {
    if (nextState.layout.help) {
      document.body.scrollTop = 0;
      this.actions.common.disableBodyScroll();
    } else {
      if (this.state.layout && this.state.layout.help) {
        this.actions.common.enableBodyScroll();
      }
    }
  },

  componentWillUnmount() {
    this.actions.common.enableBodyScroll();
  },

  hideInstructions() {
    this.setState({ showInstructions: false });
  },

  render() {
    if (!this.state.layout.help) {
      return null;
    }

    return (
      <div className="help-panel">
        <Overlay />
        <Portal isOpened={this.state.showInstructions} className="help-instructions">
          <div>
            {this.utils.isMobile()
              ? 'Touch'
              : 'Hover over'}
            &nbsp;
            the
            <span className="help-indicator-example">+</span>
            &nbsp;
            signs to learn all about the awesome features HomeASAP has to offer!
            <div role="button" tabIndex="-1" aria-label="Close" className="help-instructions-close" onClick={this.hideInstructions}>x</div>
          </div>
        </Portal>
        <HelpInformation header="Map / List View" targets={['.map-grid-toggle']} anchor="bottom">
          Tired of the map? Switch it up a bit! These buttons allow you to easily switch back and forth between map and grid view.
        </HelpInformation>
        <HelpInformation header="Agent information" targets={['.agent-header-image']} anchor="right">
          To learn more about the agent, simply click on the agents picture either in the main navigation or in property details.
        </HelpInformation>
        <HelpInformation header="Search" targets={['.search-button-container']} anchor="bottom">
          This is where the magic begins and where you can edit it all. Adjust all your search filters so we can help you find exactly what you&apos;re looking for!
        </HelpInformation>
        <HelpInformation header="Profile &amp; Notifications" targets={['.buyer-container']} anchor="bottom">
          This is where we store all of your settings and amazingness. Notifications about properties, searches and more can be found here. Just click your dazzling face!
        </HelpInformation>
        <HelpInformation header="Search Results" targets={['.listings-container']} anchor="center">
          These are your search results. Click on an image to see more awesomeness about that home. But wait, that&apos;s not all! You can also sort the order your results are shown! Just scroll and the sort bar will magically appear.
        </HelpInformation>
        <HelpInformation header="Map Controls" targets={['.leaflet-control-recenter', '.leaflet-control-zoom', '.leaflet-control-layers']} anchor="right" requireLayout="map">
          Control the map with these amazing buttons! Zoom in or out. Recenter the map. Reposition the radius. Switch to aerial view. Take control.
        </HelpInformation>
        <HelpInformation header="Radius Tool" targets={['.map-circle-editor']} anchor="center" requireLayout="map">
          AKA &quot;The Magical Circle of Goodness&quot;. Simply drag this tool to where you want to live. To adjust the size of the circle, click on the arrows and drag. You&apos;re in control.
        </HelpInformation>
      </div>
    );
  },

});
