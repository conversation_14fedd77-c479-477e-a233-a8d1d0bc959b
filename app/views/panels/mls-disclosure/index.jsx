const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins/index');
const SpinnerRound = require('../../components/spinner_round.jsx');

module.exports = React.createClass({

  displayName: 'panels.mls-disclosure',

  mixins: [mixins.debug, mixins.utils, mixins.cursors, mixins.actions, mixins.pureRender],

  cursors: {
    mlsData: ['shared', 'mlsData', { location: 'props', path: ['mlsId'] }],
    agentBrokerageName: ['shared', 'agent', 'data', 'BrokerName'],
  },

  componentDidMount() {
    if (!this.state.mlsData && this.props.mlsId) {
      this.actions.listing.getMlsData(this.props.mlsId);
    }
  },

  partial() {
    if (!this.state.mlsData) {
      return null;
    }

    return (
      <div>
        {
        this.state.mlsData.LogoUrl
          ? <img alt="Mls" src={this.state.mlsData.LogoUrl} /> : null
        }
        {
        this.props.extraMessage
          ? <p className="extra-message">{this.props.extraMessage}</p> : null
        }
        <p dangerouslySetInnerHTML={{ __html: (this.state.mlsData.IdxStatement || '').replace(/\[BROKERAGE NAME HERE\]/, this.state.agentBrokerageName || '') }} />
        <p dangerouslySetInnerHTML={{ __html: this.state.mlsData.Copyright || '' }} />
        {
          this.state.mlsData.DataProvider === 'MlsGrid'
            || this.props.mlsId === 'moselmo' // Temporary band-aid for MLS Grid compliance
            ? (
              <p>
                Based on information submitted to the MLS GRID as of&nbsp;
                {this.state.mlsData ? new Date(`${this.state.mlsData.Latestupdate}Z`).toLocaleString() : 'Not Available'}
                .
                All data is obtained from various sources and may not have been verified by broker or MLS GRID.
                Supplied Open House Information is subject to change without notice. All information should be independently reviewed and verified for accuracy.
                Properties may or may not be listed by the office/agent presenting the information.
              </p>
            ) : null
        }
      </div>
    );
  },

  render() {
    if (!this.props.mlsId) {
      return null;
    }

    let css = '';

    if (this.state.mlsData) {
      if (this.state.mlsData.RestrictListingsToMemberAgents) {
        css = `
          .listing-mlsid-${this.props.mlsId}.listing-mlsidcount-1 {
            display: none !important;
          }

          body[data-agentmlsid="${this.props.mlsId}"] .listing-mlsid-${this.props.mlsId}.listing-mlsidcount-1 {
            display: block !important;
          }

          body[data-agentmlsid="${this.props.mlsId}"] .grid-cards-container .listing-mlsid-${this.props.mlsId}.listing-mlsidcount-1 {
            display: inline-block !important;
          }
        `;
      }
    }

    return (
      <div className={classNames('mls-disclosure', this.props.className)}>
        {
        this.state.mlsData === null
          ? (
            <center className="mt15 mb15">
              <SpinnerRound />
            </center>
          ) : this.partial()
        }
        {css ? <style dangerouslySetInnerHTML={{ __html: css }} /> : null}
      </div>
    );
  },
});
