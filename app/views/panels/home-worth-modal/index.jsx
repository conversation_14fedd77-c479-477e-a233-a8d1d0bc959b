const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.home-worth-modal',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    layout: ['layout', 'homeWorthModal'],
  },
  componentDidMount() {
    this.sessionToken = String(+new Date());
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.layout && !prevState.layout) {
      this.mountAutocompleteInterval = setInterval(() => {
        if (window.google) {
          const input = document.getElementById('home-worth-address-input');
          if (input) {
            google.maps.places.Autocomplete(input, { types: ['address'], sessionToken: this.sessionToken });
          }
          clearInterval(this.mountAutocompleteInterval);
        }
      }, 100);
    }
  },

  handleSubmit(e) {
    e.stopPropagation();
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {};
    for (const [key, value] of formData) {
      data[key] = value;
    }
    this.actions.common.submitHomeValue(data, () => {
      this.setState({ submitted: true });
    });
  },

  renderForm() {
    return (
      <div className="home-worth-form-container">
        <img alt="Home value" className="home-value" src="//nplayassets.blob.core.windows.net/images/ICON-homevalue.png" />
        <div className="subtitle">How much is your home worth?</div>
        <div className="description">Fill out the form to request a free analysis and find out what your home is currently worth.</div>
        <form onSubmit={this.handleSubmit} name="home-worth-form">
          <input type="text" required placeholder="First name" name="firstName" />
          <input type="text" required placeholder="Last name" name="lastName" />
          <input type="email" required placeholder="Email" name="email" />
          <input type="tel" placeholder="Phone" name="phone" />
          <input type="text" id="home-worth-address-input" className="address-input" required placeholder="Address" name="address" />
          <select placeholder="When do you want to sell?" name="whenWantToSell">
            <option>&nbsp;</option>
            <option>As soon as possible</option>
            <option>Within 3-6 months</option>
            <option>Within a year</option>
            <option>Just curious</option>
          </select>
          <button type="submit" className="btn btn-outline">Request an estimate</button>
        </form>
      </div>
    );
  },

  renderSuccess() {
    return (
      <div className="home-worth-success-container">
        <div className="title">Success!</div>
        <img alt="Success" className="success-checkmark" src="//nplayassets.blob.core.windows.net/images/ICON-success-checkmark.png" />
        <div className="agent-card">
          <AgentProfileImage className="agent-image" />
          <div className="agent-details">
            <div className="agent-name">
              {`${this.state.agentData.FirstName} ${this.state.agentData.LastName} `}
              <VerifiedBadge />
            </div>
            <div className="broker-name">{this.state.agentData.BrokerName}</div>
          </div>
        </div>
        <div className="subtitle">Thank you for submitting your information for a complete, custom home evaluation.</div>
        <div className="description">I will be contacting you shortly with your home&apos;s estimated value and to answer any questions you may have.</div>
        {
          this.state.agentData.ProductServices.indexOf('IDX') !== -1
            ? <button type="button" className="btn btn-success" onClick={this.actions.common.hideHomeWorthModal}>Continue searching for homes</button>
            : <button type="button" className="btn btn-success" onClick={() => this.actions.agent.onNav()}>View My Profile</button>
        }
      </div>
    );
  },

  render() {
    return (
      <Modal show={this.state.layout} onHide={this.actions.common.hideHomeWorthModal} className={this.utils.isMobile() ? 'home-worth-modal mobile' : 'home-worth-modal'}>
        <Modal.Body>
          <div role="button" tabIndex="-1" aria-label="Close" className="close-button-landing close-button" onClick={this.actions.common.hideHomeWorthModal}>
            <SVGIcon name="icon-close-button" />
          </div>
          {this.state.submitted
            ? this.renderSuccess()
            : this.renderForm()}
        </Modal.Body>
      </Modal>
    );
  },

});
