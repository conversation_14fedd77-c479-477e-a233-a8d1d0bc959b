const React = require('react');
const Slider = require('react-slick');
const classNames = require('classnames');
const SubmitBtn = require('../../components/submit_btn');
const mixins = require('../../../lib/mixins');
const SaleTypeDropdown = require('./sale-type');
const SearchField = require('./search-field');

module.exports = React.createClass({

  displayName: 'panels.onboarding',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    ref: ['screens', 'onboarding', 'ref'],
    buyerData: ['shared', 'buyer', 'data'],
    agentData: ['shared', 'agent', 'data'],
    login: ['layout', 'login'],
    facebookLayout: ['layout', 'facebookHeader'],
    IsSearchLicenseAgreementShown: ['shared', 'agent', 'mlsData', 'IsSearchLicenseAgreementShown'],
  },

  defaultSearchSelection: {
    propertyType: null,
    maxPrice: null,
    minPrice: null,
    minBeds: null,
    minBaths: null,
  },

  componentWillMount() {
    const menuSelections = this.actions.menu.getMenuSelections();
    this.setState({ initMenu: menuSelections, keywords: menuSelections.keywords });
  },

  componentDidMount() {

  },

  componentWillUnmount() {
  },

  searchSubmit(e) {
    e.preventDefault();

    const saleType = this.refs.SaleType.state.saleType;
    console.log(`Sale Type ${saleType}`);

    const res = this.refs.SearchField.state.res;
    if (!this.refs.SearchField.state.hasPrevious // Using Previous
      && (!res.locationQuery || res.locationQuery !== this.refs.SearchField.state.value)) {
      const suggestions = this.refs.SearchField.state.suggestions;
      if (suggestions && suggestions[0] && suggestions[0].type === 'mlsListingIdMatch') {
        return this.refs.SearchField.autosuggestSelected(e, { suggestion: suggestions[0] });
      }

      console.log('Need google geocoding');
      this.actions.onboarding.geocodeAddress(this.refs.SearchField.state.value, (res1) => {
        const _res = {
          lat: res1.geometry.location.lat,
          lon: res1.geometry.location.lng,
          locationQuery: res1.formatted_address,
        };
        console.log(`Search Field: ${_res.locationQuery}`);
        this.actions.onboarding.onNav(_res.lat, _res.lon, res.radius, _res.locationQuery, saleType, {
          listingId: res.listingId,
        });
      });
    } else {
      console.log(`Search Field: ${res.locationQuery}`);
      this.actions.onboarding.onNav(res.lat, res.lon, res.radius, res.locationQuery, saleType, {
        listingId: res.listingId,
      });
    }
  },

  featuredListingClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/featured');
  },

  loginClicked() {
    this.actions.login.start({ action: 'facebook_onboarding' }, this.loginComplete);
    this.actions.analytics.sendEvent('login', 'start', 'onboarding');
  },

  loginComplete(statusObj) {
    console.log(`!!! Login complete: ${statusObj.status}; data: ${statusObj.data}`);
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/agent');
  },

  buyerProfile() {
    this.actions.common.goToRoute('/buyer');
  },

  render() {
    const textSliderSettings = {
      dots: false,
      infinite: true,
      speed: 500,
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: false,
      autoplay: true,
      autoplaySpeed: 8000,
      draggable: false,
    };

    return (

      <div className={classNames('onboarding row', {
        'facebook-header-version': this.state.facebookLayout,
        'facebook-page-admin': this.state.facebookLayout === 'admin',
      })}
      >

        <div className="buyer-container">
          <div className="image-container">
            <img alt="Your profile" src={this.state.buyerData ? (this.state.buyerData.ProfileImage || '//nplayassets.blob.core.windows.net/search2/fb-login.png') : '//nplayassets.blob.core.windows.net/search2/1x1.png'} />
          </div>
          <div className="buyer-name">
            <a
              role="button"
              tabIndex="-1"
              style={{ cursor: this.state.buyerData && this.state.buyerData.FirstName ? 'default' : 'pointer' }}
              onClick={this.state.buyerData ? (this.state.buyerData.FirstName ? this.buyerProfile : this.loginClicked) : this.loginClicked}
            >
              {this.state.buyerData ? (this.state.buyerData.FirstName ? this.state.buyerData.FirstName : 'Login') : ''}
            </a>
          </div>
        </div>

        <div className="onboarding-center">
          <div className="inputs-container">
            <form
              className="search-form"
              onSubmit={this.searchSubmit}
            >
              <Slider {...textSliderSettings} className="panel-text">
                <div className="pText3">Find your home.</div>
                <div className="pText1">Your Dream House Awaits.</div>
                <div className="pText2">Search options like no other.</div>
              </Slider>

              <SaleTypeDropdown ref="SaleType" />
              <SearchField ref="SearchField" autoFocus={false} />
              <SubmitBtn
                spinner={this.state.ref.spinner}
                className="btn-primary"
                icon="icon-search"
              />
            </form>
            {this.state.IsSearchLicenseAgreementShown
              ? <a role="button" tabIndex="-1" className="implicit-agree" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>By searching you agree to the terms and conditions</a>
              : null}
          </div>
        </div>

        <div className="copyright">
          <p>
            <span className="text-transform-none text-block">
              &copy;
              {window.CURRENT_YEAR || ''}
              &nbsp;
              HomeASAP LLC
            </span>
            <span>&nbsp;·&nbsp;</span>
            <a role="button" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>Terms</a>
            <span>&nbsp;·&nbsp;</span>
            <a role="button" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}>
              Privacy/DMCA Policy
            </a>
          </p>
        </div>
      </div>
    );
  },

});
