const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const CustomDropdown = require('../../components/custom_dropdown');
const DropdownData = require('../menu/dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.onboarding.sale-type',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  getInitialState() {
    return {
      saleType: 1,
    };
  },

  componentWillMount() {
    this.dropdownData = DropdownData.saleTypeOptions;

    this.dropdownDataArray = map(this.dropdownData, (obj) => obj.name);

    this.dropdownNameToValue = function (name) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdownValueToName = function (val) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
    };
  },

  getCurrentItem1() {
    if (this.state.saleType) {
      return this.dropdownValueToName(this.state.saleType);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdownNameToValue(item) || null;

    if (this.state.saleType !== value) {
      this.setState({ saleType: value });
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdownNameToValue(item) || null;
    if (value === this.state.saleType) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.saleType) {
      return this.dropdownValueToName(this.state.saleType);
    }

    return 'Sale Type';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Sale Type"
        ref="SaleType"
        displayInPlace
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray}
        currentItem1={this.getCurrentItem1()}
        displayString={this.displayFunction}
      />
    );
  },

});
