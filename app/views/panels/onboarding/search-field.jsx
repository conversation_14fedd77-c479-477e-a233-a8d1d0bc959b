const React = require('react');
const Autosuggest = require('react-autosuggest');
const _ = require('lodash');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.onboarding.search-field',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  getInitialState() {
    return {
      res: {

      },
      hasPrevious: false,
      suggestions: [],
      value: '',
    };
  },

  componentWillMount() {
    let lastSearchItemString = window.localStorageAlias.getItem('LAST_SEARCH_TERM');
    if (lastSearchItemString) {
      lastSearchItemString = JSON.parse(lastSearchItemString);
    }
    if (lastSearchItemString) {
      this.setState({ res: lastSearchItemString, hasPrevious: true, value: lastSearchItemString.locationQuery });
    }
  },

  componentDidMount() {
    if (this.state.hasPrevious) {
      this.refs.autosuggest.input.addEventListener('click', this.wipePrevious);
    }
  },

  componentWillUpdate(nextProps, nextState) {
    if (this.state.res.locationQuery != nextState.res.locationQuery && nextState.res.locationQuery) {
      this.setState({ value: nextState.res.locationQuery });
    }
  },

  wipePrevious() {
    this.setState({ res: {}, hasPrevious: false });
    this.refs.autosuggest.input.removeEventListener('click', this.wipePrevious);
    this.refs.autosuggest.input.placeholder = 'Address, Neighborhood, School, City, Zip';
  },

  componentWillUnmount() {
    if (this.state.hasPrevious) {
      this.refs.autosuggest.input.removeEventListener('click', this.wipePrevious);
    }
  },

  renderSuggestion(data/* , inputVal */) {
    if (data.value == 'Current Location') {
      return (
        <span className="current-location">
          <SVGIcon name={this.utils.isIos() ? 'icon-location-ios' : 'icon-center'} className="autosuggest-icon" />
          {data.value}
        </span>
      );
    }
    if (data.location) {
      if (data.location.locType === 'C') {
        return (
          <span>
            <SVGIcon name="icon-map" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'S') {
        return (
          <span>
            <SVGIcon name="icon-school-hat" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'N' || data.location.locType === 'Z') {
        return (
          <span>
            <SVGIcon name="icon-map-pin" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'A') {
        return (
          <span>
            <SVGIcon name="icon-front-of-house" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
    }
    return <span>{data.value}</span>;
  },

  autosuggestSelected(e, selection) {
    const data = selection.suggestion;
    if (data && data.location) {
      // this.actions.onboarding.onNav(
      //  data.location.lat, data.location.lon, data.location.radius, data.value);
      this.setState({
        res: {
          lat: data.location.lat,
          lon: data.location.lon,
          radius: data.location.radius,
          locationQuery: data.value,
          listingId: data.location.locType === 'A' && data.location.locId,
        },
      });

      // Save to Saved Searches if School
      if (data.location.locType === 'S') {
        this.actions.common.setLocationCustomName(data.location.lat, data.location.lon, data.value);
      }
    } else if (data && data.value === 'Current Location') {
      setTimeout(() => {
        this.setState({ value: 'Loading Location...' });
      }, 0);
      this.actions.menu.searchCurrentLocation(this.props.saleType || 1);
    }
  },

  searchSubmit(e) {
    e.preventDefault();
  },

  searchKeywordEntered(e, suggestion) {
    const autoSuggestValue = _.get(suggestion, 'focusedSuggestion.value') || (this.state && this.state.value);
    if (autoSuggestValue !== this.state.res.locationQuery) {
      this.actions.onboarding.geocodeAddress(autoSuggestValue, (res) => {
        this.setState({
          res: {
            lat: res.geometry.location.lat,
            lon: res.geometry.location.lng,
            locationQuery: res.formatted_address,
          },
        });
      });
    }
  },

  loadSuggestions(value) {
    this.actions.onboarding.onAutosuggest.api(value, (err, results) => {
      results = _.filter(results, (result) => _.get(result.location, 'lat'));
      this.setState({ suggestions: results || [] });
    });
  },

  onSuggestionsFetchRequested({ value }) {
    this.loadSuggestions(value);
  },

  onSuggestionsClearRequested() {
    this.setState({
      suggestions: [],
    });
  },

  onChange(event, { newValue }) {
    this.setState({
      value: newValue,
    });
  },

  render() {
    return (
      <Autosuggest
        ref="autosuggest"
        inputProps={{
          type: 'search',
          autoFocus: (typeof this.props.autoFocus === 'boolean') ? this.props.autoFocus : !this.state.res.locationQuery,
          className: 'form-control',
          placeholder: this.state.hasPrevious ? this.state.res.locationQuery : 'Address, Neighborhood, School, City, Zip',
          value: this.state.value || '',
          onBlur: () => {
            setTimeout(this.searchKeywordEntered, 100);
          },
          onChange: this.onChange,
        }}
        suggestions={this.state.suggestions}
        shouldRenderSuggestions={this.actions.onboarding.onAutosuggest.showWhen}
        onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
        onSuggestionsClearRequested={this.onSuggestionsClearRequested}
        onSuggestionSelected={this.autosuggestSelected}
        getSuggestionValue={(data) => data.value}
        renderSuggestion={this.renderSuggestion}
        focusInputOnSuggestionClick={false}
        alwaysRenderSuggestions
        scrollBar
      />
    );
  },

});
