const React = require('react');
const _ = require('lodash');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');

const Agent = require('./agent');

module.exports = React.createClass({

  displayName: 'panels.agent-search',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
    pickedAgent: ['screens', 'agentSearch', 'pickedAgent'],
    agents: ['screens', 'agentSearch', 'agents'],
    location: ['panels', 'listings', 'meta', 'locationStr'],
    locationQuery: ['panels', 'listings', 'meta', 'locationQuery'],
    layout: ['layout', 'agentSearch'],
  },

  getInitialState() {
    return {
      showMore: false,
      showEvenMore: false,
    };
  },

  handleShowMoreClick() {
    this.setState({ showMore: true });
  },

  handleShowEvenMoreClick() {
    this.setState({ showEvenMore: true });
  },

  handleAgentClick(e) {
    e.stopPropagation();
    e.preventDefault();
    this.router.go(e.currentTarget.pathname);
    const agentId = (e.currentTarget.dataset && e.currentTarget.dataset.id) || e.currentTarget.getAttribute('data-id');
    this.actions.home.trackSelectAgentActivity(agentId);
  },

  handlePickedAgentClick(e) {
    e.stopPropagation();
    e.preventDefault();

    const href = e.currentTarget.pathname;
    // we want to deliver a forced lead here
    const location = this.utils.validateLocationStr(this.state.location);
    this.actions.home.onAgentSelected(this.state.pickedAgent, href);
    this.models.agentPicker.deliverChargedLead({ lat: location.Lat, lng: location.Lon, agentId: this.state.pickedAgent.Id }, () => {
      this.router.go(href);
    });
  },

  renderMoreAgents() {
    if (!(this.state.agents && this.state.agents.length)) {
      return <div>No results</div>;
    }

    const location = this.utils.validateLocationStr(this.state.location);

    let agents = _.map(this.state.agents, (agent) => {
      const href = this.router.generateUrl(this.router.ROUTE_NAMES.MAP, {
        agentId: agent.CustomURL || agent.Id,
        location: `${location.Lat},${location.Lon},${6200}`,
      });
      return (
        <li>
          <a href={href} onClick={this.handleAgentClick} data-id={agent.Id}>
            <Agent
              agent={agent}
              key={agent.Id}
            />
          </a>
        </li>
      );
    });

    const SHOW_MORE_MAX = 4;
    const SHOW_EVEN_MORE_MAX = 20;

    if (!this.state.showEvenMore) {
      agents = agents.slice(0, SHOW_MORE_MAX);
    } else {
      agents = agents.slice(0, SHOW_EVEN_MORE_MAX);
    }

    return (
      <div className="more-agents">
        <ul className="agents-list">
          {agents}
        </ul>
        { (this.state.showEvenMore || (this.state.agents.length <= SHOW_MORE_MAX))
          ? null
          : <button type="button" className="show-more" onClick={this.handleShowEvenMoreClick}>Show me more agents</button>}
      </div>
    );
  },

  renderFirstAgent() {
    const agent = this.state.pickedAgent || (this.state.agents && this.state.agents[0]);

    if (!agent) {
      return <div>No agent found</div>;
    }

    const location = this.utils.validateLocationStr(this.state.location);

    const href = this.router.generateUrl(this.router.ROUTE_NAMES.MAP, {
      agentId: agent.CustomURL || agent.Id,
      location: `${location.Lat},${location.Lon},${6200}`,
    });

    return (
      <div className="best-agent">
        <a href={href} onClick={this.state.pickedAgent ? this.handlePickedAgentClick : this.handleAgentClick} data-id={agent.Id}>
          <Agent
            agent={agent}
            key={agent.Id}
          />
        </a>
        { (this.state.agents.length > 1)
          ? <div className="or-divider">- or -</div>
          : null}
        { (this.state.agents.length > 1)
          ? <button type="button" className="show-more" onClick={this.handleShowMoreClick}>Show me more agents</button>
          : null}
      </div>
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className={classNames('agent-search')}>
        { !this.state.agents
          ? (
            <div className="container">
              <div className="title">
                Finding the best agent for your search area...
              </div>
            </div>
          )
          : (
            <div className="container">
              <div className="title">
                {this.state.showMore
                  ? 'Choose a local expert to assist you while searching:'
                  : 'We\'ve picked a local expert to assist you while searching:'}
              </div>
              { this.state.locationQuery
                ? <div className="location">{this.state.locationQuery}</div>
                : null}
              { this.state.showMore
                ? this.renderMoreAgents()
                : this.renderFirstAgent()}
            </div>
          )}
      </div>
    );
  },

});
