const React = require('react');
const _ = require('lodash');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const AgentProfileImage = require('../../components/agent_profile_image');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.agent-search-result',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  getInitialState() {
    return {
      expandAbout: false,
      expandServices: false,
    };
  },

  handleExpandAboutClick() {
    this.setState({
      expandAbout: true,
    });
  },

  handleExpandServicesClick() {
    this.setState({
      expandServices: true,
    });
  },

  render() {
    if (!this.props.agent) {
      return null;
    }

    const zipCodeNames = _(this.props.agent.ZipCodes)
      .map((z) => z.City)
      .uniq()
      .valueOf();

    const specializing = zipCodeNames ? zipCodeNames.join(', ') : '-';

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Agent"
        className={classNames('agent-search-result')}
        onClick={this.props.onClick}
      >
        <div className="content">
          <div className="top">
            <div className="image">
              <AgentProfileImage className="picture" agentData={this.props.agent} />
              {/* agent since is repeated twice, this one will be visible in mobile via css */}
              <div className="agent-since">
                AGENT SINCE
                <span>{this.props.agent.YearLicensed ? this.props.agent.YearLicensed : '?'}</span>
              </div>
              <div className="featured">FEATURED AGENT</div>
            </div>
            <div className="top-details">
              <div className="agent-name" title={`${this.props.agent.FirstName} ${this.props.agent.LastName}`}>
                {this.props.agent.FirstName}
                &nbsp;
                {this.props.agent.LastName}
                &nbsp;
                <VerifiedBadge />
              </div>
              <div className="broker-name" title={this.props.agent.BrokerName}>{this.props.agent.BrokerName}</div>
              <div className="specializing" title={specializing}>{specializing}</div>
              {/* agent since is repeated twice, this one will be visible in desktop via css */}
              <div className="agent-since">
                AGENT SINCE
                <span>{this.props.agent.YearLicensed ? this.props.agent.YearLicensed : '?'}</span>
              </div>
            </div>
          </div>
        </div>
        <button type="button" className="bottom-button">CONTINUE TO SEARCH RESULTS</button>
      </div>
    );
  },

});
