const React = require('react');
const classNames = require('classnames');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const Call = require('./call.jsx');
const Email = require('./email.jsx');
const Showing = require('./showing.jsx');
const CMA = require('./cma.jsx');
const Detail = require('./listingDetail.jsx');
const AgentProfileImage = require('../../components/agent_profile_image');
const CloseButton = require('../../components/close_btn_cross');
const Alert = require('../../components/alert');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({
  displayName: 'panel.contact.agent-contact',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils],

  getInitialState() {
    return {
      loginAlert: false,
    };
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    showAgentContact: ['shared', 'contact', 'showAgentContact'],
    activeTab: ['shared', 'contact', 'activeTab'],
    buyerData: ['shared', 'buyer', 'data'],
    activeListingData: ['shared', 'contact', 'listingData'],
    showDetails: ['shared', 'contact', 'showDetails'],
  },

  show() {
    this.actions.agent.showAgentContact(this.props.listingData);
  },
  hide(clearData) {
    this.actions.agent.hideAgentContact(clearData);
  },
  tabs() {
    const tabs = this.state.activeListingData ? [{ Tag: 'Call', Icon: 'icon-phone' }, { Tag: 'Email', Icon: 'icon-email' }, {
      Tag: 'Showing',
      Icon: 'icon-calendar',
    }, { Tag: 'Market Analysis', Icon: 'icon-cma' }] : [{ Tag: 'Call', Icon: 'icon-phone' }, { Tag: 'Email', Icon: 'icon-email' }];
    return (
      <div className="holder">
        {
          tabs.map(function (item) {
            return (
              <a
                role="button"
                tabIndex="-1"
                className={classNames('options', { 'two-count': !this.state.activeListingData }, { active: this.state.activeTab === item.Tag })}
                key={item.Tag}
                onClick={this.tabItemClick.bind(this, item.Tag)}
              >
                <SVGIcon className="options-icon" name={item.Icon} />
                <p>{item.Tag}</p>
              </a>
            );
          }, this)
          }
      </div>
    );
  },
  tabItemClick(item) {
    this.setState({ activeTab: item });
  },

  loginCallBack(statusObj) {
    this.show();
    if (statusObj.status != 'Login successful') {
      this.setState({ loginAlert: true });
    } else {
      this.setState({ loginAlert: false });
    }
  },

  getUrl() {
    const url = `http://maps.google.com?q=${encodeURIComponent(`${this.state.agentData.BrokerAddress}, ${this.state.agentData.BrokerZip}`)}`;
    return url;
  },

  clearLocalStorage() {
    this.actions.agent.setContactLocalStorage('AD', '');
    this.actions.agent.setContactLocalStorage('AT', '');
    this.actions.agent.setContactLocalStorage('AM', '');
    /* window.localStorageAlias.removeItem("AD");
    window.localStorageAlias.removeItem("AT");
    window.localStorageAlias.removeItem("ACM"); */
  },

  panel() {
    if (this.state.activeTab == 'Call') {
      return <Call phone={this.state.agentData ? this.state.agentData.Phone : null} />;
    }
    if (this.state.activeTab == 'Email') {
      return <Email buyerData={this.state.buyerData} agentData={this.state.agentData} listingId={this.state.activeListingData ? this.state.activeListingData.Id : null} callBack={this.hide} completedAction={this.completedAction} clearLocalStorage={this.clearLocalStorage} showDetails={this.state.showDetails} />;
    }
    if (this.state.activeTab == 'Showing') {
      return <Showing buyerData={this.state.buyerData} agentData={this.state.agentData} listingId={this.state.activeListingData ? this.state.activeListingData.Id : null} callBack={this.hide} completedAction={this.completedAction} clearLocalStorage={this.clearLocalStorage} showDetails={this.state.showDetails} />;
    }
    if (this.state.activeTab == 'Market Analysis') {
      return <CMA buyerData={this.state.buyerData} agentData={this.state.agentData} listingId={this.state.activeListingData ? this.state.activeListingData.Id : null} callBack={this.hide} completedAction={this.completedAction} clearLocalStorage={this.clearLocalStorage} showDetails={this.state.showDetails} />;
    }
  },

  render() {
    console.log(`active listing data${this.state.activeListingData}`);
    this.state.activeListingData && this.state.activeListingData.length > 0
      ? console.log(`length${this.state.activeListingData.length}`) : null;
    if (!this.state.showAgentContact) {
      return null;
    }
    return (
      <Modal
        show={this.state.showAgentContact}
        onHide={this.hide}
        className={classNames('agent-contact-modal col-lg-5 col-md-6 col-sm-16 col-xs-24', {
          'agent-contact-modal-mobile': this.props.mobile,
        })}
      >
        <CloseButton onClick={this.hide.bind(this, true)} />
        <div className="agent-contact ">
          <div className="first-panel">
            <div className="content-holder">
              <AgentProfileImage className="photo" />
              <div className="broker-info">
                <p className="m0">
                  <strong>{`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`}</strong>
                          &nbsp;
                  <VerifiedBadge />
                </p>
                <p className="agent-email">
                  {this.state.agentData.Email}
                </p>
                <p className="broker-name">
                  <span>{this.state.agentData.BrokerName}</span>
                </p>
                {this.state.agentData && this.state.agentData.BrokerAddress ? (
                  <p className="directions">
                    <a href={this.getUrl()} target="_blank">
                      Directions To My Office
                    </a>
                  </p>
                ) : null}
              </div>
            </div>
          </div>
          {
            this.state.activeListingData ? (
              <div className="second-panel">
                <Detail activeId={this.state.activeListingData.Id} listingDetailData={this.state.activeListingData} />
              </div>
            ) : null
            }
          <div className="third-panel">
            {this.tabs()}
          </div>
          <div className="forth-panel">
            {this.panel()}
            {this.state.loginAlert ? (
              <Alert className="login-alert alert-component" message="You must be logged into Facebook to contact agent!" />) : null}
          </div>
        </div>
      </Modal>
    );
  },
});
