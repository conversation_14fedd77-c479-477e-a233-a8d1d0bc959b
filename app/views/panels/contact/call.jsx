const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.contact.call',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils, mixins.router],

  render() {
    const formattedPhone = this.utils.formatPhone(this.props.phone);
    return (
      <div
        role="button"
        tabIndex={-1}
        onClick={() => {
          if (formattedPhone) {
            if (window.rateplug.rp_buyer) {
              this.actions.analytics.sendEvent('rateplug', 'contact agent', 'phone open', '', { source: 'rateplug' });
            }
            window.open(`tel:${formattedPhone}`, '_blank');
          }
        }}
      >
        <p>
          {formattedPhone}
        </p>
      </div>
    );
  },
});
