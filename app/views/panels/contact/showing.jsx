const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.contact.showing',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],
  getInitialState() {
    return {
      confirmation: false,
      submitting: false,
      isValid: true,
    };
  },

  componentDidMount() {
    const date = this.actions.agent.getLocalStorage('AD');
    const time = this.actions.agent.getLocalStorage('AT');
    const dateInput = this.refs.DateInput;
    const timeInput = this.refs.TimeInput;
    dateInput ? dateInput.value = date : '';
    timeInput ? timeInput.value = time : '';

    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'showing', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'showing open', agentData.Id, { source: 'rateplug' });
    }
  },
  showConfirmation(statusCode) {
    if (statusCode != 200) {
      this.setState({ confirmation: 'failed', submitting: false });
    } else {
      this.setState({ confirmation: 'success', submitting: false });
    }
  },
  onSubmit(e) {
    e.preventDefault();
    const dateInput = this.refs.DateInput;
    const timeInput = this.refs.TimeInput;
    if (!dateInput.value) {
      this.setState({ isValid: 'emptyDate' });
      return;
    }
    if (!timeInput.value) {
      this.setState({ isValid: 'emptyTime' });
      return;
    }
    if (!this.validateDate(dateInput.value) || !this.validateTime(timeInput.value)) {
      return;
    }
    // if(!this.props.buyerData || !this.props.buyerData.FirstName) {
    //  this.actions.agent.setContactLocalStorage("AD", dateInput.value);
    //  this.actions.agent.setContactLocalStorage("AT", timeInput.value);
    //  this.props.checkLogin ? this.props.checkLogin() : '';
    // }
    // else {
    const emailInput = this.refs.EmailInput;
    const nameInput = this.refs.NameInput;
    if (!emailInput.value) {
      this.setState({ isValid: 'emptyEmail' });
      return;
    }

    const formattedDate = dateInput.valueAsDate.toLocaleDateString();
    const [h, m] = timeInput.value.split(':');
    const formattedTime = `${(h % 12 + 12 * (h % 12 == 0))}:${m} ${h >= 12 ? 'PM' : 'AM'}`;

    this.setState({ submitting: true });
    this.actions.agent.submitShowing(nameInput.value, emailInput.value, this.props.showDetails && this.props.listingId ? this.props.listingId : '', formattedDate, formattedTime, this.props.agentData.Id, this.showConfirmation);
    this.props.clearLocalStorage ? this.props.clearLocalStorage() : '';

    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'showing send', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'showing send', agentData.Id, { source: 'rateplug' });
    }
    // }
    this.props.completedAction();
  },
  handleChange(event) {
    if (this.state.isValid != true) {
      this.setState({ isValid: true });
    }
    this.setState({ value: event.target.value });
  },
  validateDate(date) {
    if (!date) {
      this.setState({ isValid: 'invalidDate' });
      this.refs.DateInput != null ? this.refs.DateInput.focus() : '';
      return false;
    }
    const today = new Date();
    const showingDate = new Date(date);
    const validDate = showingDate instanceof Date && !isNaN(showingDate.valueOf());
    if (!validDate) {
      this.setState({ isValid: 'invalidDate' });
      return false;
    }
    if (showingDate < today) {
      this.setState({ isValid: 'pastDate' });
      this.refs.DateInput != null ? this.refs.DateInput.focus() : '';
      return false;
    }
    return true;
  },
  validateTime(time) {
    if (!time) {
      this.setState({ isValid: 'invalidTime' });
      this.refs.TimeInput != null ? this.refs.TimeInput.focus() : '';
      return false;
    }
    return true;
  },
  render() {
    return (
      <div>
        {
          !this.state.confirmation ? (
            <form className="showing-form" onSubmit={this.onSubmit}>
              <p className="title">Schedule a showing of this home</p>
              <div className="input-group">
                <input
                  className="form-control mb5"
                  placeholder="Your Name"
                  required
                  type="text"
                  ref="NameInput"
                  defaultValue={this.props.buyerData ? this.props.buyerData.FirstName : null}
                  onChange={this.handleChange}
                />
                <input
                  className={classNames('form-control mb5 pull-right', { error: this.state.isValid === 'emptyEmail' })}
                  placeholder="Your Email"
                  type="email"
                  ref="EmailInput"
                  defaultValue={this.props.buyerData ? this.props.buyerData.Email : null}
                  onChange={this.handleChange}
                />
                <input
                  className={classNames('form-control', { error: this.state.isValid === 'invalidDate' || this.state.isValid === 'emptyDate' })}
                  placeholder="mm/dd/yyyy"
                  type="date"
                  ref="DateInput"
                  onChange={this.handleChange}
                />
                <input
                  className={classNames('form-control pull-right', { error: this.state.isValid === 'invalidTime' || this.state.isValid === 'emptyTime' })}
                  placeholder="e.g. 3:00 PM"
                  type="time"
                  ref="TimeInput"
                  onChange={this.handleChange}
                />
                <span className="input-group-btn">
                  <button type="submit" className="btn btn-default mt5" disabled={this.state.submitting}>
                    Send Request
                  </button>
                </span>
                {
                  this.state.isValid === 'invalidDate'
                    ? (<p className="error-message"> Please enter a valid date! </p>) : null
                }
                {
                  this.state.isValid === 'invalidTime'
                    ? (<p className="error-message"> Please enter a valid time! (e.g. 3:00 PM) </p>) : null
                }
                {
                  this.state.isValid == 'pastDate'
                    ? (<p className="error-message"> Please enter a date later than today! </p>) : null
                }
                {
                  this.state.isValid == 'emptyDate'
                    ? (<p className="error-message"> Please enter date! </p>) : null
                }
                {
                  this.state.isValid == 'emptyTime'
                    ? (<p className="error-message"> Please enter time! </p>) : null
                }
                {
                  this.state.isValid == 'emptyEmail'
                    ? (<p className="error-message"> Please enter your email! </p>) : null
                }
              </div>
            </form>
          )
            : this.state.confirmation === 'success'
              ? (<div className="title">Your mail has been sent. Agent will contact you asap!</div>)
              : (<div className="title">Failed to send mail!</div>)
        }
      </div>
    );
  },
});
