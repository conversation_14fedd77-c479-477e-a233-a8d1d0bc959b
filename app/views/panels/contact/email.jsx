const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.contact.email',
  message: '',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],
  getInitialState() {
    return {
      confirmation: false,
    };
  },
  componentDidMount() {
    const text = this.actions.agent.getLocalStorage('AM');
    const messageInput = this.refs.MessageInput;
    messageInput.value = text;

    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'email', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'email open', agentData.Id, { source: 'rateplug' });
    }
  },

  showConfirmation(statusCode) {
    if (statusCode != 200) {
      this.setState({ confirmation: 'failed' });
    } else {
      this.setState({ confirmation: 'success' });
    }
  },
  submitEmail(e) {
    e.preventDefault();
    const messageInput = this.refs.MessageInput;
    console.log(messageInput.value);
    // if(!this.props.buyerData || !this.props.buyerData.FirstName) {
    //  this.actions.agent.setContactLocalStorage("AM", messageInput.value);
    // /// this.props.checkLogin ? this.props.checkLogin() : '';
    // }
    //  else {
    const emailInput = this.refs.EmailInput;
    const nameInput = this.refs.NameInput;
    // var messageInput = this.refs.MessageInput;
    console.log(`email=${emailInput.value}and message=${messageInput.value}`);
    this.actions.agent.submitContactEmail((nameInput.value || this.props.buyerData.FirstName), emailInput.value, messageInput.value, this.props.showDetails && this.props.listingId ? this.props.listingId : '', this.props.agentData.Id, this.showConfirmation);
    this.props.clearLocalStorage ? this.props.clearLocalStorage() : '';

    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'email send', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'email send', agentData.Id, { source: 'rateplug' });
    }
    // }
    this.props.completedAction();
  },
  handleChange(event) {
    this.setState({ value: event.target.value });
  },

  render() {
    return (
      <div>
        {!this.state.confirmation
          ? (
            <form className="email-form" onSubmit={this.submitEmail}>
              <p className="title">Want to Learn More?</p>
              <div className="col-1-1">
                <input
                  className="form-control mb5"
                  placeholder="Your Name"
                  required
                  type="text"
                  ref="NameInput"
                  defaultValue={this.props.buyerData ? this.props.buyerData.FirstName : null}
                  onChange={this.handleChange}
                />
                <input
                  className="form-control mb5"
                  placeholder="Your Email"
                  type="email"
                  required
                  ref="EmailInput"
                  defaultValue={this.props.buyerData ? this.props.buyerData.Email : null}
                  onChange={this.handleChange}
                />

                <textarea
                  className="form-control mb5"
                  placeholder="Your Message"
                  type="text"
                  ref="MessageInput"
                  rows="5"
                  required
                  onChange={this.handleChange}
                />
                <div className="text-right">
                  <button type="submit" className="btn btn-default mrn">
                    Send
                  </button>
                </div>
              </div>
            </form>
          )
          : this.state.confirmation === 'success'
            ? (<div className="title">Your mail has been sent. Agent will contact you asap!</div>)
            : (<div className="title">Failed to send mail!</div>)}
      </div>
    );
  },
});
