const React = require('react');
const mixins = require('../../../lib/mixins');
const CloseButton = require('../../components/close_btn_cross');

module.exports = React.createClass({
  displayName: 'panel.contact.listingDetail',
  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],
  getInitialState() {
    return {
      data: null,
      noPhotoAvailable: false,
      show: true,
    };
  },
  cursors: {
    show: ['shared', 'contact', 'showDetails'],
    showOnlyRangeLivingSquare: ['shared', 'agent', 'mlsData', 'ShowOnlyRangeLivingSquare'],
  },
  close() {
    this.actions.agent.hideDetails();
  },

  componentDidMount() {
    this.checkStreetViewImage(this.props.listingDetailData);
  },

  getDetails() {
    if (!this.state.show) {
      return;
    }

    const squareFootage = this.utils.formatSquareFeet(this.props.listingDetailData, this.state.showOnlyRangeLivingSquare);
    const price = (this.props.listingDetailData.RangePriceFlag == 'Y' && this.props.listingDetailData.RangeHighPrice)
      ? `${this.utils.getDollarSymbol(this.props.listingDetailData)}${this.utils.addThousandSep(this.props.listingDetailData.RangeHighPrice)}`
      : this.props.listingDetailData.ListPrice
        ? `${this.utils.getDollarSymbol(this.props.listingDetailData)}${this.utils.addThousandSep(this.props.listingDetailData.ListPrice)}`
        : 'Price Not Provided';

    return (
      <div>
        <CloseButton onClick={this.close} />
        {
          this.props.listingDetailData.Images && this.props.listingDetailData.Images.length > 0 && this.props.listingDetailData.Images[0].lg ? (
            <img alt="Streetview" src={this.state.noPhotoAvailable ? this.utils.streetViewNotAvailableImage() : this.props.listingDetailData.Images[0].lg} />) : null
        }

        <div className="details">

          <p className="header mb5">HOME OF INTEREST</p>
          <span className="bold">{price}</span>
          <p className="m0">{this.props.listingDetailData && this.props.listingDetailData.Address ? this.props.listingDetailData.Address.FullStreetAddress : null}</p>
          {this.props.listingDetailData && this.props.listingDetailData.Address
            ? (
              <p>
                <span>
                  {this.props.listingDetailData.Address.CityName}
                  ,
                  &nbsp;
                </span>
                &nbsp;
                <span>
                  {this.props.listingDetailData.Address.State}
                  &nbsp;
                </span>
                &nbsp;
              </p>
            ) : null}
          <span className="pipe">
            &nbsp;
            {this.props.listingDetailData.TotalBedrooms || this.props.listingDetailData.Rooms.TotalBedrooms}
            &nbsp;
            beds
            &nbsp;
          </span>
          <span className="pipe">
            &nbsp;
            {this.props.listingDetailData.TotalBaths || this.props.listingDetailData.Rooms.TotalBaths}
            &nbsp;
            baths
            &nbsp;
          </span>
          <span>
            {squareFootage}
            &nbsp;
            sqft
          </span>
        </div>
      </div>
    );
  },

  checkStreetViewImage(listingData) {
    if (listingData.Images && listingData.Images.length > 0
      && (listingData.Images[0].sm || '').indexOf('maps.googleapis.com/maps/api/streetview') !== -1) {
      // this.actions.map.getStreetViewAvailability(listingData).then(function (res) {
      //  if (res === false) {
      this.setState({ noPhotoAvailable: true });
      //  }
      // }.bind(this))
    }
  },

  render() {
    if (!this.props.listingDetailData) {
      return null;
    }
    return (
      <div>
        {this.getDetails()}
      </div>
    );
  },
});
