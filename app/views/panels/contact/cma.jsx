const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.contact.cma',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],
  getInitialState() {
    return {
      confirmation: false,
      submitting: false,
    };
  },

  componentDidMount() {
    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'CMA', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'cma open', agentData.Id, { source: 'rateplug' });
    }
  },
  showConfirmation(statusCode) {
    if (statusCode != 200) {
      this.setState({ confirmation: 'failed', submitting: false });
    } else {
      this.setState({ confirmation: 'success', submitting: false });
    }
  },
  onSubmit(e) {
    e.preventDefault();
    // if(!this.props.buyerData || !this.props.buyerData.FirstName) {
    //  this.props.checkLogin ? this.props.checkLogin() : ''
    // }
    // else {
    const emailInput = this.refs.EmailInput;
    const nameInput = this.refs.NameInput;
    this.setState({ submitting: true });
    this.actions.agent.submitCMA(nameInput.value, emailInput.value, this.props.showDetails && this.props.listingId ? this.props.listingId : '', this.props.agentData.Id, this.showConfirmation);

    const agentData = this.actions.common.getAgentData();
    this.actions.analytics.sendEvent('agent contact cards', 'CMA send', `${agentData.NameLookup} ${agentData.Id}`);
    // For Rateplug
    if (window.rateplug.rp_buyer) {
      this.actions.analytics.sendEvent('rateplug', 'contact agent', 'cma send', agentData.Id, { source: 'rateplug' });
    }
    // }
    this.props.completedAction();
  },
  handleChange(event) {
    this.setState({ value: event.target.value });
  },

  render() {
    return (
      <div>
        {
          !this.state.confirmation ? (
            <form className="showing-form" onSubmit={this.onSubmit}>
              <p className="title">Request a Comparative Market Analysis on this home</p>
              <div className="input-group">
                <input
                  className="form-control mb5"
                  placeholder="Your Name"
                  type="text"
                  ref="NameInput"
                  value={this.props.buyerData ? this.props.buyerData.FirstName : null}
                  onChange={this.handleChange}
                />
                <input
                  className="form-control mb5 pull-right"
                  placeholder="Your Email"
                  type="email"
                  required
                  ref="EmailInput"
                  defaultValue={this.props.buyerData ? this.props.buyerData.Email : null}
                  onChange={this.handleChange}
                />

                <span className="input-group-btn">
                  <button type="submit" className="btn btn-default" disabled={this.state.submitting}>
                    Send Request
                  </button>
                </span>
              </div>
            </form>
          )
            : this.state.confirmation === 'success'
              ? (<div className="title">Your mail has been sent. Agent will contact you asap!</div>)
              : (<div className="title">Failed to send mail!</div>)
        }
      </div>
    );
  },
});
