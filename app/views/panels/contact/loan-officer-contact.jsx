const React = require('react');
const classNames = require('classnames');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const CloseButton = require('../../components/close_btn_cross');

const EMAIL_TAB = 'Email';
const CALL_TAB = 'Call';

module.exports = React.createClass({
  displayName: 'panels.listing.loan-officer-contact',
  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender, mixins.router],

  cursors: {
    showLoanOfficerContact: ['shared', 'contact', 'showLoanOfficerContact'],
    activeTab: ['shared', 'contact', 'activeTab'],
    loanOfficerData: ['shared', 'contact', 'loanOfficerData'],
  },

  getInitialState() {
    return {
      contactResult: null,
      contactSubject: '',
      contactMessage: '',
      submitting: false,
      confirmation: false,
    };
  },

  componentDidMount() {
    // Send analytics event when modal opens
    if (this.props.loData) {
      this.actions.analytics.sendEvent('rateplug', 'loan officer contact modal', 'open');
    }
  },

  hide(clearData) {
    this.actions.agent.hideLoanOfficerContact(clearData);
  },

  closeModal() {
    this.hide(true);
  },

  tabItemClick(item) {
    this.setState({ activeTab: item });
  },

  submitEmail(e) {
    e.preventDefault();

    this.setState({ submitting: true });

    const subject = this.refs.SubjectInput.value;
    const message = this.refs.MessageInput.value;

    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'email send');

    this.utils.sendLOMessage({
      CustomerGuid: window.rateplug.rp_buyer,
      LOGuid: window.rateplug.rp_lo,
      ContactSubject: subject,
      ContactMessage: message,
    }, (result) => {
      if (result && !result.ErrorMessage) {
        this.setState({
          contactResult: result,
          submitting: false,
          confirmation: 'success',
        });
      } else {
        this.setState({
          contactResult: result,
          submitting: false,
          confirmation: 'failed',
        });
      }
    });
  },

  tabs() {
    const tabs = [
      { Tag: CALL_TAB, Icon: 'icon-phone' },
      { Tag: EMAIL_TAB, Icon: 'icon-email' },
    ];

    return (
      <div className="holder">
        {tabs.map(function (item) {
          return (
            <a
              role="button"
              tabIndex="-1"
              className={classNames('options', { 'two-count': true }, { active: this.state.activeTab === item.Tag })}
              key={item.Tag}
              onClick={this.tabItemClick.bind(this, item.Tag)}
            >
              <SVGIcon className="options-icon" name={item.Icon} />
              <p>{item.Tag}</p>
            </a>
          );
        }, this)}
      </div>
    );
  },

  panel() {
    const loData = this.state.loanOfficerData || this.props.loData;

    if (!loData) {
      return <div>No loan officer data available</div>;
    }

    const formattedPhone = loData.Phone ? this.utils.formatPhone(loData.Phone) : '';

    if (this.state.activeTab === CALL_TAB) {
      return (
        <div
          role="button"
          tabIndex={-1}
          onClick={() => {
            if (formattedPhone) {
              this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'phone open');
              window.open(`tel:${formattedPhone}`, '_blank');
            }
          }}
          className="call-panel"
        >
          <p className="title">Call Your Loan Officer</p>
          <p className="phone-number">{formattedPhone}</p>
          {loData.PhoneExtension ? (
            <p>
              Extension:
              {loData.PhoneExtension}
            </p>
          ) : null}
        </div>
      );
    }

    if (this.state.activeTab === EMAIL_TAB) {
      return (
        <div>
          {!this.state.confirmation
            ? (
              <form className="email-panel" onSubmit={this.submitEmail}>
                <p className="title">Message Your Loan Officer</p>
                <div className="col-1-1">
                  <input
                    className="form-control mb5"
                    placeholder="Subject"
                    required
                    type="text"
                    ref="SubjectInput"
                    defaultValue={this.props.program || ''}
                  />

                  <textarea
                    className="form-control mb5"
                    placeholder="Your Message"
                    type="text"
                    ref="MessageInput"
                    rows="5"
                    required
                  />

                  <div className="text-right">
                    <button
                      type="submit"
                      className="btn btn-default mrn"
                      disabled={this.state.submitting}
                    >
                      {this.state.submitting ? 'Sending...' : 'Send'}
                    </button>
                  </div>
                </div>
              </form>
            )
            : this.state.confirmation === 'success'
              ? (<div className="email-panel"><p className="title">{this.state.contactResult}</p></div>)
              : (<div className="email-panel"><p className="title">Failed to send message, please reach the loan officer directly using contact information above.</p></div>)}
        </div>
      );
    }

    return null;
  },

  render() {
    const loData = this.state.loanOfficerData || this.props.loData;

    if (!loData || (!this.state.showLoanOfficerContact && !this.props.show)) {
      return null;
    }

    return (
      <Modal
        show={this.state.showLoanOfficerContact || this.props.show}
        onHide={this.hide}
        className={classNames('agent-contact-modal col-lg-5 col-md-6 col-sm-16 col-xs-24', {
          'agent-contact-modal-mobile': this.props.mobile,
        })}
      >
        <CloseButton onClick={this.hide.bind(this, true)} />
        <div className="agent-contact">
          <div className="first-panel">
            <div className="content-holder">
              <div className="text-center mb15">
                <img src={loData.LogoMedTransparent} alt="Company Logo" width="150" />
              </div>
              <img src={loData.Picture} className="photo" alt="Profile pic" />
              <div className="broker-info">
                <p className="m0">
                  <strong>{`${loData.FirstName} ${loData.LastName}`}</strong>
                </p>
                <p className="broker-name">
                  <span>{loData.Licenses}</span>
                </p>
                <p className="broker-name">
                  <span>
                    {loData.Email}
                  </span>
                </p>
                <p className="broker-name">
                  <span>
                    {this.utils.formatPhone(loData.Phone)}
                  </span>
                  {loData.PhoneExtension ? (
                    <span>
                      &nbsp;Ext:&nbsp;
                      {loData.PhoneExtension}
                    </span>
                  ) : null}
                </p>
              </div>
            </div>
          </div>
          <div className="third-panel">
            {this.tabs()}
          </div>
          <div className="forth-panel">
            {this.panel()}
          </div>
        </div>
      </Modal>
    );
  },
});
