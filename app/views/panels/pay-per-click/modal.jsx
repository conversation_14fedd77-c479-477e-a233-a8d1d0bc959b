const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.pay-per-click.modal',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils, mixins.events],

  getInitialState() {
    return {
      submitting: false,
    };
  },

  cursors: {
    buyerId: ['shared', 'buyer', 'Id'],
    agentData: ['shared', 'agent', 'data'],
    showPayPerClickModal: ['layout', 'payPerClickModal'],
  },

  listingViewCount: 0,

  componentDidMount() {
    this.events.on(this.events.LISTING_VIEWED, () => {
      if (this.actions.common.forceLoginFirstInteraction() === 'ppc') {
        this.listingViewCount += 1;

        if (this.listingViewCount === 1) {
          // LISTING_VIEWED is triggered during route change
          // We need to add a delay because so the layout is not reset
          setTimeout(() => {
            this.actions.common.showPayPerClickModal();
          }, 100);
        }
      }
    });
  },

  modalClose() {
    // this.setState({ url: null });
    // this.actions.common.hidePayPerClickModal();
  },

  submit(e) {
    e.preventDefault();
    const buyer = ['firstName', 'lastName', 'email', 'phoneNumber'].reduce((map, name) => {
      map[name] = e.target.elements[name].value.trim();
      return map;
    }, {});

    this.actions.analytics.sendEvent('ppc', 'submit');

    this.setState({ submitting: true });
    this.actions.buyer.ppcBuyerRegister(buyer, () => {
      this.setState({ submitting: false });
      this.actions.common.hidePayPerClickModal();
    });
  },

  renderForm() {
    return (
      <div className="ppc-modal-content">
        <form onSubmit={this.submit}>
          <h3 className="mb15">
            Free Access to Exclusive Listings
          </h3>
          <p className="mb5">
            Sign in to receive instant alerts about
            new and updated listings. Updated every 5
            minutes. Find homes eligible for low/no down
            payment, have low rate assumable mortgages and
            down payment assistance.
          </p>
          <p className="mb15">
            <em>Your dream home could be just a notification away - don&apos;t miss out!</em>
          </p>

          <div className="col-1-2 pr5">
            <input type="text" className="form-control" name="firstName" placeholder="First Name*" required />
          </div>
          <div className="col-1-2 pl5">
            <input type="text" className="form-control" name="lastName" placeholder="Last Name*" required />
          </div>
          <div className="col-1-1 mt10">
            <input type="email" className="form-control" name="email" placeholder="Email*" required />
          </div>
          <div className="col-1-1 mt10">
            <input type="tel" className="form-control" name="phoneNumber" placeholder="Phone* (for instant text alerts)" required />
          </div>

          <div className="col-1-1 mt15">
            <button type="submit" className="btn btn-success btn-block pt10 pb10" disabled={this.state.submitting}>
              {this.state.submitting ? 'Loading...' : 'Continue to See All Listing Data'}
            </button>
          </div>

          <div className="ppc-modal-footer mt15">
            <small>
              By clicking continue, you agree to our&nbsp;
              <a role="link" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>
                Terms of Use
              </a>
              &nbsp;and&nbsp;
              <a role="link" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}>
                Privacy Policy
              </a>
              .
            </small>
          </div>
        </form>
      </div>
    );
  },

  render() {
    if (this.state.buyerId) {
      return null;
    }

    if (!this.state.showPayPerClickModal) {
      return null;
    }

    return (
      <Modal
        show={this.state.showPayPerClickModal}
        onHide={this.modalClose}
        id="ppc-modal"
        className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
      >
        {this.renderForm()}
      </Modal>
    );
  },
});
