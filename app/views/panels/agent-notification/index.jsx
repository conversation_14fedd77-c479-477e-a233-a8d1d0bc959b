const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const mixins = require('../../../lib/mixins/index');
const CloseBtn = require('../../components/close_btn_cross');

module.exports = React.createClass({

  displayName: 'panels.agent-notification',

  mixins: [mixins.debug, mixins.cursors, mixins.router, mixins.actions, mixins.pureRender],

  cursors: {
    READAgent: ['shared', 'detectedAgent'],
  },

  message() {
    const status = this.state.READAgent.status;
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.FINISH_SETUP) {
      return (
        <p className="message">
          <strong>Boost traffic to your site</strong>
          <br />
          Add a payment method to have Search Alliance send local traffic and leads directly to your site!
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PAUSED) {
      return (
        <p className="message">
          <strong>Boost traffic to your site</strong>
          <br />
          Reactivate your budget to have Search Alliance send local traffic and leads directly to your site!
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PENDING) {
      return (
        <p className="message">
          <strong>Your IDX Site is Pending MLS Approval</strong>
          <br />
          Your required MLS approval documents are still pending signature or final MLS approval.
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.COMPLETE) {
      return <p className="message">Your site is ready to receive local leads and traffic.</p>;
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.GET_APP) {
      return (
        <p className="message">
          <strong>Download the free mobile app</strong>
          <br />
          Get lead notifications on the go and manage potential clients directly from your mobile device.
        </p>
      );
    }

    return null;
  },

  smallMessage() {
    const status = this.state.READAgent.status;
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.FINISH_SETUP) {
      return (
        <p className="message">
          <a
            role="button"
            tabIndex="-1"
            onClick={() => {
              this.openActionLink(status);
            }}
          >
            Boost traffic to your site
          </a>
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PAUSED) {
      return (
        <p className="message">
          <a
            role="button"
            tabIndex="-1"
            onClick={() => {
              this.openActionLink(status);
            }}
          >
            Boost traffic to your site
          </a>
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PENDING) {
      return (
        <p className="message">
          <a
            role="button"
            tabIndex="-1"
            onClick={() => {
              this.openActionLink(status);
            }}
          >
            IDX Site Status: Pending Approval
          </a>
        </p>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.GET_APP) {
      return (
        <p className="message">
          <a
            role="button"
            tabIndex="-1"
            onClick={() => {
              this.openActionLink(status);
            }}
          >
            Download the free mobile app!
          </a>
        </p>
      );
    }

    return null;
  },

  actionButton() {
    const status = this.state.READAgent.status;
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.FINISH_SETUP) {
      return (
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-primary"
          onClick={() => {
            this.openActionLink(status);
          }}
        >
          Finish Setup
        </a>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PAUSED) {
      return (
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-primary"
          onClick={() => {
            this.openActionLink(status);
          }}
        >
          Manage Account
        </a>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PENDING) {
      return (
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-primary"
          onClick={() => {
            this.openActionLink(status);
          }}
        >
          Check Dashboard
        </a>
      );
    }

    return null;
  },

  linkButton() {
    const status = this.state.READAgent.status;
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.COMPLETE) {
      return (
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-link"
          onClick={() => {
            this.openActionLink(status);
          }}
        >
          View Leads
        </a>
      );
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.GET_APP) {
      return (
        <div>
          <a
            role="button"
            tabIndex="-1"
            className="action-button btn btn-link"
            onClick={() => {
              this.openActionLink(status);
            }}
          >
            Get the App
          </a>
          <div><small>Currently available for iPhone only. Android coming soon.</small></div>
        </div>
      );
    }

    return null;
  },

  openActionLink() {
    const status = this.state.READAgent.status;
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.FINISH_SETUP) {
      return window.open(window.CONFIG.READ_URL.concat('SAControlPanel'), '_blank');
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PAUSED) {
      return window.open(window.CONFIG.READ_URL.concat('SAControlPanel'), '_blank');
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.PENDING) {
      return window.open(window.CONFIG.READ_URL.concat('SAControlPanel'), '_blank');
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.COMPLETE) {
      return window.open(window.CONFIG.READ_URL.concat('Leads'), '_blank');
    }
    if (status === this.actions.read.AGENT_NOTIFICATION_STATUS.GET_APP) {
      return window.open('https://itunes.apple.com/us/app/homeasap-agent/id1104979116?mt=8', '_blank');
    }
  },

  render() {
    if (!this.state.READAgent
      || !this.state.READAgent.type
      || !this.state.READAgent.status
      || !this.state.READAgent.data
      || !this.state.READAgent.data.isSearchAllianceMember) {
      return <noscript />;
    }

    if (['Agent', 'Featured'].includes(this.router.currentRoute && this.router.currentRoute.name)) {
      return <noscript />;
    }

    return (
      <div className="agent-notification-container">
        <ReactCSSTransitionGroup
          transitionName="agent-notification-transition"
          transitionAppear
          transitionAppearTimeout={300}
          transitionEnterTimeout={300}
          transitionLeaveTimeout={300}
        >
          {
            this.state.READAgent.hidden != this.state.READAgent.status
              ? (
                <div className="agent-notification-container-main">
                  <CloseBtn onClick={() => {
                    this.actions.read.setAgentNotificationHidden(this.state.READAgent.status);
                  }}
                  />
                  <p className="welcome">
                    {this.state.READAgent.status === this.actions.read.AGENT_NOTIFICATION_STATUS.COMPLETE ? 'Congrats' : 'Welcome'}
                  &nbsp;
                    {this.state.READAgent.data.firstName}
                    !
                  </p>
                  <div className="agent-notification-container-main-flex">
                    <div>
                      <img
                        alt="Agent"
                        className="agent-notification-container-agent-image"
                        src={
                          this.state.READAgent.status === this.actions.read.AGENT_NOTIFICATION_STATUS.COMPLETE
                            ? 'https://nplayassets.blob.core.windows.net/search2/round-check.jpg'
                            : this.state.READAgent.data.profile_image
                        }
                      />
                    </div>
                    <div className="agent-notification-container-main-message">
                      {this.message()}
                      <div className="mb5">{this.linkButton()}</div>
                    </div>
                  </div>
                  <div>{this.actionButton()}</div>
                </div>
              )
              : null
          }
        </ReactCSSTransitionGroup>

        <ReactCSSTransitionGroup
          transitionName="agent-notification-sm-transition"
          transitionAppear
          transitionAppearTimeout={600}
          transitionEnterTimeout={600}
          transitionLeaveTimeout={300}
        >
          {
            this.state.READAgent.hidden == this.state.READAgent.status && this.smallMessage()
              ? (
                <div className="agent-notification-container-small">
                  <img alt="Agent" className="agent-notification-container-agent-image" src={this.state.READAgent.data.profile_image} />
                  {this.smallMessage()}
                </div>
              )
              : null
          }
        </ReactCSSTransitionGroup>
      </div>
    );
  },
});
