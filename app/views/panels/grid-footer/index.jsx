const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins');
const WhyUseHomeasap = require('../whyusehomeasap');
const MLSDisclosure = require('../mls-disclosure');
const LastRefreshed = require('../last-refreshed');

module.exports = React.createClass({

  displayName: 'screens.grid.grid-footer',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.pureRender],

  cursors: {
    agentMlsId: ['shared', 'agent', 'data', 'MlsId'],
  },

  mlsDisclosures() {
    const mlsIds = this.props.mlsIds;

    if (!mlsIds || mlsIds.length < 1) {
      return (
        <div className="col-1-2 mb15">
          <MLSDisclosure mlsId={this.state.agentMlsId} />
        </div>
      );
    }

    return map(mlsIds, (mlsId) => (
      <div className="col-1-2 mb15" key={mlsId}>
        <MLSDisclosure mlsId={mlsId} />
      </div>
    ));
  },

  render() {
    return (
      <div className={this.props.className}>
        <div className="grid-footer-container">
          {this.mlsDisclosures()}
          <div className="col-1-2 mb15">
            <div className="whyusehomeasap-logo">
              <a href="https://about.homeasap.com" target="_blank">&nbsp;</a>
            </div>
            <WhyUseHomeasap />
          </div>
          <div className="col-1-2 mb15">
            <LastRefreshed />
          </div>
        </div>
      </div>
    );
  },

});
