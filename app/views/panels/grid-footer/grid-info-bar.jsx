const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'screens.grid.grid-footer.grid-info-bar',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.pureRender],

  facets: {
    headerControlDisabled: ['headerControlDisabled'],
  },

  mapClicked() {
    this.actions.map.onNav();
  },

  data(c, ref) {
    return (
      <div className={classNames('grid-info-bar', this.props.className, c)} ref={ref}>
        {/*
        <div className="grid-info-logo pull-left">
          <a href="https://about.homeasap.com" target="_blank">
            <SVGIcon name="logo-inline" />
          </a>
        </div>
      */}
        <div className="grid-info-exit pull-right">
          <a
            role="button"
            tabIndex="-1"
            className="btn btn-sm btn-primary"
            onClick={this.mapClicked}
          >
            <SVGIcon name="icon-map" />
            BACK TO MAP
          </a>
        </div>
      </div>
    );
  },

  render() {
    if (this.state.headerControlDisabled) {
      return null;
    }

    return (
      <div>
        {/* this.data(null, "block") */}
        {this.data('fixed', 'fixed')}
      </div>
    );
  },

});
