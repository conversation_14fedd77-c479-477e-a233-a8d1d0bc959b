const React = require('react');
const classNames = require('classnames');
const clone = require('clone');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.photos.caption',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    photoCaptions: ['shared', 'photo', 'captions'],
    options: ['user', 'photoSliderOptions'],
  },

  captionsBeingLoadedForId: '',

  componentDidMount() {
    this.updatePhotoCaptions(this.props, this.state);
  },

  shouldComponentUpdate(nextProps, nextState) {
    this.updatePhotoCaptions(nextProps, nextState);
    return true;
  },

  updatePhotoCaptions(props, state) {
    if (props.listing.Id !== this.captionsBeingLoadedForId
      && props.listing.Id !== state.photoCaptions.Id) {
      this.captionsBeingLoadedForId = props.listing.Id;
      this.actions.common.getPhotoCaptions(props.listing);
    }
  },

  toggleThumbnails(e, shouldShow) {
    e.preventDefault();

    const options = clone(this.state.options);
    options.thumbnails = typeof shouldShow === 'boolean' ? shouldShow : !options.thumbnails;
    if (options.thumbnails !== this.state.options.thumbnails) {
      this.actions.common.setPhotoSliderOptions(options);
    }
  },

  getCaption() {
    if (!this.props.listing || !this.state.photoCaptions) {
      return '';
    }

    if (this.props.listing.REALData && this.state.photoCaptions.REALData) {
      if (this.props.listing.Id === this.state.photoCaptions.Id) {
        return this.state.photoCaptions && this.state.photoCaptions.Captions
          && this.state.photoCaptions.Captions[this.props.index]
          ? ` | ${this.state.photoCaptions.Captions[this.props.index]}` : '';
      }
    }

    if (this.state.photoCaptions
      && (
        this.props.listing.MlsIds && this.props.listing.MlsIds[0]
        && (this.props.listing.MlsIds[0] === this.state.photoCaptions.MlsId)
      )
      && (
        this.props.listing.PropertyListingId
        && (this.props.listing.PropertyListingId === (this.state.photoCaptions.PropertyListingId || this.state.photoCaptions.ListingId))
      )
    ) {
      return this.state.photoCaptions && this.state.photoCaptions.Captions
        && this.state.photoCaptions.Captions[this.props.index]
        ? ` | ${this.state.photoCaptions.Captions[this.props.index]}` : '';
    }
    return '';
  },

  getAddress() {
    // if (this.props.listing && this.props.listing.Address) {
    //  return this.props.listing.Address.FullStreetAddress + ", " +
    //    this.props.listing.Address.CityName + ", " +
    //    this.props.listing.Address.State + " " + this.props.listing.Address.ZipCode
    // }
    return this.props.listing && this.props.listing.FullStreetAddress;
  },

  render() {
    return (
      <div className={classNames('photo--caption', this.props.className)}>
        <p className="pull-left text-left">
          {this.props.index + 1}
          &nbsp;
          /
          {this.props.count}
          {this.getCaption()}
        </p>

        <div role="button" tabIndex="-1" aria-label="Show thumbnails" className="btn toggle" onClick={this.toggleThumbnails} title="Show/hide thumbnails">
          <SVGIcon name="icon-back-to-top" className={classNames({ 'flip-vertical': this.state.options.thumbnails })} />
        </div>

        <p className="pull-right text-right">
          {this.getAddress() || ''}
        </p>
      </div>
    );
  },

});
