const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.photos.primary',

  mixins: [mixins.debug, mixins.utils, mixins.initState, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    url: React.PropTypes.string,
  },

  getInitialState() {
    return {
      playing: false,
      active: false,
    };
  },

  hasLoaded: false,

  componentDidMount() {
    if (this.props.active) {
      setTimeout(() => {
        this.setState({ active: true });
      }, 10);
    }
    if (this.props.playing) {
      setTimeout(() => {
        this.setState({ playing: true });
      }, 20);
    }
  },

  componentWillReceiveProps(nextProps) {
    if (!nextProps.playing && !nextProps.active) {
      this.setState(this.getInitialState());
      return;
    }
    if (nextProps.playing && (!this.props || !this.props.playing)) {
      this.setState({ active: false });
      setTimeout(() => {
        this.setState({ active: true });
      }, 10);
      setTimeout(() => {
        this.setState({ playing: true });
      }, 20);
    } else {
      if (this.state.playing) {
        this.setState({ playing: false });
      }
      if (this.state.active !== nextProps.active) {
        this.setState({ active: nextProps.active });
      }
    }
  },

  componentWillLeave() {
    this.setState(this.getInitialState());
  },

  render() {
    if (this.props.shouldPreload) {
      this.hasLoaded = true;
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Open photo"
        className={classNames('photo', this.props.className,
          {
            active: this.state.active,
            playing: this.state.playing,
          })}
        style={{ backgroundImage: 'url(\''.concat(this.hasLoaded ? this.props.url : this.utils.emptyImageUrl, '\')') }}
        onClick={function (e) {
          e.stopPropagation();
        }}
      >
        <img alt="Listing" src={this.hasLoaded ? this.props.url : this.utils.emptyImageUrl} />
      </div>
    );
  },
});
