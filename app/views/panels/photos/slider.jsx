const React = require('react');
const ReactDOM = require('react-dom');
// scrollIntoView = require('scroll-into-view'),
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.photos.slider',

  mixins: [mixins.debug, mixins.pureRender, mixins.actions],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    url: React.PropTypes.string,
    index: React.PropTypes.number,
    isActive: React.PropTypes.bool,
    onActive: React.PropTypes.func,
  },

  componentWillReceiveProps(nextProps) {
    if (nextProps.isActive) {
      const target = ReactDOM.findDOMNode(this);
      if (target.parentNode.scrollLeft < (target.offsetLeft - target.parentNode.offsetWidth + target.offsetWidth)) {
        target.parentNode.scrollLeft = (target.offsetLeft - target.parentNode.offsetWidth + target.offsetWidth);
      } else if (target.parentNode.scrollLeft > target.offsetLeft) {
        target.parentNode.scrollLeft = target.offsetLeft;
      }
    }
  },

  onActive(e) {
    e.preventDefault();
    this.props.onActive(this.props.index);

    this.actions.analytics.sendEvent('photo viewer', 'thumbnail click');
  },

  render() {
    return (
      <li onClick={this.onActive}>
        <img
          alt="Listing slider"
          className={this.props.isActive ? 'active' : null}
          src={this.props.url}
        />
      </li>
    );
  },

});
