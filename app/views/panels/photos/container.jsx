const React = require('react');
const ReactDOM = require('react-dom');
const clone = require('clone');
const map = require('lodash.map');
const classNames = require('classnames');
const screenfull = require('screenfull');
const Slider = require('./slider');
const Primary = require('./primary');
const Caption = require('./caption');
const mixins = require('../../../lib/mixins');
const ShareListing = require('../../components/share_listing');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.photos.container',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.events],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    data: React.PropTypes.array,
  },

  getInitialState() {
    return {
      active: -1,
      // this is a hack until we do a better way to layout the close button
      shiftCloseButton: false,
      fullScreen: false,
      mouseStopped: false,
      data: null,
    };
  },

  cursors: {
    photoSliderIndex: ['shared', 'photoSliderIndex'],
    options: ['user', 'photoSliderOptions'],
    buyerLoggedIn: ['shared', 'buyer', 'Id'],
  },

  componentWillMount() {
    if (window.location.href.match(/\/agent/)) {
      this.setState({ shiftCloseButton: true });
    }
    this.setState({ data: this.props.data });
  },

  componentWillReceiveProps() {
    this.initialPlayIndex = 0;
    this.setState({ data: null, active: 0 });
    setTimeout(() => {
      this.setState({ data: this.props.data });
      this.setActive(0);
    }, 0);
  },

  componentDidMount() {
    this.setActive(this.state.photoSliderIndex || 0);
    document.addEventListener('keydown', this.handleKeydown, false);
    if (this.state.options.play) {
      this.startPlay();
    }
  },

  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeydown);
    // this.preLoadImage = null
    this.pausePlay();
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.options.play && !this.playTimeout && nextState.active !== this.props.data.length) {
      // this.setState({active: 0})

      this.startPlay();
    }

    return true;
  },

  startPlay() {
    if (this.playTimeout) {
      clearTimeout(this.playTimeout);
      this.playTimeout = null;
    }

    this.playTimeout = setTimeout(() => {
      this.initialPlayIndex = null;
      this.nextPic();
    }, 5000);
  },

  pausePlay() {
    if (this.playTimeout) {
      clearTimeout(this.playTimeout);
      this.playTimeout = null;
    }
  },

  playPauseButtonClicked(e) {
    e.preventDefault();
    e.stopPropagation();

    const options = clone(this.state.options);
    options.play = !options.play;
    if (!options.play) {
      this.pausePlay();
    }
    this.initialPlayIndex = options.play ? this.state.active : null;
    this.actions.common.setPhotoSliderOptions(options);

    this.actions.analytics.sendEvent('photo viewer', options.play ? 'play' : 'pause');
  },

  nextPic(e) {
    e ? e.stopPropagation() : '';
    this.state.active === (this.props.data.length + 1 - 1)
      ? this.setActive(0)
      : this.setActive(this.state.active + 1);

    this.actions.analytics.sendEvent('photo viewer', 'next');

    this.events.emit(this.events.GALLERY_PHOTO_VIEWED);
  },

  prevPic(e) {
    e ? e.stopPropagation() : '';
    this.state.active
      ? this.setActive(this.state.active - 1)
      : this.setActive(this.props.data.length + 1 - 1);

    this.actions.analytics.sendEvent('photo viewer', 'prev');
  },

  setActive(i) {
    if (this.state.options.play && i !== this.props.data.length) {
      this.startPlay();
    } else {
      this.pausePlay();
    }
    if (i === this.state.active) {
      return;
    }

    setTimeout(() => {
      this.setState({ active: i });
    }, 0);

    if (i === this.props.data.length) {
      window.sendEvent('photo viewer options', 'opened');
    }

    // Commented out because we are now loading everything at once
    // var nxtPhotoIdx = this.state.active === (this.props.data.length + 1 - 1)
    //  ? 0 : this.state.active + 1
    // this.preLoadImage = null
    // this.preLoadImage = new Image()
    // this.preLoadImage.src = this.props.data[nxtPhotoIdx].lg || '//:0'
  },

  onClose(e) {
    e.preventDefault();

    if (this.state.photoSliderIndex !== 0) {
      this.actions.common.setPhotoSliderIndex(this.state.active);
    }
    this.actions.panels.reset('photos', true);

    if (this.state.fullScreen) {
      screenfull.exit();
    }
  },

  handleKeydown(e) {
    switch (e.keyCode) {
      case 27: // escape
        e.preventDefault();
        e.stopPropagation();
        this.initialPlayIndex = null;
        this.actions.panels.reset('photos', true);
        break;
      case 39: // right
        e.preventDefault();
        e.stopPropagation();
        this.initialPlayIndex = null;
        this.nextPic();
        break;
      case 37: // left
        e.preventDefault();
        e.stopPropagation();
        this.initialPlayIndex = null;
        this.prevPic();
        break;
      case 38: // up
        e.preventDefault();
        e.stopPropagation();
        this.initialPlayIndex = null;
        this.refs.Caption.toggleThumbnails(e, true);
        break;
      case 40: // down
        e.preventDefault();
        e.stopPropagation();
        this.initialPlayIndex = null;
        this.refs.Caption.toggleThumbnails(e, false);
        break;
      default:
        break;
    }
  },

  fullScreenChanged() {
    if (!screenfull.isFullscreen) {
      // Full screen switched off
      this.setState({ fullScreen: false });
      document.removeEventListener(screenfull.raw.fullscreenchange, this.fullScreenChanged);

      ReactDOM.findDOMNode(this).removeEventListener('mousemove', this.fullScreenMouseMove);
      this.removeMouseStoppedTimeout();
    }
  },

  mouseStoppedTimeout: null,
  removeMouseStoppedTimeout() {
    if (this.mouseStoppedTimeout) {
      clearTimeout(this.mouseStoppedTimeout);
      this.mouseStoppedTimeout = null;
    }
    if (this.state.mouseStopped) {
      this.setState({ mouseStopped: false });
    }
  },
  fullScreenMouseMove() {
    this.removeMouseStoppedTimeout();
    this.mouseStoppedTimeout = setTimeout(() => {
      this.setState({ mouseStopped: true });
    }, 2000);
  },

  onFullScreen(e) {
    e.preventDefault();

    if (screenfull.enabled) {
      if (!this.state.fullScreen) {
        screenfull.request();
        this.setState({ fullScreen: true });
        this.refs.Caption.toggleThumbnails(e, false);

        document.addEventListener(screenfull.raw.fullscreenchange, this.fullScreenChanged);
        ReactDOM.findDOMNode(this).addEventListener('mousemove', this.fullScreenMouseMove);
        this.mouseStoppedTimeout = setTimeout(() => {
          this.setState({ mouseStopped: true });
        }, 2000);
      } else {
        screenfull.exit();
      }
    }
  },

  setAgentContact(activeTab) {
    console.log(`${activeTab}is the tab`);
    this.actions.agent.setActiveTab(activeTab);
    console.log(`the property is ${this.props.listing}`);
    this.actions.agent.showAgentContact(this.props.listing);

    window.sendEvent('photo viewer options', `agent contact - ${activeTab}` || '');
  },

  receiveInstantUpdates() {
    if (!this.state.buyerLoggedIn) {
      this.events.emit(this.events.PROMPT_FOR_LOGIN, {
        loginButtonText: 'Login to receive updates',
        modalType: 'photo_gallery_receive_updates',
      });
      window.sendEvent('photo viewer options', 'Receive Instant Updates');
    }
  },

  render() {
    return (
      <div className={classNames('photo-component',
        {
          'layout--2f-3': !this.props.fullScreen,
          'layout--full': this.props.fullScreen,
          'photo-full': this.state.fullScreen,
          playing: this.playTimeout,
          'mouse-stopped': this.state.mouseStopped,
        })}
      >

        <span
          role="button"
          tabIndex="-1"
          aria-label="Close"
          className="close"
          style={this.state.shiftCloseButton ? { top: '6em' } : {}}
          onClick={this.onClose}
        >
          <SVGIcon name="icon-close-button" className="tag-close-button" />
        </span>

        {
          screenfull.enabled
            ? (
              <span
                role="button"
                tabIndex="-1"
                aria-label="Full screen"
                className="full-screen"
                style={this.state.shiftCloseButton ? { top: '6em' } : {}}
                onClick={this.onFullScreen}
              >
                {
                  this.state.fullScreen
                    ? <SVGIcon name="icon-exit-fullscreen" /> : null
                }
                {
                  !this.state.fullScreen
                    ? <SVGIcon name="icon-fullscreen" /> : null
                }
              </span>
            ) : null
        }

        <div role="button" tabIndex="-1" aria-label="Close" className="photo--primary" onClick={this.onClose}>
          {
            map(this.state.data, function (imgObj, idx) {
              return (
                <Primary
                  url={imgObj.lg}
                  key={imgObj.lg}
                  index={idx}
                  className={classNames({ in: idx === this.initialPlayIndex })}
                  active={this.state.active == idx}
                  playing={this.state.active == idx && !!this.playTimeout}
                  shouldPreload={Math.abs(this.state.active - idx) < 3}
                />
              );
            }, this)
          }

          <div
            className={classNames('photo more-container',
              { active: this.state.active == this.props.data.length })}
            style={{
              backgroundImage: 'url(\''.concat(this.state.data && this.state.data.length > 0 ? this.state.data[this.state.data.length - 1].lg : this.utils.emptyImageUrl, '\')'),
              backgroundSize: 'cover',
            }}
          >
            <div className="more-container-darken" />
            <div
              role="button"
              tabIndex="-1"
              aria-label="More"
              className="more"
              onClick={function (e) {
                e.stopPropagation();
              }}
            >
              <h1 className="text-center">
                Wow, this is an awesome house!
                &nbsp;
                <br />
                &nbsp;
                <em>Would you like to...</em>
              </h1>
              <div role="button" tabIndex="-1" aria-label="Receive updates" className={'col-lg-1-3 col-md-1-2 col-sm-1-1 col-1-1'.concat(this.state.buyerLoggedIn ? ' disabled' : null)} onClick={this.receiveInstantUpdates}>
                <SVGIcon name="icon-envelope" />
                <p>
                  Receive Instant
                  <br />
                  Updates
                </p>
              </div>
              <div role="button" tabIndex="-1" aria-label="Ask question" className="col-lg-1-3 col-md-1-2 col-sm-1-1 col-1-1" onClick={this.setAgentContact.bind(this, 'Email')}>
                <SVGIcon name="icon-ask-a-question" />
                <p>Ask a Question</p>
              </div>
              <div role="button" tabIndex="-1" aria-label="Visit this home" className="col-lg-1-3 col-md-1-2 col-sm-1-1 col-1-1" onClick={this.setAgentContact.bind(this, 'Showing')}>
                <SVGIcon name="icon-visit-home" />
                <p>Visit This Home</p>
              </div>
              <div
                role="button"
                tabIndex="-1"
                aria-label="See more homes"
                className="col-lg-1-3 col-md-1-2 col-sm-1-1 col-1-1"
                onClick={function () {
                  window.sendEvent('photo viewer options', 'see more homes like this');
                  this.actions.menu.seeMoreHomesLikeThis(this.props.listing);
                }.bind(this)}
              >
                <SVGIcon name="icon-see-more-homes" />
                <p>
                  See More Homes
                  <br />
                  Like This
                </p>
              </div>
              <div className="col-lg-1-3 col-md-1-2 col-sm-1-1 col-1-1">
                <ShareListing listing={this.props.listing} alwaysShow />
                <p>
                  Share with
                  <br />
                  Family &amp; Friends
                </p>
              </div>
              <p className="text-center back-to-last">
                <a role="button" tabIndex="-1" onClick={this.prevPic}>Back to previous photo</a>
              </p>
            </div>
          </div>

          <div
            role="button"
            tabIndex="-1"
            aria-label="Next"
            className={classNames('next-container', { hidden: !this.state.data || this.state.data.length + 1 < 2 })}
            onClick={this.nextPic}
          >
            <span
              className="next"
            >
              <SVGIcon name="icon-chevron-right" />
            </span>
          </div>

          <div
            role="button"
            tabIndex="-1"
            aria-label="Previous"
            className={classNames('prev-container', { hidden: !this.state.data || this.state.data.length + 1 < 2 })}
            onClick={this.prevPic}
          >
            <span
              className="prev"
            >
              <SVGIcon name="icon-chevron-left" />
            </span>
          </div>

          <div role="button" tabIndex="-1" aria-label="Play" className="play-pause-button-container" onClick={this.playPauseButtonClicked}>
            {
              this.state.options.play
                ? <SVGIcon name="icon-pause" /> : null
            }
            {
              !this.state.options.play
                ? <SVGIcon name="icon-play" /> : null
            }
          </div>
        </div>

        <div className={classNames('photo--slider',
          {
            'slide-up-enter slide-up-enter-active': this.state.options.thumbnails,
            'slide-up-leave slide-up-leave-active': !this.state.options.thumbnails,
          })}
        >

          <Caption
            index={this.state.active}
            count={this.props.data.length + 1}
            listing={this.props.listing}
            ref="Caption"
          />

          <div className="photo--scrollable-container">
            <ul className="photo--scrollable">

              {map(this.props.data, function (img, i) {
                return (
                  <Slider
                    key={img.sm}
                    url={img.sm}
                    index={i}
                    onActive={this.setActive}
                    isActive={this.state.active === i}
                  />
                );
              }, this)}

              <Slider
                key="options"
                url={`${window.CONFIG.CDN_URL}search2/logo-thumbnail.png`}
                index={this.props.data.length}
                onActive={this.setActive}
                isActive={this.state.active === this.props.data.length}
              />

            </ul>
          </div>
        </div>

      </div>
    );
  },

});
