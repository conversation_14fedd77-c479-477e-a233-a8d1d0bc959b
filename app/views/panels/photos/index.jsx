const React = require('react');
const Container = require('./container');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.photos',

  mixins: [mixins.cursors],

  cursors: {
    layout: ['layout', 'photos'],
    data: ['panels', 'photos', 'data'],
  },

  render() {
    return this.state.layout && this.state.data
      ? (
        <Container
          data={this.state.data.ImageUrls || []}
          listing={this.state.data}
          key={this.state.data.Id}
          fullScreen={this.state.layout === 'full'}
        />
      )
      : null;
  },

});
