const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.modals-blank',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'blankModal'],
  },
  show() {
    this.actions.common.showBlankModal();
  },
  hide() {
    this.actions.common.hideBlankModal();
  },

  render() {
    return (
      <Modal show={this.state.layout} onHide={this.hide} className="col-lg-5 col-md-6 col-sm-16 col-xs-24" />
    );
  },
});
