const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.agent-listing-notification',

  mixins: [mixins.debug, mixins.cursors, mixins.router, mixins.actions, mixins.pureRender, mixins.utils],

  cursors: {
    READAgent: ['shared', 'detectedAgent'],
    currentAgentId: ['shared', 'agent', 'data', 'Id'],
  },

  facets: {
    activeListingData: ['activeListingData'],
  },

  smallMessage() {
    return (
      <p className="message">
        {this.state.READAgent.data.firstName}
        , you can now advertise or create a landing page for this listing in
        <strong> MINUTES!</strong>
        !
      </p>
    );
  },

  actionButton() {
    return (
      <div>
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-sm btn-info"
          target="_blank"
          href={`${window.CONFIG.READ_URL}Ads/Listing/${this.state.activeListingData.Id}`}
        >
          Advertise
        </a>
        <a
          role="button"
          tabIndex="-1"
          className="action-button btn btn-sm btn-info"
          target="_blank"
          href={`${window.CONFIG.READ_URL}MyListings/Website/Templates/${this.state.activeListingData.Id}`}
        >
          Landing Page
        </a>
      </div>
    );
  },

  render() {
    if (!['Agent', 'Featured'].includes(this.router.currentRoute && this.router.currentRoute.name)) {
      return <noscript />;
    }

    if (!this.state.currentAgentId || !this.state.READAgent || !this.state.READAgent.data || !this.state.activeListingData) {
      return <noscript />;
    }

    if (this.state.currentAgentId !== this.state.READAgent.data.agentId) {
      return <noscript />;
    }

    if (this.utils.useMobileSite()) {
      return (
        <a
          className="agent-listing-notification-mobile-container"
          target="_blank"
          href={`${window.CONFIG.READ_URL}MyListings`}
        >
          Advertise or create a landing page in MINUTES!
        </a>
      );
    }

    return (
      <div className="agent-listing-notification-container">
        <ReactCSSTransitionGroup
          transitionName="agent-listing-notification-sm-transition"
          transitionAppear
          transitionAppearTimeout={600}
          transitionEnterTimeout={600}
          transitionLeaveTimeout={300}
        >
          <div className="agent-listing-notification-container-small">
            <img
              alt="Agent"
              className="agent-listing-notification-container-agent-image"
              src={this.state.READAgent.data.profile_image}
            />
            {this.smallMessage()}
            {this.actionButton()}
          </div>
        </ReactCSSTransitionGroup>
      </div>
    );
  },
});
