const React = require('react');
const SVGIcon = require('../../../components/svg_icon');
const mixins = require('../../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'listings.no-results',

  mixins: [mixins.actions],

  render() {
    if (this.props.fromSearch) {
      return (
        <div className="listings-no-results">
          <SVGIcon name="icon-no-results" />
          <div><strong>You have awesome taste.</strong></div>
          <div>However, we can&apos;t find the level of awesome you are looking for.</div>
          <div>Try a different search.</div>
        </div>
      );
    }
    return (
      <div className="listings-no-results">
        <div>No results</div>
      </div>
    );
  },

});
