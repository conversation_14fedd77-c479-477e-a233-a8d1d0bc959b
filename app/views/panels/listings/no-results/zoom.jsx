const React = require('react');
const SVGIcon = require('../../../components/svg_icon');
const mixins = require('../../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'listings.no-results-zoom',

  mixins: [mixins.actions],

  render() {
    return (
      <div className="listings-no-results-zoom">
        <SVGIcon name="icon-no-results-zoom" />
        <div>zoom in where you would like to view homes</div>
      </div>
    );
  },

});
