import SwipeableViews from 'react-swipeable-views';

const React = require('react');
const SpinnerRound = require('../../components/spinner_round');
const Card = require('../card');
const mixins = require('../../../lib/mixins');
const NoResults = require('./no-results');

module.exports = React.createClass({

  displayName: 'panel.listings.horizontal',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },

  getInitialState() {
    return {
      slideToIndex: 0,
    };
  },

  onNav(id) {
    this.actions.common.flagUserAsInteractedWithSite();
    this.props.onNav(id);
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  spinner() {
    return (
      <center className="mt30 mb30">
        <SpinnerRound />
      </center>
    );
  },

  swipeCallback(event) {
    this.props.swipeCallback && this.props.swipeCallback(event);
  },

  swipeEndCallback(index) {
    this.props.swipeEndCallback && this.props.swipeEndCallback(index);
    this.refs.ReactSwipe.forceUpdate();
  },

  scrollTo(index) {
    this.setState({ slideToIndex: index });
  },

  cards() {
    return (
      <SwipeableViews
        ref="ReactSwipe"
        index={this.state.slideToIndex}
        onTouchStart={this.swipeCallback}
        onChangeIndex={this.swipeEndCallback}
        animateTransitions
      >
        {
          this.props.listings.map((i, idx) => (
            <Card
              key={i.Id}
              isActive
              listing={i}
              mlsId={this.actions.common.getListingMlsId(i)}
              onNav={this.onNav}
              ref={`Card-${idx}`}
            />
          ), this)
        }
      </SwipeableViews>
    );
  },

  body() {
    if (this.props.listings === 0) {
      return null;
    }

    if (this.props.listings === false) {
      return null;
    }

    if (this.props.listings && this.props.listings.length === 0) {
      return null;
    }

    if (!this.props.listings) {
      return null;
    }

    if (this.props.spinner) {
      return null;
    }

    return this.cards();
  },

  render() {
    return (
      <div className="horizontal-listings-container">
        {this.body()}
      </div>
    );
  },

});
