const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.leftbar',

  mixins: [mixins.debug, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'leftbar'],
    data: ['panels', 'leftbar', 'data'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className="layout--leftbar">

        <p>Leftbar</p>

      </div>
    );
  },

});
