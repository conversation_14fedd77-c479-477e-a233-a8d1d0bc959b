const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.last-refreshed',

  mixins: [mixins.debug, mixins.utils, mixins.cursors, mixins.actions, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    mlsData: ['shared', 'agent', 'mlsData'],
  },

  render() {
    if (!this.state.agentData) {
      return null;
    }

    return (
      <div className={classNames(this.props.className, 'last-refreshed')}>
        <p>
          <strong>Last Refreshed</strong>
          &nbsp;
          {this.state.mlsData ? new Date(`${this.state.mlsData.Latestupdate}Z`).toLocaleString() : 'Not Available'}
        </p>
        {
          this.state.mlsData && !this.state.mlsData.ShowAgentInListingDetail ? null
            : (
              <p>
                <strong>Broker</strong>
                &nbsp;
                {this.state.agentData.BrokerName ? this.state.agentData.BrokerName : 'None'}
                <br />
                <strong>Agent</strong>
                &nbsp;
                {this.state.agentData.NameLookup}
                {this.state.agentData.Phone ? (
                  <span>
                    ,&nbsp;
                    <span role="button" tabIndex={-1} onClick={() => this.state.agentData.Phone && window.open(`tel:${this.state.agentData.Phone}`, '_blank')}>
                      {this.state.agentData.Phone}
                    </span>
                  </span>
                ) : ''}
                <br />
                <strong>License</strong>
                &nbsp;
                {this.state.agentData.StateLicenseNumber}
                { this.state.agentData.BrokerLogo ? [<br key="1" />, <img key="2" alt="Broker" className="last-refreshed-broker-logo" src={this.state.agentData.BrokerLogo} />] : null}
              </p>
            )
        }
        <p className="mb5">Information is deemed reliable but is not guaranteed.</p>
        {
          window.rateplug.rp_buyer
            ? (
              <p className="text-left" style={{ marginTop: 27.5 }}>
                <strong>Payment Calculation</strong>
                <br />
                Payments are based on a 30 Year fixed mortgage national average rate. Actual rates may vary depending on your situation.
              </p>
            ) : null
        }
        {
          window.rateplug.rp_buyer
            ? <p className="text-left mb5">You can get current interest rate and loan program information when you click through the individual properties.</p> : null
        }
        {
          window.rateplug.rp_buyer
            ? <p className="text-left mb5">Special Finance data uses Public Record data and other 3rd party data sources. Accuracy is not guaranteed.</p> : null
        }
      </div>
    );
  },
});
