const React = require('react');
const classnames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const HeaderSearch = require('../header/search');
const Sort = require('../../panels-mobile/sort');

module.exports = React.createClass({

  displayName: 'panel.read-header',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils],

  cursors: {
    layout: ['layout', 'readHeader'],
    detectedAgent: ['shared', 'detectedAgent', 'data'],
    agentCount: ['shared', 'agentCount', 'AgentCount'],
  },

  getInitialState() {
    return {
      showScrollTop: false,
    };
  },

  componentDidMount() {
    if (this.utils.useMobileSite()) {
      document.addEventListener('scroll', this.documentScrollListener);
    }
  },

  componentWillUnmount() {
    if (this.utils.useMobileSite()) {
      document.removeEventListener('scroll', this.documentScrollListener);
    }
  },

  documentScrollListener() {
    let shouldShowScrollTop = false;
    if (window.scrollY > this.refs.readHeaderWrapper.clientHeight) {
      shouldShowScrollTop = true;
    }
    if (shouldShowScrollTop !== this.state.shouldShowScrollTop) {
      this.setState({ showScrollTop: shouldShowScrollTop });
    }
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className="read-header-wrapper" ref="readHeaderWrapper">
        <div
          className={classnames('read-header-scroll-up', {
            active: this.state.showScrollTop,
          })}
          role="button"
          tabIndex="-1"
          onClick={() => window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'smooth',
          })}
        >
          <SVGIcon name="icon-search" />
        </div>
        <div className="read-header">
          <div className="read-header-left">
            <SVGIcon name="logo-read-white" />
          </div>
          <p className="read-header-middle">
            <strong>{this.utils.addThousandSep(this.state.agentCount || 600000)}</strong>
            &nbsp;
            Real Estate professionals nation wide!
          </p>
          <div className="read-header-right">
            {
              this.state.detectedAgent
                ? (
                  <a
                    role="button"
                    tabIndex="-1"
                    aria-label="Login"
                    href={window.CONFIG.READ_URL}
                    target="_blank"
                  >
                    Agent Login
                  </a>
                )
                : (
                  <a
                    role="button"
                    tabIndex="-1"
                    aria-label="Join Free Today!"
                    href="https://read.homeasap.com/join"
                    target="_blank"
                  >
                    Agents Join Free!
                  </a>
                )
            }
          </div>
        </div>
        <div className={`${this.state.layout}-header`}>
          <div className="toggle-container">
            <a
              role="button"
              tabIndex="-1"
              aria-label="Find an agent"
              className={classnames({ active: this.state.layout === 'member-search' })}
              onClick={this.state.layout !== 'member-search' ? this.actions.memberSearch.onNav : null}
            >
              FIND AN AGENT
            </a>
            <a
              role="button"
              tabIndex="-1"
              aria-label="Find a home"
              className={classnames({ active: this.state.layout === 'member-listings' })}
              onClick={this.state.layout !== 'member-listings' ? this.actions.memberListings.onNav : null}
            >
              FIND A HOME
            </a>
          </div>
          <div className="search-container">
            {
              this.state.layout === 'member-listings' && !this.utils.useMobileSite()
                ? (
                  <div className="member-listings-sort">
                    <Sort />
                  </div>
                )
                : null
            }
            {
              this.state.layout === 'member-listings'
                ? <HeaderSearch hideAddresses />
                : null
            }
          </div>
        </div>
      </div>
    );
  },
});
