const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.rateplug-welcome-modal',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    layout: ['layout', 'rateplugWelcomeModal'],
  },

  componentDidMount() {
    if (window.rateplug.rp_welcome) {
      setTimeout(() => {
        this.actions.common.showRateplugWelcomeModal();
      }, 100);
    }
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.layout && !prevState.layout) {
      this.actions.analytics.sendEvent('rateplug', 'landing', 'show');
    }
  },

  closeModal() {
    this.actions.common.hideRateplugWelcomeModal();
  },

  renderBody() {
    return (
      <div className="rateplug-landing-step">
        <h2>Welcome to the MORE Home Search!</h2>
        <div className="rateplug-landing-flex-wrapper">
          <img
            alt="Home graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/rateplug-onboarding-step-1${document.body.dataset.theme === 'more' ? '-more' : ''}.svg`}
          />
          <div>
            <p className="mb20">See homes for sale right now that fit your budget and may be eligible special financing programs.</p>
            <a tabIndex={-1} role="button" className="btn btn-primary btn-block" onClick={this.closeModal}>
              Continue
            </a>
          </div>
        </div>
      </div>
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <Modal show={this.state.layout} onHide={this.closeModal} className={this.utils.isMobile() ? 'rateplug-landing-modal mobile' : 'rateplug-landing-modal'}>
        <Modal.Body>
          {
            this.renderBody()
          }
        </Modal.Body>
      </Modal>
    );
  },

});
