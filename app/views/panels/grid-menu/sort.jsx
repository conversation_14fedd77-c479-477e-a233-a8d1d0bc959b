const React = require('react');
const ListGroup = require('react-bootstrap').ListGroup;
const ListGroupItem = require('react-bootstrap').ListGroupItem;
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.grid-menu.sort',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.pureRender],
  cursors: {
    sortType: ['shared', 'menu', 'sortType'],
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },
  sortClicked(sortCriteria) {
    this.actions.menu.toggleSortType(sortCriteria);
    this.props.onSortChange();
    this.actions.common.scrollElementTo(document.body, 0, 500);
  },

  render() {
    return (
      <div className="sort-container">
        <ListGroup>
          <ListGroupItem
            active={!this.state.sortType || this.state.sortType === 'daysOnMarket_asc'}
            onClick={this.sortClicked.bind(null, 'daysOnMarket_asc')}
          >
            {this.state.showDaysOnMarket ? 'Days on market - newest first' : 'Newest first'}
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'price_asc'}
            onClick={this.sortClicked.bind(null, 'price_asc')}
          >
            Price - lowest first
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'price_desc'}
            onClick={this.sortClicked.bind(null, 'price_desc')}
          >
            Price - highest first
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'sqft_desc'}
            onClick={this.sortClicked.bind(null, 'sqft_desc')}
          >
            Square feet - largest first
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'distance_asc'}
            onClick={this.sortClicked.bind(null, 'distance_asc')}
          >
            Distance - closest first
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'yearBuilt_asc'}
            onClick={this.sortClicked.bind(null, 'yearBuilt_asc')}
          >
            Year built - newest first
          </ListGroupItem>
          <ListGroupItem
            active={this.state.sortType === 'yearBuilt_desc'}
            onClick={this.sortClicked.bind(null, 'yearBuilt_desc')}
          >
            Year built - oldest first
          </ListGroupItem>
        </ListGroup>
      </div>
    );
  },
});
