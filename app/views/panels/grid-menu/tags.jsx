const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'screens.grid.tags',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.pureRender],

  cursors: {
    tags: ['user', 'tags'],
  },

  addTagCard(text) {
    this.actions.tagging.onNavWithTagName(text);
    this.props.onTagChange ? this.props.onTagChange() : '';
  },
  render() {
    return (
      <div className="tags-container">
        <p className="tag-header">Tags You&apos;ve Created</p>
        <div className="tag-holder">
          {
            this.state.tags && Object.keys(this.state.tags).length > 0
              ? map(this.state.tags, function (cnt, tag) {
                return (
                  <p className="tag-content">
                    &nbsp;
                    (
                    {cnt}
                    )
                    <a
                      role="button"
                      tabIndex="-1"
                      onClick={this.addTagCard.bind(this, tag)}
                    >
                      &nbsp;
                      {tag}
                    </a>
                  </p>
                );
              }, this)
              : <p className="tag-content">No tags found</p>
          }
        </div>
      </div>
    );
  },

});
