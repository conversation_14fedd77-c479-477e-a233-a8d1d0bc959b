const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins/index');
const Sort = require('../sort/drop-down');
const Tags = require('./tags-dropdown');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'screens.grid.menu',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils/* , mixins.pureRender */],

  cursors: {
    lightsOff: ['screens', 'grid', 'lightsOut'],
    sortType: ['shared', 'menu', 'sortType'],
    showMenu: ['screens', 'grid', 'showMenu'],
  },

  getInitialState() {
    return {
      showPanel: false,
    };
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.sortType !== this.state.sortType) {
      return true;
    }
    if (nextState.lightsOff !== this.state.lightsOff) {
      return true;
    }
    if (nextState.showMenu !== this.state.showMenu) {
      return true;
    }
    if (nextState.showPanel !== this.state.showPanel) {
      return true;
    }
    return false;
  },

  facets: {
    hasSearchResults: ['currentActiveHeaderControl'],
  },

  showMenu() {
    this.actions.grid.showMenu();
  },

  hideMenu() {
    this.actions.grid.hideMenu();
  },

  handleScroll(e) {
    e.preventDefault();
    const scrollTop = (document.documentElement && document.documentElement.scrollTop)
                      || document.body.scrollTop;

    const st = scrollTop;
    if (st > this.lastScrollPosition && this.lastScrollPosition > 0) {
      this.hideMenu();
    } else if (st < this.lastScrollPosition) {
      this.showMenu();
    }
    this.lastScrollPosition = st;
  },

  componentWillMount() {
    //  Open Tagging Panel Filter on Load
    // if (this.props.mode === 'tagging') {
    //  this.setState({showPanel: true})
    // }
  },

  componentDidMount() {
    window.addEventListener('scroll', this.handleScroll);
  },

  componentWillUnmount() {
    window.removeEventListener('scroll', this.handleScroll);
  },

  menuClicked() {
    this.actions.menu.toggle();
  },

  mapClicked() {
    this.actions.map.onNav();
  },

  tabs() {
    return (
      <nav className={classNames('nav-scrollable listing-nav')}>
        {
        this.state.hasSearchResults
          ? (
            <a
              role="button"
              tabIndex="-1"
              className={classNames('item', 'pull-left', { active: this.props.mode === 'search-results' })}
              key="Search Results"
              onClick={this.actions.grid.onNav.bind(null, null)}
            >
              Search Results
            </a>
          ) : null
        }
        <a
          role="button"
          tabIndex="-1"
          className={classNames('item', 'pull-left', { active: this.props.mode === 'featured' })}
          key="Featured Homes"
          onClick={this.actions.featured.onNav.bind(null, null)}
        >
          Featured Homes
        </a>
        <div key="Tags Dropdown" className={classNames('item', 'pull-left', { active: this.props.mode === 'tagging' })}>
          <SVGIcon className="menu-icon tag-icon pull-left" name="icon-bell" />
          <Tags />
        </div>
        <a
          role="button"
          tabIndex="-1"
          className={classNames('item pull-right')}
          onClick={this.actions.grid.toggleLights}
          key="Lights"
        >
          {this.state.lightsOff ? 'Lights On' : 'Lights Off'}
        </a>

        {
        this.props.mode === 'search-results'
          ? (
            <div key="Sort Results" className="item pull-right">
              <Sort />
            </div>
          ) : null
        }
      </nav>
    );
  },

  panel() {
    return null;
    // return (this.props.mode === 'tagging' && this.state.showPanel)
    //   ? (
    //     <Tags onTagChange={function () {
    //       this.state.showPanel ? this.setState({ showPanel: false }) : '';
    //     }.bind(this)}
    //     />
    //   ) : null;
  },

  render() {
    return (
      <div
        key="menu"
        className={
        classNames('layout--nav layout--front menu-container cards-width',
          {
            'slide-enter slide-enter-active': this.state.showMenu,
            'slide-leave slide-leave-active': !this.state.showMenu,
          })
}
      >
        <div className="scroll-container">
          {this.tabs()}
        </div>
        <div className="menu-panel">
          {this.panel()}
        </div>
      </div>
    );
  },
});
