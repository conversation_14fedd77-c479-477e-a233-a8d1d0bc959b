const React = require('react');
const forEach = require('lodash.foreach');
const DropdownButton = require('react-bootstrap').DropdownButton;
const MenuItem = require('react-bootstrap').MenuItem;
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.grid-menu.tags-dropdown',
  default: 'All',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils/* , mixins.pureRender */],

  cursors: {
    tags: ['user', 'tags'],
    tagName: ['layout', 'tagging', 'grid'],
  },
  onTagSelect(e) {
    if (e.toString() === 'All') {
      this.actions.tagging.onNavWithTagName();
    } else {
      this.actions.tagging.onNavWithTagName(e.toString());
    }
    /* this.actions.menu.toggleSortType(e.toString())
    this.actions.common.scrollElementTo(document.body, 0, 500) */
  },

  /* sortClicked: function (sortCriteria) {
   this.actions.menu.toggleSortType(sortCriteria)
   //this.props.onSortChange();
   this.actions.common.scrollElementTo(document.body, 0, 500)
   }, */

  tagMenuItems() {
    const items = [];
    items.push(<MenuItem key="All" eventKey="All"> All</MenuItem>);
    /*  if(this.state.tags && Object.keys(this.state.tags).length == 0){
      items.push(<MenuItem key="fav" eventKey='All'>(0) favorite</MenuItem>)
      items.push(<MenuItem key="dis" eventKey='All'>(0) dislike</MenuItem>)
    } */
    if (this.state.tags && Object.keys(this.state.tags).length > 0) {
      forEach(this.state.tags, (cnt, tag) => {
        items.push(<MenuItem key={tag} eventKey={tag}>{`(${cnt}) ${tag}`}</MenuItem>);
      }, this);
    }

    return items;
  },
  getTitle() {
    if (!this.state.tagName) {
      return 'All';
    }
    return this.state.tagName === true ? 'All' : this.state.tagName;
  },

  render() {
    return (
      <DropdownButton id="tags-dropdown" key="My Homes" className="pull-left tags-holder" bsStyle="default" title={`MY HOMES: ${this.getTitle()}`} noCaret onSelect={this.onTagSelect}>
        {this.tagMenuItems()}
      </DropdownButton>
    );
  },
});
