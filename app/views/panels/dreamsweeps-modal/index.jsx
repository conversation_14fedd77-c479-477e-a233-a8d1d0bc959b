const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const Spinner = require('../../components/spinner_round');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.dreamsweeps-modal',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  getInitialState() {
    return {
      submitting: false,
    };
  },

  cursors: {
    buyerId: ['shared', 'buyer', 'Id'], // Used only as a trigger to rerender
    agentProducts: ['shared', 'agent', 'data', 'ProductServices'], // Used only as a trigger to rerender, which then calls Common.getAgentHasDS
    layout: ['layout', 'dreamsweepsModal'],
    data: ['shared', 'dreamsweeps'],
  },

  componentDidMount() {
    if (this.actions.common.forceDreamSweeps()) {
      setTimeout(() => {
        this.actions.common.showDreamsweepsModal();
      }, 1000);
    }
  },

  componentWillUpdate(nextProps, nextState) {
    if (((nextState.layout && !this.state.layout) || (!this.state.buyerId || !this.state.data)) && nextState.buyerId && nextState.data) {
      this.actions.buyer.getDreamsweepsEntry((err, res) => {
        if (res) {
          this.setState({ entry: res });
        }
      });
    }
  },

  enterDS(e) {
    e.preventDefault();

    const buyer = ['firstName', 'lastName', 'email', 'phone'].reduce((map, name) => {
      map[name] = e.target.elements[name].value.trim();
      return map;
    }, {});

    this.actions.analytics.sendEvent('dreamsweeps', 'submit');

    this.setState({ submitting: true });
    this.actions.buyer.createDreamsweepsLead(buyer, (totalEntries) => {
      this.setState({ submitting: false, entry: { Total: totalEntries } });
    }, (error) => {
      this.setState({ submitting: false, error });
    });
  },

  render() {
    const buyer = this.actions.common.getBuyerData();

    if (this.state.layout === 'success') {
      return (
        <Modal show={this.actions.common.getAgentHasDS()} onHide={this.actions.common.hideDreamsweepsSuccessModal} className={this.utils.isMobile() ? 'dreamsweeps-success-modal mobile' : 'dreamsweeps-success-modal'}>
          <Modal.Body>
            <div
              role="button"
              tabIndex="-1"
              aria-label="Close"
              className="close-button-landing close-button"
              onClick={this.actions.common.hideDreamsweepsModal}
            >
              <SVGIcon name="icon-close-button" />
            </div>
            <div className="dreamsweeps-modal-success-container">
              <div className="title">Thank you!</div>
              <img alt="Success" className="success-check" src="//nplayassets.blob.core.windows.net/images/ICON-success-checkmark.png" />
              <div className="description">
                <strong>{`You've successfully entered ${this.state.entry ? `${this.state.entry.Total}/${this.state.data.MaxApplications} times` : ''}.`}</strong>
                <br />
                You can enter again in 24 hours.
              </div>
              <button type="button" className="btn btn-success" onClick={this.actions.common.hideDreamsweepsModal}>Continue searching for homes</button>
            </div>
          </Modal.Body>
        </Modal>
      );
    }

    if (this.state.layout === true) {
      return (
        <Modal show={this.actions.common.getAgentHasDS()} onHide={this.actions.common.hideDreamsweepsModal} className={this.utils.isMobile() ? 'dreamsweeps-modal mobile' : 'dreamsweeps-modal'}>
          <Modal.Body>
            <div
              role="button"
              tabIndex="-1"
              aria-label="Close"
              className="close-button-landing close-button"
              onClick={this.actions.common.hideDreamsweepsModal}
            >
              <SVGIcon name="icon-close-button" />
            </div>
            {
              this.state.data === null
                ? <center><Spinner /></center>
                : this.state.data === false
                  ? <p>Please try again later.</p>
                  : (
                    <div className="dreamsweeps-modal-container">
                      <img alt="Sweepstakes" className="cards" src="//nplayassets.blob.core.windows.net/images/DS-modal-graphic.png" />
                      <div className="title">{this.state.data.Name}</div>
                      <div className="description">
                        {this.state.data.PromoText}
                        {this.state.entry && this.state.entry.Total ? (
                          <strong>
                            <br />
                            You have entered
                            &nbsp;
                            {this.state.entry.Total}
                            /
                            {this.state.data.MaxApplications}
                            &nbsp;
                            times.
                          </strong>
                        ) : null}
                        {this.state.error ? (
                          <span style={{ color: 'red' }}>
                            <br />
                            <br />
                            {this.state.error}
                          </span>
                        ) : null}
                      </div>
                      <form onSubmit={this.enterDS}>
                        <div className="col-1-2 pr5">
                          <input type="text" className="form-control" data-populate-buyer="FirstName" name="firstName" placeholder="First Name" defaultValue={buyer && buyer.FirstName || ''} required />
                        </div>
                        <div className="col-1-2 pl5">
                          <input type="text" className="form-control" data-populate-buyer="LastName" name="lastName" placeholder="Last Name" defaultValue={buyer && buyer.LastName || ''} required />
                        </div>
                        <div className="col-1-2 mt10 pr5">
                          <input type="email" className="form-control" data-populate-buyer="Email" name="email" placeholder="Email" defaultValue={buyer && buyer.Email || ''} required />
                        </div>
                        <div className="col-1-2 mt10 pl5">
                          <input type="tel" className="form-control" data-populate-buyer="Phone" name="phone" placeholder="Phone" defaultValue={buyer && buyer.Phone || ''} required />
                        </div>

                        <div className="col-1-1 mt15">
                          <button type="submit" className="btn btn-success btn-block pt10 pb10" disabled={this.state.submitting}>
                            {this.state.submitting ? 'Loading...' : 'Enter today'}
                          </button>
                        </div>
                      </form>
                    </div>
                  )
            }
          </Modal.Body>
        </Modal>
      );
    }

    return null;
  },

});
