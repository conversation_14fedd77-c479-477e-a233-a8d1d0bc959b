const React = require('react');
const Autosuggest = require('react-autosuggest');
const _ = require('lodash');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

const constants = require('../../../lib/constants');

module.exports = React.createClass({

  displayName: 'panel.home-more.search-field',

  defaultPlaceholder: 'Address, Neighborhood, Zip, City, School ...',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    agentId: ['shared', 'agent', 'data', 'Id'],
  },

  getInitialState() {
    return {
      res: {

      },
      hasPrevious: false,
      suggestions: [],
      value: '',
    };
  },

  wipeField() {
    if (this.state.value) {
      this.setState({ value: '', suggestions: [] });
    }
    this.refs.autosuggest.input.placeholder = 'Address, Neighborhood, School, City, Zip';
  },

  componentWillUpdate(nextProps, nextState) {
    if (this.state.res.locationQuery != nextState.res.locationQuery && nextState.res.locationQuery) {
      this.setState({ value: nextState.res.locationQuery });
    }
  },

  renderSuggestion(data/* , inputVal */) {
    // if (data.value == 'Current Location') {
    //   return (
    //     <span className="current-location">
    //       <SVGIcon name={this.utils.isIos() ? 'icon-location-ios' : 'icon-center'} className="autosuggest-icon" />
    //       {data.value}
    //     </span>
    //   );
    // }
    if (data.location) {
      if (data.location.locType === 'C') {
        return (
          <span>
            <SVGIcon name="icon-map" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'S') {
        return (
          <span>
            <SVGIcon name="icon-school-hat" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'N' || data.location.locType === 'Z') {
        return (
          <span>
            <SVGIcon name="icon-map-pin" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'A') {
        return (
          <span>
            <SVGIcon name="icon-front-of-house" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
    }
    return <span>{data.value}</span>;
  },

  autosuggestSelected(e, selection) {
    const data = selection.suggestion;
    this.actions.common.flagUserAsInteractedWithSite();

    if (data && data.location) {
      const res = {
        lat: data.location.lat,
        lon: data.location.lon,
        radius: data.location.radius,
        locationQuery: data.value,
      };

      this.setState({ res });
      window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify(res));
      this.props.setLatLng(data.location.lat, data.location.lon);

      // this.actions.home.performAgentSearch(data.location.lat, data.location.lon, () => {
      //   this.router.go(this.router.generateUrl(this.router.ROUTE_NAMES.AGENT_SEARCH, {
      //     location: `${data.location.lat},${data.location.lon},${6200}`,
      //   }));
      // });

      setTimeout(() => {
        (this.refs.autosuggest && this.refs.autosuggest.input)
          ? this.refs.autosuggest.input.blur() : '';
      }, 100);

      this.setState({
        suggestions: [],
      });
    } else if (data && data.searchHistory) {
      this.props.setLatLng(data.searchHistory.Latitude, data.searchHistory.Longitude);
    }
    // else if (data && data.value === 'Current Location') {
    //   setTimeout(() => {
    //     this.setState({ value: 'Loading Location...' });
    //   }, 0);
    //   this.actions.menu.agentSearchCurrentLocation((err) => {
    //     if (err) {
    //       this.setState({ value: 'Geolocation failed, please try a search' });
    //     }
    //   });
    // }
  },

  searchSubmit(e) {
    e.preventDefault();
  },

  searchKeywordEntered(e, suggestion) {
    const autoSuggestValue = _.get(suggestion, 'focusedSuggestion.value') || (this.state && this.state.value);
    if (autoSuggestValue && (autoSuggestValue !== this.state.res.locationQuery)
      && (autoSuggestValue.toLowerCase().indexOf('loading') === -1)) {
      this.utils.geocodeAddress(autoSuggestValue, (res) => {
        if ((!res) || _.includes(res.types, 'country')) {
          this.setState({ value: 'Sorry, no results.' });
          return false;
        }

        this.setState({
          res: {
            lat: res.geometry.location.lat,
            lon: res.geometry.location.lng,
            locationQuery: res.formatted_address,
          },
        });
        window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({
          lat: res.geometry.location.lat,
          lon: res.geometry.location.lng,
          radius: constants.SEARCH_RADIUS_DEFAULT_METERS,
          locationQuery: res.formatted_address,
        }));
        this.props.setLatLng(res.geometry.location.lat, res.geometry.location.lng);
      });
    }
  },

  loadSuggestions(value) {
    const fetchSuggestions = this.actions.menu.onAutosuggest.api_more;

    fetchSuggestions(value, (err, results) => {
      this.setState({ suggestions: results || [] });
    });
  },

  onSuggestionsFetchRequested({ value }) {
    this.loadSuggestions(value);
  },

  onSuggestionsClearRequested() {
    this.setState({
      suggestions: [],
    });
  },

  onChange(event, { newValue }) {
    this.setState({
      value: newValue,
    });
  },

  render() {
    return (
      <Autosuggest
        ref="autosuggest"
        inputProps={{
          type: 'search',
          autoFocus: false, //! this.state.res.locationQuery,
          className: 'form-control',
          placeholder: this.state.hasPrevious ? this.state.res.locationQuery : this.defaultPlaceholder,
          value: this.state.value || '',
          onBlur: () => {
            setTimeout(this.searchKeywordEntered, 100);
          },
          onChange: this.onChange,
        }}
        suggestions={this.state.suggestions}
        shouldRenderSuggestions={this.actions.menu.onAutosuggest.showWhen}
        onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
        onSuggestionsClearRequested={this.onSuggestionsClearRequested}
        onSuggestionSelected={this.autosuggestSelected}
        getSuggestionValue={(data) => data.value}
        renderSuggestion={this.renderSuggestion}
        scrollBar
        focusInputOnSuggestionClick={false}
        alwaysRenderSuggestions
      />
    );
  },

});
