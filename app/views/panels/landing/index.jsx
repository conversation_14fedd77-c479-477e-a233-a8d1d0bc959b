const React = require('react');
const _ = require('lodash');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SearchField = require('./search-field');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const SubmitBtn = require('../../components/submit_btn');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.landing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    saleType: ['shared', 'menu', 'saleType'],
    IsSearchLicenseAgreementShown: ['shared', 'agent', 'mlsData', 'IsSearchLicenseAgreementShown'],
    ShowLargeBrokerOnALP: ['shared', 'agent', 'mlsData', 'ShowLargeBrokerOnALP'],
  },

  componentDidMount() {
    this.sessionToken = String(+new Date());
  },

  searchSubmit(e) {
    e.preventDefault();

    const saleType = this.state.saleType;
    console.log(`Sale Type ${saleType}`);

    if (saleType !== 3) {
      const res = this.refs.SearchField.state.res;
      if (!this.refs.SearchField.state.hasPrevious // Using Previous
        && (!res.locationQuery || res.locationQuery !== this.refs.SearchField.state.value)) {
        const suggestions = this.refs.SearchField.state.suggestions;
        if (suggestions && suggestions[0] && suggestions[0].type === 'mlsListingIdMatch') {
          return this.refs.SearchField.autosuggestSelected(e, { suggestion: suggestions[0] });
        }

        console.log('Need google geocoding');
        this.actions.onboarding.geocodeAddress(this.refs.SearchField.state.value, (res1) => {
          const _res = {
            lat: res1.geometry.location.lat,
            lon: res1.geometry.location.lng,
            locationQuery: res1.formatted_address,
          };
          console.log(`Search Field: ${_res.locationQuery}`);
          this.actions.landing.onSearch(_res.lat, _res.lon, res.radius, _res.locationQuery, saleType);
        });
      } else {
        console.log(`Search Field: ${res.locationQuery}`);
        this.actions.landing.onSearch(res.lat, res.lon, res.radius, res.locationQuery, saleType);
      }
    } else {
      const formData = new FormData(e.target);
      const address = formData.get('address');
      if (!address || address.length == 0) {
        return;
      }

      this.actions.homeWorth.fetchSearchResult(address, (result) => {
        // TODO surface error messages for user for EMPTY, ERROR, and COUNTRY
        if (!result) {
          return;
        }

        // TODO surface error messages for user for EMPTY, ERROR, and COUNTRY
        if (result.error_message) {
          return;
        }

        // filter out USA
        // TODO surface error messages for user for EMPTY, ERROR, and COUNTRY
        if (_.includes(result.types, 'country')) {
          return;
        }

        this.actions.homeWorth.submitHomeWorthSearch(address);
      });
    }
  },

  featuredListingClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/featured');
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/agent');
  },

  agentContactClicked(e) {
    e.preventDefault();
    this.actions.agent.showAgentContact();
  },

  componentDidUpdate() {
    if (window.google && !this.autocomplete) {
      const input = document.getElementById('home-worth-landing-input');
      if (input) {
        this.autocomplete = new google.maps.places.Autocomplete(input, { types: ['address'], sessionToken: this.sessionToken });
        this.autocomplete.addListener('place_changed', this.autocompleteChanged);
      }
    }
  },

  toggleSaleType(type) {
    this.actions.menu.updateSearchField([{ key: 'saleType', value: type }]);
  },

  showDreamsweepsModal() {
    this.actions.common.showDreamsweepsModal();
  },

  hideDreamsweepsModal() {
    this.actions.common.hideDreamsweepsModal();
  },

  autocompleteChanged() {
    const place = this.autocomplete.getPlace();
    if (place && place.formatted_address) {
      const address = place.formatted_address;

      this.actions.homeWorth.submitHomeWorthSearch(address, place);
    }
  },

  landingContent() {
    if (!this.state.agentData) {
      return null;
    }

    return [
      <div className="agent-info" key="agent-info">
        <AgentProfileImage className="agent-profile-image" />

        <div className={classNames('agent-right', { 'same-size': this.state.agentData.MlsId === 'scccar' })}>
          <h1 className="agent-name">
            {this.state.agentData.NameLookup}
            &nbsp;
            <VerifiedBadge style={{ width: 30, height: 30 }} />
          </h1>
          <p className={'broker-name'.concat(this.state.ShowLargeBrokerOnALP ? ' large' : ' ')}><small>{this.state.agentData.BrokerName}</small></p>
          <p className="headline">
            <em>
              {this.state.agentData.Headline || 'Your local real estate expert! Always the best agent in town!'}
              {this.state.ShowLargeBrokerOnALP ? (
                <span>
                  <br />
                  <br />
                </span>
              ) : null}
            </em>
          </p>
        </div>
      </div>,
      <div className="search-area" key="search-area">
        <nav className={classNames('saletype-nav')}>
          {
            this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'alphidesale') !== 'true'
              ? (
                <a
                  role="button"
                  tabIndex="-1"
                  className={classNames({ active: this.state.saleType === 1 })}
                  onClick={this.toggleSaleType.bind(null, 1)}
                >
                  <small>For Sale</small>
                </a>
              ) : null
          }
          {
            this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'alphiderent') !== 'true'
              ? (
                <a
                  role="button"
                  tabIndex="-1"
                  className={classNames({ active: this.state.saleType === 2 })}
                  onClick={this.toggleSaleType.bind(null, 2)}
                >
                  <small>For Rent</small>
                </a>
              ) : null
          }
          {
            this.actions.common.getAgentHasHV()
              ? (
                <a
                  role="button"
                  tabIndex="-1"
                  className={classNames('home-worth-button', { active: this.state.saleType === 3 })}
                  onClick={this.actions.homeWorth.onNav}
                >
                  <small>Your Home Value</small>
                </a>
              ) : null
          }
          {
            this.actions.common.getAgentHasLendingTree(this.state.agentData)
              ? (
                <a
                  role="button"
                  tabIndex="-1"
                  className="lending-tree-bg"
                  onClick={() => {
                    this.actions.analytics.sendEvent('lending tree', 'open from alp');
                    this.actions.common.showLendingTreeModal();
                  }}
                >
                  <small>See Current Rates</small>
                </a>
              ) : null
          }
        </nav>
        <form className="search-form" onSubmit={this.searchSubmit}>
          <div className={classNames('input-group', {
            'home-worth-search': this.state.saleType === 3,
          })}
          >
            <SearchField ref="SearchField" saleType={this.state.saleType} />
            <input id="home-worth-landing-input" name="address" placeholder="Enter address of home" />
            <SubmitBtn className="btn-primary cursor-default" icon="icon-search" />
          </div>
        </form>
        <div className="helper-buttons">
          <div>
            <a
              role="button"
              tabIndex="-1"
              className="btn btn-block btn-default"
              onClick={this.agentClicked}
            >
              ABOUT ME
            </a>
          </div>
          <div>
            <a
              role="button"
              tabIndex="-1"
              className="btn btn-block btn-default"
              onClick={this.featuredListingClicked}
            >
              FEATURED LISTINGS
            </a>
          </div>
          <div>
            <a
              role="button"
              tabIndex="-1"
              className="btn btn-block btn-default"
              onClick={this.agentContactClicked}
            >
              CONTACT ME
            </a>
          </div>
          {
            this.utils.getAgentSettingValue(this.state.agentData && this.state.agentData.AgentSettings, 'local_market_reports_url')
              ? (
                <div>
                  <a
                    role="button"
                    tabIndex="-1"
                    className="btn btn-block btn-default"
                    href={this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'local_market_reports_url')}
                    target="_blank"
                  >
                    Local Market Reports
                  </a>
                </div>
              ) : null
          }
          {
            this.actions.common.getAgentHasDS()
              ? (
                <div className="sweepstakes-button">
                  <a
                    role="button"
                    tabIndex="-1"
                    className="btn btn-block btn-default"
                    onClick={this.showDreamsweepsModal}
                  >
                    <SVGIcon className="star" name="icon-star" />
                    ENTER TO WIN $200
                    <SVGIcon className="star" name="icon-star" />
                  </a>
                </div>
              ) : null
          }
        </div>
        {this.state.IsSearchLicenseAgreementShown
          ? (
            <a
              role="button"
              tabIndex="-1"
              className="implicit-agree"
              onClick={this.actions.common.toggleModal.bind(null, 'Terms')}
            >
              By searching you agree to the terms and conditions
            </a>
          )
          : null}
        <p className="msg">Over 1 million homes updated every 5 minutes. Find yours.</p>
      </div>,
    ];
  },

  render() {
    return (

      <div className={classNames('landing-wrapper-container', this.props.className)}>

        { this.props.children}

        <div className="landing-wrapper">
          {this.landingContent()}
        </div>

        <div className="links">
          <p>
            <a href="https://homeasap.com/agent/login" target="_blank">Agent Login</a>
          </p>
        </div>

        <div className="footer">

          <div className="logo">
            <a
              role="button"
              tabIndex="-1"
              href="https://about.homeasap.com"
              target="_blank"
            >
            &nbsp;
            </a>
          </div>

          <div className="copyright">
            <p className="links-mobile">
              <a href="https://homeasap.com/agent/login" target="_blank">Agent Login</a>
            </p>
            <p>
              <span className="text-transform-none text-block">
                &copy;
                {window.CURRENT_YEAR || ''}
                &nbsp;
                HomeASAP LLC
              </span>
              <span>&nbsp;·&nbsp;</span>
              <a
                role="button"
                tabIndex="-1"
                onClick={this.actions.common.toggleModal.bind(null, 'Terms')}
              >
                Terms
              </a>
              <span>&nbsp;·&nbsp;</span>
              <a
                role="button"
                tabIndex="-1"
                onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}
              >
                Privacy/DMCA Policy
              </a>
              <span>&nbsp;·&nbsp;</span>
              <a href="/search/ADA" target="_blank">Accessibility</a>
            </p>
          </div>
          <div className="social-links" title="">
            {this.state.agentData.FacebookUrl
              ? (
                <a target="_blank" className="facebook" title="View my page on Facebook" href={this.state.agentData.FacebookUrl.match(/\.co/) ? this.state.agentData.FacebookUrl : `https://www.facebook.com/${this.state.agentData.FacebookUrl}`}>
                  <SVGIcon name="icon-facebook" />
                </a>
              )
              : null}
            {this.state.agentData.InstagramUrl
              ? (
                <a target="_blank" className="instagram" title="View my page on Instagram" href={this.state.agentData.InstagramUrl.match(/\.co/) ? this.state.agentData.InstagramUrl : `https://instagram.com/${this.state.agentData.InstagramUrl.replace('@', '')}`}>
                  <SVGIcon name="icon-instagram" />
                </a>
              )
              : null}
            {this.state.agentData.YouTubeUrl
              ? (
                <a target="_blank" className="youtube" title="View my page on Youtube" href={this.state.agentData.YouTubeUrl.match(/\.co/) ? this.state.agentData.YouTubeUrl : `https://www.youtube.com/${this.state.agentData.YouTubeUrl}`}>
                  <SVGIcon name="icon-youtube" />
                </a>
              )
              : null}
            {this.state.agentData.GoogleUrl
              ? (
                <a target="_blank" className="google" title="View my page on Google" href={this.state.agentData.GoogleUrl}>
                  <SVGIcon name="icon-google" />
                </a>
              )
              : null}
            {this.state.agentData.TwitterUrl
              ? (
                <a target="_blank" className="twitter" title="View my page on Twitter" href={this.state.agentData.TwitterUrl.match(/\.co/) ? this.state.agentData.TwitterUrl : `https://www.twitter.com/${this.state.agentData.TwitterUrl}`}>
                  <SVGIcon name="icon-twitter" />
                </a>
              )
              : null}
            {this.state.agentData.LinkedInUrl
              ? (
                <a target="_blank" className="linkedin" title="View my page on LinkedIn" href={this.state.agentData.LinkedInUrl.match(/\.co/) ? this.state.agentData.LinkedInUrl : `https://www.linkedin.com/${this.state.agentData.LinkedInUrl}`}>
                  <SVGIcon name="icon-linkedin" />
                </a>
              )
              : null}
            {this.state.agentData.PinterestUrl
              ? (
                <a target="_blank" className="pinterest" title="View my page on Pinterest" href={this.state.agentData.PinterestUrl.match(/\.co/) ? this.state.agentData.PinterestUrl : `https://www.pinterest.com/${this.state.agentData.PinterestUrl}`}>
                  <SVGIcon name="icon-pinterest" />
                </a>
              )
              : null}
          </div>
        </div>
      </div>

    );
  },

});
