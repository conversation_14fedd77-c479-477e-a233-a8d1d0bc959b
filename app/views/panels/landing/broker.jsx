const React = require('react');
const _ = require('lodash');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.landing.broker',

  mixins: [mixins.debug, mixins.utils, mixins.cursors],

  cursors: {
    agent: ['shared', 'agent', 'data'],
    mlsData: ['shared', 'agent', 'mlsData'],
  },

  render() {
    if (!_.get(this.state.agent, 'BrokerName')) {
      return null;
    }

    let phone = this.state.agent.BrokerPhone
      ? this.state.agent.BrokerPhone
      : (this.state.agent.Phone
        ? this.state.agent.Phone
        : null
      );
    phone = this.utils.formatPhone(phone);

    return (
      <div className="landing-broker-container">
        <div className="landing-broker">
          {this.state.agent.BrokerLogo
            ? (
              <div className="landing-broker-logo">
                <img alt="Brokerage" src={this.state.agent.BrokerLogo} />
              </div>
            ) : null}
          <div className="landing-broker-details">
            <div className="broker">{this.state.agent.BrokerName}</div>
            <div>
              <span role="button" tabIndex={-1} onClick={() => phone && window.open(`tel:${phone}`, '_blank')}>
                {phone}
              </span>
              {(phone && this.state.agent.BrokerAddress) ? ' | ' : ''}
              {this.state.agent.BrokerAddress}
              ,
              &nbsp;
              {this.state.agent.BrokerCity}
              ,
              &nbsp;
              {this.state.agent.BrokerState}
              &nbsp;
              {this.state.agent.BrokerZip}
            </div>
            <div>{this.state.agent.StateLicenseNumber ? (`License: ${this.state.agent.StateLicenseNumber}`) : ''}</div>
            <div />
          </div>
        </div>
        <div className="landing-right">
          {
          this.state.mlsData && this.state.mlsData.Id === 'casmls-r'
            ? (
              <div className="landing-mls-disclaimer">
                {this.state.mlsData.LogoUrl ? <div className="mls-logo"><img alt="MLS Logo" src={this.state.mlsData.LogoUrl} /></div> : null}
                {this.state.mlsData.IdxStatement ? (
                  <p>{this.state.mlsData.IdxStatement}</p>
                ) : null}
              </div>
            )
            : null
          }
          <div className="landing-mls-disclaimer">
            <p dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.state.agent.Disclosures) }} />
          </div>
          {
          this.state.mlsData && this.state.mlsData.Id.startsWith('tx')
            ? (
              <div className="landing-mls-disclaimer">
                <p><a href="https://www.trec.texas.gov/sites/default/files/pdf-forms/CN%201-5_0.pdf" target="_blank">Consumer Protection Notice</a></p>
              </div>
            )
            : null
          }
        </div>
      </div>
    );
  },

});
