const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panel.facebook.header',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'facebookHeader'],
    agentData: ['shared', 'agent', 'data'],
    activeTab: ['screens', 'facebook', 'activeTab'],
  },

  onNav(tab) {
    const pathPrefix = window.location.href.match(/\/frame\//) ? 'frame' : 'facebook';
    this.actions.analytics.sendEvent('navigation', `${pathPrefix}-embedded`, tab);
    this.actions.common.facebookRoute(`/${pathPrefix}/${tab}`);
  },

  goToOnboarding(e) {
    e.preventDefault();
    this.onNav('onboarding');
  },

  goToAgent(e) {
    e.preventDefault();
    this.onNav('agent');
  },

  goToFeatured(e) {
    e.preventDefault();
    this.onNav('featured');
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className={'facebook-header '.concat(this.state.layout === 'admin' ? 'facebook-page-admin' : '')}>
        <a className="agent-profile-bar text-center" href={window.CONFIG.GATEWAY_URL.concat('agentprofile')} target="_blank">
          ACCESS YOUR AGENT ACCOUNT HERE
        </a>
        <div className="facebook-header-left">
          {
            this.state.agentData
              ? (
                <div
                  role="button"
                  tabIndex="-1"
                  aria-label="Agent"
                  className="agent-container"
                  onClick={this.goToAgent}
                  ref="agentContainer"
                >
                  <div className="agent-texts full-name">
                    <p className="agent-name" title={`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`}>
                      <span>
                        {`${this.state.agentData.FirstName} ${this.state.agentData.LastName} `}
                        <VerifiedBadge />
                      </span>
                      {
                      this.state.mlsData
                      && this.state.mlsData.RealEstateLicenseNumberOnOff
                      && this.state.agentData.StateLicenseNumber
                      && this.state.agentData.StateLicenseNumber.length > 0
                        ? (
                          <span className="license-num">{this.state.agentData ? ` (${this.state.agentData.StateLicenseNumber})` : ''}</span>
                        ) : null
                    }
                    </p>
                    {
                    this.state.agentData.BrokerName
                      ? <p className="brokerage" title={this.state.agentData.BrokerName}><span>{this.state.agentData.BrokerName}</span></p>
                      : null
                    }
                  </div>
                </div>
              )
              : null
          }
        </div>
        <div className="facebook-header-right">
          <div
            role="button"
            tabIndex="-1"
            aria-label="Agent"
            className={classNames({ active: this.state.activeTab == 'agent' })}
            onClick={this.goToAgent}
          >
            Agent Profile
          </div>
          <div
            role="button"
            tabIndex="-1"
            aria-label="Featured"
            className={classNames({ active: this.state.activeTab == 'featured' })}
            onClick={this.goToFeatured}
          >
            Featured Listings
          </div>
          { this.state.agentData && this.state.agentData.HomeSearchRegisteredDateTime
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Find a home"
                className={classNames({ active: this.state.activeTab == 'onboarding' })}
                onClick={this.goToOnboarding}
              >
                Find a Home
              </div>
            )
            : null }
        </div>
      </div>
    );
  },
});
