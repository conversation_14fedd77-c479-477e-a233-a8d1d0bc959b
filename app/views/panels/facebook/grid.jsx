const React = require('react');
const classNames = require('classnames');
const Card = require('../card');
const SpinnerRound = require('../../components/spinner_round');
const Alert = require('../../components/alert');
const LazyLoad = require('../../../thirdparty/react-lazy-load/LazyLoad');
const mixins = require('../../../lib/mixins');
// const ResultsCount = require('../results-count');
const NoResults = require('../listings/no-results');

module.exports = React.createClass({

  displayName: 'panel.facebook.grid-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    layout: ['layout', 'facebookGrid'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },
  componentDidMount() {
  },

  onNav(listingId) {
    if (listingId) {
      const listing = this.props.listings.filter((l) => l.Id === listingId)[0];
      if (listing) {
        this.actions.common.goToAgentListing(listing);
      }
    }
  },

  onPhotos(listing) {
    this.props.onPhotos
      ? this.props.onPhotos(listing)
      : this.actions.panels.toggle('photos', listing, 'full');
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  spinner() {
    return (
      <center className="mt30 mb30">
        <SpinnerRound />
      </center>
    );
  },

  cards() {
    return this.props.listings.map((i) => (
      <LazyLoad
        key={i.Id}
        className={classNames('card-holder',
          `listing-mlsidcount-${(i.MlsIds || []).length}`,
          `${(i.MlsIds || []).map((m) => `listing-mlsid-${m}`).join(' ')}`)}
        height="224px"
        width="248px"
        buffer={600}
      >
        <Card
          key={i.Id}
          listing={i}
          mlsId={this.actions.common.getListingMlsId(i)}
          onNav={this.onNav}
        />
      </LazyLoad>
    ), this);
  },

  body() {
    if (this.props.listings === false) {
      return <Alert message="Could not connect to server. Please try again later." />;
    }

    if (this.props.listings && this.props.listings.length === 0) {
      return this.noListings();
    }

    if (!this.props.listings) {
      return this.spinner();
    }

    if (this.props.spinner) {
      return (this.spinner(), this.cards());
    }

    return [this.cards(), <div className="n-play-footer" key="1" />];
  },

  render() {
    if (!this.state.layout) {
      return false;
    }

    return (
      <div className="row col-center layout--1ns grid-container lights-on">
        {this.props.children}
        <div className="grid-cards-container" id="grid-container">
          <div className="cards-holder">
            {this.body()}
          </div>
        </div>
      </div>
    );
  },
});
