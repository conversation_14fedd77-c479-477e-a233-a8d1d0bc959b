const React = require('react');
const classNames = require('classnames');
const Card = require('./card');
const SpinnerRound = require('../../components/spinner_round');
const Alert = require('../../components/alert');
const LazyLoad = require('../../../thirdparty/react-lazy-load/LazyLoad');
const mixins = require('../../../lib/mixins');
const ResultsCount = require('../results-count');
const GridInfoBar = require('../grid-footer/grid-info-bar');
const NoResults = require('../listings/no-results');
const RateplugSubheader = require('../header/rateplug-subheader');
const IDXPlusSubheader = require('../header/idx-plus-subheader');

module.exports = React.createClass({

  displayName: 'panel.grid-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    lightsOff: ['screens', 'grid', 'lightsOut'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },
  componentDidMount() {
  },

  onNav(listingId) {
    this.props.onNav ? this.props.onNav(listingId) : ' ';
  },

  onPhotos(listing) {
    this.props.onPhotos
      ? this.props.onPhotos(listing)
      : this.actions.panels.toggle('photos', listing, 'full');
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  spinner() {
    return (
      <center className="mt30 mb30">
        <SpinnerRound />
      </center>
    );
  },

  cards() {
    return this.props.listings.map((i) => (
      <LazyLoad
        key={i.Id}
        className={classNames('card-holder',
          `listing-mlsidcount-${(i.MlsIds || []).length}`,
          `${(i.MlsIds || []).map((m) => `listing-mlsid-${m}`).join(' ')}`)}
        height="224px"
        width="280px"
      >
        <Card
          key={i.Id}
          id={i.Id}
          listing={i}
          mlsId={this.actions.common.getListingMlsId(i)}
          onNav={this.onNav}
          onPhotos={this.onPhotos}
        />
      </LazyLoad>
    ), this);
  },

  body() {
    if (this.props.listings === 0) {
      return <Alert message="Please login." onClick={this.actions.login.start} />;
    }

    if (this.props.listings === false) {
      return <Alert message="Could not connect to server. Please try again later." />;
    }

    if (this.props.listings && this.props.listings.length === 0) {
      return this.noListings();
    }

    if (!this.props.listings) {
      return this.spinner();
    }

    if (this.props.spinner) {
      return (this.spinner(), this.cards());
    }

    return this.cards();
  },

  render() {
    return (
      <div className={classNames('row col-center layout--1ns grid-container', { 'lights-out': this.state.lightsOff }, { 'lights-on': !this.state.lightsOff })}>
        <div className="grid-rateplug-subheader">
          <RateplugSubheader />
          <IDXPlusSubheader />
        </div>

        {this.props.children}

        <div className="grid-cards-container" id="grid-container">
          {
            this.props.showResultCount
              ? (
                <ResultsCount />
              ) : null
            }
          <div className="cards-holder">
            {this.body()}
          </div>

          <GridInfoBar />
        </div>
      </div>
    );
  },
});
