const React = require('react');
const CardFront = require('../card');
const CardBack = require('../card/back');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.grid-listings.card',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      photoAvailable: true,
      showCardBack: false,
    };
  },

  toggleCard() {
    this.actions.common.flagUserAsInteractedWithSite();
    this.setState({ showCardBack: !this.state.showCardBack });
    this.loadDetails();
  },

  onPhotos(listing) {
    if (this.props.onPhotos) {
      this.props.onPhotos(listing);
    }
  },

  onPhotoNotAvailable() {
    this.setState({ photoAvailable: false });
  },

  handleMouseEnter() {
    this.loadDetails();
  },

  handleTouch() {
    this.loadDetails();
  },

  loadDetails() {
    if ((!this.state.listingDetails) && !this.loadingDetails) {
      this.loadingDetails = true;
      this.actions.grid.getCardData(this.props.listing.Id, (err, data) => {
        this.loadingDetails = false;
        this.setState({ listingDetails: data });
      });
    }
  },

  render() {
    return (
      <div
        onMouseEnter={this.handleMouseEnter}
        onTouchStart={this.handleTouch}
        onTouchEnd={this.handleTouch}
      >
        {
          this.state.showCardBack
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Toggle card"
                onClick={this.toggleCard}
              >
                <CardBack
                  listing={this.props.listing}
                  details={this.state.listingDetails}
                  key={this.props.listing.Id}
                  photoNotAvailable={!this.state.photoAvailable}
                  onNav={this.props.onNav}
                  onPhotos={this.onPhotos}
                />
              </div>
            )
            : (
              <CardFront
                isActive={false}
                listing={this.props.listing}
                mlsId={this.props.mlsId}
                images={this.state.listingDetails && this.state.listingDetails.Images}
                enableSlideshow
                key={this.props.id}
                onNav={this.toggleCard}
                onPhotoNotAvailable={this.onPhotoNotAvailable}
              />
            )
          }
      </div>
    );
  },
});
