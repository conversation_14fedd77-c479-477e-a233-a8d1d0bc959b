const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins');
const Listings = require('../listings');
const MLSDisclosure = require('../mls-disclosure');
const LastRefreshed = require('../last-refreshed');
const NPlayFooter = require('../n-play-footer');
const NoResults = require('../listings/no-results');

module.exports = React.createClass({

  displayName: 'panel.body-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    data: ['panels', 'listings', 'data'],
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    zoomMsg: ['panels', 'listings', 'meta', 'zoomMsg'],
    activeId: ['panels', 'listings', 'activeId'],
    agentMlsId: ['shared', 'agent', 'data', 'MlsId'],
  },

  onNav(id) {
    this.actions[this.props.screen].onNav(id);
  },

  mlsDisclosures() {
    if (!this.state.data || this.state.spinner) {
      return null;
    }

    const mlsIds = this.utils.uniqueMLSIdsFromListings(this.state.data);

    if (!mlsIds || mlsIds.length < 1) {
      return null;
    }

    return map(mlsIds, (mlsId) => <MLSDisclosure mlsId={mlsId} key={mlsId} />);
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  render() {
    return (
      <div className={this.props.className}>

        <div className="listing-shadow" />

        <Listings listings={this.state.data} activeId={this.state.activeId} spinner={this.state.spinner} zoomMsg={this.state.zoomMsg} onNav={this.onNav} fromSearch />
        <div className="right-rail-footer">
          {this.mlsDisclosures()}
          <LastRefreshed className="mb20 mt10" />
          <NPlayFooter />
        </div>
      </div>
    );
  },

});
