const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const mixins = require('../../../lib/mixins');
const GMap = require('./gmap');
const GoogleParcel = require('../map/google-parcel');
const ListingEdgeTabbar = require('../../components/listing_edge_tabbar');

module.exports = React.createClass({

  displayName: 'panel.sub',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  getInitialState() {
    const shouldBeStreet = this.props.showSub ? this.props.showSub === 'street' : false;

    return {
      streetView: shouldBeStreet,
    };
  },

  onAerial(e) {
    e.preventDefault();
    this.props.onAerial ? this.props.onAerial() : this.actions.analytics.sendEvent('detail view', 'map aerial click');
    if (this.refs.gmap) {
      this.setState({ streetView: false });
    }
  },

  onStreet(e) {
    e.preventDefault();
    this.props.onStreet ? this.props.onStreet() : this.actions.analytics.sendEvent('detail view', 'map street click', this.props.streetViewAvailable ? 'available' : 'not available');
    if (this.props.streetViewAvailable) {
      this.setState({ streetView: true });
    }
  },

  componentWillReceiveProps(nextProps) {
    if (nextProps.showSub === 'street' && this.props.streetViewAvailable) {
      this.setState({ streetView: true });
    } else if (nextProps.showSub === 'aerial' || nextProps.showSub === 'map') {
      this.setState({ streetView: false });
    }
  },

  panel() {
    if (!this.props.showSub || !this.props.data) {
      return null;
    }
    return (
      <div className="layout--1f-4 listing-map-subpanel">
        <ListingEdgeTabbar
          className="on-map"
          onClose={this.props.onCloseSub}
          onStreet={this.onStreet}
          onAerial={this.onAerial}
          onParcel={this.props.onParcel}
          onMap={this.props.onMap}
          data={this.props.data}
          streetViewAvailable={this.props.streetViewAvailable}
        />
        {
            this.props.showSub === 'parcel'
              ? <GoogleParcel listing={this.props.data} />
              : (
                <GMap
                  ref="gmap"
                  type={this.props.showSub}
                  streetView={this.state.streetView}
                  location={this.props.data.Location}
                  displayAddress={this.props.data.DisplayAddress}
                />
              )
          }
      </div>
    );
  },

  render() {
    return (
      <div className="sub">
        {this.props.showSub
          ? (
            <div
              role="button"
              tabIndex="-1"
              aria-label="Close"
              className="layout--1f-4-hide"
              onClick={this.onClose}
            />
          ) : null}

        <ReactCSSTransitionGroup transitionName="sub-left" transitionLeave={false} transitionEnterTimeout={200}>
          {this.panel()}
        </ReactCSSTransitionGroup>
      </div>
    );
  },

});
