const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'sub.gmap',

  mixins: [mixins.debug, mixins.actions],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    location: React.PropTypes.object,
  },

  marker: null,

  componentDidMount() {
    this.actions.analytics.sendEvent('detail view', 'gmap', 'load');

    this.map = this.createMap();
    this.streetView = this.map.getStreetView();

    this.streetView.setOptions(
      {
        addressControl: this.map.get('addressControl'),
        panControl: this.map.get('panControl'),
        panControlOptions: this.map.get('panControlOptions'),
        zoomControl: this.map.get('zoomControl'),
        zoomControlOptions: this.map.get('zoomControlOptions'),
        enableCloseButton: false,
      },
    );

    const propertyLatLon = new google.maps.LatLng(this.props.location.Lat, this.props.location.Lon);
    this.streetView.setPosition(propertyLatLon);
    this.streetView.setVisible(this.props.streetView || false);

    const thatStreetView = this.streetView;
    const marker = this.marker;
    google.maps.event.addListenerOnce(thatStreetView, 'status_changed', () => {
      const heading = google.maps.geometry.spherical.computeHeading(
        thatStreetView.getLocation().latLng,
        propertyLatLon,
      );
      thatStreetView.setPov({
        heading,
        pitch: 0,
      });
      if (marker) {
        marker.setPosition(propertyLatLon);
      }
    });
  },

  componentWillUnmount() {
    // How to remove the maps instance from memory
    // http://stackoverflow.com/questions/21142483/google-maps-js-v3-detached-dom-tree-memory-leak
  },

  componentWillReceiveProps(nextProps) {
    if (this.streetView) {
      const streetViewIsVisible = this.streetView.getVisible();
      if (streetViewIsVisible != nextProps.streetView) {
        this.streetView.setPosition({ lat: this.props.location.Lat, lng: this.props.location.Lon });
        this.streetView.setVisible(nextProps.streetView);
      }
    }
    if (nextProps.type === 'aerial') {
      this.setMapType('SATELLITE');
    }
    if (nextProps.type === 'map') {
      this.setMapType('ROADMAP');
    }
  },

  createMap() {
    const propertyLatLon = new google.maps.LatLng(this.props.location.Lat, this.props.location.Lon);

    const mapOptions = {
      mapTypeId: this.props.type === 'map' ? google.maps.MapTypeId.ROADMAP : google.maps.MapTypeId.SATELLITE,
      zoom: 19,
      tilt: 45,
      mapTypeControl: false,
      streetViewControl: false,
      overviewMapControl: false,
      addressControl: false,
      center: propertyLatLon,
      zoomControl: true,
      zoomControlOptions: {
        style: google.maps.ZoomControlStyle.LARGE,
        position: google.maps.ControlPosition.TOP_RIGHT,
      },
      panControl: false,
      panControlOptions: {
        position: google.maps.ControlPosition.TOP_RIGHT,
      },
    };

    const map = new google.maps.Map(this.refs.map, mapOptions);
    this.actions.analytics.sendEvent('detail view', 'gmap', 'create map');

    if (this.props.displayAddress) {
      this.marker = new google.maps.Marker({
        position: propertyLatLon,
        map,
        title: 'House',
      });
    }

    return map;
  },

  setMapType(type) {
    if (!this.map) {
      return;
    }
    this.map.setMapTypeId(google.maps.MapTypeId[type]);
  },

  inStreetView() {
    return this.streetView ? this.streetView.getVisible() : false;
  },

  shouldComponentUpdate() {
    // Force the map to never re-render
    return false;
  },

  render() {
    return (
      <div className="map" ref="map" />
    );
  },

});
