const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins');
const ListingsHorizontal = require('../listings/horizontal');
const MLSDisclosure = require('../mls-disclosure');
const NoResults = require('../listings/no-results');

module.exports = React.createClass({

  displayName: 'panel.body-listings-horizontal',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils],

  cursors: {
    data: ['panels', 'listings', 'data'],
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    activeId: ['screens', 'map', 'mobilePreviewData', 'Id'],
    agentMlsId: ['shared', 'agent', 'data', 'MlsId'],
  },

  componentDidMount() {
    this.scrollToActiveId(this.state.activeId);
  },

  componentWillUpdate(nextProps) {
    if (nextProps.activeId && this.state.data) {
      if (nextProps.activeId !== this.props.activeId) {
        this.scrollToActiveId(nextProps.activeId);
      }
    }
  },

  scrollToActiveId(activeId) {
    let index = -1;

    for (let i = 0; i < (this.state.data || []).length; i++) {
      const l = this.state.data[i];
      if (l.Id === activeId) {
        index = i;
        break;
      }
    }

    if (index > -1) {
      this.refs.ListingsHorizontal.scrollTo(index);
      this.refs.ListingsHorizontal.forceUpdate();
    }
  },

  onNav(id) {
    this.actions.map.onNav(id);
  },

  swipeCallback() {
    this.actions.panels.hideHeaders();
  },

  swipeEndCallback(index) {
    const id = this.state.data[index].Id;
    this.actions.map.setMobileCardDataWithId(id);
    this.actions.map.setActiveMarker(id);
  },

  mlsDisclosures() {
    if (!this.state.data || this.state.spinner) {
      return null;
    }

    const mlsIds = this.utils.uniqueMLSIdsFromListings(this.state.data);

    if (!mlsIds || mlsIds.length < 1) {
      return null;
    }

    return map(mlsIds, (mlsId) => <MLSDisclosure mlsId={mlsId} key={mlsId} />);
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  render() {
    return (
      <div className={this.props.className}>
        <ListingsHorizontal
          listings={this.state.data}
          activeId={this.state.activeId}
          spinner={this.state.spinner}
          onNav={this.onNav}
          fromSearch
          swipeCallback={this.swipeCallback}
          swipeEndCallback={this.swipeEndCallback}
          ref="ListingsHorizontal"
        />
      </div>
    );
  },

});
