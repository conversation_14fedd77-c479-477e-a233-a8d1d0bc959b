import Draggable from 'react-draggable';

const React = require('react');
const _ = require('lodash');
const querystring = require('querystring');
const copy = require('clipboard-copy');
const mixins = require('../../../lib/mixins/index');
const SVGIcon = require('../../components/svg_icon');

const EmployeeUi = React.createClass({

  displayName: 'panels.employee-ui',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.router, mixins.pureRender],

  cursors: {
    currentAgent: ['shared', 'agent', 'data'],
    currentAgentPickTrackingId: ['shared', 'agentPickerMetadata', 'currentAgentPickTrackingId'],
    lastAgentSwitchTrackingId: ['shared', 'agentPickerMetadata', 'lastAgentSwitchTrackingId'],
    onboardingAgent: ['shared', 'onboardingAgent', 'data'],
    currentListings: ['panels', 'listings', 'data'],
    layout: ['layout', 'employeeUi'],
    mlsArray: ['shared', 'mlsData'],
    currentMapListing: ['screens', 'map', 'data'],
    currentGridListing: ['screens', 'grid', 'data'],
    currentAgentListing: ['screens', 'agent', 'activeListing'],
    currentFeaturedListing: ['screens', 'featured', 'detail'],
    currentTaggedListings: ['screens', 'tagging', 'data', 'Listings'],
  },

  facets: {
    currentFeaturedListings: ['featuredListings'],
  },

  getInitialState() {
    return {
    };
  },
  renderOnboardingAgent() {
    const facelessUserUrl = 'https://nplayassets.blob.core.windows.net/images/faceless-user.png';
    const agent = this.actions.common.getOnboardingAgent();
    if (!agent) {
      return <img alt="No agent" src={facelessUserUrl} title="No onboarding agent" />;
    }

    const src = _.get(agent, 'ProfileImage');
    const href = `${BASE_API_URL}agentsearch/getagent/?id=${agent.Id}`;
    return (
      <a href={href} target="_blank">
        <img
          alt="Agent"
          title={`View onboarding agent data | ${agent.FirstName} ${agent.LastName} #${agent.Id}`}
          src={src}
          onError={(e) => {
            e.target.onerror = null; e.target.src = facelessUserUrl;
          }}
        />
      </a>
    );
  },

  renderCurrentAgent() {
    const facelessUserUrl = 'https://nplayassets.blob.core.windows.net/images/faceless-user.png';
    if (!this.state.currentAgent) {
      return <img alt="No agent" src={facelessUserUrl} />;
    }

    const agent = this.state.currentAgent;
    const src = agent.ProfileImage;
    const href = `${BASE_API_URL}agentsearch/getagent/?id=${agent.Id}`;
    return (
      <a href={href} target="_blank">
        <img
          alt="Agent"
          title={`View current agent data | ${agent.FirstName} ${agent.LastName} #${agent.Id}`}
          src={src}
          onError={(e) => {
            e.target.onerror = null; e.target.src = facelessUserUrl;
          }}
        />
      </a>
    );
  },

  renderCurrentListing() {
    const listing = this.actions.common.getActiveListing();
    if (!listing) {
      return (
        <div title="No current listing">
          <SVGIcon name="icon-house" />
        </div>
      );
    }

    const href = `${BASE_API_URL}listings/${listing.Id}`;
    return (
      <a href={href} target="_blank">
        <img alt="Listing" src={listing.DefaultImage} title="View current listing data" />
      </a>
    );
  },

  renderMlsList() {
    const mlsArray = this.actions.common.getAllVisibleMlsArray();
    let links = _.map(mlsArray, (mls) => (
      <div key={mls.Id}>
        <a
          title={`View data for ${mls.Name}`}
          href={`${BASE_API_URL}mls/${mls.Id}`}
          target="_blank"
        >
          {mls.Id}
        </a>
      </div>
    ));

    if (!links.length) {
      links = <div className="note">No MLS</div>;
    }

    return (
      <div className="mlsList">
        {links}
      </div>
    );
  },

  renderTrackingIdLinks() {
    const currentTrackingIdUrl = `https://logging.homeasap.com/app/kibana#/dashboard/33560e80-fe40-11e8-bf45-1dfb11f30015?_g=(refreshInterval:(display:on,pause:!f,value:0),time:(from:now-24h,interval:auto,mode:quick,timezone:America%2FNew_York,to:now))&amp;_a=(description:'',filters:!(),fullScreenMode:f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((gridData:(h:7,i:'1',w:12,x:0,y:0),id:a6cbed50-fe3e-11e8-bf45-1dfb11f30015,panelIndex:'1',type:search,version:'6.2.2')),query:(language:lucene,query:'trackingId:%222d3371db-5b15-4caf-86a7-c850aae7b00b%22'),timeRestore:!f,title:'View+trackingId+log',viewMode:view)&_a=(description:'',filters:!(),fullScreenMode:!f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((gridData:(h:14,i:'1',w:12,x:0,y:0),id:a6cbed50-fe3e-11e8-bf45-1dfb11f30015,panelIndex:'1',sort:!(sequence,asc),type:search,version:'6.2.2')),query:(language:lucene,query:'trackingId:+%22${this.state.currentAgentPickTrackingId}%22'),timeRestore:!f,title:'View+trackingId+log',viewMode:view)`;
    const lastSwitchTrackingIdUrl = `https://logging.homeasap.com/app/kibana#/dashboard/33560e80-fe40-11e8-bf45-1dfb11f30015?_g=(refreshInterval:(display:on,pause:!f,value:0),time:(from:now-24h,interval:auto,mode:quick,timezone:America%2FNew_York,to:now))&amp;_a=(description:'',filters:!(),fullScreenMode:f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((gridData:(h:7,i:'1',w:12,x:0,y:0),id:a6cbed50-fe3e-11e8-bf45-1dfb11f30015,panelIndex:'1',type:search,version:'6.2.2')),query:(language:lucene,query:'trackingId:%222d3371db-5b15-4caf-86a7-c850aae7b00b%22'),timeRestore:!f,title:'View+trackingId+log',viewMode:view)&_a=(description:'',filters:!(),fullScreenMode:!f,options:(darkTheme:!f,hidePanelTitles:!f,useMargins:!t),panels:!((gridData:(h:14,i:'1',w:12,x:0,y:0),id:a6cbed50-fe3e-11e8-bf45-1dfb11f30015,panelIndex:'1',sort:!(sequence,asc),type:search,version:'6.2.2')),query:(language:lucene,query:'trackingId:+%22${this.state.lastAgentSwitchTrackingId}%22'),timeRestore:!f,title:'View+trackingId+log',viewMode:view)`;
    const links = (
      <div>
        <div>
          <a
            title="Current tracking id"
            href={`${currentTrackingIdUrl}`}
            target="_blank"
          >
            Why Picked?
          </a>
        </div>
        <div>
          <a
            title="Last switch tracking id"
            href={`${lastSwitchTrackingIdUrl}`}
            target="_blank"
          >
            Why Kept?
          </a>
        </div>
      </div>
    );

    return (
      <div className="trackingIdLinks">
        {links}
      </div>
    );
  },

  renderCopyFilters() {
    const links = (
      <a
        title="Copy filters"
        href="#"
        onClick={(e) => {
          e.preventDefault();
          const filterQs = this.actions.menu.getSearchSelectionsOpts();
          const rawQs = this.utils._optsToParams(filterQs);
          // Delete rateplug specific filters
          delete rawQs.hp;
          delete rawQs.lp;
          delete rawQs.dp;
          delete rawQs.ir;
          delete rawQs.nm;

          const qs = querystring.stringify(rawQs)
            .replace(/tp=/g, 'tp[]=')
            .replace(/att=/g, 'att[]=');
          copy(qs);
        }}
        role="button"
        tabIndex={-1}
      >
        Copy Filters
      </a>
    );

    const filters = this.state.currentAgent && this.utils.getAgentSettingValue(this.state.currentAgent.AgentSettings, 'feature_listings_filters');
    // eslint-disable-next-line
    const label = filters ? <label title={filters}>✔️ Has FL Filters</label> : null;

    return (
      <div>
        {links}
        <br />
        {label}
      </div>
    );
  },

  handleClose() {
    this.actions.common.disableEmployeeUi();
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div id="employee-ui-bounds">
        <Draggable
          bounds="parent"
          handle=".handle"
        >
          <div id="employee-debug-popup">
            <div className="handle">
              <SVGIcon name="icon-drag-handle" />
            </div>
            <div
              role="button"
              tabIndex="-1"
              aria-label="Close"
              className="close"
              onClick={this.handleClose}
              title="Exit Employee UI. To re-enable, visit https://homeasap.com/employee"
            >
              <SVGIcon name="icon-close-button" />
            </div>
            <div className="sections">
              <div>
                <div>
                  {this.renderOnboardingAgent()}
                </div>
              </div>
              <div>
                <div>
                  {this.renderCurrentAgent()}
                </div>
              </div>
              <div>
                <div>
                  {this.renderTrackingIdLinks()}
                </div>
              </div>
              <div>
                <div>
                  {this.renderCurrentListing()}
                </div>
              </div>
              <div>
                <div>
                  {this.renderMlsList()}
                </div>
              </div>
              <div>
                <div>
                  {this.renderCopyFilters()}
                </div>
              </div>
            </div>
          </div>
        </Draggable>
      </div>
    );
  },

});

module.exports = EmployeeUi;
