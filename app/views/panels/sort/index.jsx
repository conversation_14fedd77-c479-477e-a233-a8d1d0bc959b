const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const mixins = require('../../../lib/mixins/index');
const Sort = require('../grid-menu/sort');

module.exports = React.createClass({

  displayName: 'panels.sort',

  lastScrollPosition: 0,

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    sortType: ['shared', 'menu', 'sortType'],
    showSort: ['screens', 'map', 'sort'],
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },

  getInitialState() {
    return {
      showPanel: false,
    };
  },
  showSortRail() {
    this.actions.panels.showSortRail();
  },

  hideSortRail() {
    this.actions.panels.hideSortRail();
  },
  handleScroll(e) {
    e.preventDefault();
    const scrollTop = window.pageYOffset;

    if (scrollTop > this.lastScrollPosition && this.lastScrollPosition > 0) {
      this.hideSortRail();
    } else if (scrollTop < this.lastScrollPosition) {
      this.showSortRail();
    }
    this.lastScrollPosition = scrollTop;
  },

  handleClick() {
    if (this.state.showPanel) {
      this.setState({ showPanel: false });
    }
  },

  componentDidMount() {
    window.addEventListener('scroll', this.handleScroll);
    window.addEventListener('click', this.handleClick);
  },

  componentWillUnmount() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('click', this.handleClick);
  },

  onSortClick(e) {
    e.stopPropagation();
    this.setState({ showPanel: !this.state.showPanel });
  },

  onSortChange() {
    if (this.state.showPanel) {
      this.setState({ showPanel: false });
    }
  },

  getSortText() {
    switch (this.state.sortType) {
      case 'price_asc':
        return 'Price - lowest first';
      case 'price_desc':
        return 'Price - highest first';
      case 'sqft_desc':
        return 'Square feet - largest first';
      case 'distance_asc':
        return 'Distance - closest first';
      case 'yearBuilt_asc':
        return 'Year built - newest first';
      case 'yearBuilt_desc':
        return 'Year built - oldest first';
      default: // case "daysOnMarket_asc":
        return (this.state.showDaysOnMarket ? 'Days on market - newest first' : 'Newest first');
    }
  },

  getSort() {
    return (
      <span className="mn">
        &nbsp;
        {this.getSortText()}
      </span>
    );
  },

  render() {
    return (
      <div>
        <ReactCSSTransitionGroup transitionName="slide" transitionLeaveTimeout={200} transitionEnterTimeout={200}>
          {
            this.state.showSort
              ? (
                <div role="button" tabIndex="-1" aria-label="Sort" onClick={this.onSortClick} className="sort-header">
                  <b>Sort:&nbsp;</b>
                  {this.getSort()}
                </div>
              )
              : null
          }
        </ReactCSSTransitionGroup>
        { this.state.showPanel ? (
          <Sort key="sort" onSortChange={this.onSortChange} />) : null}

      </div>
    );
  },
});
