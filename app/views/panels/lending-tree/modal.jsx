const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const CloseButton = require('../../components/close_btn_cross');

module.exports = React.createClass({
  displayName: 'panel.lending-tree.modal',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils, mixins.events],

  getInitialState() {
    return {
      submitting: false,
      url: null,
    };
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    showLendingTreeModal: ['layout', 'lendingTreeModal'],
  },

  listingViewCount: 0,

  hasSubmitted: () => !!window.localStorageAlias.getItem('HA_LENDINGTREE_SUBMITTED'),
  hasViewed: () => !!window.sessionStorageAlias.getItem('HA_LENDINGTREE_VIEWED'),

  componentDidMount() {
    this.events.on(this.events.LISTING_VIEWED, () => {
      this.listingViewCount += 1;

      if (this.listingViewCount
        === (this.actions.common.forceLoginFirstInteraction() ? 2 : 1)) {
        if (!this.hasSubmitted() && !this.hasViewed()) {
          window.sessionStorageAlias.setItem('HA_LENDINGTREE_VIEWED', '1');
          // LISTING_VIEWED is triggered during route change
          // We need to add a delay because so the layout is not reset
          setTimeout(() => {
            this.actions.common.showLendingTreeModal();
          }, 100);
        }
      }
    });
  },

  modalClose() {
    this.setState({ url: null });
    this.actions.common.hideLendingTreeModal();
  },

  submit(e) {
    e.preventDefault();
    const buyer = ['firstName', 'lastName', 'email'].reduce((map, name) => {
      map[name] = e.target.elements[name].value.trim();
      return map;
    }, {});

    this.actions.analytics.sendEvent('lending tree', 'submit');
    window.localStorageAlias.setItem('HA_LENDINGTREE_SUBMITTED', '1');

    this.actions.buyer.createLendingTreeLead(buyer, (err) => {
      if (!err) {
        this.actions.analytics.sendEvent('lending tree', 'saved lead');
      } else {
        this.actions.analytics.sendEvent('lending tree', 'failed to saved lead', 'buyer', JSON.stringify(buyer));
      }
    });

    const lendingtreeParams = new URLSearchParams();
    lendingtreeParams.append('firstName', buyer.firstName);
    lendingtreeParams.append('lastName', buyer.lastName);
    lendingtreeParams.append('email', buyer.email);

    const activeListing = this.actions.common.getActiveListing();
    if (activeListing && activeListing.Id) {
      lendingtreeParams.append('listingId', activeListing.Id);
    }

    // this.setState({ submitting: false, url: `/lendingtree-widget?${lendingtreeParams.toString()}` });
    window.open(`${window.location.origin}/lendingtree-widget?${lendingtreeParams.toString()}`, '_blank');
    this.actions.common.hideLendingTreeModal();
  },

  renderForm() {
    const buyer = this.actions.common.getBuyerData();

    return (
      <div className="lending-tree-modal-content">
        <form onSubmit={this.submit}>
          <h3 className="mb15">
            See Current Mortgage Rates
            Before You Shop for a Home.
          </h3>
          <p className="mb15">{`${''}In today's volatile markets, finding the best mortgage rates before you begin looking at homes is essential to know what you can afford. Knowledge is power, and use it to get your next dream home. In just a few minutes, see current mortgage rates and loan programs that fit your budget, then start home shopping!`}</p>

          <div className="col-1-2 pr5">
            <input type="text" className="form-control" data-populate-buyer="FirstName" name="firstName" placeholder="First Name" defaultValue={buyer && buyer.FirstName || ''} required />
          </div>
          <div className="col-1-2 pl5">
            <input type="text" className="form-control" data-populate-buyer="LastName" name="lastName" placeholder="Last Name" defaultValue={buyer && buyer.LastName || ''} required />
          </div>
          <div className="col-1-1 mt10">
            <input type="email" className="form-control" data-populate-buyer="Email" name="email" placeholder="Email" defaultValue={buyer && buyer.Email || ''} required />
          </div>

          <div className="col-1-1 mt15">
            <button type="submit" className="btn btn-success btn-block pt10 pb10" disabled={this.state.submitting}>
              {this.state.submitting ? 'Loading...' : 'GET STARTED NOW!'}
            </button>
          </div>

          <div className="lending-tree-modal-footer mt15">
            <small />
            <img alt="Lending Tree" src="https://nplayassets.blob.core.windows.net/images/powered-by-lendingtree.svg" />
          </div>
        </form>
      </div>
    );
  },

  render() {
    if (!this.state.showLendingTreeModal) {
      return null;
    }

    if (!this.actions.common.getAgentHasLendingTree(this.state.agentData)) {
      return null;
    }

    return (
      <Modal
        show={this.state.showLendingTreeModal}
        onHide={this.modalClose}
        id="lending-tree-modal"
        className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
      >
        <CloseButton onClick={this.modalClose} />
        {this.state.url ? (
          <div className="lending-tree-modal-iframe">
            <iframe title="Lending Tree Embed" src={this.state.url} />
          </div>
        ) : this.renderForm()}
      </Modal>
    );
  },
});
