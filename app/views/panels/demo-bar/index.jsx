const React = require('react');
const ReactDOM = require('react-dom');
const map = require('lodash.map');
const filter = require('lodash.filter');
const Modal = require('react-bootstrap').Modal;
const FormGroup = require('react-bootstrap').FormGroup;
const ControlLabel = require('react-bootstrap').ControlLabel;
const FormControl = require('react-bootstrap').FormControl;
const Button = require('react-bootstrap').Button;
const classNames = require('classnames');
const mixins = require('../../../lib/mixins/index');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.demo-bar',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'demo'],
    demoAgentData: ['shared', 'demoAgent', 'data'],
  },

  getInitialState() {
    return {
      stateCode: 0,
      mlsId: 0,
      mlses: [],
      officeIdStatus: 'default',
    };
  },

  componentDidUpdate() {
    if (this.state.demoAgentData && this.state.demoAgentData.MlsId && this.state.officeIdStatus === 'default' && !this.hasAutoLoaded) {
      this.hasAutoLoaded = true;

      this.actions.demo.getMlsState(this.state.demoAgentData.MlsId,
        (stateCode) => {
          if (stateCode) {
            setTimeout(function (state) {
              this.setState({
                stateCode: state,
                mlsId: this.state.demoAgentData.MlsId,
              });
              this.actions.demo.mlsByState(state, (mlses) => {
                this.setState({ mlses });
              });
            }.bind(this, stateCode), 0);
          }
        });
    }
  },

  stopDemo() {
    this.actions.demo.stopDemo();
  },

  hideDemoBar() {
    this.actions.demo.hideDemoBar();
  },

  inactiveAck() {
    this.stopDemo();
    window.location.href = '/search';
  },

  setDemoTrue() {
    this.actions.demo.setDemoLayout(true);
  },

  stateChanged() {
    this.actions.analytics.sendEvent('Demo Site', 'select state', this.state.demoAgentData && this.state.demoAgentData.Id);

    const stateCode = (ReactDOM.findDOMNode(this.refs['state-selection']) || {}).value;
    if (stateCode) {
      this.setState({ stateCode, mlses: [], mlsId: 0 });
      const that = this;
      this.actions.demo.mlsByState(stateCode, (mlses) => {
        that.setState({ mlses });
      });
    } else {
      this.setState({ stateCode: 0 });
    }
  },

  mlsChanged() {
    this.actions.analytics.sendEvent('Demo Site', 'select mls', this.state.demoAgentData && this.state.demoAgentData.Id);

    this.setState({ mlsId: (ReactDOM.findDOMNode(this.refs['mls-selection']) || {}).value || 0 });
  },

  getSelectedMlsName() {
    const mlsMatch = filter(this.state.mlses, (mls) => mls.id === this.state.mlsId);

    if (mlsMatch && mlsMatch.length === 1) {
      return mlsMatch[0].name;
    }
    return '';
  },

  buildSite(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.analytics.sendEvent('Demo Site', 'build my site', this.state.mlsId);

    this.hasAutoLoaded = true;

    const officeId = (ReactDOM.findDOMNode(this.refs['office-id']) || {}).value;
    const agentId = (ReactDOM.findDOMNode(this.refs['agent-id']) || {}).value;

    if (this.state.mlsId) {
      this.actions.demo.checkCredentials({
        mlsId: this.state.mlsId,
        officeId,
        agentId,
        nplayAgentId: this.state.demoAgentData && this.state.demoAgentData.Id,
      }, (res) => {
        if (res) {
          const officeIdVerified = res.IsValidOfficeId;
          const agentIdVerified = res.IsValidAgentId;

          if (!officeId && !agentId) {
            this.setState({ officeIdStatus: 'incorrect-both' });
            this.actions.analytics.sendEvent('Demo Site', 'build my site incorrect-office-and-agent-id', this.state.mlsId);
          } else if (officeId && !officeIdVerified) {
            this.setState({ officeIdStatus: 'incorrect-office-id' });
            this.actions.analytics.sendEvent('Demo Site', 'build my site incorrect-office-id', this.state.mlsId);
          } else if (agentId && !agentIdVerified) {
            this.setState({ officeIdStatus: 'incorrect-agent-id' });
            this.actions.analytics.sendEvent('Demo Site', 'build my site incorrect-agent-id', this.state.mlsId);
          }
        } else {
          this.setState({ officeIdStatus: 'error' });
          this.actions.analytics.sendEvent('Demo Site', 'build my site error', this.state.mlsId);
        }
      }, { dontSetUnlessValidated: true });
    }
  },

  getServiceZip() {
    if (this.state.layout === 'built') {
      const info = this.actions.demo.getInfo();
      if (info && info.Address) {
        return ` Near ${info.Address.ZipCode}`;
      }
    }

    return '';
  },

  getIDX() {
    this.actions.analytics.sendEvent('Demo Site', 'get IDX', this.state.demoAgentData.Id);

    window.open(`/_readredirect?membershipid=${this.state.demoAgentData.Id
    }&id=${this.actions.demo.getCid() || ''
    }&ReturnUrl=/NPlay.AgentDirectory.Web/Services/SearchAlliance/Join`,
    '_blank');
  },

  finishSetup() {
    this.actions.analytics.sendEvent('Demo Site', 'finish setup', this.state.demoAgentData.Id);

    window.open(`/_readredirect?membershipid=${this.state.demoAgentData.Id
    }&id=${this.actions.demo.getCid() || ''
    }&ReturnUrl=/NPlay.AgentDirectory.Web/SAControlPanel`,
    '_blank');
  },

  toggleToolTip(shouldShow) {
    this.setState({ toolTipShow: typeof shouldShow === 'boolean' ? shouldShow : !this.state.toolTipShow });
  },

  showToolTip() {
    this.toggleToolTip(true);
  },

  hideToolTip() {
    this.toggleToolTip(false);
  },

  reEnter() {
    window.location.reload();
  },

  getActionLink() {
    if (!this.state.demoAgentData) {
      return null;
    }

    const purchased = this.state.demoAgentData.IsHomeSearchSignupCompleted;
    const allGood = this.state.demoAgentData.HomeSearchRegisteredDateTime
      && this.state.demoAgentData.HomeSearchRegisteredDateTime != 'demo'
      && this.state.demoAgentData.AsapStatus;

    if (purchased && !allGood) {
      return (
        <a
          role="button"
          tabIndex="-1"
          className="btn btn-md btn-success cursor-pointer"
          onClick={this.finishSetup}
        >
          FINISH SETUP
        </a>
      );
    }

    return (
      <a
        role="button"
        tabIndex="-1"
        className="btn btn-md btn-success cursor-pointer"
        onClick={this.getIDX}
      >
        JOIN ALLIANCE
      </a>
    );
  },

  render() {
    if (this.state.layout === 'purchased') {
      this.actions.analytics.sendEvent('Demo Site', 'already purchased', this.state.demoAgentData && this.state.demoAgentData.Id);
    }

    return (
      <div className="demo-bar">
        <div className="demo-logo">
          <SVGIcon name="logo-search-alliance" className="logo-search-alliance" />
        </div>
        <p className="demo-title text-muted text-center">PRIVATE PREVIEW</p>
        <div className="pull-right demo-buttons">
          {this.getActionLink()}
        </div>

        <Modal onHide={function () { }} show={false}>
          <Modal.Header>
            <Modal.Title>
              Exit Demo
              <Button
                type="button"
                className="pull-right hidden-md hidden-lg hidden-xl"
                onClick={function () {
                  this.setState({ showModal: false });
                }.bind(this)}
              >
                Cancel
              </Button>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Do you want to hide the bar or stop this demo for good
              ?
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              type="button"
              className="hidden-xs hidden-sm"
              onClick={function () {
                this.setState({ showModal: false });
              }.bind(this)}
            >
              Cancel
            </Button>
            <Button type="button" bsStyle="danger" onClick={this.stopDemo}>Quit Demo</Button>
            <Button type="button" onClick={this.hideDemoBar}>Hide Bar</Button>
          </Modal.Footer>
        </Modal>

        <Modal onHide={function () { }} show={this.state.layout === 'purchased'} className="demo-modal">
          <div className="header-svg check">
            <SVGIcon name="icon-checkmark" />
          </div>
          <h1>You&apos;ve Already Purchased Search Alliance!</h1>
          <p>Check out some of the awesome new features we&apos;ve added for you!</p>
          <Button type="button" bsStyle="success" onClick={this.setDemoTrue}>GO CHECK IT OUT!</Button>
        </Modal>

        <Modal onHide={function () { }} show={this.state.layout === 'pending-build'} className="demo-modal pending-build">
          <div className="header-svg build">
            <SVGIcon name="icon-rocket" />
          </div>
          <h1>Let’s Launch Your Demo Site!</h1>
          <p>Just select your MLS and click launch to begin. </p>

          <form>
            <FormGroup validationState={function () {
              return this.state.stateCode && 'success' || null;
            }.bind(this)()}
            >
              <ControlLabel>SELECT YOUR STATE*</ControlLabel>
              <FormControl
                type="select"
                componentClass="select"
                ref="state-selection"
                value={this.state.stateCode}
                placeholder="State"
                required
                onChange={this.stateChanged}
              >

                <option value="0">--- State ---</option>
                <option id="AL" value="AL">Alabama</option>
                <option id="AK" value="AK">Alaska</option>
                <option id="AS" value="AS">American Samoa</option>
                <option id="AZ" value="AZ">Arizona</option>
                <option id="AR" value="AR">Arkansas</option>
                <option id="CA" value="CA">California</option>
                <option id="CO" value="CO">Colorado</option>
                <option id="CT" value="CT">Connecticut</option>
                <option id="DE" value="DE">Delaware</option>
                <option id="DC" value="DC">District of Columbia</option>
                <option id="FL" value="FL">Florida</option>
                <option id="GA" value="GA">Georgia</option>
                <option id="GU" value="GU">Guam</option>
                <option id="HI" value="HI">Hawaii</option>
                <option id="ID" value="ID">Idaho   </option>
                <option id="IL" value="IL">Illinois</option>
                <option id="IN" value="IN">Indiana</option>
                <option id="IA" value="IA">Iowa</option>
                <option id="KS" value="KS">Kansas</option>
                <option id="KY" value="KY">Kentucky </option>
                <option id="LA" value="LA">Louisiana </option>
                <option id="ME" value="ME">Maine</option>
                <option id="MD" value="MD">Maryland</option>
                <option id="MA" value="MA">Massachusetts</option>
                <option id="MI" value="MI">Michigan</option>
                <option id="MN" value="MN">Minnesota</option>
                <option id="MS" value="MS">Mississippi</option>
                <option id="MO" value="MO">Missouri</option>
                <option id="MT" value="MT">Montana</option>
                <option id="NE" value="NE">Nebraska</option>
                <option id="NV" value="NV">Nevada</option>
                <option id="NH" value="NH">New Hampshire</option>
                <option id="NJ" value="NJ">New Jersey</option>
                <option id="NM" value="NM">New Mexico</option>
                <option id="NY" value="NY">New York</option>
                <option id="NC" value="NC">North Carolina</option>
                <option id="ND" value="ND">North Dakota</option>
                <option id="OH" value="OH">Ohio</option>
                <option id="OK" value="OK">Oklahoma </option>
                <option id="OR" value="OR">Oregon</option>
                <option id="PA" value="PA">Pennsylvania</option>
                <option id="PR" value="PR">Puerto Rico</option>
                <option id="RI" value="RI">Rhode Island</option>
                <option id="SC" value="SC">South Carolina</option>
                <option id="SD" value="SD">South Dakota</option>
                <option id="TN" value="TN">Tennessee</option>
                <option id="TX" value="TX">Texas</option>
                <option id="UT" value="UT">Utah</option>
                <option id="VT" value="VT">Vermont</option>
                <option id="VI" value="VI">Virgin Islands</option>
                <option id="VA" value="VA">Virginia</option>
                <option id="WA" value="WA">Washington</option>
                <option id="WV" value="WV">West Virginia</option>
                <option id="WI" value="WI">Wisconsin</option>
                <option id="WY" value="WY">Wyoming</option>

              </FormControl>
            </FormGroup>

            <FormGroup validationState={function () {
              return this.state.mlsId && 'success' || null;
            }.bind(this)()}
            >
              <ControlLabel className="mt15">SELECT YOUR MLS*</ControlLabel>
              <FormControl
                type="select"
                componentClass="select"
                ref="mls-selection"
                value={this.state.mlsId}
                placeholder="MLS"
                required
                onChange={this.mlsChanged}
              >
                <option value="0">---  MLS  ---</option>
                {
                  map(this.state.mlses, (mls) => <option value={mls.id} key={mls.id}>{mls.name}</option>)
                }
              </FormControl>
            </FormGroup>

            <div className="col-1-1 optional-label">
              <label htmlFor="demo-optional">Optional</label>
              <hr />
            </div>

            <div id="demo-optional" className={classNames('row optional', { hidden: this.state.officeIdStatus !== 'default' })}>

              <div className="row-expanded">
                <div className="col-5-12">
                  <ControlLabel>Agent ID</ControlLabel>
                  <FormControl
                    type="text"
                    ref="agent-id"
                    placeholder=""
                    onFocus={function () {
                      this.actions.analytics.sendEvent('Demo Site', 'enter agentID', this.state.demoAgentData && this.state.demoAgentData.Id);
                    }.bind(this)}
                    className="mb0"
                  />
                </div>
                <label htmlFor="demo-optional" className="col-2-12 mt30 text-center"><em>-or-</em></label>
                <div className="col-5-12">
                  <ControlLabel>Office ID</ControlLabel>
                  <FormControl
                    type="text"
                    ref="office-id"
                    placeholder="(ex: WTS15)"
                    onFocus={function () {
                      this.actions.analytics.sendEvent('Demo Site', 'enter officeID', this.state.demoAgentData && this.state.demoAgentData.Id);
                    }.bind(this)}
                    className="mb0"
                  />
                </div>
                <div>
                  <small className="text-center mt0"><em>This is how we will find your featured listings.</em></small>
                </div>
              </div>

              <Button type="button" bsStyle="success" className="mt20" onClick={this.buildSite}>
                Launch My Site!
              </Button>
            </div>

            <div className={classNames('row optional',
              { hidden: this.state.officeIdStatus.indexOf('incorrect') === -1 })}
            >

              {
                this.state.officeIdStatus === 'incorrect-office-id'
                  ? (
                    <div>
                      <small className="text-danger">
                        Oh no!
                        <br />
                        We don&apos;t recognize that Office ID!
                      </small>
                    </div>
                  )
                  : this.state.officeIdStatus === 'incorrect-agent-id'
                    ? (
                      <div>
                        <small className="text-danger">
                          Oh no!
                          <br />
                          We don&apos;t recognize that Agent ID!
                        </small>
                      </div>
                    )
                    : null
              }

              <div>
                <small className="text-center mt0">
                  <em>
                    Build My Site without featured listings
                    <br />
                    or try a different ID
                  </em>
                </small>
              </div>

              <Button
                bsStyle="default"
                className="mt20"
                onClick={function () {
                  this.setState({ officeIdStatus: 'default' });
                }.bind(this)}
              >
                Try Different ID
              </Button>

              <Button
                type="button"
                bsStyle="success"
                className="mt20"
                onClick={() => {
                  this.actions.demo.siteBuilt(this.state.mlsId);
                }}
              >
                Continue to Launch My Site!
              </Button>
            </div>

          </form>
        </Modal>

        <Modal onHide={function () { }} show={this.state.layout === 'inactive'} className="demo-modal">
          <div className="header-svg build">
            <SVGIcon name="icon-rocket" />
          </div>

          <SVGIcon name="graphics-uh-oh" className="graphics-uh-oh" />

          <h1>Oh No!</h1>
          <p>
            Your MLS is not active in our Database!
            <br />
            Try searching in a different location to see how HomeASAP works.
          </p>

          <Button type="button" className="mt20" onClick={this.inactiveAck}>
            CHECK OUT HomeASAP.COM
          </Button>

          <hr />

          <a
            role="button"
            tabIndex="-1"
            className="text-center"
            onClick={this.reEnter}
          >
            Go Back
          </a>
        </Modal>

        <Modal onHide={function () { }} show={this.state.layout === 'mls-disable'} className="demo-modal">
          <div className="header-svg build">
            <SVGIcon name="icon-rocket" />
          </div>

          <SVGIcon name="graphics-uh-oh" className="graphics-uh-oh" />

          <h1>Oh No!</h1>
          <p>
            We&apos;re sorry but we don&apos;t have enough information to create your preview site. Please contact our sales department to find out more about Search Alliance.
          </p>
          <p>
            Contact Sales: 904-549-7616
            <br />
            Email:
            &nbsp;
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>

          <Button type="button" className="mt20" onClick={this.inactiveAck}>
            CHECK OUT HomeASAP.COM
          </Button>

          <hr />

          <a
            role="button"
            tabIndex="-1"
            className="text-center"
            onClick={this.reEnter}
          >
            Go Back
          </a>
        </Modal>

        <Modal onHide={function () { }} show={this.state.layout === 'built'} className="demo-modal built">
          <div className="header-svg build">
            <SVGIcon name="icon-rocket" />
          </div>
          <h1>
            We&apos;re Busy Building
            <em>Your</em>
            &nbsp;
            Awesome Site!
          </h1>
          <ul>
            <li>Applying MLS Rules</li>
            <li>Building Agent Profile From Real Estate Agent Directory</li>
            <li>Downloading Active &amp; Featured Listings</li>
            <li>Integrating Maps, Finding Local Schools</li>
            <li>
              Setting Your Demo Service Area
              {this.getServiceZip()}
            </li>
            <li>Connecting to the Search Alliance Network</li>
          </ul>
          <h2>Congratulations! Your Demo Site Is Ready!</h2>
          <Button type="button" bsStyle="success" onClick={this.setDemoTrue}>CHECK IT OUT!</Button>
        </Modal>
      </div>
    );
  },
});
