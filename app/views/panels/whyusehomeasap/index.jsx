const React = require('react');
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.whyusehomeasap',

  mixins: [mixins.debug, mixins.utils, mixins.cursors, mixins.actions, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  showAgentContact() {
    console.log('clicked show agent contact');
    this.actions.agent.setActiveTab('Call');
    this.actions.agent.showAgentContact(null);
  },

  partial() {
    return (
      <div>
        <p>
          WHY USE myIDX Home Search
          ?
        </p>
        <p>
          MyIDX Home Search makes searching for your perfect home fun and easy. You have instant access to homes available
          for sale or rent, updated every 5 minutes! Use tagging, saved searches and auto-alerts to stay on top of new listings
          in your search area, price changes, or status updates. To receive a detailed property report on this listing,
          please contact&nbsp;
          <a role="button" tabIndex="-1" onClick={this.showAgentContact}>{this.state.agentData.NameLookup}</a>
          .
        </p>
      </div>
    );
  },

  render() {
    if (!(this.state.agentData && this.state.agentData.NameLookup)) {
      return null;
    }

    return (
      <div className="whyusehomeasap">{this.partial()}</div>
    );
  },
});
