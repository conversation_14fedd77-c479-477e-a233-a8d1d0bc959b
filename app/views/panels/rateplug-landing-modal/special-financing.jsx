const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

const MODAL_CONTENT = {
  VA: {
    header: 'Zero Down (VA)',
    body: (
      <div>
        <p>A VA Loan allows active-duty service members, veterans and eligible surviving spouses to finance a home with no down payment, no mortgage insurance and more lenient credit requirements.</p>
        <p>The properties with orange pins are eligible for VA financing based on the potential loan size.</p>
      </div>
    ),
  },
  USDA: {
    header: 'Zero Down (USDA)',
    body: (
      <div>
        <p className="text-danger">Welcome to our USDA view. Because this loan option is so specific, we have disabled search filters so you will only see listings with USDA eligibility. To exit, choose a different Special Finance option.</p>
        <p>A USDA Loan allows for no down payment on certain homes in designated rural areas. In order to be eligible for a USDA loan, household income must meet certain guidelines.</p>
        <p>The properties with orange pins are eligible for USDA financing based on location.</p>
      </div>
    ),
  },
  FHA: {
    header: '3.5% Down (FHA)',
    body: (
      <div>
        <p>An FHA Loan allows a low down payment option, competitive interest rate and more lenient credit requirements.</p>
        <p>The orange pins highlight single family homes that are eligible for an FHA Loan.</p>
      </div>
    ),
  },
  FHACondo: {
    header: 'FHA Condo Eligibility',
    body: (
      <div>
        <p className="text-danger">Welcome to our FHA Condo view. Because this loan option is so specific, we have disabled search filters so you will only see listings with FHA Condo eligibility. To exit, choose a different Special Finance option.</p>
        <ul>
          <li>See Condos that are eligible for an FHA Loan</li>
          <li>Lower down payment (3.5%)</li>
          <li>More lenient qualifying</li>
        </ul>
        <p>Properties with orange pins are eligible or conditionally eligible for an FHA Loan.</p>
      </div>
    ),
  },
  Assumable: {
    header: 'Assumable',
    body: (
      <div>
        <p className="text-danger">Welcome to our Assumable Loan view. Because this loan option is so different, we have disabled search filters so you will only see listings with an Assumable loan option. To exit, choose a different Special Finance option.</p>
        <p>
          What is an Assumable Loan?  You take over Seller&apos;s existing loan, existing interest rate, payment amount and loan balance. This could offer significant savings.
        </p>
      </div>
    ),
  },
};

module.exports = React.createClass({

  displayName: 'panels.rateplug-landing-modal.spacial-financing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    layout: ['layout', 'rateplugSpecialFinancingModal'],
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  getInitialState() {
    return {
    };
  },

  iconPrimaryColor: '#44B559',

  componentWillMount() {
    if (document.body.dataset.theme === 'more') {
      this.iconPrimaryColor = '#105FA8';
    } else if (document.body.dataset.theme === 'afordal') {
      this.iconPrimaryColor = '#1578FF';
    } else if (document.body.dataset.theme === 'fairway') {
      this.iconPrimaryColor = '#00973A';
    }
  },

  componentDidMount() {
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.layout && !prevState.layout) {
      this.actions.analytics.sendEvent('rateplug', 'special-financing', (this.state.specialFinancing || '').toString(), 'show');
    }

    // if (this.state.specialFinancing !== prevState.specialFinancing) {
    //   this.showModalIfnecessary(this.state.specialFinancing);
    // }
  },

  showModalIfnecessary(program) {
    if (window.rateplug.rp_rate && window.rateplug.rp_downpayment && !this.hasViewed(program)) {
      setTimeout(() => {
        this.actions.common.showRateplugSpecialFinancingModal();
      }, 100);
    }
  },

  hasViewed: (program) => !!window.sessionStorageAlias.getItem(`HA_RATEPLUG_SPECIAL_FINANCING_${program}_VIEWED`),
  setHasViewed: (program) => window.sessionStorageAlias.setItem(`HA_RATEPLUG_SPECIAL_FINANCING_${program}_VIEWED`, '1'),

  closeModal() {
    this.actions.common.hideRateplugSpecialFinancingModal();
    this.setHasViewed(this.state.specialFinancing);
    this.actions.analytics.sendEvent('rateplug', 'special-financing', (this.state.specialFinancing || '').toString(), 'close');
  },

  renderModal(program) {
    if (!MODAL_CONTENT[program] || this.hasViewed(program)) {
      return null;
    }

    const { header, body } = MODAL_CONTENT[program];

    return (
      <div className="rateplug-landing-step">
        <h2>Special Financing</h2>
        <div className="rateplug-landing-flex-wrapper">
          <img
            alt="Step 1 graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/${this.state.specialFinancing}${['more', 'fairway'].includes(document.body.dataset.theme) ? `-${document.body.dataset.theme}` : ''}.svg?v=1`}
            style={{ maxHeight: 250 }}
          />
          <div>
            <h3>{header}</h3>
            <div className="mb20">
              {body}
            </div>
            <a tabIndex={-1} role="button" className="btn btn-primary btn-block" onClick={this.closeModal}>
              Continue
            </a>
          </div>
        </div>
      </div>
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    const renderedModal = this.renderModal(this.state.specialFinancing);

    if (!renderedModal) {
      return null;
    }

    return (
      <Modal show={this.state.layout} onHide={this.closeModal} className={this.utils.isMobile() ? 'rateplug-landing-modal mobile' : 'rateplug-landing-modal'}>
        <Modal.Body>
          {renderedModal}
        </Modal.Body>
      </Modal>
    );
  },

});
