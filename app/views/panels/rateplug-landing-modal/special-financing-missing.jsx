const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

const MODAL_CONTENT = {
  VA: {
    header: 'Zero Down (VA)',
    body: (
      <div>
        <p>There are no Zero Down VA options that meet your search criteria. By changing your Filter options above, you may find eligible properties.</p>
        <p className="mt10 text-danger">Please change your Location, Payment Range and/or Down Payment.</p>
      </div>
    ),
  },
  USDA: {
    header: 'Zero Down (USDA)',
    body: (
      <div>
        <p>There are no Zero Down USDA options that meet your search criteria. By changing your Filter options above, you may find eligible properties.</p>
        <p className="mt10 text-danger">Please change your Location, Payment Range and/or Down Payment.</p>
      </div>
    ),
  },
  FHA: {
    header: '3.5% Down (FHA)',
    body: (
      <div>
        <p>There are no 3.5% Down FHA options that meet your search criteria. By changing your Filter options above, you may find eligible properties.</p>
        <p className="mt10 text-danger">Please change your Location, Payment Range and/or Down Payment.</p>
      </div>
    ),
  },
  FHACondo: {
    header: 'FHACondo',
    body: (
      <div>
        <p>There are no FHA Condo options that meet your search criteria. By changing your Filter options above, you may find eligible properties.</p>
        <p className="mt10 text-danger">Please change your Location.</p>
      </div>
    ),
  },
  Assumable: {
    header: 'Assumable',
    body: (
      <div>
        <p>There are no Assumable options that meet your search criteria. By changing your Filter options above, you may find eligible properties.</p>
        <p className="mt10 text-danger">Please change your Location.</p>
      </div>
    ),
  },
};

module.exports = React.createClass({

  displayName: 'panels.rateplug-landing-modal.spacial-financing-missing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    layout: ['layout', 'rateplugSpecialFinancingMissingModal'],
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  getInitialState() {
    return {
    };
  },

  iconPrimaryColor: '#44B559',

  componentWillMount() {
    if (document.body.dataset.theme === 'more') {
      this.iconPrimaryColor = '#105FA8';
    } else if (document.body.dataset.theme === 'afordal') {
      this.iconPrimaryColor = '#1578FF';
    } else if (document.body.dataset.theme === 'fairway') {
      this.iconPrimaryColor = '#00973A';
    }
  },

  componentDidMount() {
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.layout && !prevState.layout) {
      this.actions.analytics.sendEvent('rateplug', 'special-financing-missing', this.state.specialFinancing, 'show');
    }
  },

  hasViewed: (program) => !!window.sessionStorageAlias.getItem(`HA_RATEPLUG_SPECIAL_FINANCING_MISSING_${program}_VIEWED`),
  setHasViewed: (program) => window.sessionStorageAlias.setItem(`HA_RATEPLUG_SPECIAL_FINANCING_MISSING_${program}_VIEWED`, '1'),

  closeModal() {
    this.actions.common.hideRateplugSpecialFinancingMissingModal();
    this.setHasViewed(this.state.specialFinancing);
    this.actions.analytics.sendEvent('rateplug', 'special-financing-missing', this.state.specialFinancing, 'close');
  },

  renderModal(program) {
    if (!MODAL_CONTENT[program] || this.hasViewed(program)) {
      return null;
    }

    const { header, body } = MODAL_CONTENT[program];

    return (
      <div className="rateplug-landing-step">
        <h2>NO PROPERTY MATCHES FOUND</h2>
        <div className="rateplug-landing-flex-wrapper">
          <img
            alt="Step 1 graphic"
            src={`${window.CONFIG.CDN_URL}search2/rateplug/${this.state.specialFinancing}${['more', 'fairway'].includes(document.body.dataset.theme) ? `-${document.body.dataset.theme}` : ''}.svg?v=1`}
            style={{ maxHeight: 250 }}
          />
          <div>
            <h3>{header}</h3>
            <div className="mb20">
              {body}
            </div>
            <a tabIndex={-1} role="button" className="btn btn-primary btn-block" onClick={this.closeModal}>
              Continue
            </a>
          </div>
        </div>
      </div>
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    const renderedModal = this.renderModal(this.state.specialFinancing);

    if (!renderedModal) {
      return null;
    }

    return (
      <Modal style={{ zIndex: 1049 }} show={this.state.layout} onHide={this.closeModal} className={this.utils.isMobile() ? 'rateplug-landing-modal mobile' : 'rateplug-landing-modal'}>
        <Modal.Body>
          {renderedModal}
        </Modal.Body>
      </Modal>
    );
  },

});
