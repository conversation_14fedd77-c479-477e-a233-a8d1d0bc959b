const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.map.custom-controls',

  mixins: [mixins.debug, mixins.cursors, mixins.pureRender],

  cursors: {
    layout: ['layout', 'hints'],
  },

  render() {
    return (
      <div className={classNames('leaflet-bar leaflet-control leaflet-control-custom leaflet-control-recenter',
        { pulse: this.state.layout })}
      >
        <a onClick={this.props.recenter} href="#" title="Recenter">
          <SVGIcon className="leaflet-control-svg" name="icon-center" />
        </a>
        <a onClick={this.props.reposition} href="#" title="Reposition">
          <SVGIcon className="leaflet-control-svg" name="icon-new-radius" />
        </a>
      </div>
    );
  },
});
