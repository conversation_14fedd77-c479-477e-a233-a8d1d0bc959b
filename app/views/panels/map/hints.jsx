const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.map.hints',

  mixins: [mixins.debug, mixins.cursors, mixins.pureRender],

  cursors: {
    layout: ['layout', 'hints'],
  },

  LC_KEY: 'MAP_HINT_GOTIT',

  getInitialState() {
    return {
      gotIt: window.localStorageAlias.getItem(this.LC_KEY),
    };
  },

  gotIt(e) {
    e.preventDefault();
    e.stopPropagation();

    window.localStorageAlias.setItem(this.LC_KEY, 1);
    this.setState({ gotIt: true });
  },

  render() {
    if (!this.state.layout || this.state.gotIt) {
      return null;
    }

    return (
      <div className="hints">
        <div className="top-hint">
          <p className="title">Recenter map to original search</p>
          <p>Moves the radius tool back to original search results.</p>
        </div>

        <div className="top-hint-arrow" />

        <div className="bottom-hint">
          <p className="title">View properties at this location</p>
          <p>This will drop a new radius tool at this location.</p>

          <div className="got-it-container">
            <a role="button" tabIndex="-1" className="btn btn-block btn-primary" onClick={this.gotIt}>GOT IT! (hides hints)</a>
          </div>
        </div>

        <div className="bottom-hint-arrow" />
      </div>
    );
  },
});
