const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'listings.map-zoommsg',

  mixins: [mixins.actions, mixins.cursors],

  cursors: {
    zoomMsg: ['panels', 'listings', 'meta', 'zoomMsg'],
  },

  render() {
    if (!this.state.zoomMsg) {
      return null;
    }

    return (
      <div className="map-zoommsg">
        <p>Double click to zoom in, or use the mouse wheel to zoom in on the map to see property pins.</p>
      </div>
    );
  },

});
