const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'listings.map.map-exit',

  mixins: [mixins.actions, mixins.cursors, mixins.actions],

  cursors: {
    layout: ['layout'],
    zoomMsg: ['panels', 'listings', 'meta', 'zoomMsg'],
  },

  gridClicked() {
    this.actions.grid.onNav();
  },

  viewMoreHomesClicked() {
    this.actions.listing.onNavToMap();
  },

  render() {
    if (this.state.zoomMsg) {
      return null;
    }

    if (this.state.layout.listing) {
      return (
        <div className="map-exit">
          <a role="button" tabIndex="-1" className="btn btn-sm btn-primary" onClick={this.viewMoreHomesClicked}>
            View More Homes
          </a>
        </div>
      );
    }

    return (
      <div className="map-exit">
        <a role="button" tabIndex="-1" className="btn btn-sm btn-primary" onClick={this.gridClicked}>
          <SVGIcon name="icon-back-to-grid" />
          HIDE MAP
        </a>
      </div>
    );
  },
});
