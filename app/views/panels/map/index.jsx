const React = require('react');
const classNames = require('classnames');
const Alert = require('../../components/alert');
const SVGIcon = require('../../components/svg_icon');
const Leaflet = require('./leaflet');
const MapExit = require('./map-exit');
const ZoomMsg = require('./zoom');
const RatePlugSubheader = require('../header/rateplug-subheader');
const IDXPlusSubheader = require('../header/idx-plus-subheader');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.map',

  mixins: [mixins.debug, mixins.cursors],

  cursors: {
    ref: ['panels', 'map', 'ref'],
    showYelpMarkers: ['screens', 'listing', 'showYelpMarkers'],
  },

  render() {
    const style = { display: 'none' };

    return (
      <div
        style={this.state.ref.className === null ? style : null}
        className={this.state.ref.className === null
          ? null : this.state.ref.className}
      >
        <div className="map-rateplug-subheader">
          <RatePlugSubheader />
          <IDXPlusSubheader />
        </div>

        <div className={classNames('map-container',
          {
            'show-yelp': this.state.showYelpMarkers,
          })}
        >
          <Alert
            message={this.state.ref.alert}
            className="map-alert"
          />

          <Leaflet />

          <div className="map-logo" />

          <div className="map-north">
            <SVGIcon name="icon-north" />
          </div>

          <MapExit />

          <ZoomMsg />

        </div>
      </div>
    );
  },

});
