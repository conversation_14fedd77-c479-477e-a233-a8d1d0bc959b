const React = require('react');
const Ref = require('../../screens/map/ref');
const mixins = require('../../../lib/mixins/index');
const ListingFullView = require('../listing/full');

module.exports = React.createClass({

  displayName: 'screens.map.full-content',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    data: ['screens', 'map', 'data'],
    ref: ['screens', 'map', 'ref', 'display'],
  },

  componentWillUpdate(nextProps, nextState) {
    if (nextState.ref === false) {
      this.props.onClose();
    }
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data.ZipCode);
  },

  partial() {
    return (
      <ListingFullView listing={this.state.data} onPhotoClick={this.onPhotoClick} onClose={this.props.onClose} />
    );
  },

  render() {
    if (!this.state.ref) {
      return null;
    }

    return (
      <div className="row col-center layout--2s-3">

        <div className="col-23-24 map-detail">

          <Ref />

          {this.state.data ? this.partial() : null}

        </div>
      </div>
    );
  },

});
