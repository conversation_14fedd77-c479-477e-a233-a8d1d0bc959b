const React = require('react');
const ReactDOM = require('react-dom');
const mixins = require('../../../lib/mixins');
const CustomControls = require('./custom-controls');
const Hints = require('./hints');

module.exports = React.createClass({

  displayName: 'panel.map.leaflet',

  mixins: [mixins.actions, mixins.utils, mixins.leaflet],

  componentDidMount() {
    this.leaflet.m = L.map(ReactDOM.findDOMNode(this), {
      keyboard: false,
      attributionControl: false,

      // Zoom things
      zoomControl: true,
      // scrollWheelZoom: 'center',
      // doubleClickZoom: 'center',

      boxZoom: false,
      bounceAtZoomLimits: false,
      inertia: false,
      // tapTolerance: 50,
      // fadeAnimation: false,
      // zoomAnimation: false,
      minZoom: this.utils.useMobileSite() ? this.leaflet.opts.minZoomPhone : this.leaflet.opts.minZoom,
      maxZoom: this.leaflet.opts.maxZoom,
      maxNativeZoom: this.leaflet.opts.maxNativeZoom,
      maxBounds: this.leaflet.opts.maxBounds,
      layers: [this.leaflet.parcelLayer],
      nowrap: this.leaflet.opts.noWrap,
      contextmenu: !this.utils.useMobileSite(),
      contextmenuWidth: 180,
      contextmenuItems: [{
        text: 'Pin Options',
        disabled: true,
      }, {
        text: 'What is it?',
        callback: () => {},
        mouseOverCallback: this.actions.map.showLegend,
        mouseOutCallback: this.actions.map.hideLegend,
        hideOnSelect: false,
      }, '-', {
        text: 'Map Options',
        disabled: true,
      }, {
        text: 'Search here',
        callback: (e) => {
          this.leaflet.routeToPoint(e.latlng);
        },
      }, {
        text: 'Filter search',
        callback: () => {
          this.actions.menu.setEditingFilters(true);
        },
      }, '-', {
        text: 'Help?',
        callback: this.actions.help.toggleHelp,
      }],
    })
      .setView(this.leaflet.opts.center, 13); // Initial zoom
    // .addControl(this.leaflet.centerControl);  // centerControl

    // Event Handlers
    this.leaflet.m.on('zoomstart', this.leaflet.onZoomStart);
    this.leaflet.m.on('zoomend', this.leaflet.onZoomEnd);
    this.leaflet.m.on('dragstart', (e) => {
      this.leaflet.onDragStart(e);
      this.dismissMobilePreviewData();
    });
    this.leaflet.m.on('dragend', this.leaflet.onDragEnd);
    this.leaflet.m.on('drag', this.leaflet.onDrag);
    this.leaflet.m.on('click', () => {
      this.dismissMobilePreviewData();
    });
    this.leaflet.m.on('mousemove', this.leaflet.onMouseMove);

    // Init Map
    this.leaflet.initMap();
    this.actions.analytics.sendEvent('map actions', 'loaded');
  },

  componentWillUnmount() {
    return false;
  },

  shouldComponentUpdate() {
    // Force the map to never re-render
    return false;
  },

  swapLayers(e) {
    e.preventDefault();
    this.leaflet.toggleLayers();
    this.actions.analytics.sendEvent('map actions', 'layers');
  },

  recenter(e) {
    e.preventDefault();
    this.leaflet.panTo();
    this.actions.analytics.sendEvent('map actions', 'recenter');
  },

  reposition(e) {
    e.preventDefault();
    this.leaflet.routeToCenter();
    this.actions.analytics.sendEvent('map actions', 'reposition');
  },

  dismissMobilePreviewData() {
    this.actions.map.setMobileCardDataWithId(null);
    this.actions.panels.hideHeaders();
  },

  render() {
    return (
      <div className="map">
        <CustomControls recenter={this.recenter} reposition={this.reposition} />
        <div className="leaflet-control leaflet-control-custom leaflet-control-layers ">
          <a role="button" tabIndex="-1" className="leaflet-control-layers-toggle" onClick={this.swapLayers} href="#" title="Layers">&nbsp;</a>
        </div>
        <Hints />
      </div>
    );
  },
});
