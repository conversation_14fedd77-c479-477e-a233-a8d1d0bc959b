const React = require('react');
const classNames = require('classnames');
const Ref = require('../../screens/map/ref');
const mixins = require('../../../lib/mixins/index');
const SVGIcon = require('../../components/svg_icon');
const Sub = require('../sub/index');
const ListingEdgeTabbar = require('../../components/listing_edge_tabbar');
const ListingPhotoSlider = require('../listing/photo_slider/index');
const ListingSnapshot = require('../listing/snapshot/index');
const ListingDetails = require('../listing/details/for-flyout');
// ListingFinancial = require('../../panels/listing/financial/for-flyout'),
const ListingNeighborhood = require('../listing/neighborhood/for-flyout');
// const ListingSchools = require('../listing/schools/for-flyout');
const ListingFooter = require('../listing/footer/index');
const ShareListing = require('../../components/share_listing');

module.exports = React.createClass({

  displayName: 'screens.map.content',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  getIsLatLon0(state) {
    if (state.data && state.data.Location.Lat && state.data.Location.Lon) {
      return false;
    }
    return true;
  },

  getInitialState() {
    return {
      tabs: ['Details'],
      activeTab: 'Details',
      barShouldStick: false,
      barPlaceholderHeight: 0,
      showSub: false,
    };
  },

  tempData: {
    currentId: false,
  },

  cursors: {
    ref: ['screens', 'map', 'ref', 'display'],
    data: ['screens', 'map', 'data'],
    streetViewAvailable: ['screens', 'map', 'streetViewAvailable'],
    mlsDatas: ['shared', 'mlsData'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },

  componentWillMount() {

  },

  componentDidMount() {
    if (this.state.data) {
      console.log(`Viewing: ${this.state.data.Id}`);
      this.tempData.currentId = this.state.data.Id;
      this.actions.common.setPropertyViewed(this.state.data);
      this.setState({
        isLatLon0: this.getIsLatLon0(this.state),
        tabs: this.getIsLatLon0(this.state) ? ['Details'] : ['Details'/* , "Financial" */, 'Neighborhood'/* , "Schools" */],
      });
    }
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.showSub != this.state.showSub) {
      return true;
    }

    const isNewListing = nextState.data && this.tempData.currentId !== nextState.data.Id;
    this.tempData.currentId = nextState.data && nextState.data.Id;

    if (isNewListing) {
      this.actions.common.setPropertyViewed(nextState.data);
      console.log(`Viewing: ${nextState.data.Id}`);
      const newState = this.getInitialState();
      newState.isLatLon0 = this.getIsLatLon0(nextState);
      newState.tabs = this.getIsLatLon0(nextState) ? ['Details'] : ['Details'/* , "Financial" */, 'Neighborhood'/* , "Schools" */];
      this.setState(newState);
      return false;
    }

    if (nextState.streetViewAvailable != this.state.streetViewAvailable) {
      return true;
    }

    return true;
  },

  handleContact() {
    this.actions.agent.setActiveTab('Call');
    if (this.state.data && !this.state.data.isTaxProperty) {
      this.actions.agent.showAgentContact(this.state.data);
    }

    this.actions.analytics.sendEvent('detail view', 'agent', this.state.data.ZipCode);
  },

  onClose(e) {
    e.preventDefault();
    this.actions.map.onNav();
  },

  onCloseSub() {
    this.setState({ showSub: false });
  },

  onStreet(e) {
    e.preventDefault();
    this.actions.analytics.sendEvent('detail view', 'map street click', this.state.streetViewAvailable ? 'available' : 'not available');

    if (this.state.streetViewAvailable) {
      this.setState({ showSub: 'street' });
    }
  },

  onAerial(e) {
    e.preventDefault();
    this.actions.analytics.sendEvent('detail view', 'map aerial click');

    this.setState({ showSub: 'aerial' });
  },

  onPhotoClick(e) {
    if (this.state.barShouldStick) {
      this.scrollToTopClick();
    } else {
      this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
      this.actions.panels.toggle('photos', this.state.data);

      this.actions.analytics.sendEvent('detail view', 'photos', this.state.data.ZipCode);
    }
  },

  onExpandViewClick(e) {
    e.preventDefault();

    this.props.onExpandViewClick ? this.props.onExpandViewClick() : '';
    this.actions.analytics.sendEvent('detail view', 'open full', this.state.data.ZipCode);
  },

  goToVirtualTour(e) {
    e.preventDefault();
    if (this.state.data && this.state.data.VirtualTourLink) {
      let link = this.state.data.VirtualTourLink;
      if (link.toLowerCase().indexOf('http') !== 0) {
        link = `http://${link}`;
      }
      window.open(link, '_blank');
      this.actions.analytics.sendEvent('detail view', 'virtual tour', this.state.data.ZipCode);
    }
  },

  print(e) {
    e.preventDefault();

    if (this.state.data && this.state.data.Id) {
      this.actions.common.printListingById(this.state.data.Id);
    }
  },

  scrollToTopClick(e) {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const scrollContainer = this.refs.scrollContainer;

    if (scrollContainer) {
      this.actions.common.scrollElementTo(scrollContainer, 0, 500);
    }
  },
  scrollToTopIcon() {
    return (
      <div role="button" tabIndex="-1" aria-label="Back to top" className="icon-back-to-top-container" onClick={this.scrollToTopClick}>
        <SVGIcon name="icon-back-to-top" className="icon-back-to-top" />
      </div>
    );
  },

  tabItemClick(e) {
    e.preventDefault();
    e.stopPropagation();

    const refName = e.target.innerHTML;

    this.actions.analytics.sendEvent('detail view nav', refName, this.state.data.ZipCode);

    if (refName === 'Details') {
      this.scrollToTopClick();
      return;
    }

    const scrollContainer = this.refs.scrollContainer;
    const element = this.refs[refName];

    if (scrollContainer && element) {
      this.actions.common.scrollElementTo(scrollContainer,
        element.offsetTop
        - this.headerStickPixels,
        500);
    }
  },

  tabs() {
    return (
      <nav className={classNames('nav-scrollable listing-nav')}>
        {
        this.state.tabs.map(function (item) {
          return (
            <a
              role="button"
              tabIndex="-1"
              className={classNames('item', 'pull-left', { active: this.state.activeTab === item })}
              key={item}
              onClick={this.tabItemClick}
            >
              {item}
            </a>
          );
        }, this)
        }
      </nav>
    );
  },

  tabContents(listing, mlsData = {}) {
    if (this.state.isLatLon0) {
      return (
        <div className="listing-panels-container">
          <div ref="Details">
            <ListingSnapshot listing={listing} mlsData={mlsData} />
          </div>
          <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
          {/* <hr ref="Financial" />
          {this.scrollToTopIcon()}
          <ListingFinancial listing={listing} mlsData={mlsData} /> */}
          <hr />
          {this.scrollToTopIcon()}
          <ListingFooter listing={listing} mlsData={mlsData} />
        </div>
      );
    }

    return (
      <div className="listing-panels-container">
        <div ref="Details">
          <ListingSnapshot listing={listing} mlsData={mlsData} />
        </div>
        <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
        {/* <hr ref="Financial" />
       {this.scrollToTopIcon()}
       <ListingFinancial listing={listing} mlsData={mlsData} /> */}
        <hr ref="Neighborhood" />
        {this.scrollToTopIcon()}
        <ListingNeighborhood listing={listing} mlsData={mlsData} />
        {/* <hr ref="Schools" />
        {this.scrollToTopIcon()}
        <ListingSchools listing={listing} mlsData={mlsData} /> */}
        <hr />
        {this.scrollToTopIcon()}
        <ListingFooter listing={listing} mlsData={mlsData} />
      </div>
    );
  },

  onScroll(e) {
    this.updateActiveTabAtScrollTop(e.target.scrollTop);
  },

  headerStickPixels: 103,

  updateActiveTabAtScrollTop(scrollTop) {
    if (this.refs.listingHeaderContainer && scrollTop > this.refs.listingHeaderContainer.offsetHeight - this.headerStickPixels) {
      if (!this.state.barShouldStick) {
        this.setState({
          barShouldStick: true,
          barPlaceholderHeight: this.refs.listingHeaderContainer.offsetHeight,
        });
      }
    } else {
      if (this.state.barShouldStick) {
        this.setState({ barShouldStick: false, barPlaceholderHeight: 0 });
      }
    }

    const tabs = this.state.tabs;
    for (let i = tabs.length - 1; i > -1; i--) {
      const tabName = tabs[i];
      const tabElem = this.refs[tabName] && this.refs[tabName];
      if (tabElem && scrollTop
        >= tabElem.offsetTop
        - this.headerStickPixels
      ) {
        if (this.state.activeTab !== tabName) {
          this.setState({ activeTab: tabName });
        }
        return;
      }
    }
  },

  sub() {
    return (
      <Sub
        key="sub"
        data={this.state.data}
        streetViewAvailable={this.state.streetViewAvailable}
        showSub={this.state.showSub}
        onCloseSub={this.onCloseSub}
      />
    );
  },

  componentDidUpdate() {
    const mlsId = this.actions.common.getListingMlsId(this.state.data);
    const mlsData = this.state.mlsDatas[mlsId];
    if (mlsId && !mlsData) {
      setTimeout(() => this.actions.listing.getMlsData(mlsId), 500);
    }
  },

  render() {
    if (!this.state.ref) {
      return null;
    }

    const mlsId = this.actions.common.getListingMlsId(this.state.data);
    const mlsData = this.state.mlsDatas[mlsId] || {};

    return (

      <div className="listing-flyout" key={this.state.data && this.state.data.Id || 'Listing'}>

        {this.sub()}

        <div className="layout--2s-4">

          {
              this.state.showSub ? null
                : (
                  <ListingEdgeTabbar
                    className="on-listing-detail"
                    onClose={this.onClose}
                    onStreet={this.onStreet}
                    onAerial={this.onAerial}
                    data={this.state.data}
                    streetViewAvailable={this.state.streetViewAvailable}
                  />
                )
              }

          { !this.state.data
            ? <Ref />
            : (
              <div className="scroll-container" ref="scrollContainer" onScroll={this.onScroll}>
                <div
                  ref="listingHeaderContainer"
                  className={classNames('listing-header-container', { 'visibility-hidden': this.state.barShouldStick })}
                >
                  <ListingPhotoSlider listing={this.state.data} mlsData={mlsData} onPhotoClick={this.onPhotoClick} />
                  { this.tabs() }
                </div>

                <a
                  role="button"
                  tabIndex="-1"
                  aria-label="Close"
                  className="pull-right top-icons first"
                  title="View full details"
                  onClick={this.onExpandViewClick}
                >
                  <SVGIcon name="icon-expanded-view" className="" />
                </a>
                <ShareListing className="pull-right top-icons" title="Share this home" listing={this.state.data} />

                <a
                  role="button"
                  tabIndex="-1"
                  className="pull-right top-icons"
                  title="Print"
                  onClick={this.print}
                >
                  <SVGIcon name="icon-printer" className="" />
                </a>
                { this.state.data.VirtualTourLink
                  ? (
                    <a
                      role="button"
                      tabIndex="-1"
                      className="pull-right top-icons"
                      title="Virtual tour"
                      onClick={this.goToVirtualTour}
                    >
                      <SVGIcon name="icon-virtual-tour" className="" />
                    </a>
                  )
                  : null}
                {mlsData.ShowBrokerOnSlider
                  ? (
                    <a role="button" tabIndex="-1" className="pull-right top-icons" title="Contact" onClick={this.handleContact}>
                      <SVGIcon name="icon-chat-bubbles" />
                    </a>
                  )
                  : null }
                { this.tabContents(this.state.data, mlsData) }
              </div>
            )}
          { this.state.data && this.state.barShouldStick
            ? (
              <div role="button" tabIndex="-1" className="overlay-container cursor-pointer" onClick={this.scrollToTopClick}>
                <div
                  className="listing-header-container stick"
                  style={{
                    marginTop: -1 * (this.state.barPlaceholderHeight - this.headerStickPixels),
                  }}
                >
                  <ListingPhotoSlider listing={this.state.data} mlsData={mlsData} onPhotoClick={this.onPhotoClick} />
                  { this.tabs() }
                </div>
              </div>
            )
            : null}
        </div>
      </div>
    );
  },
});
