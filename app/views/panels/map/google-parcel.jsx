const
  React = require('react');
const classNames = require('classnames');
const ReactDOM = require('react-dom');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.map.google-parcel',

  cursors: {
    activeListingId: ['panels', 'listings', 'activeId'],
  },

  mixins: [mixins.actions, mixins.googleParcel, mixins.cursors],

  componentDidMount() {
    const activeListing = this.props.listing;
    if (activeListing) {
      this.actions.analytics.sendEvent('detail view', 'google parcel', 'load');
      this.googleParcel.createMap(activeListing, ReactDOM.findDOMNode(this));
    }
  },

  componentWillUnmount() {
    // How to remove the maps instance from memory
    // http://stackoverflow.com/questions/21142483/google-maps-js-v3-detached-dom-tree-memory-leak
  },

  shouldComponentUpdate() {
    // Force the map to never re-render
    return false;
  },

  render() {
    return (
      <div className={classNames('parcelmap', this.props.className)} />
    );
  },
});
