const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.header',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'header'],
    data: ['panels', 'header', 'data'],
  },

  onNotification() {
    this.actions.notification.trigger();
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className="layout--header" />
    );
  },

});
