const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const AgentProfileImage = require('../../components/agent_profile_image');
const Search = require('./search');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panel.header',

  mixins: [mixins.debug, mixins.events, mixins.actions, mixins.cursors, mixins.router, mixins.utils],

  cursors: {
    headerLayout: ['layout', 'header'],
    menuOn: ['layout', 'menu'],
    agentLayout: ['layout', 'agent'],
    login: ['layout', 'login'],
    agentData: ['shared', 'agent', 'data'],
    buyerData: ['shared', 'buyer', 'data'],
    notificationHeader: ['shared', 'notificationCenter', 'header'],
    data: ['panels', 'header', 'data'],
    loadingAgent: ['panels', 'header', 'loadingAgent'],
    mlsData: ['shared', 'agent', 'mlsData'],
    agentHeaderDropdownLayout: ['layout', 'agentHeaderDropdown'],
  },

  facets: {
    headerControl: ['currentActiveHeaderControl'],
    shouldHideSearchBar: ['shouldHideSearchBar'],
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.agent.toggleAgentHeaderDropdown();
  },

  handleProfileClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.common.goToRoute('/agent');
  },

  handleFeaturedListingsClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.featured.onNav();
  },

  handleContactAgentClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.agent.setActiveTab('Call');
    this.actions.agent.showAgentContact();
  },

  handleHomePageClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.goToHomePage();
    this.actions.agent.hideAgentHeaderDropdown();
  },

  handleLoginClick() {
    this.actions.login.promptDirectLogin({ type: 'direct' });
    this.actions.analytics.sendEvent('login', 'start', 'header');
  },

  tagClicked() {
    this.actions.tagging.onNav();
  },

  helpClicked() {
    this.actions.help.toggleHelp();
  },

  notificationClicked() {
    this.events.emit(this.events.PROGRESS_CLICKED);
  },

  buyerProfile() {
    this.actions.common.goToRoute('/buyer');
  },

  render() {
    if (!this.state.headerLayout) {
      return null;
    }

    return (
      <div className="layout--header">

        <div className={classNames('header-left', { hover: this.state.agentHover })}>
          { this.state.agentData
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Agent"
                className={classNames('agent-container',
                  {
                    active: this.state.agentLayout.agentData || this.state.agentLayout.listingDetail,
                  })}
                onClick={this.agentClicked}
                onFocus={function () {
                  this.setState({ agentHover: true });
                }.bind(this)}
                onMouseOver={function () {
                  this.setState({ agentHover: true });
                }.bind(this)}
                onBlur={function () {
                  this.setState({ agentHover: false });
                }.bind(this)}
                onMouseOut={function () {
                  this.setState({ agentHover: false });
                }.bind(this)}
                ref="agentContainer"
              >
                <AgentProfileImage className="agent-header-image" />
                <div className="agent-texts full-name">
                  <p className="agent-name" title={`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`}>
                    <span>
                      {`${this.state.agentData.FirstName} ${this.state.agentData.LastName} `}
                      <VerifiedBadge />
                    </span>
                    {
                    this.state.mlsData
                    && this.state.mlsData.RealEstateLicenseNumberOnOff
                    && this.state.agentData.StateLicenseNumber
                    && this.state.agentData.StateLicenseNumber.length > 0
                      ? (
                        <span className="license-num">{this.state.agentData ? ` (${this.state.agentData.StateLicenseNumber})` : ''}</span>
                      ) : null
                  }
                  </p>

                  {
                  this.state.agentData.BrokerName
                    ? <p className="brokerage" title={this.state.agentData.BrokerName}><span>{this.state.agentData.BrokerName}</span></p>
                    : null
                  }
                </div>
                <div className="agent-texts first-name">
                  <p className="agent-name" title={`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`}>
                    <span>{`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`}</span>
                    {
                    this.state.mlsData
                    && this.state.mlsData.RealEstateLicenseNumberOnOff
                    && this.state.agentData.StateLicenseNumber
                    && this.state.agentData.StateLicenseNumber.length > 0
                      ? (
                        <span className="license-num">{this.state.agentData ? ` (${this.state.agentData.StateLicenseNumber})` : ''}</span>
                      ) : null
                  }
                  </p>
                </div>

                <div className="dropdown-chevron">
                  <SVGIcon name="icon-chevron-down" />
                </div>
                <div className={classNames('header-agent-dropdown', { visible: this.state.agentHeaderDropdownLayout })}>
                  <ul>
                    <li onClick={this.handleProfileClick}>
                      <SVGIcon name="icon-license" />
                      <span>Hello! View My Profile</span>
                    </li>
                    <li onClick={this.handleFeaturedListingsClick}>
                      <SVGIcon name="icon-contact" />
                      <span>Featured Listings</span>
                    </li>
                    {
                      this.actions.common.getAgentHasHV()
                        ? (
                          <li onClick={this.actions.homeWorth.onNav}>
                            <SVGIcon name="icon-conact" />
                            <span>What&apos;s your Home Worth?</span>
                          </li>
                        )
                        : null
                    }
                    {
                      this.actions.common.getAgentHasLendingTree(this.state.agentData)
                        ? (
                          <li
                            className="lending-tree-txt"
                            onClick={() => {
                              this.actions.analytics.sendEvent('lending tree', 'open from agent dropdown');
                              this.actions.common.showLendingTreeModal();
                            }}
                          >
                            <SVGIcon name="icon-rates" />
                            <span>See Current Rates</span>
                          </li>
                        )
                        : null
                    }
                    {
                      this.utils.getAgentSettingValue(this.state.agentData && this.state.agentData.AgentSettings, 'local_market_reports_url')
                        ? (
                          <li
                            onClick={() => window.open(this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'local_market_reports_url'), '_blank')}
                          >
                            <SVGIcon name="icon-homevalue" />
                            <span>Local Market Reports</span>
                          </li>
                        )
                        : null
                    }
                    {
                      this.actions.common.getAgentHasDS()
                        ? (
                          <li onClick={this.actions.common.showDreamsweepsModal}>
                            <SVGIcon name="icon-star" />
                            <span>Enter To Win $200</span>
                          </li>
                        )
                        : null
                    }
                    <li onClick={this.handleContactAgentClick}>
                      <SVGIcon name="icon-contact" />
                      <span>Contact Now</span>
                    </li>
                    <li>
                      <SVGIcon name="icon-contact" />
                      <span className="share-agent" title="">
                        <span>Share My Site</span>
                        <a
                          role="button"
                          tabIndex="-1"
                          className="share-facebook"
                          title="Share my site on Facebook"
                          onClick={this.actions.common.shareAgentOnFacebook}
                        >
                          <SVGIcon name="icon-facebook" />
                        </a>
                        <a
                          role="button"
                          tabIndex="-1"
                          className="share-twitter"
                          title="Share my site on Twitter"
                          onClick={this.actions.common.shareAgentOnTwitter}
                        >
                          <SVGIcon name="icon-twitter" />
                        </a>
                        <a
                          role="button"
                          tabIndex="-1"
                          className="share-email"
                          title="Share my site via Email"
                          onClick={this.actions.common.shareAgentViaEmail}
                        >
                          <SVGIcon name="icon-share-email" />
                        </a>
                      </span>
                    </li>
                    {/*
                    <li>
                      <SVGIcon name="icon-contact" />
                      <span>Sell Your Home</span>
                    </li>
                    */}
                    {
                      // Hide Return to Home Page if current route is /agent, onboarding agent is current agent or missing, and current agent does not have idx
                      this.router.currentRoute.url.endsWith('/agent')
                      && [null, this.state.agentData.Id].indexOf(this.actions.common.getOnboardingAgentId()) > -1
                      && !this.state.agentData.HomeSearchRegisteredDateTime ? null
                        : (
                          <li onClick={this.handleHomePageClick}>
                            <SVGIcon name="icon-contact" />
                            <span>Return to Home Page</span>
                          </li>
                        )
                    }
                  </ul>
                </div>
              </div>
            )
            : (
              <div
                className={classNames('agent-container')}
                ref="agentContainer"
              >
                <img alt="Agent" className="agent-header-image" src="//nplayassets.blob.core.windows.net/images/faceless-user.png" />
              </div>
            )}
          { this.state.loadingAgent
            ? (
              <div className="header-spinner-container">
                <div className="spinner-round-component lg" />
              </div>
            )
            : null}
          {
            this.state.shouldHideSearchBar
              ? (
                <div className="controls-container search-button-container">
                  <div className="filter-container">
                    {
                      this.actions.common.getAgentHasLendingTree(this.state.agentData)
                        ? (
                          <button
                            type="button"
                            className="btn mr10 lending-tree-bg"
                            onClick={() => {
                              this.actions.analytics.sendEvent('lending tree', 'open from agent profile no idx');
                              this.actions.common.showLendingTreeModal();
                            }}
                          >
                            See Current Rates
                          </button>
                        )
                        : null
                    }
                    <button
                      type="button"
                      className="btn btn-primary mrn see-more-homes"
                      onClick={this.handleContactAgentClick}
                    >
                      See more homes
                    </button>
                  </div>
                </div>
              )
              : <Search ref="searchContainer" />
          }
        </div>

        <div className="header-right">
          { this.state.buyerData && this.state.buyerData.Id
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Your profile"
                className="buyer-container"
                onClick={this.buyerProfile}
              >
                <div className="image-container">
                  <img alt="Your profile" src={this.state.buyerData.ProfileImage} />
                </div>
              </div>
            )
            : (
              <button
                type="button"
                className="direct-login-button"
                onClick={this.state.notificationHeader && this.state.notificationHeader.totalCount > 0
                  ? this.notificationClicked : this.handleLoginClick}
              >
                Login
                {
                this.state.notificationHeader && this.state.notificationHeader.totalCount
                  ? <span className="total-count">{this.state.notificationHeader.totalCount}</span> : null
                }
              </button>
            )}
          { this.state.buyerData && this.state.buyerData.Id
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Tag"
                className="tag-control"
                onClick={this.tagClicked}
              >
                <SVGIcon name="icon-bell" />
              </div>
            )
            : null}
          { this.state.buyerData && this.state.buyerData.Id
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Help"
                className="help-control"
                onClick={this.helpClicked}
              >
                <div className="help-icon">?</div>
              </div>
            )
            : null}
        </div>
      </div>
    );
  },
});
