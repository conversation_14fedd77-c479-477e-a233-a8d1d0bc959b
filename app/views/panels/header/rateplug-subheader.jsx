const React = require('react');
const { OverlayTrigger, Popover } = require('react-bootstrap');
const mixins = require('../../../lib/mixins');

const SVGIcon = require('../../components/svg_icon');
const MortgagePaymentDropdown = require('../menu/search-components/mortgage-payment');
const DownPaymentDropdown = require('../menu/search-components/down-payment');
const SpecialFinancingDropdown = require('../menu/search-components/special-financing');

module.exports = React.createClass({

  displayName: 'panel.header.ratelug-subheader',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils],

  cursors: {
    viewingGrid: ['layout', 'grid', 'grid'],
    viewingMap: ['layout', 'map'],
    viewingMemberListings: ['layout', 'memberListings'],
    // we listen to listings so we update when they change, to check if we should display the terms
    mapGridListings: ['panels', 'listings', 'data'],
    featuredListings: ['screens', 'featured', 'data'],
    taggedListings: ['screens', 'tagging', 'data', 'Listings'],
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  getInitialState() {
    return {

    };
  },

  filterPanel() {
    const popoverAssumable = (
      <Popover id="rateplug-terms-overlay">
        <div>
          <div>
            <p>You are in Assumable Loan view. To exit, choose a different Special Finance option.</p>
          </div>
        </div>
      </Popover>
    );
    const popoverFHACondo = (
      <Popover id="rateplug-terms-overlay">
        <div>
          <div>
            <p>You are in FHA Condo view. To exit, choose a different Special Finance option.</p>
          </div>
        </div>
      </Popover>
    );
    const popoverUSDA = (
      <Popover id="rateplug-terms-overlay">
        <div>
          <div>
            <p>You are in USDA view. To exit, choose a different Special Finance option.</p>
          </div>
        </div>
      </Popover>
    );
    const popoverTerms = this.state.specialFinancing === 'Assumable' ? popoverAssumable
      : this.state.specialFinancing === 'FHACondo' ? popoverFHACondo
        : this.state.specialFinancing === 'USDA' ? popoverUSDA : (
          <Popover id="rateplug-terms-overlay">
            <div>
              <div>
                <p>Payments are based on a 30 Year fixed mortgage national average rate. Actual rates may vary depending on your situation.</p>
                <p>You can get current interest rate and loan program information when you click through the individual properties.</p>
              </div>
            </div>
          </Popover>
        );
    const popoverMonthlyPayment = this.state.specialFinancing === 'Assumable' ? popoverAssumable
      : this.state.specialFinancing === 'FHACondo' ? popoverFHACondo
        : this.state.specialFinancing === 'USDA' ? popoverUSDA : (
          <Popover id="rateplug-terms-overlay">
            <div>
              <div>
                <p>
                  <strong>Monthly Payment range </strong>
                  includes principal and interest, taxes, and insurance.
                </p>
              </div>
            </div>
          </Popover>
        );
    const popoverDownPayment = this.state.specialFinancing === 'Assumable' ? popoverAssumable
      : this.state.specialFinancing === 'FHACondo' ? popoverFHACondo
        : this.state.specialFinancing === 'USDA' ? popoverUSDA : (
          <Popover id="rateplug-terms-overlay">
            <div>
              <div>
                <p>
                  The system defaults to a 20% down payment based on assumptions. Please change the down payment to the amount that fits your financial situation.
                </p>
              </div>
            </div>
          </Popover>
        );

    return (
      <div className={`rateplug-filters-container ${this.state.viewingGrid ? 'on-grid' : ''}`}>
        <div className="filters">
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div className="special-financing-highlight" onClick={this.utils.useMobileSite() ? () => this.props.filterClick && this.props.filterClick('special-financing-filter') : undefined}>
            <div style={{ pointerEvents: this.utils.useMobileSite() ? 'none' : 'auto' }}>
              <SpecialFinancingDropdown />
            </div>
          </div>
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div className="has-info-button" onClick={this.utils.useMobileSite() && this.state.specialFinancing !== 'Assumable' && this.state.specialFinancing !== 'FHACondo' && this.state.specialFinancing !== 'USDA' ? () => this.props.filterClick && this.props.filterClick('downpayment-filter') : undefined}>
            <OverlayTrigger trigger={['focus', 'click']} rootClose placement="bottom" overlay={popoverDownPayment}>
              <div className="info-button" onClick={null}>
                <SVGIcon name="icon-info" className="icon-info" />
              </div>
            </OverlayTrigger>
            <div style={{ pointerEvents: this.utils.useMobileSite() ? 'none' : 'auto' }}>
              <DownPaymentDropdown />
            </div>
          </div>
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div className="has-info-button" onClick={this.utils.useMobileSite() && this.state.specialFinancing !== 'Assumable' && this.state.specialFinancing !== 'FHACondo' && this.state.specialFinancing !== 'USDA' ? () => this.props.filterClick && this.props.filterClick('mortgage-payment-filter') : undefined}>
            <OverlayTrigger trigger={['focus', 'click']} rootClose placement="bottom" overlay={popoverMonthlyPayment}>
              <div className="info-button" onClick={null}>
                <SVGIcon name="icon-info" className="icon-info" />
              </div>
            </OverlayTrigger>
            <div style={{ pointerEvents: this.utils.useMobileSite() && this.state.specialFinancing !== 'Assumable' && this.state.specialFinancing !== 'FHACondo' && this.state.specialFinancing !== 'USDA' ? 'none' : 'auto' }}>
              <MortgagePaymentDropdown />
            </div>
          </div>
          <div className="dropdown btn-group btn-group-lg">
            <OverlayTrigger trigger={['focus', 'click']} rootClose placement="bottom" overlay={popoverTerms}>
              <button className="btn btn-lg btn-default label-only" type="button" onClick={null}>
                Term: 30 years
              </button>
            </OverlayTrigger>
          </div>
          <div className="dropdown btn-group btn-group-lg">
            <button
              className="btn btn-lg btn-default"
              type="button"
              onClick={this.utils.useMobileSite()
                ? () => this.props.filterClick && this.props.filterClick()
                : () => this.actions.menu.setEditingFilters(true)}
            >
              More
              <span className="caret" />
            </button>
          </div>
          {/* Add More Filters button that opens more filters */}
        </div>
      </div>
    );
  },

  render() {
    if (!this.state.viewingGrid && !this.state.viewingMap) {
      return null;
    }

    if (!window.rateplug.rp_buyer) {
      return null;
    }

    return (
      <div className="controls-container rateplug-filters-wrapper">
        {this.filterPanel()}
      </div>
    );
  },
});
