const React = require('react');
const Modal = require('react-bootstrap').Modal;

const _ = require('lodash');
const PropertyTaxByCounty = require('../../../../lib/property-tax-by-county');
const IDXPlusAssumableInfo = require('./idx-plus-assumable-info');
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const CloseBtn = require('../../../components/close_btn_cross');

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.mortgage-calculator',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  getInitialState() {
    let downPaymentAmount = window.localStorageAlias.getItem('mortgage-calculator.downPaymentAmount');
    if (!downPaymentAmount || isNaN(downPaymentAmount)) {
      downPaymentAmount = null;
    } else {
      downPaymentAmount = Number(downPaymentAmount);
    }

    let downPaymentPercentage = window.localStorageAlias.getItem('mortgage-calculator.downPaymentPercentage');
    if (!downPaymentPercentage || isNaN(downPaymentPercentage)) {
      downPaymentPercentage = null;
    } else {
      downPaymentPercentage = Number(downPaymentPercentage);
    }

    if (!downPaymentAmount && !downPaymentPercentage) {
      downPaymentPercentage = 20;
    }

    let interestRate = window.localStorageAlias.getItem('mortgage-calculator.interestRate');
    if (!interestRate || isNaN(interestRate)) {
      interestRate = window.idx_rate || 7.62;
    } else {
      interestRate = Number(interestRate);
    }

    let years = window.localStorageAlias.getItem('mortgage-calculator.years');
    if (!years || isNaN(years)) {
      years = 30;
    } else {
      years = Number(years);
    }

    return {
      showAdvanced: false,
      homePrice: null,
      downPaymentAmount,
      downPaymentPercentage,
      interestRate,
      years,
      propertyTaxAmount: 0,
      propertyTaxPercentage: 0,
      includePMI: false,
      homeInsurance: 0,
      hoa: 0,
      showModal: false,
      randomLendingTreeCTA: _.sample([
        'Compare Mortgage Rates!',
        'See Loan Quotes!',
        'View Free Offers!',
      ]),
    };
  },

  componentWillMount() {
    this.setState({
      homePrice: this.props.price,
      homeInsurance: Math.round((this.props.price || 0) * 0.002516),
      propertyTaxPercentage: Number(this.props.propertyTaxRate) || 0,
    });
  },

  componentDidMount() {
    if (!this.state.propertyTaxPercentage && this.props.location) {
      this.utils.geocodeLatlng(this.props.location.Lat, this.props.location.Lon, (res) => {
        if (res && !this.state.propertyTaxPercentage && !this.state.propertyTaxAmount) {
          if (Array.isArray(res.address_components)) {
            let countyName = null; let
              stateAbbr = null;
            res.address_components.forEach((addressComponent) => {
              if (addressComponent.types.indexOf('administrative_area_level_2') != -1) {
                countyName = addressComponent.long_name.toUpperCase().replace(' COUNTY', '');
              } else if (addressComponent.types.indexOf('administrative_area_level_1') != -1) {
                stateAbbr = addressComponent.short_name;
              }
            });

            if (countyName && stateAbbr) {
              const rate = PropertyTaxByCounty[[countyName, stateAbbr].join(', ')];
              if (rate && !isNaN(rate)) {
                this.setState({ propertyTaxPercentage: rate });
              }
            }
          }
        }
      });
    }
  },

  onClick(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.analytics.sendEvent('detail view', 'mortgage calculator toggle');
    this.setState({ showModal: !this.state.showModal });
  },

  onInputPaymentAmount(e) {
    if (this.state.downPaymentAmount !== e.target.value) {
      this.setState({ downPaymentAmount: e.target.value, downPaymentPercentage: null });
      window.localStorageAlias.setItem('mortgage-calculator.downPaymentAmount', e.target.value);
      window.localStorageAlias.removeItem('mortgage-calculator.downPaymentPercentage');
      setTimeout(() => {
        const input = document.getElementById('down-payment');
        input.focus();
        const v = input.value;
        input.value = '';
        input.value = v;
      });
    }
  },

  onInputPaymentPercentage(e) {
    if (!Number.isNaN(e.target.value)) {
      if (this.state.downPaymentPercentage !== e.target.value) {
        this.setState({ downPaymentPercentage: e.target.value, downPaymentAmount: null });
        window.localStorageAlias.setItem('mortgage-calculator.downPaymentPercentage', e.target.value);
        window.localStorageAlias.removeItem('mortgage-calculator.downPaymentAmount');
        setTimeout(() => {
          const input = document.getElementById('down-payment-percentage');
          input.focus();
          const v = input.value;
          input.value = '';
          input.value = v;
        });
      }
    }
  },

  onInputInterestRate(e) {
    if (!Number.isNaN(e.target.value)) {
      this.setState({ interestRate: e.target.value });
      window.localStorageAlias.setItem('mortgage-calculator.interestRate', e.target.value);
    }
  },

  onInputYears(e) {
    this.setState({ years: e.target.value });
    window.localStorageAlias.setItem('mortgage-calculator.years', e.target.value);
  },

  togglePMI() {
    this.setState({ includePMI: !this.state.includePMI });
  },

  onInputPropertyTaxAmount(e) {
    if (this.state.propertyTaxAmount !== e.target.value) {
      this.setState({ propertyTaxAmount: e.target.value, propertyTaxPercentage: null });
      setTimeout(() => {
        const input = document.getElementById('property-tax');
        input.focus();
        const v = input.value;
        input.value = '';
        input.value = v;
      });
    }
  },

  onInputPropertyTaxPercentage(e) {
    if (!Number.isNaN(e.target.value)) {
      if (this.state.propertyTaxPercentage !== e.target.value) {
        this.setState({ propertyTaxPercentage: e.target.value, propertyTaxAmount: null });
        setTimeout(() => {
          const input = document.getElementById('property-tax-percentage');
          input.focus();
          const v = input.value;
          input.value = '';
          input.value = v;
        });
      }
    }
  },

  onInputHomeInsurance(e) {
    this.setState({ homeInsurance: e.target.value });
  },

  onInputHOA(e) {
    this.setState({ hoa: e.target.value });
  },

  render() {
    const monthlyPayment = this.utils.calculateMortgagePayment({
      sellingPrice: Number(this.props.price),
      downPaymentAmount: Number(this.state.downPaymentAmount),
      downPaymentPercentage: Number(this.state.downPaymentPercentage),
      interestRate: Number(this.state.interestRate),
      years: Number(this.state.years),
      propertyTaxAmount: Number(this.state.propertyTaxAmount),
      propertyTaxPercentage: Number(this.state.propertyTaxPercentage),
      includePMI: this.state.includePMI,
      homeInsurance: Number(this.state.homeInsurance),
      hoa: Number(this.state.hoa),
    });

    const assumableMonthlyPaymentTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__AssumableMonthlyPayment__'));
    const assumableMonthlyPayment = assumableMonthlyPaymentTag ? Math.round(Number(assumableMonthlyPaymentTag.Name.split('__')[2])) : undefined;

    let insuranceAmount = 0;
    if (monthlyPayment && this.state.includePMI) {
      insuranceAmount += Number(this.props.price) * 0.01 / 12;
    }
    if (monthlyPayment && this.state.homeInsurance) {
      insuranceAmount += Number(this.state.homeInsurance) / 12;
    }
    if (monthlyPayment && this.state.hoa) {
      insuranceAmount += Number(this.state.hoa);
    }
    insuranceAmount = Math.round(insuranceAmount);
    const insuranceDeg = Math.round(insuranceAmount / monthlyPayment * 360);
    let taxAmount = this.state.propertyTaxPercentage
      ? Number(this.props.price) * Number(this.state.propertyTaxPercentage / 100) : this.state.propertyTaxAmount;
    taxAmount = monthlyPayment ? (taxAmount || 0) / 12 : 0;
    taxAmount = Math.round(taxAmount);
    const taxDeg = Math.round(taxAmount / monthlyPayment * 360);

    const principleInterestAmount = monthlyPayment - insuranceAmount - taxAmount;
    const principleInterestDeg = principleInterestAmount / monthlyPayment * 360;

    if (assumableMonthlyPayment) {
      return <IDXPlusAssumableInfo key={this.props.listing.Id} listing={this.props.listing} assumableMonthlyPayment={assumableMonthlyPayment} />;
    }

    const settingsModal = (
      <Modal
        show={this.state.showModal}
        onHide={() => {
          this.setState({ showModal: false });
        }}
        id="mortgage-calculator-popover"
        className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
      >
        <CloseBtn onClick={() => {
          this.setState({ showModal: false });
        }}
        />
        <h3 className="popover-title">Estimated Monthly Payment</h3>
        <div className="row pl10 pt10 pr10 pb10 popover-content">
          <div className="col-9-24">
            <label htmlFor="home-price">Home Price</label>
            <div className="input-group home-price">
              <span className="input-group-addon">{this.utils.getDollarSymbol(this.props.listing)}</span>
              <input id="home-price" type="text" disabled className="form-control" value={this.utils.addThousandSepIfNumber(this.props.price)} />
            </div>
          </div>
          <div className="col-2-24" />
          <div className="col-13-24">
            <label htmlFor="down-payment">Down Payment</label>
            <div className="col-14-24">
              <div className="input-group down-payment-amount" key={this.state.downPaymentPercentage}>
                <span className="input-group-addon">{this.utils.getDollarSymbol(this.props.listing)}</span>
                <input
                  id="down-payment"
                  type="number"
                  className="form-control"
                  onBlur={this.onInputPaymentAmount}
                  onKeyUp={this.onInputPaymentAmount}
                  defaultValue={this.state.downPaymentAmount || this.state.homePrice * this.state.downPaymentPercentage / 100 || ''}
                />
              </div>
            </div>
            <div className="col-10-24">
              <div className="input-group down-payment-percentage" key={this.state.downPaymentAmount}>
                <input
                  id="down-payment-percentage"
                  type="text"
                  className="form-control"
                  onBlur={this.onInputPaymentPercentage}
                  onKeyUp={this.onInputPaymentPercentage}
                  defaultValue={this.state.downPaymentPercentage ? this.state.downPaymentPercentage : Math.round(this.state.downPaymentAmount / this.state.homePrice * 100)}
                />
                <span className="input-group-addon">%</span>
              </div>
            </div>
          </div>
          <div className="col-1-1 mt5" />
          <div className="col-9-24">
            <label htmlFor="loan-term">Loan Term</label>
            <div className="input-group loan-term">
              <input
                id="loan-term"
                type="number"
                className="form-control"
                onBlur={this.onInputYears}
                onKeyUp={this.onInputYears}
                defaultValue={this.state.years || ''}
              />
              <span className="input-group-addon">years</span>
            </div>
          </div>
          <div className="col-2-24" />
          <div className="col-13-24">
            <label htmlFor="interest-rate">Interest Rate</label>
            <div className="input-group interest-rate">
              <input
                id="interest-rate"
                type="text"
                className="form-control"
                onBlur={this.onInputInterestRate}
                onKeyUp={this.onInputInterestRate}
                defaultValue={this.state.interestRate || ''}
              />
              <span className="input-group-addon">%</span>
            </div>
          </div>

          <div className="col-1-1 mt15 mb0 text-center advanced-toggle">
            <a
              role="button"
              tabIndex="-1"
              className="cursor-pointer"
              onClick={() => {
                this.actions.analytics.sendEvent('detail view', 'mortgage calculator advanced toggle', (!this.state.showAdvanced) ? 'advanced' : 'simple'); this.setState({ showAdvanced: !this.state.showAdvanced });
              }}
            >
              {this.state.showAdvanced ? 'SIMPLE' : 'ADVANCED'}
            </a>
          </div>

          <div className={this.state.showAdvanced ? 'mt5' : 'hidden'}>
            <div className="col-15-24">
              <label htmlFor="property-tax">Property Tax</label>
              <div className="col-16-24">
                <div className="input-group property-tax-amount" key={this.state.propertyTaxPercentage}>
                  <span className="input-group-addon">$</span>
                  <input
                    id="property-tax"
                    type="number"
                    className="form-control"
                    onBlur={this.onInputPropertyTaxAmount}
                    onKeyUp={this.onInputPropertyTaxAmount}
                    defaultValue={this.state.propertyTaxAmount || this.state.homePrice * this.state.propertyTaxPercentage / 100 || ''}
                  />
                  <span className="input-group-addon">/yr</span>
                </div>
              </div>
              <div className="col-8-24">
                <div className="input-group property-tax-percentage" key={this.state.propertyTaxAmount}>
                  <input
                    id="property-tax-percentage"
                    type="text"
                    className="form-control"
                    onBlur={this.onInputPropertyTaxPercentage}
                    onKeyUp={this.onInputPropertyTaxPercentage}
                    defaultValue={(this.state.propertyTaxPercentage
                      ? !Number.isNaN(this.state.propertyTaxPercentage) && Number(this.state.propertyTaxPercentage) || 0
                      : (this.state.propertyTaxAmount / this.state.homePrice * 100)).toFixed(1).replace('0.0', '')}
                  />
                  <span className="input-group-addon">%</span>
                </div>
              </div>
            </div>
            <div className="col-1-24" />
            <div className="col-8-24">
              <label htmlFor="pmi-toggle" className="mt30">
                <input id="pmi-toggle" type="checkbox" defaultValue={this.state.includePMI} onClick={this.togglePMI} />
&nbsp;Include PMI
              </label>
            </div>

            <div className="col-1-1 mt5" />

            <div className="col-11-24">
              <label htmlFor="home-insurance">Home Insurance</label>
              <div className="input-group hoa">
                <input
                  id="home-insurance"
                  type="number"
                  className="form-control"
                  onBlur={this.onInputHomeInsurance}
                  onKeyUp={this.onInputHomeInsurance}
                  defaultValue={this.state.homeInsurance || ''}
                />
                <span className="input-group-addon">/year</span>
              </div>
            </div>
            <div className="col-2-24" />
            <div className="col-11-24">
              <label htmlFor="hoa">HOA Dues</label>
              <div className="input-group hoa">
                <input
                  id="hoa"
                  type="number"
                  className="form-control"
                  onBlur={this.onInputHOA}
                  onKeyUp={this.onInputHOA}
                  defaultValue={this.state.hoa || ''}
                />
                <span className="input-group-addon">/month</span>
              </div>
            </div>
          </div>

          <div className="divider" />

          <div className="col-1-2 text-center">
            <p className="mt20">Your Estimated Payment:</p>
            <p className="text-primary estimate">
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(monthlyPayment) || '-'}
              &nbsp;
              <sub>/mo</sub>
            </p>
          </div>
          <div className="col-1-2">
            <div id="pieContainer">
              <div className="pieBackground" />
              <div className="pieSlice1 hold">
                <div className="pie" style={{ transform: `rotate(${Math.min(principleInterestDeg, 180)}deg)` }} />
              </div>
              {
                principleInterestDeg > 180
                  ? (
                    <div className="pieSlice1 hold" style={{ transform: `rotate(${180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${principleInterestDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
              <div className="pieSlice2 hold" style={{ transform: `rotate(${principleInterestDeg}deg)` }}>
                <div className="pie" style={{ transform: `rotate(${Math.min(taxDeg, 180)}deg)` }} />
              </div>
              {
                taxDeg > 180
                  ? (
                    <div className="pieSlice2 hold" style={{ transform: `rotate(${principleInterestDeg + 180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${taxDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
              <div className="pieSlice3 hold" style={{ transform: `rotate(${principleInterestDeg + taxDeg}deg)` }}>
                <div className="pie" style={{ transform: `rotate(${Math.min(insuranceDeg, 180)}deg)` }} />
              </div>
              {
                insuranceDeg > 180
                  ? (
                    <div className="pieSlice3 hold" style={{ transform: `rotate(${principleInterestDeg + taxDeg + 180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${insuranceDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
              <div className="pieCenter" style={this.props.image ? { backgroundImage: `url('${this.props.image}')` } : {}} />
            </div>
          </div>
          <div className="col-9-24 text-center">
            <p className="legend-label">
              <span className="legend-icon legend1" />
              Principle &amp; Interest
            </p>
            <p className="legend-amount">
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(principleInterestAmount) || ' -'}
            </p>
          </div>
          <div className="col-6-24 text-center">
            <p className="legend-label">
              <span className="legend-icon legend2" />
              Taxes
            </p>
            <p className="legend-amount">
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(taxAmount) || ' -'}
            </p>
          </div>
          <div className="col-9-24 text-center">
            <p className="legend-label">
              <span className="legend-icon legend3" />
              Insurance &amp; HOA
            </p>
            <p className="legend-amount">
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(insuranceAmount) || ' -'}
            </p>
          </div>

          {
            this.actions.common.getAgentHasLendingTree(this.state.agentData)
              ? <div className="divider" />
              : null
          }

          {
            this.actions.common.getAgentHasLendingTree(this.state.agentData)
              ? (
                <div
                  role="button"
                  tabIndex={-1}
                  className="col-1-1 mortgage-calculator-lending-tree-cta"
                  onClick={() => {
                    this.setState({ showModal: false });
                    this.actions.analytics.sendEvent('lending tree', 'open from mortgage calculator modal', this.state.randomLendingTreeCTA);
                    this.actions.common.showLendingTreeModal();
                  }}
                >
                  <h2>{this.state.randomLendingTreeCTA}</h2>
                  <img alt="Lending Tree" src="https://nplayassets.blob.core.windows.net/images/powered-by-lendingtree.svg" />
                </div>
              )
              : null
          }
        </div>
        <p className="disclaimer pl10 pr10 pt5 text-center">
          Please note that these are estimates and not guaranteed.
          <br />
          Calculations provided by HomeASAP LLC.
        </p>
      </Modal>
    );

    if (this.props.shortVersion) {
      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="See mortgage calculator"
          className="mortgage-calculator-short"
          onClick={this.onClick}
        >
          <p><small><a role="button" tabIndex="-1" onClick={this.onClick}>Est. monthly payment</a></small></p>
          {settingsModal}
        </div>
      );
    }
    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="See mortgage calculator"
        className="mortgage-calculator"
        onClick={this.onClick}
      >
        <p>Mortgage Estimate</p>
        <p>
          <span className="monthly-payment">
            {this.utils.getDollarSymbol(this.props.listing)}
            {this.utils.addThousandSepIfNumber(monthlyPayment) || '-'}
            &nbsp;
            /mo
          </span>
          <SVGIcon name="icon-calculator" />
        </p>
        {
          this.actions.common.getAgentHasLendingTree(this.state.agentData)
            ? (
              <p className="mortgage-calculator-cta">
                <a
                  role="button"
                  tabIndex="-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    this.actions.analytics.sendEvent('lending tree', 'open from mortgage calculator snapshot cta');
                    this.actions.common.showLendingTreeModal();
                  }}
                >
                  See current rates
                </a>
              </p>
            )
            : null
        }
        {settingsModal}
      </div>
    );
  },
});
