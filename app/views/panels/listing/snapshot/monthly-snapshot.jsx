const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.monthly-snapshot',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  getInitialState() {
    return {

    };
  },

  componentWillMount() {

  },

  componentDidMount() {

  },

  render() {
    const monthlyPayment = this.props.monthlyPayment;

    const insuranceAmount = this.props.insuranceAmount;
    const insuranceDeg = Math.round(insuranceAmount / monthlyPayment * 360);
    const taxAmount = this.props.taxAmount;
    const taxDeg = Math.round(taxAmount / monthlyPayment * 360);

    const principleInterestAmount = monthlyPayment - insuranceAmount - taxAmount;
    const principleInterestDeg = principleInterestAmount / monthlyPayment * 360;

    return (
      <div>
        <div className="col-1-2 text-center">
          <p className="mt20">Your Estimated Payment:</p>
          <p className="text-primary estimate">
            {this.utils.getDollarSymbol(this.props.listing)}
            {this.utils.addThousandSepIfNumber(monthlyPayment) || '-'}
              &nbsp;
            <sub>/mo</sub>
          </p>
        </div>
        <div className="col-1-2">
          <div id="pieContainer">
            <div className="pieBackground" />
            <div className="pieSlice1 hold">
              <div className="pie" style={{ transform: `rotate(${Math.min(principleInterestDeg, 180)}deg)` }} />
            </div>
            {
                principleInterestDeg > 180
                  ? (
                    <div className="pieSlice1 hold" style={{ transform: `rotate(${180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${principleInterestDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
            <div className="pieSlice2 hold" style={{ transform: `rotate(${principleInterestDeg}deg)` }}>
              <div className="pie" style={{ transform: `rotate(${Math.min(taxDeg, 180)}deg)` }} />
            </div>
            {
                taxDeg > 180
                  ? (
                    <div className="pieSlice2 hold" style={{ transform: `rotate(${principleInterestDeg + 180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${taxDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
            <div className="pieSlice3 hold" style={{ transform: `rotate(${principleInterestDeg + taxDeg}deg)` }}>
              <div className="pie" style={{ transform: `rotate(${Math.min(insuranceDeg, 180)}deg)` }} />
            </div>
            {
                insuranceDeg > 180
                  ? (
                    <div className="pieSlice3 hold" style={{ transform: `rotate(${principleInterestDeg + taxDeg + 180}deg)` }}>
                      <div className="pie" style={{ transform: `rotate(${insuranceDeg - 180}deg)` }} />
                    </div>
                  ) : null
              }
            <div className="pieCenter" style={this.props.image ? { backgroundImage: `url('${this.props.image}')` } : {}} />
          </div>
        </div>
        <div className="col-9-24 text-center">
          <p className="legend-label">
            <span className="legend-icon legend1" />
            Principle &amp; Interest
          </p>
          <p className="legend-amount">
            {this.utils.getDollarSymbol(this.props.listing)}
            {this.utils.addThousandSepIfNumber(principleInterestAmount) || ' -'}
          </p>
        </div>
        <div className="col-6-24 text-center">
          <p className="legend-label">
            <span className="legend-icon legend2" />
            Taxes
          </p>
          <p className="legend-amount">
            {this.utils.getDollarSymbol(this.props.listing)}
            {this.utils.addThousandSepIfNumber(taxAmount) || ' -'}
          </p>
        </div>
        <div className="col-9-24 text-center">
          <p className="legend-label">
            <span className="legend-icon legend3" />
            Insurance &amp; HOA
          </p>
          <p className="legend-amount">
            {this.utils.getDollarSymbol(this.props.listing)}
            {this.utils.addThousandSepIfNumber(insuranceAmount) || ' -'}
          </p>
        </div>
      </div>
    );
  },
});
