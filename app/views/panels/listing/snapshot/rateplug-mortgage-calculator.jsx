const React = require('react');
const Modal = require('react-bootstrap').Modal;

const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const SpinnerRound = require('../../../components/spinner_round');
const CloseBtn = require('../../../components/close_btn_cross');
const MonthlySnapshot = require('./monthly-snapshot');
const RatePlugLenderInfo = require('./rateplug-lender-info');

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.rateplug-mortgage-calculator',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    menuDownPayment: ['shared', 'menu', 'downPayment'],
  },

  facets: {
    activeListingFromListings: ['activeListingFromListings'],
  },

  getInitialState() {
    let interestRate = window.rateplug.rp_rate;
    if (!interestRate || isNaN(interestRate)) {
      interestRate = 6.788; // National average 30-year on Google as of 2022-09-19
    } else {
      interestRate = Number(interestRate);
    }

    return {
      homePrice: null,
      interestRate,
      showModal: false,
      geocodeCountyName: null,
      mortgageInfo: null,
      selectedProgram: null,
    };
  },

  componentWillMount() {
    let downPaymentAmount = this.actions.menu.getMenuSelections().downPayment;
    if (!downPaymentAmount || isNaN(downPaymentAmount)) {
      downPaymentAmount = null;
    } else {
      downPaymentAmount = Number(downPaymentAmount);
    }

    this.setState({
      homePrice: this.props.price,
      downPaymentAmount,
    });
    // console.log('WillMount activeListingFromListings', this.state.activeListingFromListings);
  },

  getMortgagePaymentInfo() {
    let mortgageInfo = this.state.activeListingFromListings && this.state.activeListingFromListings.MortgagePaymentInfo;

    if (!mortgageInfo) {
      mortgageInfo = this.props.listing.MortgagePaymentInfo;
    }

    if (!mortgageInfo) {
      mortgageInfo = {};
    }

    return {
      principalAndInterest: mortgageInfo.Mortgage || 0,
      homeInsurance: mortgageInfo.Insurance || 0,
      propertyTaxAmount: mortgageInfo.Taxes || 0,
      hoa: mortgageInfo.HOA || 0,
    };
  },

  componentDidMount() {
    if (this.props.listing.Location.Lat && this.props.listing.Location.Lon) {
      this.utils.geocodeLatlng(this.props.listing.Location.Lat, this.props.listing.Location.Lon, (res) => {
        if (res) {
          if (Array.isArray(res.address_components)) {
            let countyName = null;
            let stateAbbr = null;
            res.address_components.forEach((addressComponent) => {
              if (addressComponent.types.indexOf('administrative_area_level_2') != -1) {
                countyName = addressComponent.long_name.replace(/\scounty/i, '');
              } else if (addressComponent.types.indexOf('administrative_area_level_1') != -1) {
                stateAbbr = addressComponent.short_name;
              }
            });

            if (stateAbbr === 'DC') {
              countyName = 'District Of Columbia';
            }

            if (countyName && stateAbbr) {
              const newState = { geocodeCountyName: countyName };
              this.setState(newState);
            }
          }
        }
      });
    }
  },

  onClick(e) {
    e.preventDefault();
    e.stopPropagation();

    if (!this.state.showModal) {
      this.actions.analytics.sendEvent('rateplug', 'mortgage calculator', 'open');
    }
    this.setState({ showModal: !this.state.showModal });

    if (!this.state.mortgageInfo && this.state.geocodeCountyName) {
      this.getMortgageInfo();
    }
  },

  getMortgageInfo() {
    if (this.state.mortgageInfo !== null) {
      let interestRate = window.rateplug.rp_rate;
      if (!interestRate || isNaN(interestRate)) {
        interestRate = 6.788; // National average 30-year on Google as of 2022-09-19
      } else {
        interestRate = Number(interestRate);
      }

      this.setState({
        mortgageInfo: null,
        selectedProgram: null,
        interestRate,
      });
    }

    this.utils.getMortgageInfo({
      state: this.props.listing.StateCode,
      county: this.state.geocodeCountyName,
      propertyType: this.utils.propertyTypeStringToRatePlugPropertyType(this.props.listing.PropertyTypeString),
      listPrice: this.props.price,
      isUSDA: this.props.listing.SpecialFinancePrograms.includes('USDA'),
    }, (mortgageInfo) => {
      if (!mortgageInfo) {
        this.setState({
          mortgageInfo: false,
          selectedProgram: false,
        });
        this.actions.analytics.sendEvent('rateplug', 'mortgage info', 'fail');
        return;
      }

      if (mortgageInfo.MortgageLenderInfo && mortgageInfo.MortgageLenderInfo[0]
          && mortgageInfo.MortgageLenderInfo[0].MortgagePrograms && mortgageInfo.MortgageLenderInfo[0].MortgagePrograms[0]
          && mortgageInfo.MortgageLenderInfo[0].MortgagePrograms[0].RATE) {
        const selectedProgram = mortgageInfo.MortgageLenderInfo[0].MortgagePrograms[0];
        this.setState({
          mortgageInfo: mortgageInfo.MortgageLenderInfo[0],
          selectedProgram,
          interestRate: selectedProgram.RATE,
        });

        // Archive
        setTimeout(() => {
          this.onArchiveRateplugModal(mortgageInfo.ArchiveID);
        }, 1000);
        this.actions.analytics.sendEvent('rateplug', 'mortgage info', 'success');
      } else if (mortgageInfo.MortgageLenderInfo && mortgageInfo.MortgageLenderInfo[0]) {
        this.setState({
          mortgageInfo: mortgageInfo.MortgageLenderInfo[0],
          selectedProgram: false,
        });
        this.actions.analytics.sendEvent('rateplug', 'mortgage info', 'no programs');
      } else {
        this.setState({
          mortgageInfo: false,
          selectedProgram: false,
        });
        this.actions.analytics.sendEvent('rateplug', 'mortgage info', 'fail');
      }
    });
  },

  onArchiveRateplugModal(ArchiveID) {
    const inputs = {
      ArchiveID,
      LOGUID: window.rateplug.rp_lo,
      CustGUID: window.rateplug.rp_buyer,
      listingNumber: this.props.listing.PropertyListingId,
      streetAddressComplete: this.props.listing.StreetAddress,
      city: this.props.listing.CityName,
      state: this.props.listing.StateCode,
      zip: this.props.listing.ZipCode,
      county: this.props.listing.CountyCode,
      Content: `<html>
      <head>
        ${document.getElementById('ha-css').outerHTML}
        <style>
          body[data-theme=rateplug] .show-on-print {
            display: block !important;
          }
          body[data-theme=rateplug] .hide-on-print {
            display: none !important;
          }
          body[data-theme=rateplug] .modal {
            overflow: visible !important;
            position: relative;
            top: unset;
            left: unset;
            right: unset;
            bottom: unset;
          }
          body[data-theme=rateplug] .modal#mortgage-calculator-popover .modal-dialog {
            max-width: 520px;
            margin-top: 20px;
          }
        </style>
      </head>
      <body data-theme="rateplug">
      ${document.getElementById('mortgage-calculator-popover').outerHTML}
      </body>
      </html>
      `,
    };

    this.utils.postRatePlugArchive(inputs);
  },

  onInputPaymentAmount(e) {
    if (this.state.downPaymentAmount !== e.target.value) {
      this.setState({ downPaymentAmount: e.target.value });
      setTimeout(() => {
        const input = document.getElementById('down-payment');
        input.focus();
        const v = input.value;
        input.value = '';
        input.value = v;
      });
    }
  },

  onInputPaymentPercentage(e) {
    if (!Number.isNaN(e.target.value)) {
      if (this.state.downPaymentPercentage !== e.target.value) {
        const downPaymentAmount = Math.round(this.props.price * Number(e.target.value) / 100);
        this.setState({ downPaymentAmount });
        setTimeout(() => {
          const input = document.getElementById('down-payment-percentage');
          input.focus();
          const v = input.value;
          input.value = '';
          input.value = v;
        });
      }
    }
  },

  onDownPaymentCalculate() {
    this.actions.menu.updateSearchField([{ key: 'downPayment', value: this.state.downPaymentAmount }]);
    this.getMortgageInfo();

    this.actions.analytics.sendEvent('rateplug', 'downpayment', 'calculate');
  },

  onMortgageProgramChange(e) {
    const newProgramName = e.target.value;
    const newProgram = this.state.mortgageInfo.MortgagePrograms.find((program) => program.SHORTNAME === newProgramName);
    this.setState({
      selectedProgram: newProgram,
      interestRate: newProgram.RATE,
    });
  },

  render() {
    const {
      propertyTaxAmount, homeInsurance, hoa,
    } = this.getMortgagePaymentInfo();

    const assumableMonthlyPaymentTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__AssumableMonthlyPayment__'));
    const assumableMonthlyPayment = assumableMonthlyPaymentTag ? Math.round(Number(assumableMonthlyPaymentTag.Name.split('__')[2])) : undefined;

    let monthlyPayment = 0;

    if (this.state.selectedProgram && this.state.selectedProgram.PrincipalAndInterest) {
      monthlyPayment = this.state.selectedProgram.PrincipalAndInterest;
    } else {
      monthlyPayment = this.utils.getMonthlyPrincipalAndInterest(this.props.price - this.state.menuDownPayment || 0, 360, this.state.interestRate / 100 / 12);
    }

    monthlyPayment = Math.max(0, monthlyPayment);

    let taxesHomeownersInsuranceHOA = propertyTaxAmount;

    let insuranceAmount = 0;
    if (homeInsurance) {
      insuranceAmount += homeInsurance;
      taxesHomeownersInsuranceHOA += homeInsurance;
      // PMI
      if (this.state.selectedProgram && this.state.selectedProgram.MortgageInsurance) {
        insuranceAmount += this.state.selectedProgram.MortgageInsurance;
      }
    }
    if (hoa) {
      insuranceAmount += hoa;
      taxesHomeownersInsuranceHOA += hoa;
    }
    insuranceAmount = Math.round(insuranceAmount);

    let taxAmount = propertyTaxAmount;
    taxAmount = Math.round(taxAmount);

    monthlyPayment += insuranceAmount;
    monthlyPayment += taxAmount;
    monthlyPayment = Math.round(monthlyPayment);

    taxesHomeownersInsuranceHOA = Math.round(taxesHomeownersInsuranceHOA);

    if (assumableMonthlyPayment) {
      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="Assumable view"
          className="mortgage-calculator cursor-default"
          onClick={this.onClick}
        >
          <p>Mortgage Estimate</p>
          <p>
            <span className="monthly-payment text-muted">
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(assumableMonthlyPayment)}
              &nbsp;
              /mo
            </span>
            <SVGIcon name="icon-calculator" />
          </p>
          <p className="mortgage-calculator-cta text-muted">
            Assumable mortgage
          </p>
        </div>
      );
    }

    const settingsModal = (
      <Modal
        show={this.state.showModal}
        onHide={() => {
          this.setState({ showModal: false });
        }}
        id="mortgage-calculator-popover"
        className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
      >
        <CloseBtn onClick={() => {
          this.setState({ showModal: false });
        }}
        />
        <h3 className="popover-title">Estimated Monthly Payment</h3>
        {
          this.state.mortgageInfo === null
            ? (
              <div className="row pl10 pt10 pr10 pb10 popover-content">
                <center className="mt30 mb30">
                  <SpinnerRound />
                  <br />
                  Finding you the best rate programs
                </center>
              </div>
            )
            : (
              <div>
                <div className="row pl10 pt10 pr10 pb10 popover-content">
                  <div className="col-9-24">
                    <div className="col-1-1">
                      <label htmlFor="home-price">Home Price</label>
                    </div>
                    <div className="input-group home-price">
                      <span className="input-group-addon">{this.utils.getDollarSymbol(this.props.listing)}</span>
                      <input id="home-price" type="text" disabled className="form-control" value={this.utils.addThousandSepIfNumber(this.props.price)} />
                    </div>
                  </div>
                  <div className="col-1-24" />
                  <div className="col-14-24">
                    <div className="col-1-1">
                      <label htmlFor="down-payment">Down Payment</label>
                    </div>
                    <div className="col-13-24">
                      <div className="input-group down-payment-amount">
                        <span className="input-group-addon">{this.utils.getDollarSymbol(this.props.listing)}</span>
                        <input
                          id="down-payment"
                          type="number"
                          className="form-control"
                          onInput={this.onInputPaymentAmount}
                          value={this.state.downPaymentAmount}
                        />
                      </div>
                    </div>
                    <div className="col-11-24">
                      <div className="input-group down-payment-percentage" key={this.state.downPaymentAmount}>
                        <input
                          id="down-payment-percentage"
                          type="text"
                          className="form-control"
                          onInput={this.onInputPaymentPercentage}
                          defaultValue={Number((this.state.downPaymentAmount / this.state.homePrice * 100).toFixed(2))}
                        />
                        <span className="input-group-addon">%</span>
                      </div>
                    </div>
                    {
                      `${this.state.menuDownPayment}` !== `${this.state.downPaymentAmount}`
                        ? (
                          <div className="col-1-1 text-center mt5">
                            <button
                              type="button"
                              className="btn btn-sm btn-primary"
                              onClick={this.onDownPaymentCalculate}
                            >
                              Calculate
                            </button>
                          </div>
                        )
                        : null
                    }
                  </div>
                  <div className="hide-on-print">
                    <div className="col-1-1 mt5" />
                    <div className="col-12-24">
                      <label htmlFor="loan-term">Loan Type</label>
                      <div className="loan-term">
                        {
                          this.state.selectedProgram && this.state.mortgageInfo
                            && this.state.mortgageInfo.MortgagePrograms
                            && this.state.mortgageInfo.MortgagePrograms.length > 0 ? (
                              <select className="form-control" value={this.state.selectedProgram.SHORTNAME} onInput={this.onMortgageProgramChange}>
                                {
                              this.state.mortgageInfo.MortgagePrograms.map((program) => (
                                <option value={program.SHORTNAME}>{program.SHORTNAME}</option>
                              ))
                            }
                              </select>
                            ) : (
                              <select className="form-control" value={window.rateplug.rp_program} disabled>
                                <option value={window.rateplug.rp_program}>{window.rateplug.rp_program || ''}</option>
                              </select>
                            )
                      }
                      </div>
                    </div>
                    <div className="col-1-24" />
                    <div className="col-7-24">
                      <label htmlFor="interest-rate">Interest Rate</label>
                      <div className="input-group interest-rate rate-label">
                        <p>{`${this.state.interestRate.toFixed(3)}%`}</p>
                      </div>
                    </div>
                    {this.state.selectedProgram && this.state.selectedProgram.APR || window.rateplug.rp_apr
                      ? (
                        <div className="col-4-24 text-right">
                          <label htmlFor="apr-rate">APR</label>
                          <div className="input-group apr-rate rate-label">
                            <p>{`${(this.state.selectedProgram && this.state.selectedProgram.APR || +window.rateplug.rp_apr).toFixed(3)}%`}</p>
                          </div>
                        </div>
                      )
                      : null}

                    {
                      this.state.mortgageInfo.MortgagePrograms && this.state.mortgageInfo.MortgagePrograms.length === 0
                        ? (
                          <div className="col-1-1 mt5 text-danger">
                            Sorry, no loan programs fit this down payment amount.  You will need to increase the down payment and/or change the payment range on the Map View.
                          </div>
                        ) : null
                    }
                  </div>

                  {
                    // Show all programs for archives
                    this.state.mortgageInfo.MortgagePrograms
                    && this.state.mortgageInfo.MortgagePrograms.length > 0 ? (
                      <div className="show-on-print hidden">
                        <div className="col-1-1 mt5" />
                        {this.state.mortgageInfo.MortgagePrograms.map((program, idx) => (
                          <div>
                            <div className="col-12-24">
                              {idx === 0 ? <label htmlFor="loan-term">Loan Type</label> : null}
                              <div className="input-group rate-label">
                                {idx === 0
                                  ? <p><strong>{`${program.SHORTNAME}`}</strong></p>
                                  : <p>{`${program.SHORTNAME}`}</p>}
                              </div>
                            </div>
                            <div className="col-1-24" />
                            <div className="col-7-24">
                              {idx === 0 ? <label htmlFor="interest-rate">Interest Rate</label> : null}
                              <div className="input-group interest-rate rate-label">
                                {idx === 0
                                  ? <p><strong>{`${program.RATE.toFixed(3)}%`}</strong></p>
                                  : <p>{`${program.RATE.toFixed(3)}%`}</p>}
                              </div>
                            </div>
                            <div className="col-4-24">
                              {idx === 0 ? <label htmlFor="apr-rate">APR</label> : null}
                              <div className="input-group apr-rate rate-label">
                                {idx === 0
                                  ? <p><strong>{`${program.APR.toFixed(3)}%`}</strong></p>
                                  : <p>{`${program.APR.toFixed(3)}%`}</p>}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      ) : null
                  }

                  <div className="divider" />

                  <MonthlySnapshot {...{
                    listing: this.props.listing,
                    image: `${this.props.image}?x-oss-process=image/resize,h_200/interlace,1`,
                    monthlyPayment,
                    insuranceAmount,
                    taxAmount,
                  }}
                  />
                  {
                    this.state.mortgageInfo
                      ? (
                        <div>
                          <div className="divider" />
                          <div className="col-1-1">
                            <RatePlugLenderInfo mortgageInfo={this.state.mortgageInfo} taxesHomeownersInsuranceHOA={taxesHomeownersInsuranceHOA} />
                          </div>
                        </div>
                      ) : null
                  }
                </div>
                {
                  this.state.mortgageInfo
                    ? (
                      <p className="disclaimer pl10 pr10 pb5 text-center" />
                    )
                    : (
                      <p className="disclaimer pl10 pr10 pt5 text-center">
                        Please note that these are estimates and not guaranteed.
                        <br />
                        Calculations provided by HomeASAP LLC.
                      </p>
                    )
                }
              </div>
            )
        }
      </Modal>
    );

    if (this.props.shortVersion) {
      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="See mortgage calculator"
          className="mortgage-calculator-short"
          onClick={this.onClick}
        >
          <p><small><a role="button" tabIndex="-1" onClick={this.onClick}>Est. monthly payment</a></small></p>
          {settingsModal}
        </div>
      );
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="See mortgage calculator"
        className="mortgage-calculator"
        onClick={this.onClick}
      >
        <p>Mortgage Estimate</p>
        <p>
          <span className="monthly-payment">
            {this.utils.getDollarSymbol(this.props.listing)}
            {monthlyPayment && monthlyPayment > 0 && this.utils.addThousandSepIfNumber(monthlyPayment) || '-'}
            /mo
            &nbsp;
          </span>
          <SVGIcon name="icon-calculator" />
        </p>
        <p className="mortgage-calculator-cta">
          <a
            role="button"
            tabIndex="-1"
          >
            See lowest rates
          </a>
        </p>
        {settingsModal}
      </div>
    );
  },
});
