const React = require('react');
const Modal = require('react-bootstrap').Modal;
const DropdownButton = require('react-bootstrap').DropdownButton;
const MenuItem = require('react-bootstrap').MenuItem;
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const CloseBtn = require('../../../components/close_btn_cross');

const PROGRAM_TYPES = {
  VA: {
    titleKey: 'VATitle',
    dataKey: 'VA',
    footerKey: 'VAFooter',
  },
  FHA: {
    titleKey: 'FHATitle',
    dataKey: 'FHA',
    footerKey: 'FHAFooter',
  },
  FHACondo: {
    titleKey: 'FHACondoTitle',
    dataKey: 'FHACondo',
    footerKey: 'FHACondoFooter',
  },
  USDA: {
    titleKey: 'USDATitle',
    dataKey: 'USDA',
    footerKey: 'USDAFooter',
  },
  DPA: {
    titleKey: 'DPATitle',
    dataKey: 'DPA',
    footer<PERSON>ey: 'DPAFooter',
  },
  Assumable: {
    titleKey: 'AssumableTitle',
    dataKey: 'Assumable',
    footerKey: 'AssumableFooter',
  },
};

const LoanOfficerContact = React.createClass({
  displayName: 'panels.listing.snapshot.rateplug-detail.loan-officer-contact',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      contactResult: null,
      loData: null,
    };
  },

  componentDidMount() {
    if (window.rateplug.rp_lo) {
      this.utils.getRateplugLOInfo(window.rateplug.rp_lo, (data) => {
        if (data) {
          this.setState({ loData: data });
        }
      });
    }
  },

  submitContact(method) {
    this.actions.agent.showLoanOfficerContact(this.state.loData, method);
  },

  render() {
    const { loData } = this.state;

    if (!loData || loData.isMPC) {
      return null;
    }

    let userWebsiteDomain = '';
    if (loData.WebSite) {
      try {
        userWebsiteDomain = (new URL(loData.WebSite)).hostname.replace('www.', '');
      } catch (_) { }
    }

    const loPhone = this.utils.formatPhone(loData.Phone);

    return (
      <div className="contact-loan-officer">
        <h3>Interested in this Program?</h3>
        <h4>Great, we can help! We can cover any questions you may have.</h4>
        <div className="col-sm-1-2 col-xs-1 mt15 pr20">
          {this.state.contactResult && this.state.contactResult.SuccessMessage ? (
            <p>{this.state.contactResult.SuccessMessage}</p>
          ) : (
            <div>
              <p>
                How would you like us to
                <br />
                contact you?
              </p>
              <div className="contact-loan-offier-methods">
                <button
                  className="btn btn-default"
                  type="button"
                  onClick={() => {
                    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'click', 'call');
                    this.submitContact('Call');
                  }}
                >
                  <SVGIcon name="icon-rp-call" />
                  Call
                </button>
                <button
                  className="btn btn-default"
                  type="button"
                  onClick={() => {
                    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'click', 'email');
                    this.submitContact('Email');
                  }}
                >
                  <SVGIcon name="icon-rp-email" />
                  Email
                </button>
              </div>
            </div>
          )}
          {this.state.contactResult && this.state.contactResult.ErrorMessage ? (
            <p className="text-danger">{this.state.contactResult.ErrorMessage}</p>
          ) : null}
          <div className="text-center mt15">
            <img src={loData.LogoMedTransparent} alt="Company Logo" width="150" />
          </div>
        </div>
        <div className="col-sm-1-2 col-xs-1 mt15">
          <div className="agent">
            <div className="col-2-30-70">
              <div className="center-image mt20"><img src={loData.Picture} className="profile-pic" alt="Profile pic" /></div>
              <div className="agent-info">
                <div className="agent-name">
                  {loData.FirstName}
                  &nbsp;
                  {loData.LastName}
                </div>
                <div className="agent-dark">{`${loData.Licenses}`}</div>
                <div />
                <div
                  className="cursor-pointer"
                  role="button"
                  tabIndex={-1}
                  onClick={() => {
                    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'click', 'email');
                    loData.Email && window.open(`mailto:${loData.Email}`);
                  }}
                >
                  {/* <span className="material-symbols-outlined material-icons">mail</span> */}
                  <span className="email">
                    {loData.Email}
                  </span>
                </div>
                <div
                  role="button"
                  tabIndex={-1}
                  onClick={() => {
                    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'click', 'phone');
                    loPhone && window.open(`tel:${loPhone}`, '_blank');
                  }}
                >
                  {/* <span className="material-symbols-outlined material-icons">smartphone</span> */}
                  {loPhone}
                  &nbsp;
                  {loData.PhoneExtension ? `Extension ${loData.PhoneExtension}` : ''}
                </div>
                <div
                  className="cursor-pointer"
                  role="button"
                  tabIndex={-1}
                  onClick={() => {
                    this.actions.analytics.sendEvent('rateplug', 'loan officer contact', 'click', 'website');
                    loData.WebSite && window.open(loData.WebSite);
                  }}
                >
                  {/* <span className="material-symbols-outlined material-icons">web</span> */}
                  {userWebsiteDomain}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.rateplug-detail',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
    downPayment: ['shared', 'menu', 'downPayment'],
    listingsData: ['panels', 'listings', 'data'],
  },

  getInitialState() {
    return {
      preSelectedProgram: null,
      activeProgram: null,
      data: null,
    };
  },

  componentDidMount() {
    if (!this.props.listing) {
      return;
    }

    this.state.preSelectedProgram = (this.state.specialFinancing || this.props.listing.SpecialFinancePrograms[0]).toString();

    const asPropertyIdTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__AS_PropertyID__'));
    const fcPropertyIdTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__FC_PropertyID__'));
    const asPropertyId = asPropertyIdTag ? Number(asPropertyIdTag.Name.split('__')[2]) : undefined;
    const fcPropertyId = fcPropertyIdTag ? Number(fcPropertyIdTag.Name.split('__')[2]) : undefined;

    const params = {
      FIPS: this.props.listing.Address.FIPS,
      FC_PropertyID: fcPropertyId,
      AS_PropertyID: asPropertyId,
      FHAEligible: this.props.listing.SpecialFinancePrograms.includes('FHA'),
      VAEligible: this.props.listing.SpecialFinancePrograms.includes('VA'),
      USDAEligible: this.props.listing.SpecialFinancePrograms.includes('USDA'),
      ListPrice: this.props.listing.ListPrice,
      PropertyType:
        this.props.listing.PropertyTypeString === 'SingleFamily' ? 1
          : this.props.listing.PropertyTypeString === 'MultiFamily' ? this.props.listing.TotalUnitsInComplex || 2 : 1,
      DownPayment: Number(this.state.downPayment),
    };

    // const params = {
    //   FIPS: '17031',
    //   FC_PropertyID: -205399207,
    //   AS_PropertyID: 44894346,
    //   FHAEligible: true,
    //   VAEligible: true,
    //   USDAEligible: true,
    //   ListPrice: 260449.00,
    //   PropertyType: 1,
    //   DownPayment: 50000.00,
    // };

    this.utils.getRateplugPropertyDetails(params, (data) => {
      if (data) {
        this.setState({ data });
      }
    });
  },

  modalClose(e) {
    e && e.preventDefault();

    // this.actions.analytics.sendEvent('home inspection', 'close');
    this.setState({ activeProgram: null });
  },

  getModalContent(program) {
    if (!program) {
      return null;
    }

    const squareFootage = this.utils.formatSquareFeet(this.props.listing, this.state.showOnlyRangeLivingSquare);

    const programTitle = this.state.data[PROGRAM_TYPES[program].titleKey];
    const programData = this.state.data[PROGRAM_TYPES[program].dataKey];
    const programFooter = this.state.data[PROGRAM_TYPES[program].footerKey];

    if (!programData) {
      return 'Program data not available';
    }

    const structuredProgramData = [];

    for (const row of programData) {
      const {
        IsHeading, Message, DataValue, Order,
      } = row;

      if (IsHeading) {
        structuredProgramData.push({
          heading: Message,
          rows: [],
        });
      } else {
        structuredProgramData[structuredProgramData.length - 1].rows.push({
          Message, DataValue, Order,
        });
        if (DataValue) {
          structuredProgramData[structuredProgramData.length - 1].hasData = true;
        }
      }
    }

    return (
      <div className="row popover-content">
        <h2>{programTitle}</h2>
        <div className="white-section">
          <div className="text-center">
            <DropdownButton
              className="more-programs-button"
              bsStyle="primary"
              title={(
                <span>
                  Additional special financing options
                </span>
            )}
              onSelect={(_program) => this.setState({ activeProgram: _program })}
            >
              {
                Object.keys(PROGRAM_TYPES)
                  .filter((_program) => _program !== this.state.activeProgram && this.state.data[PROGRAM_TYPES[_program].dataKey])
                  .map((_program) => (
                    <MenuItem key={_program} eventKey={_program}>{this.state.data[PROGRAM_TYPES[_program].titleKey]}</MenuItem>
                  ))
            }
            </DropdownButton>
          </div>
          <div className="listing-preview">
            <div className="col-sm-1-2 col-xs-1">
              <img alt="Listing" src={`${this.props.listing.DefaultImage}`} />
            </div>
            <div className="col-sm-1-2 col-xs-1 snapshot">
              <p className="mb10"><strong>Home of Interest</strong></p>
              <p>
                <span>{this.props.listing.Address.FullStreetAddress}</span>
                <br />
                <span>
                  {this.props.listing.Address.CityName}
                  ,
                  &nbsp;
                  {this.props.listing.Address.State}
                  &nbsp;
                  {this.props.listing.Address.ZipCode}
                </span>
                <br />
                {this.props.listing.Neighborhood
                  ? [
                    <span key="0">
                      Neighborhood:
                      {this.props.listing.Neighborhood}
                    </span>,
                    <br key="1" />,
                  ]
                  : null}
              </p>
              {this.props.listing.YearBuilt
                ? <p className="mb10">{`Built ${this.props.listing.YearBuilt}`}</p> : null}
              <p className="mb10">
                <span>
                  {this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms) == 1 ? 'bed' : 'beds'}
                &nbsp;|&nbsp;
                  {this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths) == 1 ? 'bath' : 'baths'}
                  {squareFootage ? (` | ${squareFootage} sqft`) : ''}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div className="program-description white-section">
          {
            structuredProgramData.map(({ heading, rows, hasData }, idx) => (
              <div className="program-description-section" key={idx}>
                {
                  heading
                    ? (
                      <h3 dangerouslySetInnerHTML={{ __html: heading }} />
                    ) : null
                }
                {
                    hasData ? (
                      <table>
                        <tbody>
                          {
                            rows.map(({
                              Message, DataValue, Order,
                            }) => (
                              <tr key={Order}>
                                <td>{Message}</td>
                                <td>{DataValue}</td>
                              </tr>
                            ))
                          }
                        </tbody>
                      </table>
                    ) : (
                      <ul>
                        {
                          rows.map(({
                            Message, DataValue, Order,
                          }) => (
                            <li key={Order}>
                              {Message}
                              {DataValue ? `: ${DataValue}` : ''}
                            </li>
                          ))
                        }
                      </ul>
                    )
                  }

              </div>
            ))
          }
        </div>
        <LoanOfficerContact key={programTitle} program={programTitle} />
        <div className="white-divider" />
        {
          programFooter ? (
            <div className="col-xs-1 mt15 disclaimer">
              {
                programFooter.map(({ IsHeading, Message }) => {
                  if (IsHeading) {
                    return (
                      <p>
                        <strong>{Message}</strong>
                      </p>
                    );
                  }
                  return (
                    <p>
                      <small>
                        {Message}
                      </small>
                    </p>
                  );
                })
              }
            </div>
          ) : null
        }
      </div>
    );
  },

  render() {
    if (!this.state.data) {
      return null;
    }

    const availablePrograms = Object.keys(PROGRAM_TYPES)
      .filter((program) => this.state.data[PROGRAM_TYPES[program].dataKey]);

    if (availablePrograms.length === 0) {
      return null;
    }

    return (
      <div className="rateplug-special-financing-details">
        {
          this.state.preSelectedProgram && availablePrograms.includes(this.state.preSelectedProgram) ? (
            <div
              className="primary-program-button"
              role="button"
              tabIndex={-1}
              onClick={() => this.setState({
                activeProgram: this.state.preSelectedProgram,
              })}
            >
              <p>{this.state.data[PROGRAM_TYPES[this.state.preSelectedProgram].titleKey]}</p>
              <p><small>Learn more</small></p>
            </div>
          ) : null
        }

        {
          (availablePrograms.length > 1)
            ? (
              <DropdownButton
                className="secondary-program-button"
                bsStyle="primary"
                title={(
                  <span>
                    Additional special
                    <br />
                    financing options
                  </span>
            )}
                onSelect={(program) => this.setState({ activeProgram: program })}
                noCaret
                pullRight
              >
                {
              availablePrograms
                .filter((program) => program !== this.state.preSelectedProgram)
                .map((program) => (
                  <MenuItem key={program} eventKey={program}>{this.state.data[PROGRAM_TYPES[program].titleKey]}</MenuItem>
                ))
            }
              </DropdownButton>
            )
            : null
        }

        <Modal
          show={this.state.activeProgram}
          onHide={this.modalClose}
          id="rateplug-special-financing-modal"
          className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
        >
          <CloseBtn onClick={this.modalClose} />
          {this.getModalContent(this.state.activeProgram)}
        </Modal>
      </div>
    );
  },
});
