const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const CloseBtn = require('../../../components/close_btn_cross');
const VerifiedBadge = require('../../../components/verified-badge');
const AgentProfileImage = require('../../../components/agent_profile_image');

const PROGRAM_TYPES = {
  // VA: {
  //   titleKey: 'VATitle',
  //   dataKey: 'VA',
  //   footerKey: 'VAFooter',
  // },
  // FHA: {
  //   titleKey: 'FHATitle',
  //   dataKey: 'FHA',
  //   footerKey: 'FHAFooter',
  // },
  // FHACondo: {
  //   titleKey: 'FHACondoTitle',
  //   dataKey: 'FHACondo',
  //   footerKey: 'FHACondoFooter',
  // },
  // USDA: {
  //   titleKey: 'USDATitle',
  //   dataKey: 'USDA',
  //   footerKey: 'USDAFooter',
  // },
  // DPA: {
  //   titleKey: 'DPATitle',
  //   dataKey: 'DPA',
  //   footerKey: 'DPAFooter',
  // },
  Assumable: {
    titleKey: 'AssumableTitle',
    dataKey: 'Assumable',
    footerKey: 'AssumableFooter',
  },
};

const Contact = React.createClass({
  displayName: 'panels.listing.snapshot.idx-plus-assumable-info.contact',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  getInitialState() {
    return {
    };
  },

  componentDidMount() {
  },

  setAgentContact(activeTab) {
    this.actions.agent.setActiveTab(activeTab);
    this.actions.agent.showAgentContact(this.props.listing);

    window.sendEvent('detail view', 'idx plus assumable', this.state.agentData.ZipCode);
  },

  render() {
    if (!this.props.program || !this.state.agentData) {
      return null;
    }

    return (
      <div className="col-1-1 contact-me-container">
        <div className="contact-me">
          <div className="left-side">
            <h3>Interested in this Program?</h3>
            <h6>Great, we can help! We can cover any questions you may have.</h6>
            <a
              role="button"
              tabIndex="-1"
              className="btn btn-primary"
              onClick={this.setAgentContact.bind(this, 'Call')}
            >
              Contact Me
            </a>
          </div>
          <div className="agent-container">
            <AgentProfileImage className="agent-image" />
            <p className="agent-name">
              {this.state.agentData.FirstName}
              &nbsp;
              {this.state.agentData.LastName}
              &nbsp;
              <VerifiedBadge />
            </p>
          </div>
        </div>
        <hr />
      </div>
    );
  },
});

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.idx-plus-assumable-info',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
    listingsData: ['panels', 'listings', 'data'],
  },

  getInitialState() {
    return {
      preSelectedProgram: null,
      activeProgram: null,
      data: null,
    };
  },

  componentDidMount() {
    if (!this.props.listing) {
      return;
    }

    const asPropertyIdTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__AS_PropertyID__'));
    const fcPropertyIdTag = this.props.listing.Tags.find((tag) => tag.Name.startsWith('RatePlug__FC_PropertyID__'));
    const asPropertyId = asPropertyIdTag ? Number(asPropertyIdTag.Name.split('__')[2]) : undefined;
    const fcPropertyId = fcPropertyIdTag ? Number(fcPropertyIdTag.Name.split('__')[2]) : undefined;

    const params = {
      FIPS: this.props.listing.Address.FIPS,
      FC_PropertyID: fcPropertyId,
      AS_PropertyID: asPropertyId,
      FHAEligible: this.props.listing.SpecialFinancePrograms.includes('FHA'),
      VAEligible: this.props.listing.SpecialFinancePrograms.includes('VA'),
      USDAEligible: this.props.listing.SpecialFinancePrograms.includes('USDA'),
      ListPrice: this.props.listing.ListPrice,
      PropertyType:
        this.props.listing.PropertyTypeString === 'SingleFamily' ? 1
          : this.props.listing.PropertyTypeString === 'MultiFamily' ? this.props.listing.TotalUnitsInComplex || 2 : 1,
      DownPayment: 0,
    };

    // const params = {
    //   FIPS: '17031',
    //   FC_PropertyID: -205399207,
    //   AS_PropertyID: 44894346,
    //   FHAEligible: true,
    //   VAEligible: true,
    //   USDAEligible: true,
    //   ListPrice: 260449.00,
    //   PropertyType: 1,
    //   DownPayment: 50000.00,
    // };

    this.utils.getRateplugPropertyDetails(params, (data) => {
      if (data) {
        this.setState({ data });
      }
    });
  },

  modalClose(e) {
    e && e.preventDefault();

    // this.actions.analytics.sendEvent('home inspection', 'close');
    this.setState({ activeProgram: null });
  },

  getModalContent(program) {
    if (!program) {
      return null;
    }

    const squareFootage = this.utils.formatSquareFeet(this.props.listing, this.state.showOnlyRangeLivingSquare);

    const programTitle = this.state.data[PROGRAM_TYPES[program].titleKey];
    const programData = this.state.data[PROGRAM_TYPES[program].dataKey];
    const programFooter = this.state.data[PROGRAM_TYPES[program].footerKey];

    if (!programData) {
      return 'Program data not available';
    }

    const structuredProgramData = [];

    for (const row of programData) {
      const {
        IsHeading, Message, DataValue, Order,
      } = row;

      if (IsHeading) {
        structuredProgramData.push({
          heading: Message,
          rows: [],
        });
      } else {
        structuredProgramData[structuredProgramData.length - 1].rows.push({
          Message, DataValue, Order,
        });
        if (DataValue) {
          structuredProgramData[structuredProgramData.length - 1].hasData = true;
        }
      }
    }

    return (
      <div className="row popover-content">
        <h2>{programTitle}</h2>
        <div className="white-section">
          <div className="listing-preview">
            <div className="col-sm-1-2 col-xs-1">
              <img alt="Listing" src={`${this.props.listing.DefaultImage}`} />
            </div>
            <div className="col-sm-1-2 col-xs-1 snapshot">
              <p className="mb10"><strong>Home of Interest</strong></p>
              <p>
                <span>{this.props.listing.Address.FullStreetAddress}</span>
                <br />
                <span>
                  {this.props.listing.Address.CityName}
                  ,
                  &nbsp;
                  {this.props.listing.Address.State}
                  &nbsp;
                  {this.props.listing.Address.ZipCode}
                </span>
                <br />
                {this.props.listing.Neighborhood
                  ? [
                    <span key="0">
                      Neighborhood:
                      {this.props.listing.Neighborhood}
                    </span>,
                    <br key="1" />,
                  ]
                  : null}
              </p>
              {this.props.listing.YearBuilt
                ? <p className="mb10">{`Built ${this.props.listing.YearBuilt}`}</p> : null}
              <p className="mb10">
                <span>
                  {this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms) == 1 ? 'bed' : 'beds'}
                &nbsp;|&nbsp;
                  {this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths) == 1 ? 'bath' : 'baths'}
                  {squareFootage ? (` | ${squareFootage} sqft`) : ''}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div className="program-description white-section">
          {
            structuredProgramData.map(({ heading, rows, hasData }, idx) => (
              <div className="program-description-section" key={idx}>
                {
                  heading
                    ? (
                      <h3 dangerouslySetInnerHTML={{ __html: heading }} />
                    ) : null
                }
                {
                    hasData ? (
                      <table>
                        <tbody>
                          {
                            rows.map(({
                              Message, DataValue, Order,
                            }) => (
                              <tr key={Order}>
                                <td>{Message}</td>
                                <td>{DataValue}</td>
                              </tr>
                            ))
                          }
                        </tbody>
                      </table>
                    ) : (
                      <ul>
                        {
                          rows.map(({
                            Message, DataValue, Order,
                          }) => (
                            <li key={Order}>
                              {Message}
                              {DataValue ? `: ${DataValue}` : ''}
                            </li>
                          ))
                        }
                      </ul>
                    )
                  }

              </div>
            ))
          }
        </div>
        <Contact key={programTitle} program={programTitle} listing={this.props.listing} />
        {
          programFooter ? (
            <div className="col-xs-1 disclaimer">
              {
                programFooter.map(({ IsHeading, Message }) => {
                  if (IsHeading) {
                    return (
                      <p>
                        <strong>{Message}</strong>
                      </p>
                    );
                  }
                  return (
                    <p>
                      <small>
                        {Message}
                      </small>
                    </p>
                  );
                })
              }
            </div>
          ) : null
        }
      </div>
    );
  },

  render() {
    let hasAssumable = false;
    if (this.state.data) {
      const availablePrograms = Object.keys(PROGRAM_TYPES)
        .filter((program) => this.state.data[PROGRAM_TYPES[program].dataKey]);
      hasAssumable = availablePrograms.includes('Assumable');
    }

    return (
      <div>
        <div
          role="button"
          tabIndex="-1"
          aria-label="Assumable view"
          className={`mortgage-calculator ${!hasAssumable ? 'cursor-default' : ''}`}
          onClick={() => {
            if (hasAssumable) {
              this.setState({ activeProgram: 'Assumable' });
            }
          }}
        >
          <p>Mortgage Estimate</p>
          <p>
            <span className={`monthly-payment ${!hasAssumable ? 'text-muted' : ''}`}>
              {this.utils.getDollarSymbol(this.props.listing)}
              {this.utils.addThousandSepIfNumber(this.props.assumableMonthlyPayment)}
              &nbsp;
              /mo
            </span>
            <SVGIcon name="icon-calculator" />
          </p>
          <p className={`mortgage-calculator-cta ${!hasAssumable ? 'text-muted' : ''}`}>
            Assumable mortgage
          </p>
        </div>

        <Modal
          show={this.state.activeProgram}
          onHide={this.modalClose}
          id="rateplug-special-financing-modal"
          className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
        >
          <CloseBtn onClick={this.modalClose} />
          {this.getModalContent(this.state.activeProgram)}
        </Modal>
      </div>
    );
  },
});
