/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable react/button-has-type */
const React = require('react');
const mixins = require('../../../../lib/mixins/index');

const RatePlugDisclosure = React.createClass({

  displayName: 'panels.listing.snapshot.rateplug-disclosure',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.pureRender],

  cursors: {
  },

  getInitialState() {
    return {

    };
  },

  componentWillMount() {

  },

  componentDidMount() {

  },

  programDetails: (rateProgram) => {
    const {
      RateId,
      Name,
      RateMsg,
      ARMMsg,
      MIMsg,
      // PaymentColmsg,
      LoanAmtmsg,
      Downpaymentmsg,
      RateProgramDetails,
    } = rateProgram;

    const rateProgramCount = RateProgramDetails.length;

    return (
      <div key={RateId} className="mt20">
        <div className="program">
          {`${Name}:`}
        </div>
        <div className="mt10">
          {
            [RateMsg, ARMMsg, MIMsg].filter(Boolean).map((message, idx) => (
              <div key={idx} dangerouslySetInnerHTML={{ __html: ` • ${message}` }} />
            ))
          }
        </div>
        <div className="rate-box mt10">
          <div className="rate-highlight">
            Projected Payments
            <br />
            <br />
            Loan Amount:&nbsp;
            {LoanAmtmsg}
            <br />
            Down Payment:&nbsp;
            {Downpaymentmsg}
          </div>
          <div className={`rate-subhighlight rate-col-${rateProgramCount + 1}`}>
            {
              ['', ...RateProgramDetails.map(({ Year }) => Year)]
                .map((text, idx) => <div key={idx}>{text}</div>)
            }
          </div>
          <div className={`rate-col-${rateProgramCount + 1}`}>
            {
              [
                { Rate: 'Interest Rate / ', APR: 'APR' },
                ...RateProgramDetails.map(({ Rate, APR }) => ({ Rate, APR })),
              ]
                .map(({ Rate, APR }, idx) => (
                  <div key={idx} className="border-right p10">
                    {Rate}
                    <span className="dark">{APR}</span>
                  </div>
                ))
            }
          </div>
          <div className={`rate-col-${rateProgramCount + 1}`}>
            {
              ['Principal & Interest', ...RateProgramDetails.map(({ PIMnthlyPayment }) => PIMnthlyPayment)]
                .map((text, idx) => <div key={idx} className="border-right p10">{text}</div>)
            }
          </div>
          <div className={`rate-col-${rateProgramCount + 1}`}>
            {
              ['Estimated monthly Taxes, Insurance & HOA/Dues', ...RateProgramDetails.map(({ TaxIns }) => TaxIns)]
                .map((text, idx) => <div key={idx} className="border-right p10">{text}</div>)
            }
          </div>
          <div className={`rate-col-${rateProgramCount + 1}`}>
            {
              ['Mortgage Insurance', ...RateProgramDetails.map(({ MI }) => MI)]
                .map((text, idx) => <div key={idx} className="border-right p10">{text}</div>)
            }
          </div>
          <div className={`rate-col-${rateProgramCount + 1}-nb`}>
            {
              ['Estimated Total Monthly Payment', ...RateProgramDetails.map(({ TtlMnthlyPayment }) => TtlMnthlyPayment)]
                .map((text, idx) => <div key={idx} className="border-right p10 dark">{text}</div>)
            }
          </div>
        </div>
        {/* end of Arm */}
      </div>
    );
  },

  render() {
    const { mortgageInfo } = this.props;

    const { PaymentStreamObj, DisclaimerObj, TridStatementObj } = mortgageInfo;

    if (!PaymentStreamObj || !DisclaimerObj || !TridStatementObj) {
      return null;
    }

    const TaxIns = `$${this.utils.addThousandSep(this.props.taxesHomeownersInsuranceHOA)}`;
    const NumTaxIns = this.props.taxesHomeownersInsuranceHOA;

    return (
      <div className="disclaimer-container">
        <div className="dark mt10" id="disclaimer">REQUIRED DISCLAIMERS</div>
        <div dangerouslySetInnerHTML={{ __html: PaymentStreamObj.FirstStatement }} />
        <div className="mt10" dangerouslySetInnerHTML={{ __html: PaymentStreamObj.StatementsHeading }} />
        {
          (PaymentStreamObj.PaymentStatements || []).map(({ ProgramStatement, RateId }) => (
            <div className="mt5" key={RateId} dangerouslySetInnerHTML={{ __html: ProgramStatement }} />
          ))
        }

        <div className="mt10">
          {
            DisclaimerObj.NCUALogo ? <img className="req-img" src={DisclaimerObj.NCUALogo} /> : null
          }
          {
            DisclaimerObj.FDICLogo ? <img className="req-img" src={DisclaimerObj.FDICLogo} /> : null
          }
          {
            DisclaimerObj.EqualLenderLogo ? <img className="req-img" src={DisclaimerObj.EqualLenderLogo} /> : null
          }
          {
            DisclaimerObj.EqualHousingLogo ? <img className="req-img" src={DisclaimerObj.EqualHousingLogo} /> : null
          }
          <div className="mt10" dangerouslySetInnerHTML={{ __html: DisclaimerObj.FirstStatment }} />
          <div className="mt10" dangerouslySetInnerHTML={{ __html: DisclaimerObj.DisclaimerStatment }} />
        </div>

        {
          TridStatementObj.RatePrograms.map((rateProgram) => this.programDetails({
            ...rateProgram,
            RateProgramDetails: rateProgram.RateProgramDetails.map((rateProgramDetails) => ({
              ...rateProgramDetails,
              TaxIns,
              NumTaxIns,
              TtlMnthlyPayment: `$${this.utils.addThousandSep((rateProgramDetails.NumPIMnthlyPayment || 0) + (NumTaxIns || 0) + (rateProgramDetails.NumMI || 0))}`,
            })),
          }))
        }

      </div>
    );
  },
});

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.rateplug-lender-info',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
  },

  getInitialState() {
    return {
      showDisclosure: false,
    };
  },

  componentWillMount() {
    if (this.props.showDisclosure) {
      this.state.showDisclosure = true;
    }
  },

  componentDidMount() {

  },

  render() {
    const { mortgageInfo } = this.props;

    const showLender = mortgageInfo && !mortgageInfo.isMPC;
    let userWebsiteDomain = '';
    if (mortgageInfo.UserWebSite) {
      try {
        userWebsiteDomain = (new URL(mortgageInfo.UserWebSite)).hostname.replace('www.', '');
      } catch (_) { }
    }

    return (
      <div className="rateplug-grid-container rateplug-lender-info">
        <div className="block">
          <div className="my-lender common">
            {
              showLender ? (
                <div className="my-lender-contact">
                  <div className="center-image"><img src={mortgageInfo.LenderLargeImage} className="mt15 mutual-logo" alt="Mutual of Omaha Mortgage Logo" /></div>
                  <div><hr /></div>
                  <div className="agent">
                    <div className="col-2-30-70">
                      <div className="center-image mt20"><img src={mortgageInfo.UserPictureWeb} className="profile-pic" alt="Profile pic" /></div>
                      <div className="agent-info">
                        <div className="agent-name">
                          {mortgageInfo.FirstName}
                          &nbsp;
                          {mortgageInfo.LastName}
                        </div>
                        <div className="agent-dark">{`${mortgageInfo.UserLicenses}${mortgageInfo.UserLicenses2 ? ` | ${mortgageInfo.UserLicenses2}` : ''}`}</div>
                        <div />
                        <div
                          className="cursor-pointer"
                          onClick={() => {
                            this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'email');
                            mortgageInfo.Email && window.open(`mailto:${mortgageInfo.Email}`);
                          }}
                        >
                          {/* <span className="material-symbols-outlined material-icons">mail</span> */}
                          <span className="email">
                            {mortgageInfo.Email}
                          </span>
                        </div>
                        <div
                          className="cursor-pointer"
                          onClick={() => {
                            this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'phone');
                            window.open(`tel:${mortgageInfo.CellPhone}`);
                          }}
                        >
                          {/* <span className="material-symbols-outlined material-icons">smartphone</span> */}
                          {this.utils.formatPhone(mortgageInfo.CellPhone)}
                        </div>
                        <div
                          className="cursor-pointer"
                          onClick={() => {
                            this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'website');
                            if (mortgageInfo.UserWebSite) {
                              window.open(mortgageInfo.UserWebSite);
                            }
                          }}
                        >
                          {/* <span className="material-symbols-outlined material-icons">web</span> */}
                          {userWebsiteDomain}
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* <div className="mt10">Contact me directly or select a topic below and I&apos;ll reach out to you ASAP.</div> */}
                  <div className="mt10 mb10">Contact me directly if you have any questions on any topic.</div>
                  <div className="four-box" style={{ display: 'none' }}>
                    <div
                      className="but-box cursor-pointer"
                      onClick={() => {
                        this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'BYDH');
                        window.open(mortgageInfo.BYDHLink);
                      }}
                    >
                      {/* <img className="button-img" src="https://nplayassets.blob.core.windows.net/images/rateplug/MORE-images/home.png" alt="home" /> */}
                      <span className="light-blue cta-btn">Dream Home &gt;</span>
                    </div>
                    <div
                      className="but-box cursor-pointer"
                      onClick={() => {
                        this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'DS');
                        window.open(mortgageInfo.DSLink);
                      }}
                    >
                      {/* <img className="button-img" src="https://nplayassets.blob.core.windows.net/images/rateplug/MORE-images/house.png" alt="house" /> */}
                      <span className="light-blue cta-btn">Downsize &gt;</span>
                    </div>
                    <div
                      className="but-box cursor-pointer"
                      onClick={() => {
                        this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'FIP');
                        window.open(mortgageInfo.FIPLink);
                      }}
                    >
                      {/* <img className="button-img" src="https://nplayassets.blob.core.windows.net/images/rateplug/MORE-images/for-rent.png" alt="for rent" /> */}
                      <span className="light-blue cta-btn">Investment Properties &gt;</span>
                    </div>
                    <div
                      className="but-box cursor-pointer"
                      onClick={() => {
                        this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'VP');
                        window.open(mortgageInfo.VPLink);
                      }}
                    >
                      {/* <img className="button-img" src="https://nplayassets.blob.core.windows.net/images/rateplug/MORE-images/palm.png" alt="palm" /> */}
                      <span className="light-blue cta-btn">Vacation Home &gt;</span>
                    </div>
                  </div>
                  {
                    mortgageInfo.UserWebSite2
                      ? (
                        <div>
                          <button
                            className="btn btn-block btn-primary"
                            onClick={() => {
                              this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'GPQ');
                              window.open(mortgageInfo.UserWebSite2, '_blank');
                            }}
                          >
                            GET PRE-QUALIFIED IN MINUTES
                          </button>
                        </div>
                      ) : null
                    }
                  {/* <div className="mt20">
                    <button
                      className="btn btn-block btn-info"
                      onClick={() => {
                        this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'Apply');
                        window.open(mortgageInfo.ApplyLink);
                      }}
                    >
                      APPLY NOW
                    </button>
                  </div> */}
                </div>
              ) : null
            }
            <div className="mt20"><span className="disclaimer">{mortgageInfo.PaymentDisclosure}</span></div>
            <div className="mt20 mb10">
              <button
                className="w100 button-green-stroke"
                onClick={() => {
                  this.actions.analytics.sendEvent('rateplug', 'lender detail', 'click', 'disclosure');
                  this.setState({ showDisclosure: !this.state.showDisclosure });
                }}
              >
                VIEW REQUIRED DISCLAIMER INFORMATION
              </button>
            </div>

            {/* end of third */}
            <div className={`show-on-print ${this.state.showDisclosure ? '' : 'hidden'}`}>
              <RatePlugDisclosure {...this.props} />
            </div>
          </div>
        </div>
      </div>
    );
  },
});
