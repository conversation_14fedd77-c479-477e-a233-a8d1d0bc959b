const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const CloseBtn = require('../../../components/close_btn_cross');

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot.home-inspection',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      modal: false,
      quote: false,
      submitting: false,
    };
  },

  componentDidMount() {
    if (!this.props.listing) {
      return;
    }

    this.actions.listing.getHomeInspectionQuote(this.props.listing.Id, (err, data) => {
      if (!err && data) {
        this.setState({ quote: data });
      }
    });
  },

  onClick(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.analytics.sendEvent('detail view', 'home inspection toggle');
    this.actions.analytics.sendEvent('home inspection', 'open');
    this.setState({ modal: true });
  },

  modalClose(e) {
    e && e.preventDefault();

    this.actions.analytics.sendEvent('home inspection', 'close');
    this.setState({ modal: false });
  },

  clearModal(e) {
    e.preventDefault();

    this.actions.analytics.sendEvent('home inspection', 'complete');
    this.setState({ modal: false });
  },

  submitQuoteRequest(e) {
    e.preventDefault();
    const buyer = ['firstName', 'lastName', 'email'].reduce((map, name) => {
      map[name] = e.target.elements[name].value.trim();
      return map;
    }, {});

    this.actions.analytics.sendEvent('home inspection', 'submitting');
    this.setState({ submitting: true });

    this.actions.listing.submitHomeInspectionRequest(
      this.props.listing.Id,
      this.state.quote.homeinspectionFees,
      this.state.quote.discountedRate,
      buyer,
      (err) => {
        const submitResult = !err ? 'success' : 'error';
        this.actions.analytics.sendEvent('home inspection', `submit ${submitResult}`);
        this.setState({ modal: submitResult, submitting: false });
      },
    );
  },

  render() {
    if (!this.state.quote) {
      return null;
    }

    const squareFootage = this.utils.formatSquareFeet(this.props.listing, this.state.showOnlyRangeLivingSquare);
    const buyer = this.actions.common.getBuyerData();

    const settingsModal = (
      <div>
        <Modal
          key=""
          show={this.state.modal === true}
          onHide={this.modalClose}
          id="home-inspection-popover"
          className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
        >
          <CloseBtn onClick={this.modalClose} />
          <div className="row popover-content">
            <h2>Home Inspection Cost Estimate</h2>
            <div className="col-sm-1-2 col-xs-1">
              <img alt="Listing" src={`${this.props.listing.DefaultImage}`} />
            </div>
            <div className="col-sm-1-2 col-xs-1 snapshot">
              <p className="mb10"><strong>Home of Interest</strong></p>
              <p>
                <span>{this.props.listing.Address.FullStreetAddress}</span>
                <br />
                <span>
                  {this.props.listing.Address.CityName}
                  ,
                  &nbsp;
                  {this.props.listing.Address.State}
                  &nbsp;
                  {this.props.listing.Address.ZipCode}
                </span>
                <br />
                {this.props.listing.Neighborhood
                  ? [
                    <span key="0">
                      Neighborhood:
                      {this.props.listing.Neighborhood}
                    </span>,
                    <br key="1" />,
                  ]
                  : null}
              </p>
              {this.props.listing.YearBuilt
                ? <p className="mb10">{`Built ${this.props.listing.YearBuilt}`}</p> : null}
              <p className="mb10">
                <span>
                  {this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms) == 1 ? 'bed' : 'beds'}
                &nbsp;|&nbsp;
                  {this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths || '0'}
                  &nbsp;
                  {(this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths) == 1 ? 'bath' : 'baths'}
                  {squareFootage ? (` | ${squareFootage} sqft`) : ''}
                </span>
              </p>
            </div>
            <hr />
            <form onSubmit={this.submitQuoteRequest}>
              <div className="col-1-1 mb20 text-center">
                {
                  this.state.quote && this.state.quote.originalRate && this.state.quote.homeinspectionFees && this.state.quote.discountedRate
                    ? (
                      <p className="mt10 text-125">
                        <strong>
                          Average Inspection Cost: $
                          {this.state.quote.originalRate.toFixed(2).replace('.00', '')}
                        </strong>
                      </p>
                    )
                    : null
                }
                <p>Property inspections provide critical information about the condition of the home prior to purchase, avoiding thousands in unexpected repairs. Find top quality, certified inspectors and get a discount coupon valid for 120 days.</p>
                {
                  this.state.quote && this.state.quote.originalRate && this.state.quote.homeinspectionFees && this.state.quote.discountedRate
                    ? (
                      <p className="mt10 text-125">
                        <strong>
                          Save $
                          {this.state.quote.discountedRate.toFixed(2).replace('.00', '')}
                          , only $
                          {this.state.quote.homeinspectionFees.toFixed(2).replace('.00', '')}
                          &nbsp;
                          with coupon!
                        </strong>
                      </p>
                    )
                    : null
                }
              </div>
              <div className="col-1-2 pr5">
                <input type="text" className="form-control" name="firstName" placeholder="First Name" defaultValue={buyer && buyer.FirstName || ''} required />
              </div>
              <div className="col-1-2 pl5">
                <input type="text" className="form-control" name="lastName" placeholder="Last Name" defaultValue={buyer && buyer.LastName || ''} required />
              </div>
              <div className="col-1-1 mt10">
                <input type="email" className="form-control" name="email" placeholder="Email" defaultValue={buyer && buyer.Email || ''} required />
              </div>
              <div className="col-1-1 mt30">
                <button type="submit" className="btn btn-primary btn-block" disabled={this.state.submitting}>
                  {this.state.submitting ? 'Submitting...' : 'SUBMIT REQUEST'}
                </button>
              </div>
              <div className="col-1-1 mt20 text-center text-muted">
                <small>Home inspection quotes provided by Inspector Match&trade;, a service of Inspection Depot Inc.</small>
                <br />
                <img alt="Inspection logo" className="inspection-footer-logo" src="https://nplayassets.blob.core.windows.net/images/logos/inspector-match.png" />
              </div>
            </form>
          </div>
        </Modal>
        <Modal
          key="success"
          show={this.state.modal === 'success'}
          onHide={this.modalClose}
          id="home-inspection-popover"
          className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
        >
          <CloseBtn onClick={this.modalClose} />
          <div className="row popover-content success">
            <img alt="Success" className="inspection-success-check" src="https://nplayassets.blob.core.windows.net/images/READ%20APP/ICON-SUCCESS_CHECK.png" />
            <h3>Your Request Has Been Sent!</h3>
            <p>You&apos;ll receive an email from Inspection Depot with detailed information and a discount coupon.</p>
            <button type="button" className="btn btn-success btn-block" onClick={this.clearModal}>
              OK, THANKS
            </button>
            <div className="col-1-1 mt20 text-center text-muted">
              <small>Home inspection quotes provided by Inspector Match&trade;, a service of Inspection Depot Inc.</small>
              <br />
              <img alt="Inspection logo" className="inspection-footer-logo" src="https://nplayassets.blob.core.windows.net/images/logos/inspector-match.png" />
            </div>
          </div>
        </Modal>
        <Modal
          key="error"
          show={this.state.modal === 'error'}
          onHide={this.modalClose}
          id="home-inspection-popover"
          className={this.utils.useMobileSite() ? 'mobile-modal' : ''}
        >
          <CloseBtn onClick={this.modalClose} />
          <div className="row popover-content success">
            <img alt="Warning" src="https://nplayassets.blob.core.windows.net/images/READ%20APP/ICON-WARNING.png" />
            <h3>Failed to send the request</h3>
            <p>Please try again later.</p>
            <button type="button" className="btn btn-success btn-block" onClick={this.clearModal}>
              OK, THANKS
            </button>
          </div>
        </Modal>
      </div>
    );

    if (this.props.shortVersion) {
      return (
        <div role="button" tabIndex="-1" aria-label="Home inspection" className="home-inspection-short" onClick={this.onClick}>
          <p><small><a role="button" tabIndex="-1" onClick={this.onClick}>Discount on home inspection</a></small></p>
          {settingsModal}
        </div>
      );
    }
    return (
      <div role="button" tabIndex="-1" aria-label="Home inspection" className="home-inspection-calculator" onClick={this.onClick}>
        <p>Home Inspection</p>
        <p>
          {/* <span className="home-inspection-cost">${this.state.quote.originalRate.toFixed(2).replace('.00', '')} avg.</span> */}
          <span className="home-inspection-cost">
            <small className="text-80">
              $
              <span className="text-underline">Calculate cost</span>
            </small>
          </span>
          <SVGIcon name="icon-inspection" className="icon-inspection" />
        </p>
        <p className="home-inspection-cta"><a>Get discount coupon</a></p>
        {settingsModal}
      </div>
    );
  },
});
