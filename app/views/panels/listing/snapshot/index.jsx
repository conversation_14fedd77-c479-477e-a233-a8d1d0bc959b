const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const MortgageCalculator = require('./mortgage-calculator');
const RatePlugMortgageCalculator = require('./rateplug-mortgage-calculator');
const HomeInspection = require('./home-inspection');
const RateplugDetail = require('./rateplug-detail');

module.exports = React.createClass({

  displayName: 'panels.listing.snapshot',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.pureRender],

  cursors: {
    agentSettings: ['shared', 'agent', 'data', 'AgentSettings'],
  },

  mortgageCalculator(homePrice) {
    let MortgageCalculatorComponent = MortgageCalculator;
    if (// this.utils.getAgentSettingValue(this.state.agentSettings, 'rateplug_use_mortgage_calc') === 'true' &&
      window.rateplug.rp_buyer) {
      MortgageCalculatorComponent = RatePlugMortgageCalculator;
    }

    return (
      <MortgageCalculatorComponent
        listing={this.props.listing}
        price={homePrice}
        shortVersion={!this.props.mlsData.ShowMortgageCalculatorInMLSSection}
        location={this.props.listing.Location}
        image={this.props.listing.DefaultImage}
        propertyTaxRate={this.utils.getPropertyTaxRate(this.props.listing)}
      />
    );
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    const showRateplugDetail = window.rateplug.rp_rate;

    const squareFootage = this.utils.formatSquareFeet(this.props.listing, this.props.mlsData.ShowOnlyRangeLivingSquare);
    let price = this.props.listing.ListPrice ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.ListPrice)}` : 'Price Not Provided';
    let homePrice = this.props.listing.ListPrice || 'Price Not Provided';
    if (this.props.listing.RangePriceFlag == 'Y' && this.props.listing.RangeLowPrice && this.props.listing.RangeHighPrice) {
      price = `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.RangeLowPrice)} - ${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.RangeHighPrice)}`;
      homePrice = (this.props.listing.RangeLowPrice + this.props.listing.RangeHighPrice) / 2;
    }

    const landTenure = this.props.mlsData.DisplayLandTenure && this.props.listing.LandTenure;

    return (
      <div className="listing-panels listing-snapshot">
        <p className="active-label">
          <span className="text-primary">
            {
              this.props.listing.SaleType === 2 ? 'FOR RENT'
                : this.props.listing.SaleType === 1 ? 'FOR SALE'
                  : this.props.listing.isTaxProperty ? 'NOT FOR SALE' : ''
            }
          </span>
          {this.props.listing.Status == 'Active'
            ? (this.props.mlsData.showDaysOnMarket
              ? [<span key="0">&nbsp;|&nbsp;</span>,
                <span key="1">
                  <em>
                    <strong>{this.props.listing.DaysOnMarket}</strong>
&nbsp;Days On Market
                  </em>
                </span>] : null)
            : this.props.listing.isTaxProperty
              ? null
              : [<span key="0">&nbsp;|&nbsp;</span>,
                <span className="listing-status" key="1"><em>{this.props.listing.Status}</em></span>]}
        </p>
        <hr />
        {
          this.props.listing.isTaxProperty !== true
            && this.props.listing.SaleType !== 2
            && this.props.mlsData.ShowMortgageCalculatorInMLSSection
            ? this.mortgageCalculator(homePrice)
            : null
        }
        {
          this.props.listing.isTaxProperty !== true
            && this.props.listing.SaleType !== 2
            && this.props.mlsData.ShowHomeInspectionInMLSSection
            && !showRateplugDetail
            ? <HomeInspection key={this.props.listing.Id} listing={this.props.listing} /> : null
        }
        {
          this.props.listing.isTaxProperty !== true
            && this.props.listing.SaleType !== 2
            && showRateplugDetail
            ? <RateplugDetail key={this.props.listing.Id} listing={this.props.listing} /> : null
        }
        {
          this.props.listing.isTaxProperty ? null
            : <p className="list-price">{price}</p>
        }
        {
          landTenure ? <p><small>{landTenure}</small></p> : null
        }
        {
          /* NPLAY-5157 Hiding mortgage calculator if ShowMortgageCalculatorInMLSSection is false
            this.props.listing.isTaxProperty !== true &&
            this.props.listing.SaleType !== 2 &&
            !this.props.mlsData.ShowMortgageCalculatorInMLSSection ?
              this.mortgageCalculator(homePrice)
              : null
          */
        }
        {(this.props.listing.Status == 'Active') ? (
          <div className="listing-status-active">
            <strong>Status:</strong>
            &nbsp;
            <span className="text-muted"><i>active</i></span>
          </div>
        ) : null}
        <div className="snap-container">
          <span>{this.props.listing.Address.FullStreetAddress}</span>
          <br />
          <span>
            {this.props.listing.Address.CityName}
            ,
            &nbsp;
            {this.props.listing.Address.State}
            &nbsp;
            {this.props.listing.Address.ZipCode}
          </span>
          <br />
          {this.props.listing.Neighborhood
            ? [
              <span key="0">
                Neighborhood:
                {this.props.listing.Neighborhood}
              </span>,
              <br key="1" />,
            ]
            : null}
          <span>
            {this.props.listing.YearBuilt ? (`Built ${this.props.listing.YearBuilt} | `) : ''}
            {this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms || '0'}
            &nbsp;
            {(this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms) == 1 ? 'bed' : 'beds'}
          &nbsp;|&nbsp;
            {this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths || '0'}
            &nbsp;
            {(this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths) == 1 ? 'bath' : 'baths'}
            {squareFootage ? (` | ${squareFootage} sqft`) : ''}
          </span>
          {this.props.listing.MlsIds && this.props.listing.MlsIds.indexOf('memreis-y') > -1
            ? (
              <p className="mn">
                Leased Land:
                {this.props.listing.LeasedLand ? 'Yes' : 'No'}
              </p>
            )
            : null}
          {this.props.listing.PropertyListingId
            ? <p>{`MLS ID# ${this.props.listing.PropertyListingId}`}</p>
            : null}
        </div>
      </div>
    );
  },
});
