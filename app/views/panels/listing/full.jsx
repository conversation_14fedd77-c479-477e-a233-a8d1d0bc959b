const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');

const SVGIcon = require('../../components/svg_icon');

const ListingPhotoSlider = require('./photo_slider');
const ListingSnapshot = require('./snapshot');
const ListingDetails = require('./details/for-full');
// ListingFinancial = require('./financial/for-full'),
const ListingNeighborhood = require('./neighborhood/for-full');
// const ListingSchools = require('./schools/for-full');
const ListingFooter = require('./footer');
const ShareListing = require('../../components/share_listing');
const ListingEdgeTabbar = require('../../components/listing_edge_tabbar');
const CloseButton = require('../../components/close_btn_cross');
const Sub = require('../sub/index');

module.exports = React.createClass({

  displayName: 'panels.listing.full',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors],

  cursors: {
    streetViewAvailable: ['screens', 'map', 'streetViewAvailable'],
    mlsDatas: ['shared', 'mlsData'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },

  getIsLatLon0(props) {
    if (props.listing && props.listing.Location.Lat && props.listing.Location.Lon) {
      return false;
    }
    return true;
  },

  getInitialState() {
    return {
      isLatLon0: this.getIsLatLon0(this.props),
      tabs: this.getIsLatLon0(this.props) ? ['Details'] : ['Details'/* , "Financial" */, 'Neighborhood'/* , "Schools" */],
      activeTab: 'Details',
      barShouldStick: false,
      barPlaceholderHeight: 0,
      showSub: false,
    };
  },

  tempData: {
    currentId: false,
  },

  componentDidMount() {
    if (this.props.listing) {
      console.log(`Viewing: ${this.props.listing.Id}`);
      this.tempData.currentId = this.props.listing.Id;
      this.actions.common.setPropertyViewed(this.props.listing);
    }
  },

  shouldComponentUpdate(nextProps, nextState) {
    const isNewListing = nextProps.listing && this.tempData.currentId !== nextProps.listing.Id;
    this.tempData.currentId = nextProps.listing && nextProps.listing.Id;

    if (this.state.showSub != nextState.showSub) {
      return true;
    }

    if (isNewListing) {
      this.actions.common.setPropertyViewed(nextProps.listing);
      console.log(`Viewing: ${nextProps.listing.Id}`);

      const newState = {};
      newState.isLatLon0 = this.getIsLatLon0(nextProps);
      newState.tabs = this.getIsLatLon0(nextProps) ? ['Details'] : ['Details'/* , "Financial" */, 'Neighborhood'/* , "Schools" */];
      this.setState(newState);
      return true;
    }

    if (nextState.activeTab !== this.state.activeTab
      || nextState.barShouldStick !== this.state.barShouldStick
      || nextState.barPlaceholderHeight !== this.state.barPlaceholderHeight) {
      return true;
    }

    if (nextState.streetViewAvailable != this.state.streetViewAvailable) {
      return true;
    }

    return false;
  },

  handleContact() {
    this.actions.agent.setActiveTab('Call');
    if (this.props.listing && !this.props.listing.isTaxProperty) {
      this.actions.agent.showAgentContact(this.props.listing);
    }

    this.actions.analytics.sendEvent('detail view', 'agent', this.props.listing.ZipCode);
  },

  tabItemClick(e) {
    e.preventDefault();
    e.stopPropagation();
    const scrollContainer = this.refs.scrollContainer;
    const refName = e.target.innerHTML;

    this.actions.analytics.sendEvent('detail view nav', refName, this.props.listing.ZipCode);

    const element = this.refs[refName];

    this.actions.common.scrollElementTo(scrollContainer,
      element.offsetTop
      - this.headerStickPixels,
      500);
  },

  goToVirtualTour(e) {
    e.preventDefault();
    if (this.props.listing && this.props.listing.VirtualTourLink) {
      let link = this.props.listing.VirtualTourLink;
      if (link.toLowerCase().indexOf('http') !== 0) {
        link = `http://${link}`;
      }
      window.open(link, '_blank');
      this.actions.analytics.sendEvent('detail view', 'virtual tour', this.props.data.ZipCode);
    }
  },

  print(e) {
    e.preventDefault();

    if (this.props.listing && this.props.listing.Id) {
      this.actions.common.printListingById(this.props.listing.Id);
    }
  },

  onClose() {
    if (this.props.onClose) {
      this.props.onClose();
    }
  },

  showAgentContact(tab) {
    this.actions.agent.setActiveTab(tab);
    this.actions.agent.showAgentContact(null);
  },

  tabs(listing, mlsData = {}) {
    return (
      <nav className={classNames('nav-scrollable listing-nav')}>
        {
        this.state.tabs.map(function (item) {
          return (
            <a
              role="button"
              tabIndex="-1"
              className={classNames('item', 'pull-left', { active: this.state.activeTab === item })}
              key={item}
              onClick={this.tabItemClick}
            >
              {item}
            </a>
          );
        }, this)
        }

        {
        this.props.onClose
          ? <CloseButton className="full-listing-close" onClick={this.onClose} /> : null
        }

        <div
          role="button"
          tabIndex="-1"
          aria-label="Call"
          className="btn contact-button"
          onClick={this.showAgentContact.bind(this, 'Call')}
        >
          <span> ASK A QUESTION </span>
          <SVGIcon
            className="contact-icon"
            name="icon-contact-agent"
          />
        </div>

        <ShareListing className="pull-right top-icons" title="Share this home" listing={listing} />

        <a
          role="button"
          tabIndex="-1"
          className="pull-right top-icons"
          title="Print"
          onClick={this.print}
        >
          <SVGIcon name="icon-printer" className="" />
        </a>
        { listing.VirtualTourLink
          ? (
            <a
              role="button"
              tabIndex="-1"
              className="pull-right top-icons"
              title="Virtual tour"
              onClick={this.goToVirtualTour}
            >
              <SVGIcon name="icon-virtual-tour" className="" />
            </a>
          )
          : null}
        { mlsData.ShowBrokerOnSlider
          ? (
            <a
              role="button"
              tabIndex="-1"
              className="pull-right top-icons"
              title="Ask a question"
              onClick={this.handleContact}
            >
              <SVGIcon name="icon-chat-bubbles" />
            </a>
          )
          : null }
      </nav>
    );
  },

  tabContents(listing, mlsData = {}) {
    if (this.state.isLatLon0) {
      return (
        <div className="listing-panels-container for-full row">
          <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
          {/* <hr ref="Financial" />
          {this.scrollToTopIcon()}
          <ListingFinancial listing={listing} mlsData={mlsData} /> */}
          <hr />
          {this.scrollToTopIcon()}
          <ListingFooter listing={listing} mlsData={mlsData} />
        </div>
      );
    }

    return (
      <div className="listing-panels-container for-full row">
        <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
        {/* <hr ref="Financial" />
        {this.scrollToTopIcon()}
        <ListingFinancial listing={listing} mlsData={mlsData} /> */}
        <hr ref="Neighborhood" />
        {this.scrollToTopIcon()}
        <ListingNeighborhood listing={listing} mlsData={mlsData} />
        {/* <div ref="Schools"></div>
        {this.scrollToTopIcon()}
        <ListingSchools listing={listing} mlsData={mlsData} /> */}
        <hr />
        {this.scrollToTopIcon()}
        <ListingFooter listing={listing} mlsData={mlsData} />
      </div>
    );
  },

  scrollToTopClick() {
    const scrollContainer = this.refs.scrollContainer;

    this.actions.common.scrollElementTo(scrollContainer, 0, 500);
  },
  scrollToTopIcon() {
    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Scroll to top"
        className="icon-back-to-top-container"
        onClick={this.scrollToTopClick}
      >
        <SVGIcon name="icon-back-to-top" className="icon-back-to-top" />
      </div>
    );
  },

  onScroll(e) {
    this.updateActiveTabAtScrollTop(e.target.scrollTop);
  },

  headerStickPixels: 72,

  updateActiveTabAtScrollTop(scrollTop) {
    if (scrollTop > this.refs.listingHeaderContainer.offsetHeight + 10 - this.headerStickPixels) {
      if (!this.state.barShouldStick) {
        this.setState({
          barShouldStick: true,
          barPlaceholderHeight: this.refs.listingHeaderContainer.offsetHeight,
        });
      }
    } else {
      if (this.state.barShouldStick) {
        this.setState({ barShouldStick: false, barPlaceholderHeight: 0 });
      }
    }

    const tabs = this.state.tabs;
    for (let i = tabs.length - 1; i > -1; i--) {
      const tabName = tabs[i];
      const tabElem = this.refs[tabName] && this.refs[tabName];
      if (tabElem && scrollTop
        >= tabElem.offsetTop
        - this.headerStickPixels
      ) {
        if (this.state.activeTab !== tabName) {
          this.setState({ activeTab: tabName });
        }
        return;
      }
    }
  },

  onStreet(e) {
    e && e.preventDefault();
    if (this.state.streetViewAvailable) {
      this.setState({ showSub: 'street' });
    }

    this.actions.analytics.sendEvent('detail view', 'map street click', this.state.streetViewAvailable ? 'available' : 'not available');
  },

  onAerial(e) {
    e && e.preventDefault();
    this.setState({ showSub: 'aerial' });

    this.actions.analytics.sendEvent('detail view', 'map aerial click');
  },

  onCloseSub() {
    this.setState({ showSub: false });
  },

  onParcel() {
    this.setState({ showSub: 'parcel' });

    this.actions.analytics.sendEvent('detail view', 'map parcel click');
  },

  onMap() {
    this.setState({ showSub: 'map' });
  },

  componentDidUpdate() {
    const mlsId = this.actions.common.getListingMlsId(this.props.listing);
    const mlsData = this.state.mlsDatas[mlsId];
    if (mlsId && !mlsData) {
      setTimeout(() => this.actions.listing.getMlsData(mlsId), 500);
    }
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    const mlsId = this.actions.common.getListingMlsId(this.props.listing);
    const mlsData = this.state.mlsDatas[mlsId] || {};

    return (
      <div className="listing-full" ref="Details" key={this.props.listing && this.props.listing.Id || 'Listing'}>
        { this.state.showSub
          ? (
            <Sub
              data={this.props.listing}
              streetViewAvailable={this.state.streetViewAvailable}
              onStreet={this.onStreet}
              onAerial={this.onAerial}
              onParcel={this.onParcel}
              onMap={this.onMap}
              showSub={this.state.showSub}
              onCloseSub={this.onCloseSub}
            />
          )
          : null}

        <div className="scroll-container" ref="scrollContainer" onScroll={this.onScroll}>

          <ListingEdgeTabbar
            className="on-listing-full"
            onClose={this.props.onClose}
            onStreet={this.onStreet}
            onAerial={this.onAerial}
            onParcel={this.onParcel}
            onMap={this.onMap}
            data={this.props.listing}
            streetViewAvailable={this.state.streetViewAvailable}
          />

          <div className="listing-full-content-container">

            <div
              ref="listingHeaderContainer"
              className={classNames('listing-header-container', { 'visibility-hidden': this.state.barShouldStick })}
            >

              <div className="row">
                <div className="col-2-3">
                  <ListingPhotoSlider listing={this.props.listing} mlsData={mlsData} onPhotoClick={this.props.onPhotoClick} />
                </div>

                <div className="col-1-3">
                  <ListingSnapshot listing={this.props.listing} mlsData={mlsData} />
                </div>
              </div>

              {this.tabs(this.props.listing, mlsData)}

            </div>

            {this.tabContents(this.props.listing, mlsData)}
          </div>
        </div>

        {
        this.state.barShouldStick
          ? (
            <div className="overlay-container">
              <div className="listing-full-content-container stick">
                <div
                  className="listing-header-container"
                  style={{
                    marginTop: -1 * (this.state.barPlaceholderHeight - this.headerStickPixels),
                  }}
                >

                  <div
                    role="button"
                    tabIndex="-1"
                    aria-label="Scroll to top"
                    className="row cursor-pointer"
                    onClick={this.scrollToTopClick}
                  >
                    <div className="col-2-3">
                      <ListingPhotoSlider listing={this.props.listing} mlsData={mlsData} onPhotoClick={this.props.onPhotoClick} />
                    </div>

                    <div className="col-1-3">
                      <ListingSnapshot listing={this.props.listing} mlsData={mlsData} />
                    </div>
                  </div>

                  {this.tabs(this.props.listing, mlsData)}

                </div>
              </div>
            </div>
          ) : null
        }
      </div>
    );
  },
});
