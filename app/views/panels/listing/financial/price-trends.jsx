const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.financial.price-trends',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel price-trends">

        <h3>PRICE TRENDS</h3>
        <table className="table table-striped">
          <thead />
          <tbody>
            <tr>
              <td>Compare to last quarter</td>
              <td>10%</td>
            </tr>
            <tr>
              <td>Compare to last quarter</td>
              <td>10%</td>
            </tr>
            <tr>
              <td>Compare to last quarter</td>
              <td>10%</td>
            </tr>
            <tr>
              <td>Compare to last quarter</td>
              <td>10%</td>
            </tr>
          </tbody>
        </table>

      </div>
    );
  },
});
