const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.financial.estimated-home-value',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel estimated-home-value">

        <h3>ESTINATED HOME VALUE</h3>

        <img alt="Home value" src="//placehold.it/200x100" width="100%" />

      </div>
    );
  },
});
