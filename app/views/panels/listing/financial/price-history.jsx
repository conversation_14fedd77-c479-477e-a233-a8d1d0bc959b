const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.financial.price-history',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel price-history">

        <h3>PRICE HISTORY</h3>
        <table className="table table-striped">
          <thead>
            <tr>
              <th>DATE</th>
              <th>EVENT</th>
              <th>PRICE</th>
              <th>CHANGE</th>
              <th>SOURCE</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>05/01/2015</td>
              <td>For Sale</td>
              <td>
                $
                {this.utils.addThousandSep(this.props.listing.ListPrice)}
              </td>
              <td>+24.56%</td>
              <td>MLS</td>
            </tr>
            <tr>
              <td>05/01/2015</td>
              <td>For Sale</td>
              <td>
                $
                {this.utils.addThousandSep(this.props.listing.ListPrice)}
              </td>
              <td>+24.56%</td>
              <td>MLS</td>
            </tr>
            <tr>
              <td>05/01/2015</td>
              <td>For Sale</td>
              <td>
                $
                {this.utils.addThousandSep(this.props.listing.ListPrice)}
              </td>
              <td>+24.56%</td>
              <td>MLS</td>
            </tr>
            <tr>
              <td>05/01/2015</td>
              <td>For Sale</td>
              <td>
                $
                {this.utils.addThousandSep(this.props.listing.ListPrice)}
              </td>
              <td>+24.56%</td>
              <td>MLS</td>
            </tr>
            <tr>
              <td>05/01/2015</td>
              <td>For Sale</td>
              <td>
                $
                {this.utils.addThousandSep(this.props.listing.ListPrice)}
              </td>
              <td>+24.56%</td>
              <td>MLS</td>
            </tr>
          </tbody>
        </table>

      </div>
    );
  },
});
