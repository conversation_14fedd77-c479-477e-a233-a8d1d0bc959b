const React = require('react');
const mixins = require('../../../../lib/mixins/index');

const ListingHistory = require('./listing-history');
const PriceTrends = require('./price-trends');
const EstimatedHomeValue = require('./estimated-home-value');
const PriceHistory = require('./price-history');

module.exports = React.createClass({

  displayName: 'panels.listing.financial.for-full',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  setAgentContact() {
    this.actions.agent.setActiveTab('CMA');
    this.actions.agent.showAgentContact(this.props.listing);
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-panels listing-financial">

        <h2>FINANCIAL</h2>

        <div className="col-1-2">
          <ListingHistory listing={this.props.listing} />
          <PriceTrends listing={this.props.listing} />
        </div>

        <div className="col-1-2">
          <EstimatedHomeValue listing={this.props.listing} />
          <PriceHistory listing={this.props.listing} />
          <div className="col-1-1">
            <div className="col-1-2">
              <a className="btn btn-block btn-outline">Agent Picture + Name</a>
            </div>
            <div className="col-1-2">
              <a role="button" tabIndex="-1" className="btn btn-block btn-outline" onClick={this.setAgentContact}>Get a Market Analysis on This Home</a>
            </div>
          </div>
        </div>

      </div>
    );
  },
});
