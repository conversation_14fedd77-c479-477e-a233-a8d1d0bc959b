const React = require('react');
const classnames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const GoogleParcel = require('../../map/google-parcel');

module.exports = React.createClass({

  displayName: 'panels.listing.details.parcel',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  getIsLatLon0() {
    if (this.props.listing && this.props.listing.Location.Lat && this.props.listing.Location.Lon) {
      return false;
    }
    return true;
  },

  getInitialState() {
    return {
      imgSize: [400, 200],
      containerWidth: 400,
      mode: 'satellite', // satellite or map
    };
  },

  componentDidMount() {
    window.addEventListener('resize', this.refresh);
    this.actions.analytics.sendEvent('detail view', 'load parcel');
  },

  componentWillUpdate(nextProps) {
    if (this.shouldUpdate || nextProps.listing !== this.props.listing) {
      this.shouldUpdate = false;
    }
  },

  componentWillUnmount() {
    window.removeEventListener('resize', this.refresh);
  },

  shouldUpdate: false,
  refresh() {
    this.shouldUpdate = true;
    this.forceUpdate();
  },

  render() {
    if (!this.props.listing || this.getIsLatLon0()) {
      return null;
    }

    if (!this.props.listing.DisplayAddress) {
      return null;
    }

    return (
      <div ref="Parcel" className="listing-sub-panel parcel">

        <ul className="parcel-header">
          <li
            className={classnames({ active: this.state.mode === 'satellite' })}
            onClick={() => {
              this.refs['google-parcel'].googleParcel.m.setMapTypeId(google.maps.MapTypeId.HYBRID);
              this.setState({ mode: 'satellite' });
            }}
          >
            SATELLITE
          </li>
          <li
            className={classnames({ active: this.state.mode === 'map' })}
            onClick={() => {
              this.refs['google-parcel'].googleParcel.m.setMapTypeId(google.maps.MapTypeId.ROADMAP);
              this.setState({ mode: 'map' });
            }}
          >
            MAP
          </li>
          <li onClick={this.props.onStreet} title={this.props.streetViewAvailable ? 'Show this home in street view' : 'Street view not available'} className={this.props.streetViewAvailable ? '' : 'disabled'}>STREET VIEW</li>
          <li onClick={this.props.onAerial} title="Show this home in aerial view">AERIAL VIEW</li>
        </ul>

        <div className="parcelContainer">
          <GoogleParcel ref="google-parcel" listing={this.props.listing} />
        </div>

      </div>
    );
  },
});
