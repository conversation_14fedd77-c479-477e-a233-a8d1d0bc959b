const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const LazyLoadWithinContainer = require('../../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');

const Description = require('./description');
const PropertyHighlights = require('./property-highlights');
// HomeDetails = require('./home-details'),
const Parcel = require('./parcel');

module.exports = React.createClass({

  displayName: 'panels.listing.details.for-full',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-panels listing-detail mt0">

        <PropertyHighlights listing={this.props.listing} mlsData={this.props.mlsData} />

        <Description listing={this.props.listing} mlsData={this.props.mlsData} />

        {/* <HomeDetails listing={this.props.listing} /> */}

        <LazyLoadWithinContainer height="355px" buffer={200}>
          <Parcel listing={this.props.listing} onStreet={this.props.onStreet} onAerial={this.props.onAerial} streetViewAvailable={this.props.streetViewAvailable} />
        </LazyLoadWithinContainer>

      </div>
    );
  },
});
