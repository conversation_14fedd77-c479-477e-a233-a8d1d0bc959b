const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const HomeDetailsItem = require('./home-details-item');

module.exports = React.createClass({

  displayName: 'panels.listing.details.home-details',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel home-details">
        <h3>HOME DETAILS</h3>

        <HomeDetailsItem listing={this.props.listing} />
        <HomeDetailsItem listing={this.props.listing} />
        <HomeDetailsItem listing={this.props.listing} />
      </div>
    );
  },
});
