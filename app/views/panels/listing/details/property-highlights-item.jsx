const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.listing.details.property-highlights-item',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  render() {
    if (!this.props.content) {
      return null;
    }

    return (
      <div className="property-highlights-item-container">

        <div className="property-highlights-item">
          <p className="title">{this.props.title || ''}</p>
          <p className="content">
            {this.props.content || ''}
            <small>{this.props.unit ? ` ${this.props.unit}` : ''}</small>
          </p>
          <SVGIcon name={this.props.icon || ''} className={this.props.icon || ''} />
        </div>

      </div>
    );
  },
});
