const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const MLSDisclosure = require('../../mls-disclosure');
const AgentProfileImage = require('../../../components/agent_profile_image');
const VerifiedBadge = require('../../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.listing.details.description',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  setAgentContact(activeTab) {
    console.log(`${activeTab}is the tab`);
    this.actions.agent.setActiveTab(activeTab);
    console.log(`the property is ${this.props.listing}`);
    this.actions.agent.showAgentContact(this.props.listing);

    this.actions.analytics.sendEvent('detail view', 'see this home', this.state.agentData.ZipCode);
  },

  agentToListedBy(agent, isCoAgent) {
    if (!this.props.mlsData) {
      return null;
    }

    const listedBy = (this.props.mlsData && this.props.mlsData.LabelForIdxListingBroker)
      ? this.props.mlsData.LabelForIdxListingBroker
      : 'Listing courtesy of';

    const {
      Id: mlsAgentId,
      Name: agentName,
      OfficeName: agentOfficeName,
      OfficePhone: agentOfficePhone,
      OfficeAttributionContact: agentOfficeAttributionContact,
      License: agentLicense,
      Phone: agentPhone,
    } = agent;

    const agentLabel = (agentName || mlsAgentId)
      + ((this.props.mlsData.AgentContactInfoOnOff && agentPhone) ? (` ${this.utils.formatPhone(agentPhone)}`) : '')
      + ((this.props.mlsData.RealEstateLicenseNumberOnOff && agentLicense) ? (` #${agentLicense}`) : '');

    const officeLabel = ''.concat(agentOfficeName || '')
      .concat(
        (this.props.mlsData.DetailContactInfoOnOff && (agentOfficeAttributionContact || agentOfficePhone))
          ? (` ${agentOfficeAttributionContact || this.utils.formatPhone(agentOfficePhone)}`) : '',
      );

    if (this.props.mlsData.DetailListingAgentOnOff === false && officeLabel === '') {
      return <noscript />;
    }

    return (
      <span className="agent-line">
        <strong>{isCoAgent ? 'and' : listedBy.toUpperCase()}</strong>
        &nbsp;
        <span className="agent-office-name">{((this.props.mlsData.DetailListingAgentOnOff) ? (`${agentLabel}, `) : '') + (officeLabel || '')}</span>
      </span>
    );
  },

  listedByPartial() {
    if (this.props.listing.isTaxProperty || !this.props.mlsData) {
      return null;
    }

    let coAgentEntry;
    let originatingMLSEntry;
    const agentEntry = this.agentToListedBy(this.props.listing.ListingAgent);
    // CoAgent name will be an empty string or null if there is no CoAgent
    if (this.props.listing.CoAgent.Name) {
      if (this.props.mlsData.DetailListingAgentOnOff === false
        && this.props.listing.ListingAgent
        && this.props.listing.CoAgent
        && this.props.listing.ListingAgent.OfficeName === this.props.listing.CoAgent.OfficeName
      ) {
        // Do not show the same office name twice if agent name is hidden
      } else {
        coAgentEntry = this.agentToListedBy(this.props.listing.CoAgent, true);
      }
    }

    if (this.props.listing.OriginatingMls && this.props.mlsData && this.props.mlsData.ShowOriginatingMls) {
      originatingMLSEntry = (
        <div style={{ margin: '0px 0px 15px 0px' }}>
          <strong>{'Originating MLS: '}</strong>
          <span>{this.props.listing.OriginatingMls}</span>
          <br />
          <strong>{'Source MLS: '}</strong>
          <span>{this.props.mlsData && this.props.mlsData.Name}</span>
        </div>
      );
    }

    return (
      <div className="mt20 listing-agent-section">
        <div>
          {agentEntry}
          {coAgentEntry ? <span className="sep">{'  |  '}</span> : null}
          {coAgentEntry}
          {originatingMLSEntry}
        </div>
        {
          this.props.mlsData.ShowDisclosureUnderListingDetail
            ? <MLSDisclosure mlsId={this.props.mlsData.Id} key={this.props.mlsData.Id} />
            : <img alt="Mls" src={this.props.mlsData.LogoUrl || this.utils.emptyImageUrl} />
        }
      </div>
    );
  },

  render() {
    if (!this.props.listing || !this.props.mlsData || this.props.listing.isTaxProperty) {
      return null;
    }

    return (
      <div className="listing-sub-panel description mt0">
        <h2>DESCRIPTION</h2>

        <div className={classNames('col-1-1 description-container',
          {
            'col-lg-2-3': !this.props.listing.isTaxProperty && !this.props.mlsData.ShowDisclosureUnderListingDetail && this.props.mlsData.ShowAgentInListingDetail,
          })}
        >
          <p>{this.props.listing.Description}</p>

          <div className="col-1-1">
            {this.listedByPartial()}
          </div>
        </div>

        {
          this.props.listing.isTaxProperty || !this.props.mlsData.ShowAgentInListingDetail ? null
            : (
              <div className={classNames('col-1-1 property-cta-container pl30 pr30 text-center', { 'col-lg-1-3': !this.props.mlsData.ShowDisclosureUnderListingDetail })}>

                <h6>Want to learn more about this home?</h6>
                {
                  this.state.agentData
                    ? (
                      <div className="agent-container">
                        <AgentProfileImage className="agent-image" />
                        <p className="agent-name">
                          {this.state.agentData.FirstName}
&nbsp;
                          {this.state.agentData.LastName}
                          &nbsp;
                          <VerifiedBadge />
                        </p>
                      </div>
                    )
                    : null
                }
                <a
                  role="button"
                  tabIndex="-1"
                  className="btn btn-primary"
                  onClick={this.setAgentContact.bind(this, 'Showing')}
                >
                  See This Home
                </a>
              </div>
            )
        }
      </div>
    );
  },
});
