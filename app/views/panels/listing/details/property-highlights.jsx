const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const PropertyHighlightsItem = require('./property-highlights-item');
const AgentProfileImage = require('../../../components/agent_profile_image');
const VerifiedBadge = require('../../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.listing.details.property-highlights',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender, mixins.cursors],

  setAgentContact(activeTab) {
    console.log(`${activeTab}is the tab`);
    this.actions.agent.setActiveTab(activeTab);
    console.log(`the property is ${this.props.listing}`);
    this.actions.agent.showAgentContact(!this.props.listing.isTaxProperty && this.props.listing);

    this.actions.analytics.sendEvent('detail view', 'see this home', this.state.agentData.ZipCode);
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  booleanHelper(boolVal) {
    return typeof boolVal === 'boolean'
      ? boolVal ? 'Yes' : 'No'
      : null;
  },

  agentToListedBy(agent, isCoAgent) {
    if (!this.props.mlsData) {
      return null;
    }

    const listedBy = (this.props.mlsData && this.props.mlsData.LabelForIdxListingBroker)
      ? this.props.mlsData.LabelForIdxListingBroker
      : 'Listing courtesy of';

    const {
      Name: agentName,
      OfficeName: agentOfficeName,
      OfficePhone: agentOfficePhone,
      OfficeAttributionContact: agentOfficeAttributionContact,
      License: agentLicense,
      Phone: agentPhone,
    } = agent;

    const agentLabel = agentName
      + ((this.props.mlsData.AgentContactInfoOnOff && agentPhone) ? (` ${this.utils.formatPhone(agentPhone)}`) : '')
      + ((this.props.mlsData.RealEstateLicenseNumberOnOff && agentLicense) ? (` #${agentLicense}`) : '');

    const officeLabel = ''.concat(agentOfficeName || '')
      .concat(
        (this.props.mlsData.DetailContactInfoOnOff && (agentOfficeAttributionContact || agentOfficePhone))
          ? (` ${agentOfficeAttributionContact || this.utils.formatPhone(agentOfficePhone)}`) : '',
      );

    if (this.props.mlsData.DetailListingAgentOnOff === false && officeLabel === '') {
      return <noscript />;
    }

    return (
      <p>
        <strong>{isCoAgent ? 'and' : listedBy.toUpperCase()}</strong>
        &nbsp;
        {((this.props.mlsData.DetailListingAgentOnOff) ? (`${agentLabel}, `) : '') + officeLabel}
      </p>
    );
  },

  listedByPartial() {
    if (this.props.listing.isTaxProperty || !this.props.mlsData) {
      return null;
    }

    let coAgentEntry;
    const agentEntry = this.agentToListedBy(this.props.listing.ListingAgent);
    // CoAgent name will be an empty string or null if there is no CoAgent
    if (this.props.listing.CoAgent.Name) {
      if (this.props.mlsData.DetailListingAgentOnOff === false
        && this.props.listing.ListingAgent
        && this.props.listing.CoAgent
        && this.props.listing.ListingAgent.OfficeName === this.props.listing.CoAgent.OfficeName
      ) {
        // Do not show the same office name twice if agent name is hidden
      } else {
        coAgentEntry = this.agentToListedBy(this.props.listing.CoAgent, true);
      }
    }

    return (
      <div className="mt20">
        {agentEntry}
        {coAgentEntry}
        <img alt="Mls" src={this.props.mlsData.LogoUrl || this.utils.emptyImageUrl} />
      </div>
    );
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel property-highlights">
        <h2>HIGHLIGHTS</h2>
        <div className={classNames('col-1-1 highlights-container', {
          'col-lg-2-3': this.props.listing.isTaxProperty,
        })}
        >
          <PropertyHighlightsItem
            title="Bedrooms"
            content={this.props.listing.TotalBedrooms || this.props.listing.Rooms.TotalBedrooms}
            icon="icon-bedroom"
          />
          <PropertyHighlightsItem
            title="Full Baths"
            content={this.props.listing.FullBaths || this.props.listing.Rooms.FullBaths}
            icon="icon-bathroom"
          />
          <PropertyHighlightsItem
            title="Total Baths"
            content={this.props.listing.TotalBaths || this.props.listing.Rooms.TotalBaths}
            icon="icon-sink"
          />
          <PropertyHighlightsItem
            title="Square Feet"
            content={this.utils.formatSquareFeet(
              this.props.listing,
              this.props.mlsData && this.props.mlsData.ShowOnlyRangeLivingSquare,
            )}
            icon="icon-ruler"
          />
          <PropertyHighlightsItem
            title="Stories / Levels"
            content={this.props.listing.Interior.Levels}
            icon="icon-stories-levels"
          />
          <PropertyHighlightsItem
            title="Lot Size"
            content={this.props.listing.Exterior.LotSizeAcres}
            unit="acre"
            icon="icon-lot-size"
          />
          <PropertyHighlightsItem
            title="Car Parking"
            content={this.props.listing.Exterior.GarageSpaces}
            unit="car(s)"
            icon="icon-car"
          />
          {/*  <PropertyHighlightsItem title="Gated Community"
       content="???" icon="icon-fence" /> */}
          <PropertyHighlightsItem
            title="Cooling"
            content={this.booleanHelper(this.props.listing.Interior.HasCooling)}
            icon="icon-ac"
          />
          {/* <PropertyHighlightsItem title="Heating"
       content="???" icon="icon-heating" /> */}
          <PropertyHighlightsItem
            title="Year Built"
            content={this.props.listing.YearBuilt}
            icon="icon-hammer"
          />
          <PropertyHighlightsItem
            title="Property Tax"
            content={this.utils.addThousandSep(Math.round(this.props.listing.Financial.TaxAmount))}
            unit=""
            icon="icon-property-taxes"
          />
          <PropertyHighlightsItem
            title="HOA Fees"
            content={this.utils.addThousandSep(Math.round(this.props.listing.Financial.AssociationDues))}
            unit={this.utils.associationPeriodToText(this.props.listing.Financial.AssociationPeriod)}
            icon="icon-hoa-fees"
          />
          {this.props.mlsData && this.props.mlsData.ShowDaysOnMarket
            ? (
              <PropertyHighlightsItem
                title={
                  (this.props.listing.MlsIds || []).includes('wanwmls')
                    ? 'Cumulative Days On Market'
                    : 'Days On Market'
                }
                content={this.props.listing.DaysOnMarket}
                icon="icon-calendar"
              />
            )
            : null}
          {this.props.listing.Exterior.HasPool ? (
            <PropertyHighlightsItem
              title="Pool"
              content={this.booleanHelper(this.props.listing.Exterior.HasPool)}
              icon="icon-pool"
            />
          ) : null}
          {this.props.listing.Interior.HasFireplace ? (
            <PropertyHighlightsItem
              title="Fireplace"
              content={this.booleanHelper(this.props.listing.Interior.HasFireplace)}
              icon="icon-fireplace-in"
            />
          ) : null}
          {/* <PropertyHighlightsItem title="Last Sold"
       content="???" icon="icon-price" />
       <PropertyHighlightsItem title="Last Sold Price"
       content="???" icon="icon-price-tag" /> */}

          {
            this.props.listing.isTaxProperty
              ? (
                <div className="col-1-1">
                  {this.listedByPartial()}
                </div>
              ) : null
          }
        </div>

        {
          this.props.listing.isTaxProperty
            ? (
              <div className={classNames('col-lg-1-3 col-1-1 property-cta-container text-center')}>
                <h6>
                  {
                    !this.props.listing.isTaxProperty
                      ? 'Want to learn more about this home?'
                      : 'Want to learn more about homes like this?'
                  }
                </h6>
                {
                  this.state.agentData
                    ? (
                      <div className="agent-container">
                        <AgentProfileImage className="agent-image" />
                        <p className="agent-name">
                          {this.state.agentData.FirstName}
&nbsp;
                          {this.state.agentData.LastName}
                          &nbsp;
                          <VerifiedBadge />
                        </p>
                      </div>
                    )
                    : null
                }
                {
                  !this.props.listing.isTaxProperty
                    ? (
                      <a
                        role="button"
                        tabIndex="-1"
                        className="btn btn-outline"
                        onClick={this.setAgentContact.bind(this, 'Showing')}
                      >
                        See This Home
                      </a>
                    )
                    : (
                      <a
                        role="button"
                        tabIndex="-1"
                        className="btn btn-outline"
                        onClick={this.setAgentContact.bind(this, 'Call')}
                      >
                        Contact Me
                      </a>
                    )
                }

              </div>
            ) : null
        }
      </div>
    );
  },
});
