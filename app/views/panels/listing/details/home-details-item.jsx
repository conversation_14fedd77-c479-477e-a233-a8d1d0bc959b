const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.details.home-details-item',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="home-details-item">
        <h4>Facts</h4>
        <ul>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
          <li>Line item</li>
        </ul>
      </div>
    );
  },
});
