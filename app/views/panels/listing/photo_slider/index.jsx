/* eslint-disable jsx-a11y/label-has-associated-control */
const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const classNames = require('classnames');
const _ = require('lodash');
const Slider = require('react-slick');
const mixins = require('../../../../lib/mixins');
const AgentProfileImage = require('../../../components/agent_profile_image');
const SVGIcon = require('../../../components/svg_icon');
const SubmitBtn = require('../../../components/submit_btn');
const VerifiedBadge = require('../../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels.listing.photo_slider',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.events],

  getInitialState() {
    return {
      tagPanelHidden: true,
      showNotAvailable: false,
    };
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    photoSliderIndex: ['shared', 'photoSliderIndex'],
    cardTags: ['shared', 'tagging', { location: 'props', path: ['listing', 'Id'] }],
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (this.state.photoSliderIndex !== nextState.photoSliderIndex) {
      const sliderIndex = _.get(this.refs.slider, 'innerSlider.state.currentSlide');
      if (nextState.photoSliderIndex !== sliderIndex) {
        this.refs.slider.slickGoTo(nextState.photoSliderIndex, true);
      }
      return false;
    }
    return true;
  },

  componentDidMount() {
    this.checkStreetViewImage(this.props.listing);
  },

  componentWillUpdate(nextProps) {
    if (this.props.listing !== nextProps.listing) {
      if (this.state.showNotAvailable) {
        this.setState({ showNotAvailable: false });
      }
      this.checkStreetViewImage(nextProps.listing);
    }
  },

  checkStreetViewImage(listingData) {
    if (listingData.Images && listingData.Images.length === 1
      && (listingData.Images[0].lg || '').indexOf('maps.googleapis.com/maps/api/streetview') !== -1) {
      // this.actions.map.getStreetViewAvailability(listingData).then(function(res) {
      //  if (res === false) {
      this.setState({ showNotAvailable: true });
      //  }
      // }.bind(this))
    }
  },

  next() {
    this.refs.slider.slickNext();
    this.events.emit(this.events.GALLERY_PHOTO_VIEWED);
  },
  prev() {
    this.refs.slider.slickPrev();
  },

  toggleTagPanel(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.analytics.sendEvent('detail page', 'tag icon', this.props.listing.ZipCode);
    if (this.hasNothing() || this.hasDislike()) {
      // Add favorite by default
      this.actions.tagging.setTagsForListing(this.props.listing, { favorite: true });
    } else {
      this.actions.tagging.setTagsForListing(this.props.listing, {});
    }
  },

  onTag(tagName) {
    const currCardTags = this.state.cardTags || {};
    // let added = false;
    if (tagName in currCardTags) {
      delete currCardTags[tagName];
    } else {
      currCardTags[tagName] = true;
      // added = true;
    }
    if (tagName === 'favorite' && 'dislike' in currCardTags) {
      delete currCardTags.dislike;
    }
    if (tagName === 'dislike' && 'favorite' in currCardTags) {
      delete currCardTags.favorite;
    }

    this.actions.tagging.setTagsForListing(this.props.listing, currCardTags);

    this.actions.analytics.sendEvent('property cards', 'click tag', this.props.listing.ZipCode);
  },

  onAddTag(e) {
    e.preventDefault();
    let tagName = e.target.elements['new-tag'].value;
    if (tagName) {
      // NPLAY-1880 Whitelist allowed characters
      tagName = tagName.replace(/[^a-zA-Z0-9-_]/g, '').trim();
      this.actions.tagging.addTag(tagName);
      const currCardTags = this.state.cardTags;
      if (!(tagName in currCardTags)) {
        currCardTags[tagName] = true;
        if (tagName === 'favorite' && 'dislike' in currCardTags) {
          delete currCardTags.dislike;
        }
        if (tagName === 'dislike' && 'favorite' in currCardTags) {
          delete currCardTags.favorite;
        }
        this.state.allTags = this.actions.tagging.getTags();
        this.actions.tagging.setTagsForListing(this.props.listing, currCardTags);
        this.actions.analytics.sendEvent('property cards', 'add new tag', this.props.listing.ZipCode);
      }
    }
    e.target.elements['new-tag'].value = '';
  },

  hasNothing() {
    return Object.keys(this.state.cardTags || {}).length === 0 || false;
  },
  hasSomething() {
    return Object.keys(this.state.cardTags || {}).length > 0 || false;
  },
  hasFavorite() {
    return ('favorite' in (this.state.cardTags || {})) || false;
  },
  hasDislike() {
    return ('dislike' in (this.state.cardTags || {})) || false;
  },

  tagButton() {
    if (!this.state.tagPanelHidden) {
      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="Tag home"
          className={classNames('tag-button-photo')}
          title="Tag this home"
          onClick={this.toggleTagPanel}
        >
          <SVGIcon name="icon-close-button" title="" className="tag-close-button" />
        </div>
      );
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Subscribe"
        className={classNames('tag-button-photo')}
        title="Subscribe"
        onClick={this.toggleTagPanel}
      >
        {
          this.hasSomething() && !this.hasDislike()
            ? (
              <SVGIcon
                name="icon-bell"
                className={classNames('tag-favorite')}
              />
            )
            : (
              <SVGIcon
                name="icon-bell"
                className={classNames('tag-off')}
              />
            )
        }
      </div>
    );
  },

  hideTaggedTimer() {
    if (!this.state.tagPanelHidden) {
      this.hideTagTimer = window.setTimeout(this.toggleTagPanel.bind(this, false), 300);
    }
  },
  mouseEnterInvalidateHideTagTimer() {
    if (typeof (this.hideTagTimer) !== 'undefined' && this.hideTagTimer) {
      window.clearTimeout(this.hideTagTimer);
      this.hideTagTimer = null;
    }
  },

  onPhotoClick() {
    if (!this.state.showNotAvailable) {
      const sliderIndex = _.get(this.refs.slider, 'innerSlider.state.currentSlide');
      this.props.onPhotoClick(sliderIndex || 0);
    }
    this.actions.analytics.sendEvent('property cards', 'photos', this.props.listing.ZipCode);
  },

  tagListingClicked(e) {
    e.preventDefault();
    this.actions.tagging.onNav();
  },

  setAgentContact(activeTab) {
    console.log(`${activeTab}is the tab`);
    this.actions.agent.setActiveTab(activeTab);
    this.actions.agent.showAgentContact(!this.props.listing.isTaxProperty && this.props.listing);

    this.actions.analytics.sendEvent('detail view', 'agent', this.props.listing.ZipCode);
  },

  imageFilter(imgArr) {
    if (this.props.useOne) {
      return [imgArr[0]];
    }
    return imgArr;
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    // some logic for whether to show the agent name, broker name, or both
    const agentName = this.state.agentData.NameLookup
      ? this.state.agentData.NameLookup
      : (`${this.state.agentData.FirstName} ${this.state.agentData.LastName}`);

    const brokerName = this.state.agentData.BrokerName;

    // broker name on slider, if necessary
    const courtesyLabel = (this.props.mlsData && this.props.mlsData.LabelForIdxListingBroker)
      ? this.props.mlsData.LabelForIdxListingBroker
      : 'Courtesy of';

    const listingAgentDisclosure = [
      _.get(this.props.listing.ListingAgent, 'Name'),
      _.get(this.props.listing.ListingAgent, 'Phone'),
      _.get(this.props.listing.ListingAgent, 'OfficeName'),
      _.get(this.props.listing.ListingAgent, 'OfficePhone'),
    ].filter(Boolean).join(', ').replace(/\s{2,}/g, ' ').trim();

    const coAgentDisclosure = [
      _.get(this.props.listing.CoAgent, 'Name'),
      _.get(this.props.listing.CoAgent, 'Phone'),
      _.get(this.props.listing.CoAgent, 'OfficeName'),
      _.get(this.props.listing.CoAgent, 'OfficePhone'),
    ].filter(Boolean).join(', ').replace(/\s{2,}/g, ' ').trim();

    const sliderSettings = {
      dots: false,
      infinite: true,
      speed: 500,
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: false,
    };

    return (
      <div id="photo-slider" className={this.props.className || ''}>
        {this.props.mlsData && this.props.mlsData.ShowBrokerOnSlider
          ? (
            <div className="broker-on-slider">
              <div>
                <strong>{`${courtesyLabel}: `}</strong>
                {listingAgentDisclosure || 'Information not available'}
                {
                  coAgentDisclosure ? (
                    <span>
                      <strong> and </strong>
                      {coAgentDisclosure}
                    </span>
                  ) : null
                }
              </div>
            </div>
          )
          : null}

        {
          this.utils.useMobileSite()

            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="View listing"
                className="mobile-picture"
                onClick={this.onPhotoClick}
                style={{
                  backgroundImage: `url('${this.props.listing.Images && this.props.listing.Images.length > 0 && !this.state.showNotAvailable
                    ? this.props.listing.Images[0][this.props.useSm ? 'sm' : 'lg']
                    : this.utils.streetViewNotAvailableImage()}')`,
                }}
              />
            )

            : (
              <Slider {...sliderSettings} ref="slider" lazyLoad preLoad={1}>
                { this.props.listing.Images && this.props.listing.Images.length > 0 && !this.state.showNotAvailable
                  ? this.imageFilter(this.props.listing.Images).map(function (i) {
                    return (
                      <div
                        className="photo-slide"
                        key={i.lg}
                        style={{ backgroundImage: `url('${this.props.useSm ? i.sm : i.lg}')` }}
                      />
                    );
                  }, this)
                  : (
                    <div
                      className="photo-slide not-available"
                      style={{ backgroundImage: `url('${this.utils.streetViewNotAvailableImage()}')` }}
                    />
                  )}
              </Slider>
            )
        }

        {
          this.utils.useMobileSite() ? null
            : (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Enlarge"
                className="zoom-button"
                onClick={this.onPhotoClick}
                title="Click to enlarge"
              >
                <SVGIcon name="icon-enlarge" />
              </div>
            )
        }

        {
          this.props.listing.Images && this.imageFilter(this.props.listing.Images).length > 1
            ? [
              <div
                role="button"
                tabIndex="-1"
                aria-label="Previous"
                className="photo-arrow-container photo-arrow-container-left"
                key="photo-arrow-container-left"
                onClick={this.prev}
              >
                <div className="photo-arrow photo-arrow-left">
                  <SVGIcon name="icon-chevron-left" />
                </div>
              </div>,
              <div
                role="button"
                tabIndex="-1"
                aria-label="Next"
                className="photo-arrow-container photo-arrow-container-right"
                key="photo-arrow-container-right"
                onClick={this.next}
              >
                <div className="photo-arrow photo-arrow-right">
                  <SVGIcon name="icon-chevron-right" />
                </div>
              </div>,
            ] : null
        }

        {
          this.state.agentData && this.props.mlsData && this.props.mlsData.ShowAgentOnSlider && (!this.props.mlsData.ShowBrokerOnSlider)
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Agent"
                className="agent-container"
                onClick={this.setAgentContact.bind(this, 'Call')}
              >
                <AgentProfileImage className="agent-image" />
                <span className="agent-name">
                  {agentName}
                  &nbsp;&nbsp;
                  <VerifiedBadge />
                  &nbsp;
                  {brokerName ? `| ${brokerName}` : null}
                </span>
                <SVGIcon name="icon-chat-bubbles" />
              </div>
            )
            : null
        }

        { this.tagButton()}

        <ReactCSSTransitionGroup transitionName="fade" transitionEnter={false} transitionLeaveTimeout={200}>
          {
            !this.state.tagPanelHidden
              ? (
                <div
                  className="tag-panel-photo"
                  key={`tag-panel-${this.props.listing.Id}`}
                  onMouseLeave={this.hideTaggedTimer}
                  onMouseEnter={this.mouseEnterInvalidateHideTagTimer}
                >
                  <div className="tags">
                    <label
                      className={classNames('tag', { active: (this.state.cardTags || {}).favorite })}
                      onClick={this.onTag.bind(this, 'favorite')}
                    >
                      favorite
                    </label>
                    <label
                      className={classNames('tag', { active: (this.state.cardTags || {}).dislike })}
                      onClick={this.onTag.bind(this, 'dislike')}
                    >
                      dislike
                    </label>

                    {
                      this.state.allTags ? (

                        Object.keys(this.state.allTags).map(function (tagName) {
                          if (['favorite', 'dislike'].indexOf(tagName) !== -1) {
                            return null;
                          }
                          return (
                            <label
                              key={tagName}
                              className={classNames('tag', { active: (this.state.cardTags || {})[tagName] })}
                              onClick={this.onTag.bind(this, tagName)}
                            >
                              {tagName}
                            </label>
                          );
                        }, this)

                      ) : null
                    }

                  </div>
                  <form className="add-tag" onSubmit={this.onAddTag}>
                    <input
                      type="text"
                      placeholder="add new tag?"
                      className="form-control"
                      name="new-tag"
                      id="new-tag"
                      ref="newTagInput"
                    />
                    <SubmitBtn className="btn-tagsubmit" text="ADD" />
                  </form>

                  { this.tagButton()}
                  <div className="links-wrapper">
                    <a href="/tagged" onClick={this.tagListingClicked}>My Homes</a>
                  </div>
                </div>
              )
              : null
          }
        </ReactCSSTransitionGroup>
      </div>
    );
  },
});
