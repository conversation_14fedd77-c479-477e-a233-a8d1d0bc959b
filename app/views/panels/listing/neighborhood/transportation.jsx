const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');
const SpinnerRound = require('../../../components/spinner_round');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.transportation',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel transportation">

        <h3>
          TRANSPORTATION
          <SVGIcon name="icon-transportation" />
        </h3>

        {
          this.props.data === null
            ? (
              <center className="mt15 mb15">
                <SpinnerRound />
              </center>
            )
            : this.props.data === false
              ? <center className="mt15 mb15">--- Not Available ---</center>
              : (
                <table className="table">
                  <thead />
                  <tbody>
                    <tr>
                      <td rowSpan="3" className="commute-time">
                        <div className="commute-wrapper">
                          <p>
                            {this.props.data.AverageCommuteTime}
                            &nbsp;
                            min
                          </p>
                          <SVGIcon name="icon-car" />
                          <small>Average Commute Time</small>
                        </div>
                      </td>
                      <td>Drive</td>
                      <td>
                        {this.props.data.PercentageDrivers}
                        %
                      </td>
                    </tr>
                    <tr>
                      <td>Public Transportation</td>
                      <td>
                        {this.props.data.PercentagePublicTransportation}
                        %
                      </td>
                    </tr>
                    <tr>
                      <td>Walk/Bike</td>
                      <td>
                        {this.props.data.PercentageBikersWalkers}
                        %
                      </td>
                    </tr>
                  </tbody>
                </table>
              )
        }
      </div>
    );
  },
});
