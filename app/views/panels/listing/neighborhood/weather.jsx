/* eslint-disable jsx-a11y/control-has-associated-label */
const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SpinnerRound = require('../../../components/spinner_round');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.weather',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  render() {
    console.log(this.props.data);

    if (!this.props.listing) {
      return null;
    }

    // air quality needle, start at -90 since the SVG is oriented straight up, this sets it to effectively 0
    let needleRotation = -90;
    const airQualityMax = 500;

    // scale the value to 180 degrees
    needleRotation += (this.props.data && this.props.data.AirQualityIndex) ? ((this.props.data.AirQualityIndex / airQualityMax) * 180) : 0;

    const needleStyle = {
      transform: `translate(-50%, -50%) rotateZ(${needleRotation}deg)`,
    };

    return (
      <div className="listing-sub-panel weather">

        <h3 className="border-bottom">WEATHER &amp; AIR QUALITY</h3>
        <p>
          Location:
          {this.props.listing.Address.CityName}
          ,
          {this.props.listing.Address.ZipCode}
        </p>

        {
          this.props.data === null
            ? (
              <center className="mt15 mb15">
                <SpinnerRound />
              </center>
            )
            : this.props.data === false
              ? <center className="mt15 mb15">--- Not Available ---</center>
              : (
                <div>
                  <div className="col-1-1 col-md-1-2">
                    <table className="table table-bordered">
                      <thead>
                        <tr className="seasons">
                          <th colSpan="2"><SVGIcon name="icon-flower" /></th>
                          <th colSpan="2"><SVGIcon name="icon-sandles" /></th>
                          <th colSpan="2"><SVGIcon name="icon-leaf" /></th>
                          <th colSpan="2"><SVGIcon name="icon-snow-flake" /></th>
                        </tr>
                        <tr>
                          <th colSpan="2">Spring</th>
                          <th colSpan="2">Summer</th>
                          <th colSpan="2">Fall</th>
                          <th colSpan="2">Winter</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="temp">
                          <td>
                            <span className="hi-value">{this.props.data.SpringHi}</span>
                            <br />
                            <span className="hi-label">Hi</span>
                          </td>
                          <td>
                            <span className="low-value">{this.props.data.SpringLow}</span>
                            <br />
                            <span className="low-label">Low</span>
                          </td>
                          <td>
                            <span className="hi-value">{this.props.data.SummerHi}</span>
                            <br />
                            <span className="hi-label">Hi</span>
                          </td>
                          <td>
                            <span className="low-value">{this.props.data.SummerLow}</span>
                            <br />
                            <span className="low-label">Low</span>
                          </td>
                          <td>
                            <span className="hi-value">{this.props.data.FallHi}</span>
                            <br />
                            <span className="hi-label">Hi</span>
                          </td>
                          <td>
                            <span className="low-value">{this.props.data.FallLow}</span>
                            <br />
                            <span className="low-label">Low</span>
                          </td>
                          <td>
                            <span className="hi-value">{this.props.data.WinterHi}</span>
                            <br />
                            <span className="hi-label">Hi</span>
                          </td>
                          <td>
                            <span className="low-value">{this.props.data.WinterLow}</span>
                            <br />
                            <span className="low-label">Low</span>
                          </td>
                        </tr>
                        <tr className="temp-spacing">
                          <td colSpan="2" />
                          <td colSpan="2" />
                          <td colSpan="2" />
                          <td colSpan="2" />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-1-1 col-md-1-2">
                    <table className="table table-bordered">
                      <thead>
                        <tr className="">
                          <th colSpan="2" />
                          <th colSpan="2" />
                          <th colSpan="2" />
                          <th colSpan="2" />
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td colSpan="2" className="">
                            <div className="sunny">
                              <span>Sunny</span>
                              <SVGIcon name="icon-sunny" />
                              <span>{(this.props.data.PercentSunny || this.props.data.PercentSunny === 0) ? `${this.props.data.PercentSunny}%` : ''}</span>
                            </div>
                          </td>
                          <td colSpan="2" className="">
                            <div className="cloudy">
                              <span>Cloudy</span>
                              <SVGIcon name="icon-cloudy" />
                              <span>{(this.props.data.PercentNotSunny || this.props.data.PercentNotSunny === 0) ? `${this.props.data.PercentNotSunny}%` : ''}</span>
                            </div>
                          </td>
                          <td colSpan="4" title={this.props.data.AirQualityDescription || ''} className="air-quality-td">
                            <div className="air-quality-container">
                              <div className="air-quality">
                                <div className="gauge-container">
                                  <SVGIcon className="color-gauge" name="graphics-color-gauge" />
                                  <SVGIcon className="needle" name="graphics-needle" key="1" style={needleStyle} />
                                  <div className="air-quality-min">0</div>
                                  <div className="air-quality-max">{airQualityMax}</div>
                                </div>
                                <div className="air-quality-info">
                                  <div className="air-quality-score">{this.props.data.AirQualityIndex || 'N/A'}</div>
                                  <div className="air-quality-description">
                                    The Air Quality is
                                    {this.props.data.AirQualityDescription || 'not available'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              )
          }
      </div>
    );
  },
});
