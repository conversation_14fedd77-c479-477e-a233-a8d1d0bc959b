const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SpinnerRound = require('../../../components/spinner_round');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.demographics',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel demographics">

        <h3>DEMOGRAPHICS</h3>

        {
        this.props.data === null
          ? (
            <center className="mt15 mb15">
              <SpinnerRound />
            </center>
          )
          : this.props.data === false
            ? <center className="mt15 mb15">--- Not Available ---</center>
            : (
              <table className="table">
                <thead />
                <tbody>
                  <tr>
                    <td>Average Family Size</td>
                    <td>{this.props.data.AverageFamilySize}</td>
                  </tr>
                  <tr>
                    <td>Homes with Kids</td>
                    <td>{this.props.data.PercentageHomesWithKids ? `${this.props.data.PercentageHomesWithKids}%` : '-'}</td>
                  </tr>
                  <tr>
                    <td>Median Age</td>
                    <td>{this.props.data.MedianAge ? `${this.props.data.MedianAge} Years Old` : '-'}</td>
                  </tr>
                  <tr>
                    <td>Men to Women</td>
                    <td>
                      {(this.props.data.PercentageMen && this.props.data.PercentageWomen)
                        ? `${this.props.data.PercentageMen}%  |  ${this.props.data.PercentageWomen}%` : '-'}
                    </td>
                  </tr>
                  <tr>
                    <td>Married to Single</td>
                    <td>
                      {(this.props.data.PercentageMarried)
                        ? `${this.props.data.PercentageMarried}%` : '-'}
                    </td>
                  </tr>
                  <tr>
                    <td>Median Income</td>
                    <td>
                      {this.props.data.MedianIncome
                        ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSepIfNumber(this.props.data.MedianIncome)}` : '-'}
                    </td>
                  </tr>
                </tbody>
              </table>
            )
      }
      </div>
    );
  },
});
