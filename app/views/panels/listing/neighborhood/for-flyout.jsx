const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const AgentProfileImage = require('../../../components/agent_profile_image');
const SVGIcon = require('../../../components/svg_icon');
const LazyLoadWithinContainer = require('../../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');
const Yelp = require('./yelp');
const VerifiedBadge = require('../../../components/verified-badge');
/*
const Demographics = require('./demographics');
const Weather = require('./weather');
const Transportation = require('./transportation'),
  DriveTime = require('./drive-time'),
  GettingAroundTown = require('./getting-around-town') */

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.for-flyout',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils, mixins.pureRender],

  setAgentContact(activeTab) {
    this.actions.agent.setActiveTab(activeTab);
    this.actions.agent.showAgentContact(!this.props.listing.isTaxProperty && this.props.listing);

    window.sendEvent('detail view', 'see this home', this.state.agentData.ZipCode);
  },

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  getInitialState() {
    return {
      householdData: null,
      transportationData: null,
      envData: null,
    };
  },

  componentDidMount() {
    // NPLAY-5240 Remove Policy Map Data
    // this.actions.listing.getDemographics(this.props.listing, function (res) {
    //   if (res && typeof res === 'object') {
    //     this.setState({
    //       householdData: res.HouseholdData,
    //       transportationData: res.TransportationData,
    //       envData: res.EnvironmentData
    //     })
    //   } else {
    //     this.setState({
    //       householdData: false,
    //       transportationData: false,
    //       envData: false
    //     })
    //   }
    // }.bind(this))
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-panels listing-neighborhood">

        <h2>
          <SVGIcon name="icon-neighborhood" className="icon-in-header" />
          NEIGHBORHOOD
          <a className="btn btn-link icon-view-on-map-container">
            <SVGIcon name="icon-view-on-map" className="icon-view-on-map" />
            View on Map
          </a>
        </h2>

        {/* <LazyLoadWithinContainer height='260px' buffer={200}>
          <Demographics listing={this.props.listing} data={this.state.householdData}/>
        </LazyLoadWithinContainer>
        <LazyLoadWithinContainer height='155px' buffer={200}>
          <Transportation listing={this.props.listing} data={this.state.transportationData}/>
        </LazyLoadWithinContainer>
        <p className="text-right">Data Provided by PolicyMap</p> */}
        <LazyLoadWithinContainer height="100px" buffer={200}>
          <Yelp listing={this.props.listing} includeOwnMap={this.utils.useMobileSite()} />
        </LazyLoadWithinContainer>
        {/* <LazyLoadWithinContainer height='282px' buffer={200}>
          <Weather listing={this.props.listing} data={this.state.envData}/>
        </LazyLoadWithinContainer>
        <p className="text-right mt15">Data Provided by PolicyMap</p> */}
        {/* <LazyLoadWithinContainer height='100px' buffer={200}>
          <DriveTime listing={this.props.listing} />
        </LazyLoadWithinContainer>
        <LazyLoadWithinContainer height='100px' buffer={200}>
          <GettingAroundTown listing={this.props.listing} />
        </LazyLoadWithinContainer> */}

        {
        this.state.agentData && this.props.mlsData.ShowAgentInListingDetail
          ? (
            <div className="col-1-1 contact-me-container">
              <div className={classNames('contact-me')}>
                <div className="left-side">
                  <h6>
                    Want to learn more about this Neighborhood?
                  </h6>
                  <a
                    role="button"
                    tabIndex="-1"
                    className="btn btn-primary"
                    onClick={this.setAgentContact.bind(this, 'Call')}
                  >
                    Contact Me
                  </a>
                </div>
                <div className="agent-container">
                  <AgentProfileImage className="agent-image" />
                  <p className="agent-name">
                    {this.state.agentData.FirstName}
&nbsp;
                    {this.state.agentData.LastName}
                          &nbsp;
                    <VerifiedBadge />
                  </p>
                </div>
              </div>
              <hr />
            </div>
          ) : null
        }
      </div>
    );
  },
});
