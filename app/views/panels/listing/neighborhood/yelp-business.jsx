const React = require('react');

const _ = require('lodash');
const classNames = require('classnames');
const SVGIcon = require('../../../components/svg_icon');

const YelpBusiness = React.createClass({

  render() {
    const rating = this.props.business.Rating;
    const hasHalfStar = (rating * 10) % 10;
    const starCount = Math.floor(Number.parseInt(rating, 10));
    const ratingImageUrl = `//nplayassets.blob.core.windows.net/search2/yelp/web_and_ios/regular/regular_${starCount}${hasHalfStar ? '_half' : ''}@2x.png`;
    const ratingSmallImageUrl = `//nplayassets.blob.core.windows.net/search2/yelp/web_and_ios/small/small_${starCount}${hasHalfStar ? '_half' : ''}@2x.png`;

    const categories = _.map(this.props.business.Categories, (category) => category.CategoryName);

    let imageUrl = _.isEmpty(this.props.business.ImageURL)
      ? '//nplayassets.blob.core.windows.net/search2/default-yelp.png'
      : this.props.business.ImageURL;

    imageUrl = imageUrl.replace('o.jpg', 'ms.jpg');

    return (
      <div
        className={classNames('yelp-business', {
          expanded: this.props.expanded,
        })}
        ref="main"
      >
        <div
          role="button"
          tabIndex="-1"
          aria-label="Toggle business"
          className="header"
          onClick={(e) => this.props.handleToggle(e, this.props.id)}
        >
          <div className="yelp-name">{this.props.business.Name}</div>
          <div className="yelp-business-rating">
            <img alt="Yelp business" src={ratingSmallImageUrl} />
          </div>
          <div className="distance">
            {this.props.distance}
            &nbsp;
            mi
          </div>
          <SVGIcon name="icon-chevron-down" />
        </div>
        <div className="content">
          <a href={this.props.business.YelpURL} target="_blank" title="View on Yelp">
            <img alt="View business" src={imageUrl} />
          </a>
          <div className="yelp-business-content">
            <div className="yelp-reviews">
              <div className="yelp-business-rating"><img alt="Business rating" src={ratingImageUrl} /></div>
              <div className="yelp-business-review-count">
                {this.props.business.ReviewCount}
                &nbsp;
                reviews
              </div>
            </div>
            <div className="yelp-categories">
              <span className="text-muted">Categories:</span>
              &nbsp;
              {categories.join(' | ')}
            </div>
            <div className="yelp-price">
              <span className="text-muted">Price:</span>
              &nbsp;
              {this.props.business.Price}
            </div>
          </div>
        </div>
      </div>
    );
  },

});

module.exports = YelpBusiness;
