const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.drive-time',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel drive-time">

        <h3>DRIVE TIME</h3>
        <p>Enter your work or other addresses to view actual drive times to this home</p>
        <form className="row">
          <div className="col-1-1 mb5">
            <input className="form-control" type="text" placeholder="street address" />
          </div>
          <div className="col-1-1 mb5">
            <input className="form-control" type="text" placeholder="city" />
          </div>
          <div className="col-1-2 mb5">
            <input className="form-control" type="text" placeholder="state" />
          </div>
          <div className="col-1-2 mb5">
            <input className="form-control" type="text" placeholder="zip code" />
          </div>
          <div className="col-1-2 mb5">
            <input className="btn btn-outline btn-block" type="submit" value="Calculate" />
          </div>
        </form>

      </div>
    );
  },
});
