const React = require('react');
const classNames = require('classnames');
const ReactDOMServer = require('react-dom/server');
const _ = require('lodash');
const zenscroll = require('zenscroll');
const mixins = require('../../../../lib/mixins/index');

zenscroll.setup(null, 128);

const SpinnerRound = require('../../../components/spinner_round');
const YelpBusiness = require('./yelp-business');
const YelpPopup = require('../../../components/YelpPopup');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.yelp-reviews',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.leaflet, mixins.pureRender, mixins.cursors],

  cursors: {
    categories: ['shared', 'yelpCategories'],
    searchResults: ['screens', 'listing', 'yelpSearchResults'],
    loading: ['loading', 'yelpSearchResults'],
    showYelpMarkers: ['screens', 'listing', 'showYelpMarkers'],
  },

  getInitialState() {
    return {
      currentCategory: null,
      expandedBusinessIndex: null,
    };
  },

  componentDidMount() {
    if (_.isEmpty(this.state.categories)) {
      this.actions.common.getYelpCategories();
    }

    // start with restaurants
    this.actions.listing.getYelpSearchResults(this.props.listing);

    if (this.props.includeOwnMap) {
      this.setupMap();
    }

    if (this.props.listing) {
      window.fbq('track', 'ViewContent', {
        ...this.utils.getListingPixelAttributes(this.props.listing),
        content_type: 'home_listing_neighborhood',
      });
    }
  },

  componentWillUpdate(nextProps, nextState) {
    if (this.props.includeOwnMap && !_.isEqual(this.state.searchResults, nextState.searchResults)) {
      this.updateYelpMarkers(nextState.searchResults);
    }
  },

  componentWillUnmount() {
    this.actions.listing.removeYelpResults();
  },

  handleResultsScroll(e) {
    // prevent the scroll event from propagating to ancestor elements, so it doesn't trigger the header
    e.stopPropagation();
  },

  handleToggleYelpMarkersClick() {
    if (this.state.showYelpMarkers) {
      this.actions.listing.clearActiveYelpMarker();
      this.actions.listing.hideYelpMarkers();
    } else {
      this.actions.listing.showYelpMarkers();
    }
  },

  handleCategoryChange() {
    const category = this.refs.category.value;
    if (category) {
      this.actions.listing.getYelpSearchResults(this.props.listing, category, () => {

      });
    }
    this.setState({
      currentCategory: category,
      expandedBusinessIndex: null,
    });
    this.clearActiveYelpMarker();
  },

  handleToggle(e, index) {
    if (this.state.expandedBusinessIndex === index) {
      index = null;
    }

    // const scroller = zenscroll.createScroller(this.refs.list);

    this.setState({
      expandedBusinessIndex: index,
    }, () => {
      // clear on main map
      this.actions.listing.clearActiveYelpMarker();
      // clear on included map
      if (this.props.includeOwnMap) {
        this.clearActiveYelpMarker();
      }
      // if there is a selected index, mark it active
      if (!_.isNil(this.state.expandedBusinessIndex)) {
        this.actions.listing.setYelpMarkerActive(this.state.expandedBusinessIndex);
        if (this.props.includeOwnMap) {
          const marker = this.getYelpMarkerByIndex(this.state.expandedBusinessIndex);
          if (marker) {
            this.leaflet.modifyYelpMarkerToActive(marker);
          }
        }
      }
    });
  },

  getYelpMarkerByIndex() {
    const markers = this.yelpLayerGroup.getLayers();
    return _.find(markers, (marker) => marker.index == this.state.expandedBusinessIndex);
  },

  clearActiveYelpMarker() {
    if (!this.yelpLayerGroup) {
      return;
    }
    const markers = this.yelpLayerGroup.getLayers();
    const marker = _.find(markers, (m) => m.isActive);
    if (marker) {
      this.leaflet.modifyYelpMarkerToInactive(marker);
    }
  },

  updateYelpMarkers(results) {
    if (!this.yelpLayerGroup) {
      return;
    }
    this.yelpLayerGroup.clearLayers();
    _.each(results, (result, i) => {
      const icon = L.divIcon({ className: 'yelp-result' });
      const marker = L.marker([result.Lat, result.Lon], { icon }).addTo(this.yelpLayerGroup);
      marker.index = i;

      const html = ReactDOMServer.renderToStaticMarkup(React.createElement(YelpPopup, {
        business: result,
      }));

      const paddingAmount = this.utils.useMobileSite() ? 0 : 50;
      marker.bindPopup(html, { autoPanPaddingTopLeft: new L.point(paddingAmount, paddingAmount), offset: new L.point(0, -33), className: 'yelp-popup' });
      marker.on('mouseover', () => {
        marker.openPopup();
      });
      marker.on('mouseout', () => {
        marker.closePopup();
      });
    });

    this.getListingMarker().addTo(this.yelpLayerGroup);
    this.getPulseMarker().addTo(this.yelpLayerGroup);

    if (!_.isEmpty(this.yelpLayerGroup.getLayers())) {
      this.map.fitBounds(this.yelpLayerGroup.getBounds(), { padding: [20, 20] });
    }
  },

  getListingMarker() {
    let baseIcon; let icon;
    const totalBedrooms = this.props.listing.TotalBedrooms > 4 ? 5 : (this.props.listing.TotalBedrooms || 0);
    if (this.utils.isMobile()) {
      // Get one of 5 base icons containing # bedrooms
      baseIcon = this.leaflet.generalMarker_svg[totalBedrooms];
      icon = new L.DivIcon.Label.Default(baseIcon.options);
    } else {
      // Get one of 5 base icons containing # bedrooms
      baseIcon = this.leaflet.generalMarker[totalBedrooms];
      icon = new L.DivIcon.Label.Default(baseIcon.options);
    }

    const marker = L.marker([_.get(this.props.listing, 'Location.Lat'), _.get(this.props.listing, 'Location.Lon')], {
      icon,
      draggable: false,
      riseOnHover: true,
      keyboard: false,
      clickable: false,
    });

    marker.options.icon.options.className = marker.options.icon.options.className.replace('small', 'active general');

    return marker;
  },

  getPulseMarker() {
    const pulseMarker = L.marker([_.get(this.props.listing, 'Location.Lat'), _.get(this.props.listing, 'Location.Lon')], {
      icon: this.leaflet.pulseIcon,
      clickable: false,
      draggable: false,
      keyboard: false,
    });
    return pulseMarker;
  },

  setupMap() {
    this.map = L.map(this.refs['yelp-map'], {
      keyboard: false,

      // Zoom things
      zoomControl: false,
    }).setView([_.get(this.props.listing, 'Location.Lat'), _.get(this.props.listing, 'Location.Lon')], 6);

    this.leaflet.getBasicLayer().addTo(this.map);

    this.yelpLayerGroup = new L.FeatureGroup();
    this.yelpLayerGroup.addTo(this.map);
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    // const settings = {
    //   dots: false,
    //   arrows: true,
    //   infinite: true,
    //   speed: 500,
    //   slidesToShow: 1,
    //   slidesToScroll: 1,
    // };

    const options = _.map(this.state.categories, (category) => <option value={category.CategorySearchTerm} key={category.CategorySearchTerm}>{category.CategoryName}</option>);
    options.unshift(<option value="" key="all">All categories</option>);

    let results = [];
    if (this.state.searchResults) {
      results = this.utils.useMobileSite() ? this.state.searchResults.slice(0, 8) : this.state.searchResults;
    }

    const resultItems = _.map(results, (result, index) => {
      let distance;
      if (_.get(this.props.listing, 'Location.Lat') && _.get(this.props.listing, 'Location.Lon') && result.Lat && result.Lon) {
        const meters = L.latLng(this.props.listing.Location.Lat, this.props.listing.Location.Lon).distanceTo(L.latLng(result.Lat, result.Lon));
        distance = this.utils.convertToMiles(meters, 1);
      }
      return (
        <YelpBusiness
          id={index}
          key={index}
          business={result}
          distance={distance}
          expanded={index === this.state.expandedBusinessIndex}
          handleToggle={this.handleToggle}
        />
      );
    });

    return (
      <div className="listing-sub-panel yelp-local">
        <h3>GETTING AROUND TOWN</h3>
        <div>
          {'Select a category to see what\'s around town'}
          &nbsp;
          <img src="//nplayassets.blob.core.windows.net/search2/yelp_logo_40x20.png" alt="Powered by Yelp" title="Powered by Yelp" />
        </div>
        <div className="yelp-list-and-map">
          <div className="yelp-list-wrapper">
            <div className="select-container">
              <select ref="category" onChange={this.handleCategoryChange} value={this.state.currentCategory || ''}>
                {options}
              </select>
              {this.props.includeOwnMap
                ? null
                : (
                  <button
                    type="button"
                    className={classNames('toggle-yelp-marker-button btn btn-sm btn-primary', {
                      'showing-yelp': this.state.showYelpMarkers,
                      'hiding-yelp': !this.state.showYelpMarkers,
                    })}
                    onClick={this.handleToggleYelpMarkersClick}
                  >
                    {this.state.showYelpMarkers
                      ? 'HIDE YELP'
                      : 'VIEW ON MAP'}
                  </button>
                )}
            </div>
            <div className="yelp-results" ref="list" onScroll={this.handleResultsScroll}>
              {this.state.loading
                ? <center><SpinnerRound /></center>
                : _.isEmpty(resultItems)
                  ? <div>No matches nearby</div>
                  : resultItems}
            </div>
          </div>
          {this.props.includeOwnMap
            ? <div className="yelp-map" ref="yelp-map" />
            : null}
        </div>
      </div>
    );
  },
});
