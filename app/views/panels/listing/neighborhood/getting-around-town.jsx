const React = require('react');
const mixins = require('../../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.listing.neighborhood.getting-around-town',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-sub-panel getting-around-town">

        <h3>GETTING AROUND TOWN</h3>
        <p>Select a category and learn whats around town</p>
        <form className="row">
          <div className="col-1-1">
            <select className="form-control">
              <option>Restaurants</option>
            </select>
          </div>
        </form>

      </div>
    );
  },
});
