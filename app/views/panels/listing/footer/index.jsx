const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SpinnerRound = require('../../../components/spinner_round');
const LazyLoadWithinContainer = require('../../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');
const MLSDisclosure = require('../../mls-disclosure');
const WhyUseHomeasap = require('../../whyusehomeasap');
const LastRefreshed = require('../../last-refreshed');
const NPlayFooter = require('../../n-play-footer');

module.exports = React.createClass({

  displayName: 'panels.listing.footer',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils, mixins.pureRender],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  getDisclosureMlsId() {
    const { agentData } = this.state;
    const agentMlsIds = [agentData.MlsId];
    const listingMlsIds = this.props.listing.MlsIds || [];

    // NPLAY-6388 do not show MLS disclosure if it's a featured listing of agent
    if (
      (agentData.MlsAgentId && `${agentData.MlsAgentId}`.toUpperCase() === `${this.props.listing.ListingAgent.Id}`.toUpperCase())
      || (agentData.MlsOfficeId && `${agentData.MlsOfficeId}`.toUpperCase() === `${this.props.listing.ListingAgent.OfficeId}`.toUpperCase())
    ) {
      return null;
    }

    if (listingMlsIds.indexOf(agentData.MlsId) !== -1) {
      return agentData.MlsId;
    }

    for (const affiliatedAgent of agentData.SelfAffiliations || []) {
      if (agentMlsIds.indexOf(affiliatedAgent.MlsId) === -1) {
        agentMlsIds.push(affiliatedAgent.MlsId);
      }
    }

    for (const mlsId of listingMlsIds) {
      if (agentMlsIds.indexOf(mlsId) > -1) {
        return mlsId;
      }
    }
    return listingMlsIds[0] || agentData.MlsId;
  },

  render() {
    if (this.props.listing && this.props.listing.isTaxProperty) {
      return (
        <div className="listing-panels listing-footer">
          <NPlayFooter />
        </div>
      );
    }

    if (this.props.listing === false) {
      return (
        <div className="listing-panels listing-footer">
          <p>-- Listing not available ---</p>
        </div>
      );
    }

    if (!this.props.listing) {
      return (
        <div className="listing-panels listing-footer">
          <center className="mt30">
            <SpinnerRound />
          </center>
        </div>
      );
    }

    const disclosureMlsId = this.getDisclosureMlsId();

    return (
      <div className="listing-panels listing-footer">
        {
          !this.props.mlsData || !disclosureMlsId || this.props.mlsData.ShowDisclosureUnderListingDetail ? null
            : (
              <LazyLoadWithinContainer height="100px" buffer={500}>
                <MLSDisclosure mlsId={disclosureMlsId} key={disclosureMlsId} />
                <hr />
              </LazyLoadWithinContainer>
            )
        }

        <div className="listing-footer-info-2">
          <LastRefreshed />
        </div>
        <div className="listing-footer-info-1 mb20">
          <WhyUseHomeasap />
        </div>

        <NPlayFooter />
      </div>
    );
  },
});
