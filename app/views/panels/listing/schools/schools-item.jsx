const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const SpinnerRound = require('../../../components/spinner_round');
const SchoolRow = require('./school-row');
const SchoolActiveRow = require('./school-active-row');

module.exports = React.createClass({

  displayName: 'panels.listing.schools-item',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      schoolData: null,
      activeRow: null,
      loadMore: false,
    };
  },

  updateSchool(props) {
    this.setState({
      schoolData: null,
      activeRow: null,
    });
    if (props.listing && props.schoolType === 'public') {
      this.actions.listing.getPublicSchoolData(props.listing, (res) => {
        if (res && typeof res === 'object') {
          this.setState({ schoolData: res });
        } else {
          this.setState({ schoolData: false });
        }
      });
    } else if (props.listing && props.schoolType === 'private') {
      this.actions.listing.getPrivateSchoolData(props.listing, (res) => {
        if (res && typeof res === 'object') {
          this.setState({ schoolData: res });
        } else {
          this.setState({ schoolData: false });
        }
      });
    }
  },

  componentWillMount() {
    this.updateSchool(this.props);
  },

  componentWillUpdate(nextProps) {
    if (nextProps.listing != this.props.listing) {
      this.updateSchool(nextProps);
    }
  },

  toggleActive(idx, e) {
    e.preventDefault();
    const activeRow = this.state.activeRow === idx ? null : idx;
    this.setState({ activeRow });
    if (activeRow !== null) {
      const selectedSchool = this.state.schoolData[idx].school;
      this.actions.common.setLocationType({ locType: 'S', locId: selectedSchool.ncesId });
      this.actions.listing.hoverOverSchool(selectedSchool);
      this.props.schoolClicked ? this.props.schoolClicked(selectedSchool) : '';
    } else {
      this.actions.listing.clearSchool();
    }
  },

  loadMore() {
    this.setState({ loadMore: true });
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    if (this.state.schoolData === null) {
      return (
        <center className="mt15 mb15">
          <SpinnerRound />
        </center>
      );
    }

    if (this.state.schoolData === false) {
      return (
        <center className="mt15 mb15">
          --- No
          {this.utils.toTitleCase(this.props.schoolType)}
          &nbsp;
          School Data Found ---
        </center>
      );
    }

    return (
      <div className="listing-sub-panel schools-item">
        <h3>
          {(`${this.props.schoolType}`).toUpperCase()}
          &nbsp;
          SCHOOLS
          {this.props.schoolType === 'public' ? ' (assigned)' : null}
        </h3>
        <table className="school-table table table-hover">
          <thead>
            <tr>
              <th colSpan="2">School Rating</th>
              <th>Grade</th>
              <th>Distance</th>
              <th>&nbsp;</th>
            </tr>
          </thead>
          <tbody>
            {
              (this.state.loadMore ? this.state.schoolData : this.state.schoolData.slice(0, 5)).map(function (item, idx) {
                return [
                  <SchoolRow
                    school={item.school}
                    key={idx}
                    schoolType={this.props.schoolType}
                    onClick={this.toggleActive.bind(null, idx)}
                    active={this.state.activeRow === idx}
                  />,
                  <SchoolActiveRow
                    school={item.school}
                    key={`${idx}active`}
                    schoolType={this.props.schoolType}
                    onClick={this.toggleActive.bind(null, idx)}
                    active={this.state.activeRow === idx}
                  />,
                ];
              }, this)
            }
            {
              this.state.schoolData.length > 5 && !this.state.loadMore
                ? (
                  <tr onClick={this.loadMore} className="active">
                    <td colSpan="5" className="text-center">
                      <a>Load More</a>
                    </td>
                  </tr>
                ) : null
            }
            {
              !this.state.schoolData || this.state.schoolData.length === 0
                ? (
                  <tr className="active cursor-default">
                    <td colSpan="5" className="text-center">
                      No Results
                    </td>
                  </tr>
                ) : null
            }
          </tbody>
        </table>
      </div>
    );
  },
});
