const React = require('react');
const { OverlayTrigger, Popover } = require('react-bootstrap');
const mixins = require('../../../../lib/mixins/index');
const LazyLoadWithinContainer = require('../../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');
const SVGIcon = require('../../../components/svg_icon');

const SchoolItem = require('./schools-item');

module.exports = React.createClass({

  displayName: 'panels.listing.schools.for-flyout',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  schoolClicked() {
    this.actions.analytics.sendEvent('detail view', 'school click', this.props.listing.ZipCode);
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    const popover = (
      <Popover id="school-ratings">
        <div>
          <div>
            <p>The GreatSchools Rating helps parents compare schools within a state based on a variety of school quality indicators and provides a helpful picture of how effectively each school serves all of its students.</p>
            <p>Ratings are on a scale of 1 (below average) to 10 (above average) and can include test scores, college readiness, academic progress, advanced courses, equity, discipline and attendance data.</p>
            <p>
              {'We also advise parents to visit schools, consider other information on school performance and programs, and consider family needs as part of the school selection process. '}
              <a href="https://www.greatschools.org/gk/ratings/" target="_blank">Learn more</a>
              .
            </p>
          </div>
        </div>
      </Popover>
    );

    return (
      <div className="listing-panels listing-schools">

        <h2>
          <SVGIcon name="icon-education" className="icon-in-header" />
          NEARBY SCHOOLS
        </h2>

        <div className="mt10 mb10">
          <span>Rating is out of 10. 1 being worst, 10 being best.</span>
          <OverlayTrigger trigger={['focus', 'click']} rootClose placement="left" overlay={popover}>
            <div className="info-button" onClick={null}>
              <SVGIcon name="icon-info" className="icon-info" />
            </div>
          </OverlayTrigger>
        </div>

        <LazyLoadWithinContainer height="100px" buffer={200}>
          <SchoolItem listing={this.props.listing} schoolType="public" schoolClicked={this.schoolClicked} />
          <SchoolItem listing={this.props.listing} schoolType="private" schoolClicked={this.schoolClicked} />
        </LazyLoadWithinContainer>

        <p className="text-right">
          Data Provided by
          <a href="http://GreatSchools.org" target="_blank">GreatSchools</a>
        </p>
      </div>
    );
  },
});
