const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.listing.school-row',

  mixins: [mixins.debug, mixins.utils, mixins.pureRender],

  render() {
    if (!this.props.school) {
      return null;
    }

    return (
      <tr
        role="button"
        tabIndex="-1"
        aria-label="School rating"
        className={classNames({ active: this.props.active })}
        onClick={this.props.onClick}
        onFocus={this.props.onFocus}
        onMouseOver={this.props.onHover}
      >
        <td
          className={classNames('rating', `rating-${this.props.school.gsRating}`)}
          rowSpan={this.props.active ? '2' : '1'}
        >
          <div>
            <p>{this.props.school.gsRating || 'NR'}</p>
          </div>
        </td>
        <td className="school-name">{this.props.school.name}</td>
        <td className="grade-range">{this.props.school.gradeRange}</td>
        <td className="distance">
          {this.props.school.distance}
          &nbsp;
          mi
        </td>
        <td className="arrow">
          <div>
            <SVGIcon name="icon-dropdown-arrow" />
          </div>
        </td>
      </tr>
    );
  },
});
