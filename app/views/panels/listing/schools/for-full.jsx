const React = require('react');
const mixins = require('../../../../lib/mixins/index');
const LazyLoadWithinContainer = require('../../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');
const SVGIcon = require('../../../components/svg_icon');

const SchoolItem = require('./schools-item');

module.exports = React.createClass({

  displayName: 'panels.listing.schools.for-full',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  schoolClicked() {
    this.actions.analytics.sendEvent('detail view', 'school click', this.props.listing.ZipCode);
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    return (
      <div className="listing-panels listing-schools">

        <h2>
          <SVGIcon name="icon-education" className="icon-in-header" />
          NEARBY SCHOOLS
          <small>- Rating is out of 10. 1 being worst, 10 being best.</small>
        </h2>

        <LazyLoadWithinContainer height="100px" buffer={200}>
          <div className="col-1-2">
            <SchoolItem listing={this.props.listing} schoolType="public" schoolClicked={this.schoolClicked} />
          </div>

          <div className="col-1-2">
            <SchoolItem listing={this.props.listing} schoolType="private" schoolClicked={this.schoolClicked} />
          </div>
        </LazyLoadWithinContainer>

        <p className="text-right">
          Data provided by
          <a href="http://GreatSchools.org" target="_blank">GreatSchools</a>
        </p>
      </div>
    );
  },
});
