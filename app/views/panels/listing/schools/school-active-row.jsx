const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins/index');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.listing.school-active-row',

  mixins: [mixins.debug, mixins.utils, mixins.pureRender],

  linkClicked(e) {
    e.stopPropagation();
  },

  // make sure it has a protocol like http://, just www.schoolsite.com won't work
  fixSchoolLink(link) {
    if (link.match(/^http/)) {
      return link;
    }

    return `http://${link}`;
  },

  render() {
    if (!this.props.school) {
      return null;
    }

    return (
      <tr
        className={classNames('school-active-row', { active: this.props.active })}
        onClick={this.props.onClick}
        onFocus={this.props.onHover}
        onMouseOver={this.props.onHover}
      >
        <td colSpan="4">
          <div className={classNames({
            'col-1-1': ((this.props.schoolType === 'private') && !this.props.school.website),
            'col-1-3': ((this.props.schoolType === 'public') && this.props.school.website),
            'col-1-2': ((this.props.schoolType === 'private') && this.props.school.website) || ((this.props.schoolType === 'public') && !this.props.school.website),
          })}
          >
            <p>{this.props.school.enrollment ? this.props.school.enrollment : 'n/a'}</p>
            <p className="text-muted">Students</p>
          </div>
          { this.props.schoolType === 'public'
            ? (
              <div className={classNames({
                'col-1-3': !!this.props.school.website,
                'col-1-2': !this.props.school.website,
              })}
              >
                <div className="school-district">
                  <p>{this.props.school.district}</p>
                </div>
                <p className="text-muted">District</p>
              </div>
            )
            : null}
          { this.props.school.website
            ? (
              <div className={classNames({
                'col-1-3': this.props.schoolType === 'public',
                'col-1-2': this.props.schoolType === 'private',
              })}
              >
                <div>
                  <a onClick={this.linkClicked} target="_blank" href={this.fixSchoolLink(this.props.school.website)}>
                    <p>
                      <SVGIcon className="school-website-logo" name="icon-website" />
                    </p>
                    <p className="school-website">School Website</p>
                  </a>
                </div>
              </div>
            )
            : null}
        </td>
      </tr>
    );
  },
});
