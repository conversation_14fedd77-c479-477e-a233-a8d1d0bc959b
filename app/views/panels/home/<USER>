const React = require('react');
const _ = require('lodash');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SearchField = require('./search-field');
const SubmitBtn = require('../../components/submit_btn');

const constants = require('../../../lib/constants');

module.exports = React.createClass({

  displayName: 'panels.home',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
  },

  searchSubmit(e) {
    e.preventDefault();

    // const saleType = this.state.saleType;

    this.setState({ loading: true, error: null });
    this.actions.common.flagUserAsInteractedWithSite();

    const res = this.refs.SearchField.state.res;
    const newVal = this.refs.SearchField.state.value;
    if (!this.refs.SearchField.state.hasPrevious // Using Previous
      && (!res.locationQuery || res.locationQuery !== newVal)) {
      console.log('Need google geocoding');

      if (!newVal || newVal.toLowerCase().indexOf('loading') !== -1) {
        this.setState({ loading: false });
        return;
      }

      this.utils.geocodeAddress(newVal, (res1) => {
        if ((!res) || _.includes(res.types, 'country')) {
          return this.setState({ loading: false, error: `No results for ${newVal}` });
        }

        const _res = {
          lat: res1.geometry.location.lat,
          lon: res1.geometry.location.lng,
          locationQuery: res1.formatted_address,
        };

        window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({ ..._res, radius: constants.SEARCH_RADIUS_DEFAULT_METERS }));

        console.log(`Search Field: ${_res.locationQuery}`);
        this.actions.common.saveLocationQuery(_res.locationQuery);
        this.actions.home.performAgentSearch(_res.lat, _res.lon, (err) => {
          this.setState({ loading: false });
          if (err) {
            return this.setState({ error: 'Unknown error' });
          }
          this.router.go(this.router.generateUrl(this.router.ROUTE_NAMES.AGENT_SEARCH, {
            location: `${_res.lat},${_res.lon},6200`,
          }));
        });
      }, 'Api.SearchButton.AutoSuggest');
    } else {
      console.log(`Search Field: ${res.locationQuery}`);
      this.actions.common.saveLocationQuery(res.locationQuery);

      this.actions.home.performAgentSearch(res.lat, res.lon, (err) => {
        this.setState({ loading: false });
        if (err) {
          return this.setState({ error: 'Unknown error' });
        }
        this.router.go(this.router.generateUrl(this.router.ROUTE_NAMES.AGENT_SEARCH, {
          location: `${res.lat},${res.lon},${6200}`,
        }));
      });
    }
  },

  setLatLng(lat, lng) {
    this.setState({ lat, lng });
  },

  renderSearchField() {
    return (
      <form className="search-form" onSubmit={this.searchSubmit}>
        <div className="input-group">
          <SearchField ref="SearchField" saleType={this.state.saleType} hideAddresses setLatLng={this.setLatLng} />
          <SubmitBtn className="btn-primary" spinner={this.state.loading} icon="icon-search" />
        </div>
      </form>
    );
  },

  renderSearchArea() {
    return (
      <div className={classNames('search-area')}>
        <div className="search-instruction-tab">Start your search</div>
        {this.renderSearchField()}
      </div>
    );
  },

  renderLanding() {
    return [
      <section className="hero">
        <div>
          <h1>Home is closer than you think</h1>
          <div className="subtitle">Find it with a local expert</div>
        </div>
        <div className="search-area-container">{this.renderSearchArea()}</div>
        {this.state.error
          ? <div className="error-message">{this.state.error}</div>
          : null}
      </section>,
      <section className="content">
        <div className="row">
          <div className="col-3-3">
            <h1 className="header">YOUR LOCAL HOME SEARCH</h1>
            <p className="description">Offered exclusively through local real estate professionals in markets nationwide. Knowledge is power, so you want to get it from the best. Find your amazing place today!</p>
          </div>
        </div>
        <div className="row selling-points">
          <div className="col-1-1 col-sm-1-3 selling-point">
            <img alt="Local agent" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-LOCAL.jpg" />
            <h2>Local expertise</h2>
            <p>With HomeASAP, you are just one click away from the expertise of a local real estate agent. Get the lowdown on neighborhoods, schools, and more from home pros who know it best.</p>
          </div>
          <div className="col-1-1 col-sm-1-3 selling-point">
            <img alt="Schedule showing" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-SEE_IT.jpg" />
            <h2>See it in-person</h2>
            <p>Found a listing you&apos;ve fallen in love with? Don&apos;t waste a minute – schedule a showing with the agent directly from their site.</p>
          </div>
          <div className="col-1-1 col-sm-1-3 selling-point">
            <img alt="Listing details" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-HIGHLIGHTS.jpg" />
            <h2>Vital details</h2>
            <p>Quickly get the information you want on any property: Beds, baths, square footage.  Is there a pool? Fireplace? We lay it all out in easy to read highlights. Want to know the ratings of nearby schools? We&apos;ve got that too!</p>
          </div>
        </div>
      </section>,
    ];
  },

  renderHeader() {
    const header = (
      <header>
        <div>
          <div className="about">
            <a href="https://about.homeasap.com" target="_blank">About</a>
          </div>
          <div className="contact-us">
            <a href="https://about.homeasap.com/contact/" target="_blank">Contact</a>
          </div>
        </div>
      </header>
    );
    return header;
  },

  renderFooter() {
    return (
      <footer className="homeasap-footer">
        <div className="homeasap-footer-links details">
          <a href="https://homeasap.com/agent/login" target="_blank">Agent Login</a>
          <span>&nbsp;|&nbsp;</span>
          <span className="text-transform-none text-block">
            &copy;
            {window.CURRENT_YEAR || ''}
            &nbsp;
            HomeASAP LLC
          </span>
          <span>&nbsp;·&nbsp;</span>
          <a
            role="button"
            tabIndex="-1"
            onClick={this.actions.common.toggleModal.bind(null, 'Terms')}
          >
            Terms
          </a>
          <span>&nbsp;·&nbsp;</span>
          <a
            role="button"
            tabIndex="-1"
            onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}
          >
            Privacy Policy
          </a>
          <span>&nbsp;·&nbsp;</span>
          <a href="/search/ADA" target="_blank">Accessibility</a>
          <span>&nbsp;·&nbsp;</span>
          <a href="https://about.homeasap.com" target="_blank">About</a>
        </div>
        <div className="homeasap-footer-powered" />
      </footer>
    );
  },

  render() {
    return (
      <div className={classNames('home-agent-search')}>
        {this.renderHeader()}
        {this.renderLanding()}
        {this.renderFooter()}
      </div>
    );
  },

});
