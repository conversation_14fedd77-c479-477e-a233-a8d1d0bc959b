const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.results-count',

  mixins: [
    mixins.debug, mixins.actions, mixins.pureRender, mixins.cursors,
  ],

  cursors: {
    listings: ['panels', 'listings', 'data'],
    listingCount: ['panels', 'listings', 'lastFullListingCount'],
    listingsSpinner: ['panels', 'listings', 'meta', 'spinner'],
  },

  help() {
    this.actions.common.showResultsHelp();
  },

  goToMap() {
    this.actions.map.onNav();
  },

  render() {
    if (this.state.listingsSpinner) {
      return null;
    }

    if (this.state.listings === false || !this.state.listingCount) {
      return (
        <div className="results-count cards-width">
          <p><span className="showing-count"> No homes found </span></p>
          {
            this.props.children
          }
        </div>
      );
    }

    if (this.props.condensed) {
      return (
        <div className="results-count cards-width condensed">
          <p>
            Showing&nbsp;
            <span className="showing-count">
              {this.state.listings && this.state.listings.length || 0}
              &nbsp;Homes
            </span>
          </p>
          {
            this.props.children
          }
        </div>
      );
    }
    return (
      <div className="results-count cards-width">
        <p>
          Showing&nbsp;
          <span className="showing-count">
            {this.state.listings && this.state.listings.length || 0}
            &nbsp;Homes
          </span>
          &nbsp;
          based on your
          &nbsp;
          <a role="button" tabIndex="-1" onClick={this.goToMap}>map view</a>
        </p>
        {
          this.props.children
        }
      </div>
    );
  },
});
