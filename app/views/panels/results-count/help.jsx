const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.results-count.help',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    showResultsHelp: ['layout', 'showResultsHelp'],
  },
  show() {
    this.actions.common.showResultsHelp();
  },
  hide() {
    this.actions.common.hideResultsHelp();
  },
  render() {
    console.log(`showBrokerage${this.state.showBrokerage}`);
    if (!this.state.showResultsHelp) {
      return null;
    }
    return (
      <Modal show={this.state.showResultsHelp} onHide={this.hide} className="results-help col-lg-4 col-md-5 col-sm-14 colxs-24" backdrop={false}>
        <button type="button" className="btn btn-default btn-sm close-button pull-right" onClick={this.hide}>Close</button>
        <div className="help">
          Let&apos;s face it, searching for a home can be challenging. We&apos;re here to help. We are showing you the best matches based on the information you give us. To get better results apply search filters or refine your search area.
        </div>
      </Modal>
    );
  },
});
