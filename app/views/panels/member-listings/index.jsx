const React = require('react');
const classnames = require('classnames');
const mixins = require('../../../lib/mixins');
const SpinnerRound = require('../../components/spinner_round');
const LazyLoad = require('../../../thirdparty/react-lazy-load/LazyLoad');
const Card = require('./card');
const Sort = require('../../panels-mobile/sort');

module.exports = React.createClass({

  displayName: 'panels.member-listings',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils],

  cursors: {
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    listings: ['panels', 'listings', 'data'],
  },

  render() {
    if (this.state.spinner) {
      return (
        <div className={classnames('member-listings-panel with-bg')}>
          <div className="pt30">
            <SpinnerRound />
          </div>
        </div>
      );
    }

    if (this.state.listings && this.state.listings.length === 0) {
      return (
        <div className={classnames('member-listings-panel with-bg')}>
          <p>No results, please enter a different location above.</p>
        </div>
      );
    }

    if (!this.state.listings) {
      return (
        <div className={classnames('member-listings-panel with-bg')}>
          <p>Find Directory Member Properties Nationwide – Start Your Search Now!</p>
        </div>
      );
    }

    return (
      <div className={classnames('member-listings-panel')}>
        {
          this.utils.useMobileSite()
            ? (
              <div className="member-listings-sort">
                <Sort />
              </div>
            ) : null
        }
        {
          this.state.listings.map((l) => (
            <LazyLoad key={l.Id} className="member-listings-card" height="250" buffer={600}>
              <Card key={l.Id} listing={l} />
            </LazyLoad>
          ))
        }
      </div>
    );
  },

});
