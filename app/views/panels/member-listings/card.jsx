const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panels.member-listings.card',

  mixins: [mixins.debug, mixins.cursors, mixins.utils],

  openListing() {
    const {
      ListingAgent,
      CoAgent,
      AlternativeOfficeAgent,
    } = this.props.listing;

    const { MembershipId: ListingMembershipId } = ListingAgent || {};
    const { MembershipId: CoAgentMembershipId } = CoAgent || {};
    const { MembershipId: AlternativeOfficeAgentMembershipId } = AlternativeOfficeAgent || {};

    window.open(`/${(ListingMembershipId || CoAgentMembershipId || AlternativeOfficeAgentMembershipId)
      ? `${ListingMembershipId || CoAgentMembershipId || AlternativeOfficeAgentMembershipId}/featured`
      : 'listing'}/${this.props.listing.Id}?nob=1`, '_blank');
  },

  render() {
    if (!this.props.listing) {
      return null;
    }

    const {
      ImageCount,
      ListPrice,
      TotalBedrooms,
      TotalBaths,
      LivingSquareFeet,
      ListingAgent,
      CoAgent,
      AlternativeOfficeAgent,
      Image,
      StreetAddress,
      CityName,
      State,
      ZipCode,
    } = this.props.listing;

    const { Name: ListingAgentName, ProfileImageUrl: ListingAgentProfileImageUrl, MembershipId: ListingMembershipId } = ListingAgent || {};
    const { Name: CoAgentName, ProfileImageUrl: CoAgentProfileImageUrl, MembershipId: CoAgentMembershipId } = CoAgent || {};
    const { Name: AlternativeOfficeAgentName, ProfileImageUrl: AlternativeOfficeAgentProfileImageUrl, MembershipId: AlternativeOfficeAgentMembershipId } = AlternativeOfficeAgent || {};

    let agentListedBy = 'Listed by';
    let agentName = ListingAgentName;
    let agentProfileImage = 'https://nplayassets.blob.core.windows.net/images/faceless-user.png';

    if (AlternativeOfficeAgentMembershipId) {
      agentListedBy = 'Presented by';
      agentName = AlternativeOfficeAgentName;
      agentProfileImage = AlternativeOfficeAgentProfileImageUrl || agentProfileImage;
    } else if (ListingMembershipId) {
      agentName = ListingAgentName;
      agentProfileImage = ListingAgentProfileImageUrl || agentProfileImage;
    } else if (CoAgentMembershipId) {
      agentName = CoAgentName;
      agentProfileImage = CoAgentProfileImageUrl || agentProfileImage;
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        onClick={this.openListing}
        title={`${StreetAddress}, ${CityName}, ${State} ${ZipCode}`}
      >
        <div className="member-listings-card-top" style={{ backgroundImage: `url('${Image || this.utils.streetViewNotAvailableImage()}')` }}>
          <div className="member-listings-card-top-mask" />
          <div className="member-listings-card-top-content">
            {ImageCount > 1 ? <label>{`${ImageCount} photos`}</label> : null}

            <div className="member-listings-card-top-content-bottom">
              <p>
                <span className="price">{`${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(ListPrice)}`}</span>
                <br />
                <small className="listed-by">{`${agentListedBy} ${agentName}`}</small>
                <img
                  className="agent-image"
                  alt={agentName}
                  title={agentName}
                  src={agentProfileImage}
                />
              </p>
            </div>
          </div>
        </div>
        <div className="member-listings-card-bottom">
          <div className="member-listings-card-bottom-attributes">
            <p>{`${TotalBedrooms || '-'} Bed${(TotalBedrooms || 0) > 1 ? 's' : ''}`}</p>
            <p>{`${TotalBaths || '-'} Bath${(TotalBaths || 0) > 1 ? 's' : ''}`}</p>
            <p>{`${this.utils.addThousandSep(LivingSquareFeet) || '-'} sq.f.`}</p>
          </div>
          <p className="member-listings-card-bottom-address">
            {`${StreetAddress}, ${CityName}${this.utils.useMobileSite() ? `, ${State} ${ZipCode}` : ''}`}
          </p>
        </div>
      </div>
    );
  },

});
