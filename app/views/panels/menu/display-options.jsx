const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SquareIconBtn = require('../../components/square_icon_btn');

module.exports = React.createClass({

  displayName: 'panel.menu.display-option',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  getInitialState() {
    return {
      displayOptions: {},
    };
  },

  cursors: {
    // data: ['panels','header','data']
  },

  displayOptionClicked(displayOption) {
    const displayOptions = this.state.displayOptions;
    displayOptions[displayOption] = !displayOptions[displayOption];
    this.setState({ displayOptions });
  },

  render() {
    return (
      <div className="display-options">
        <h2>Room Type</h2>
        <p className="text-muted">
          Choose an icon below to change the main image that appears in your search results. If you want to view your results via seeing the kitchen simply select the kitchen icon.
        </p>

        <div className="menu-left-item menu-left-display-options">
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.frontOfHouse })}
            icon="icon-front-of-house"
            text="Front of house"
            onClick={this.displayOptionClicked.bind(this, 'frontOfHouse')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.kitchen })}
            icon="icon-kitchen"
            text="Kitchen"
            onClick={this.displayOptionClicked.bind(this, 'kitchen')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.masterBed })}
            icon="icon-bedroom"
            text="Master bed"
            onClick={this.displayOptionClicked.bind(this, 'masterBed')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.masterBath })}
            icon="icon-bathroom"
            text="Master bath"
            onClick={this.displayOptionClicked.bind(this, 'masterBath')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.livingRoom })}
            icon="icon-living-room"
            text="Living room"
            onClick={this.displayOptionClicked.bind(this, 'livingRoom')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.backyard })}
            icon="icon-backyard"
            text="Backyard"
            onClick={this.displayOptionClicked.bind(this, 'backyard')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.garage })}
            icon="icon-garage"
            text="Garage"
            onClick={this.displayOptionClicked.bind(this, 'garage')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.studyOffice })}
            icon="icon-study-office"
            text="Study/Office"
            onClick={this.displayOptionClicked.bind(this, 'studyOffice')}
          />
          <SquareIconBtn
            className={classNames({ active: this.state.displayOptions.random })}
            icon="icon-random"
            text="Random"
            onClick={this.displayOptionClicked.bind(this, 'random')}
          />
        </div>
      </div>
    );
  },

});
