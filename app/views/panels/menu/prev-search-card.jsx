const React = require('react');
require('date-format-lite');
const mixins = require('../../../lib/mixins');

Date.am = 'am';
Date.pm = 'pm';

module.exports = React.createClass({

  displayName: 'panels.menu.prev-search-card',

  mixins: [mixins.debug, mixins.utils, mixins.actions, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    data: React.PropTypes.object,
  },

  clicked(e) {
    e.preventDefault();
    e.stopPropagation();

    const searchQuery = [
      this.props.data.Latitude,
      this.props.data.Longitude,
      this.props.data.Distance,
    ].join(',');
    const opts = this.utils._paramsToOpts(this.props.data.Filters);

    this.actions.menu.resumeSearchHistory(
      this.props.data.AgentId,
      this.props.data.CustomName || this.props.data.SearchText,
      searchQuery,
      opts,
      new Date(this.props.data.LastSearchDate),
      this.props.data.Theme,
      this.props.data.RateplugBuyerId,
    );
  },

  render() {
    const date = new Date(this.props.data.LastSearchDate);

    return (
      <div className="prev-search-card">
        <h4>
          <a
            role="button"
            tabIndex="-1"
            onClick={this.clicked}
          >
            {this.props.data.CustomName || this.props.data.SearchText}
          </a>
        </h4>
        <p>
          <small>
            <span>
              {this.props.data.ResultsCount}
              &nbsp;
              search results
            </span>
            &nbsp;:&nbsp;
            <em>
              {
                date ? date.format('H:mmA M-D-YYYY') : ''
              }
            </em>
          </small>
        </p>
      </div>
    );
  },

});
