const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.levels',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    minStories: ['shared', 'menu', 'minStories'],
  },

  componentWillMount() {
    this.dropdownData = DropdownData.storiesOptions;

    this.dropdownDataArray = map(this.dropdownData, (obj) => obj.name);

    this.dropdownNameToValue = function (name) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.name == name) {
          return obj.value;
        }
      }
    };

    this.dropdownValueToName = function (val) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.value == val) {
          return obj.name;
        }
      }
    };
  },

  getCurrentItem1() {
    if (this.state.minStories) {
      return this.dropdownValueToName(this.state.minStories);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdownNameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minStories', value }]);
  },

  shouldBeActive1(item) {
    const value = this.dropdownNameToValue(item) || null;
    if (value === this.state.minStories) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minStories) {
      return `${this.dropdownValueToName(this.state.minStories)} Levels`;
    }

    return '0+ Levels';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Levels"
        ref="levels"
        displayInPlace
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray}
        currentItem1={this.getCurrentItem1()}
        displayString={this.displayFunction}
      />
    );
  },

});
