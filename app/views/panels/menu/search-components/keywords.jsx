import { Creatable } from 'react-select';

const React = require('react');
const _ = require('lodash');
const mixins = require('../../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.keywords',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    keywords: ['shared', 'menu', 'keywords'],
  },

  keywordChanged(keywords) {
    const keywordValues = _.map(keywords, (keyword) => keyword.value);
    this.actions.menu.updateSearchField([{ key: 'keywords', value: keywordValues }]);
  },

  createKeywordsObject() {
    const keywords = _.isEmpty(this.state.keywords)
      ? []
      : _.map(this.state.keywords, (value) => ({ value, label: value }));

    return keywords;
  },

  handleBlur() {
    const { inputValue } = this.selector;
    this.selector.createNewOption(inputValue);
  },

  render() {
    return (
      <Creatable
        className="keywords-select"
        ref={(s) => {
          this.selector = s;
        }}
        value={this.createKeywordsObject()}
        delimiter=","
        onBlur={this.handleBlur}
        onCloseResetsInput={false}
        isOptionUnique={(opt) => {
          const option = opt.option;
          const options = opt.options;
          const labelKey = opt.labelKey;
          const valueKey = opt.valueKey;
          if (_.isEmpty(options)) {
            return true;
          }
          return options.filter((existingOption) => existingOption[labelKey] === option[labelKey] || existingOption[valueKey] === option[valueKey]).length === 0;
        }}
        onBlurResetsInput={false}
        multi
        allowCreate
        placeholder="Keyword search: fireplace, pool, etc."
        onChange={this.keywordChanged}
      />
    );
  },

});
