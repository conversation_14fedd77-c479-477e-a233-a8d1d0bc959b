const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.down-payment',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
    downPayment: ['shared', 'menu', 'downPayment'],
  },

  componentWillMount() {
    this.updateDropdownData(1);
  },

  componentDidMount() {
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.saleType !== this.state.saleType
      || nextState.downPayment !== this.state.downPayment) {
      return true;
    }

    return false;
  },

  updateDropdownData() {
    this.dropdownData1 = DropdownData.downpaymentOptions;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return `$${this.utils.addThousandSepIfNumber(val)}`;
    };
  },

  getCurrentItem1() {
    if (this.state.downPayment) {
      return this.state.downPayment;
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'downPayment', value }]);
  },

  onBlurInput1(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'downPayment',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.downPayment) {
      return true;
    }

    return false;
  },

  shouldBeDisabled1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (this.state.maxMortgagePayment && value && value >= this.state.maxMortgagePayment) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.downPayment) {
      return this.dropdown1ValueToName(this.state.downPayment);
    }

    return '$0';
  },

  render() {
    // Only show when coming from rateplug for now
    if (!window.rateplug.rp_buyer && !this.actions.common.getOnboardingAgentHasIDXPlus()) {
      return null;
    }

    if (this.state.saleType === 2) {
      return null;
    }

    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Down Payment"
        ref="custom-dropdown"
        displayInPlace
        inputTitle1="down payment"
        inputType="number"
        shouldBeActive1={this.shouldBeActive1}
        shouldBeDisabled1={this.shouldBeDisabled1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        onBlurInput1={this.onBlurInput1}
        displayString={() => {
          let output = this.displayFunction();
          if (!output.match(/Down Payment/i)) {
            output = (
              <span>
                Down Payment
                <span className="custom-dropdown-display-value">
                  :&nbsp;
                  {output}
                </span>
              </span>
            );
          }
          return output;
        }}
      />
    );
  },

});
