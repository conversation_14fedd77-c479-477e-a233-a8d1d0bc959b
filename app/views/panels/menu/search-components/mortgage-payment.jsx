const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.mortgage-payment',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
    downPayment: ['shared', 'menu', 'downPayment'],
    minMortgagePayment: ['shared', 'menu', 'minMortgagePayment'],
    maxMortgagePayment: ['shared', 'menu', 'maxMortgagePayment'],
  },

  componentWillMount() {
    this.updateDropdownData(1);
  },

  componentDidMount() {
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.saleType !== this.state.saleType
      || nextState.downPayment !== this.state.downPayment
      || nextState.minMortgagePayment !== this.state.minMortgagePayment
      || nextState.maxMortgagePayment !== this.state.maxMortgagePayment) {
      return true;
    }

    return false;
  },

  updateDropdownData() {
    this.dropdownData1 = DropdownData.rentPriceOptions1;
    this.dropdownData2 = DropdownData.rentPriceOptions2;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);
    this.dropdownDataArray2 = map(this.dropdownData2, (obj) => obj.name);

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return `$${this.utils.addThousandSepIfNumber(val)}`;
    };

    this.dropdown2NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown2ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return `$${this.utils.addThousandSepIfNumber(val)}`;
    };
  },

  getCurrentItem1() {
    if (this.state.minMortgagePayment) {
      return this.state.minMortgagePayment;
    }

    return null;
  },

  getCurrentItem2() {
    if (this.state.maxMortgagePayment) {
      return this.state.maxMortgagePayment;
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minMortgagePayment', value }]);
  },

  onBlurInput1(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'minMortgagePayment',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.minMortgagePayment) {
      return true;
    }

    return false;
  },

  shouldBeDisabled1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (this.state.maxMortgagePayment && value && value >= this.state.maxMortgagePayment) {
      return true;
    }

    return false;
  },

  onOptionChange2(item) {
    if (item.indexOf('Any') !== -1) {
      this.actions.menu.updateSearchField([
        // {key: 'minMortgagePayment', value: null},
        { key: 'maxMortgagePayment', value: null }]);
      return;
    }

    const value = this.dropdown2NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'maxMortgagePayment', value }]);
  },

  onBlurInput2(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'maxMortgagePayment',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (value === this.state.maxMortgagePayment) {
      return true;
    }

    return false;
  },

  shouldBeDisabled2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (this.state.minMortgagePayment && value && value <= this.state.minMortgagePayment) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minMortgagePayment && this.state.maxMortgagePayment) {
      let output = (`${this.dropdown1ValueToName(this.state.minMortgagePayment)
      } - ${this.dropdown2ValueToName(this.state.maxMortgagePayment)}`);
      output = output.replace('+ -', ' -');

      return output;
    }

    if (this.state.minMortgagePayment) {
      return this.dropdown1ValueToName(this.state.minMortgagePayment);
    }

    if (this.state.maxMortgagePayment) {
      return `$0 - ${this.dropdown2ValueToName(this.state.maxMortgagePayment)}`;
    }

    return 'Any Payment Range';
  },

  render() {
    // Only show when coming from rateplug for now
    if (!window.rateplug.rp_buyer && !this.actions.common.getOnboardingAgentHasIDXPlus()) {
      return null;
    }

    if (this.state.saleType === 2) {
      return null;
    }

    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Payment Range"
        ref="custom-dropdown"
        displayInPlace
        inputTitle1="min payment"
        inputTitle2="max payment"
        inputType="number"
        shouldBeActive1={this.shouldBeActive1}
        shouldBeDisabled1={this.shouldBeDisabled1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        onBlurInput1={this.onBlurInput1}
        shouldBeActive2={this.shouldBeActive2}
        shouldBeDisabled2={this.shouldBeDisabled2}
        onOptionChange2={this.onOptionChange2}
        options2={this.dropdownDataArray2}
        currentItem2={this.getCurrentItem2()}
        onBlurInput2={this.onBlurInput2}
        displayString={() => {
          let output = this.displayFunction();
          if (!output.match(/Payment Range/i)) {
            output = (
              <span>
                Payment Range
                <span className="custom-dropdown-display-value">
                  :&nbsp;
                  {output}
                </span>
              </span>
            );
          }
          return output;
        }}
      />
    );
  },

});
