const React = require('react');
const classNames = require('classnames');
const { Button } = require('react-bootstrap');
const mixins = require('../../../../lib/mixins');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.active-toggle',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    statusEq: ['shared', 'menu', 'statusEq'],
  },

  componentWillMount() {
  },

  onClick() {
    this.actions.menu.updateSearchField([{
      key: 'statusEq',
      value: this.state.statusEq !== 'A' ? 'A' : null,
    }]);
  },

  render() {
    return (
      <div className={classNames('active-toggle', this.props.className)}>
        <Button type="button" onClick={this.onClick}>
          <span className={classNames({ 'text-primary': this.state.statusEq === 'A' })}>Hide Pending/Contingent</span>
          {this.state.statusEq === 'A' ? <SVGIcon name="icon-checkbox-checked" />
            : <SVGIcon name="icon-checkbox-unchecked" className="icon-checkbox-unchecked" />}
        </Button>
      </div>
    );
  },

});
