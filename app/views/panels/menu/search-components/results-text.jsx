const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const SpinnerRound = require('../../../components/spinner_round');
const ResultsCount = require('../../results-count/index');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.results-text',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  facets: {
    listingCount: ['listingCount'],

  },
  render() {
    return (
      <div className={classNames('search-result', this.props.className)}>
        {
          this.state.listingCount !== false
            ? <ResultsCount condensed />
            : <SpinnerRound className="ml15 mr15" />
          }
      </div>
    );
  },

});
