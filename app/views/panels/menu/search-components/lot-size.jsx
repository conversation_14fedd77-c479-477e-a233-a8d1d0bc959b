const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.lot-size',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    minLotSizeSqft: ['shared', 'menu', 'minLotSizeSqft'],
    maxLotSizeSqft: ['shared', 'menu', 'maxLotSizeSqft'],
  },

  componentWillMount() {
    this.dropdownData1 = DropdownData.lotsizeOptions1;
    this.dropdownData2 = DropdownData.lotsizeOptions2;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);
    this.dropdownDataArray2 = map(this.dropdownData2, (obj) => obj.name);

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
    };

    this.dropdown2NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown2ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
    };
  },

  getCurrentItem1() {
    if (this.state.minLotSizeSqft) {
      return this.dropdown1ValueToName(this.state.minLotSizeSqft);
    }

    return null;
  },

  getCurrentItem2() {
    if (this.state.maxLotSizeSqft) {
      return this.dropdown2ValueToName(this.state.maxLotSizeSqft);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minLotSizeSqft', value }]);
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.minLotSizeSqft) {
      return true;
    }

    return false;
  },

  onOptionChange2(item) {
    if (item.indexOf('Any') !== -1) {
      this.actions.menu.updateSearchField([
        // {key: 'minLotSizeSqft', value: null},
        { key: 'maxLotSizeSqft', value: null }]);
      return;
    }

    const value = this.dropdown2NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'maxLotSizeSqft', value }]);
  },

  shouldBeActive2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (value === this.state.maxLotSizeSqft) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minLotSizeSqft && this.state.maxLotSizeSqft) {
      let output = (`${this.dropdown1ValueToName(this.state.minLotSizeSqft)
      } - ${this.dropdown2ValueToName(this.state.maxLotSizeSqft)}`);
      output = output.replace('+ -', ' -');
      if ((output.match(/\ssq\sft/g) || []).length > 1 && output.indexOf('acre') === -1) {
        output = output.replace(/\ssq\sft/, '');
      }
      if ((output.match(/\sacre/g) || []).length > 1) {
        output = output.replace(/\sacres?/, '');
      }
      return output;
    }

    if (this.state.minLotSizeSqft) {
      return this.dropdown1ValueToName(this.state.minLotSizeSqft);
    }

    if (this.state.maxLotSizeSqft) {
      return this.dropdown2ValueToName(this.state.maxLotSizeSqft);
    }

    return 'Any Lot Size';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Lot Size"
        ref="Lot Size"
        displayInPlace
        inputTitle1="min lot size"
        inputTitle2="max lot size"
        inputReadOnly
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        shouldBeActive2={this.shouldBeActive2}
        onOptionChange2={this.onOptionChange2}
        options2={this.dropdownDataArray2}
        currentItem2={this.getCurrentItem2()}
        displayString={this.displayFunction}
      />
    );
  },

});
