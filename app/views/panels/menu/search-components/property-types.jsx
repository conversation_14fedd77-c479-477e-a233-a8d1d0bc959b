const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../../lib/mixins');
const CustomDropdownMultiselect = require('../../../components/custom_dropdown_multiselect');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.property-types',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    homeTypesArray: ['shared', 'menu', 'homeTypesArray'],
  },

  componentWillMount() {
    this.dropdownData = DropdownData.homeTypeOptions;

    this.dropdownDataArray = map(this.dropdownData, (obj) => obj.name);

    this.dropdownNamesToValues = function (names) {
      const values = [];
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (names.indexOf(obj.name) !== -1) {
          values.push(obj.value);
        }
      }
      return values;
    };

    this.dropdownValuesToNames = function (vals) {
      const names = [];
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (vals.indexOf(obj.value) !== -1) {
          names.push(obj.name);
        }
      }
      return names;
    };
  },

  onOptionChange(items) {
    if (items) {
      const values = this.dropdownNamesToValues(items) || null;
      console.log(values);
      this.actions.menu.updateSearchField([{ key: 'homeTypesArray', value: values }]);
    } else {
      this.actions.menu.updateSearchField([{ key: 'homeTypesArray', value: [] }]);
    }
  },

  getSelectedOptions() {
    const selectedOptions = this.dropdownValuesToNames(this.state.homeTypesArray);

    console.log(selectedOptions);

    return selectedOptions;
  },

  displayFunction() {
    if (this.state.homeTypesArray && this.state.homeTypesArray.length > 0) {
      return this.dropdownValuesToNames(this.state.homeTypesArray).join(', ');
    }

    return 'Property Type';
  },

  render() {
    return (
      <CustomDropdownMultiselect
        className={this.props.className}
        title="Property type"
        ref="HomeType"
        onOptionChange={this.onOptionChange}
        displayString={this.displayFunction}
        options={this.dropdownDataArray}
        selectedOptions={this.getSelectedOptions()}
        displayInPlace
      />
    );
  },

});
