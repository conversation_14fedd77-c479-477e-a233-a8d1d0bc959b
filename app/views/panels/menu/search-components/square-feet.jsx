const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.square-feet',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    minLivingSqft: ['shared', 'menu', 'minLivingSqft'],
    maxLivingSqft: ['shared', 'menu', 'maxLivingSqft'],
  },

  componentWillMount() {
    this.dropdownData1 = DropdownData.sqftOptions1;
    this.dropdownData2 = DropdownData.sqftOptions2;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);
    this.dropdownDataArray2 = map(this.dropdownData2, (obj) => obj.name);

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
      return parseInt(this.utils.cleanNumber(name), 10) || null;
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return val;
    };

    this.dropdown2NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown2ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return val;
    };
  },

  getCurrentItem1() {
    if (this.state.minLivingSqft) {
      return this.dropdown1ValueToName(this.state.minLivingSqft);
    }

    return null;
  },

  getCurrentItem2() {
    if (this.state.maxLivingSqft) {
      return this.dropdown2ValueToName(this.state.maxLivingSqft);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minLivingSqft', value }]);
  },

  onBlurInput1(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'minLivingSqft',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.minLivingSqft) {
      return true;
    }

    return false;
  },

  shouldBeDisabled1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (this.state.maxLivingSqft && value && value >= this.state.maxLivingSqft) {
      return true;
    }

    return false;
  },

  onOptionChange2(item) {
    if (item.indexOf('Any') !== -1) {
      this.actions.menu.updateSearchField([
        // {key: 'minLivingSqft', value: null},
        { key: 'maxLivingSqft', value: null }]);
      return;
    }

    const value = this.dropdown2NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'maxLivingSqft', value }]);
  },

  onBlurInput2(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'maxLivingSqft',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (value === this.state.maxLivingSqft) {
      return true;
    }

    return false;
  },

  shouldBeDisabled2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (this.state.minLivingSqft && value && value <= this.state.minLivingSqft) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minLivingSqft && this.state.maxLivingSqft) {
      let output = (`${this.dropdown1ValueToName(this.state.minLivingSqft)
      } - ${this.dropdown2ValueToName(this.state.maxLivingSqft)}`);
      output = output.replace('+ -', ' -');

      return `${output} sq ft`;
    }

    if (this.state.minLivingSqft) {
      return `${this.dropdown1ValueToName(this.state.minLivingSqft)} sq ft`;
    }

    if (this.state.maxLivingSqft) {
      return `${this.dropdown2ValueToName(this.state.maxLivingSqft)} sq ft`;
    }

    return 'Any Square Feet';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Square Feet"
        ref="Square Feet"
        displayInPlace
        inputTitle1="min sqft"
        inputTitle2="max sqft"
        inputType="number"
        shouldBeActive1={this.shouldBeActive1}
        shouldBeDisabled1={this.shouldBeDisabled1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        onBlurInput1={this.onBlurInput1}
        shouldBeActive2={this.shouldBeActive2}
        shouldBeDisabled2={this.shouldBeDisabled2}
        onOptionChange2={this.onOptionChange2}
        options2={this.dropdownDataArray2}
        currentItem2={this.getCurrentItem2()}
        onBlurInput2={this.onBlurInput2}
        displayString={this.displayFunction}
      />
    );
  },

});
