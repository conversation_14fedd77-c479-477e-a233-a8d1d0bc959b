const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.price',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
    minPrice: ['shared', 'menu', 'minPrice'],
    maxPrice: ['shared', 'menu', 'maxPrice'],
  },

  componentWillMount() {
    this.updateDropdownData(1);
  },

  componentDidMount() {
    if (this.state.saleType !== 1) {
      this.updateDropdownData(this.state.saleType);
    }
  },

  shouldComponentUpdate(nextProps, nextState) {
    if (nextState.saleType !== this.state.saleType) {
      this.updateDropdownData(nextState.saleType);
    }

    if (nextState.saleType !== this.state.saleType
      || nextState.minPrice !== this.state.minPrice
      || nextState.maxPrice !== this.state.maxPrice) {
      return true;
    }

    return false;
  },

  updateDropdownData(saleType) {
    this.dropdownData1 = saleType === 2 ? DropdownData.rentPriceOptions1 : DropdownData.priceOptions1;
    this.dropdownData2 = saleType === 2 ? DropdownData.rentPriceOptions2 : DropdownData.priceOptions2;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);
    this.dropdownDataArray2 = map(this.dropdownData2, (obj) => obj.name);

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return `$${this.utils.addThousandSepIfNumber(val)}`;
    };

    this.dropdown2NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown2ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return `$${this.utils.addThousandSepIfNumber(val)}`;
    };
  },

  getCurrentItem1() {
    if (this.state.minPrice) {
      return this.state.minPrice;
    }

    return null;
  },

  getCurrentItem2() {
    if (this.state.maxPrice) {
      return this.state.maxPrice;
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minPrice', value }]);
  },

  onBlurInput1(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'minPrice',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.minPrice) {
      return true;
    }

    return false;
  },

  shouldBeDisabled1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (this.state.maxPrice && value && value >= this.state.maxPrice) {
      return true;
    }

    return false;
  },

  onOptionChange2(item) {
    if (item.indexOf('Any') !== -1) {
      this.actions.menu.updateSearchField([
        // {key: 'minPrice', value: null},
        { key: 'maxPrice', value: null }]);
      return;
    }

    const value = this.dropdown2NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'maxPrice', value }]);
  },

  onBlurInput2(e) {
    if (this.utils.isNumeric(e.target.value)) {
      this.actions.menu.updateSearchField([{
        key: 'maxPrice',
        value: parseInt(this.utils.cleanNumber(e.target.value), 10),
      }]);
    }
  },

  shouldBeActive2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (value === this.state.maxPrice) {
      return true;
    }

    return false;
  },

  shouldBeDisabled2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (this.state.minPrice && value && value <= this.state.minPrice) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minPrice && this.state.maxPrice) {
      let output = (`${this.dropdown1ValueToName(this.state.minPrice)
      } - ${this.dropdown2ValueToName(this.state.maxPrice)}`);
      output = output.replace('+ -', ' -');

      return output;
    }

    if (this.state.minPrice) {
      return this.dropdown1ValueToName(this.state.minPrice);
    }

    if (this.state.maxPrice) {
      return `$0 - ${this.dropdown2ValueToName(this.state.maxPrice)}`;
    }

    return 'Any Price';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Price"
        ref="Price"
        displayInPlace
        inputTitle1="min price"
        inputTitle2="max price"
        inputType="number"
        shouldBeActive1={this.shouldBeActive1}
        shouldBeDisabled1={this.shouldBeDisabled1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        onBlurInput1={this.onBlurInput1}
        shouldBeActive2={this.shouldBeActive2}
        shouldBeDisabled2={this.shouldBeDisabled2}
        onOptionChange2={this.onOptionChange2}
        options2={this.dropdownDataArray2}
        currentItem2={this.getCurrentItem2()}
        onBlurInput2={this.onBlurInput2}
        displayString={this.displayFunction}
      />
    );
  },

});
