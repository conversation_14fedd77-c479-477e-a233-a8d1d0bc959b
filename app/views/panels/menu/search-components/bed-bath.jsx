const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.bed-bath',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    minBeds: ['shared', 'menu', 'minBeds'],
    minBaths: ['shared', 'menu', 'minBaths'],
  },

  componentWillMount() {
    this.dropdownData1 = DropdownData.bedOptions;
    this.dropdownData2 = DropdownData.bathOptions;

    this.dropdownDataArray1 = map(this.dropdownData1, (obj) => obj.name);
    this.dropdownDataArray2 = map(this.dropdownData2, (obj) => obj.name);

    this.dropdownDataArrayValues1 = map(this.dropdownData1, (obj) => Number(obj.value));
    this.dropdownDataArrayValues2 = map(this.dropdownData2, (obj) => Number(obj.value));

    this.dropdown1NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown1ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData1.length; i++) {
        const obj = this.dropdownData1[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
    };

    this.dropdown2NameToValue = function (name) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
    };

    this.dropdown2ValueToName = function (val) {
      for (let i = 0; i < this.dropdownData2.length; i++) {
        const obj = this.dropdownData2[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
    };
  },

  getCurrentItem1() {
    if (this.state.minBeds) {
      return this.dropdown1ValueToName(this.state.minBeds) || this.state.minBeds;
    }

    return null;
  },

  getCurrentItem2() {
    if (this.state.minBaths) {
      return this.dropdown2ValueToName(this.state.minBaths) || this.state.minBaths;
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdown1NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minBeds', value }]);
  },

  onBlurInput1(e) {
    if (this.utils.isNumeric(e.target.value)) {
      const beds = parseInt(this.utils.cleanNumber(e.target.value), 10);
      if (!isNaN(beds)) {
        this.actions.menu.updateSearchField([{
          key: 'minBeds',
          value: beds,
        }]);
        return;
      }
    }
    e.target.value = '';
  },

  shouldBeActive1(item) {
    const value = this.dropdown1NameToValue(item) || null;
    if (value === this.state.minBeds) {
      return true;
    }

    return false;
  },

  onOptionChange2(item) {
    const value = this.dropdown2NameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'minBaths', value }]);
  },

  onBlurInput2(e) {
    if (this.utils.isNumeric(e.target.value)) {
      const baths = Number(this.utils.cleanNumber(e.target.value));
      if (!isNaN(baths)) {
        this.actions.menu.updateSearchField([{
          key: 'minBaths',
          value: baths,
        }]);
        return;
      }
    }
    e.target.value = '';
  },

  shouldBeActive2(item) {
    const value = this.dropdown2NameToValue(item) || null;
    if (value === this.state.minBaths) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minBeds && this.state.minBaths) {
      const output = ''.concat(
        this.dropdown1ValueToName(this.state.minBeds) || this.state.minBeds,
        ' Beds / ',
        this.dropdown2ValueToName(this.state.minBaths) || this.state.minBaths,
        ' Baths',
      );
      return output;
    }

    if (this.state.minBeds) {
      return `${this.dropdown1ValueToName(this.state.minBeds) || this.state.minBeds} Beds`;
    }

    if (this.state.minBaths) {
      return `${this.dropdown2ValueToName(this.state.minBaths) || this.state.minBaths} Baths`;
    }

    return 'Beds / Baths';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Beds / Baths"
        ref="BedsBaths"
        displayInPlace
        inputTitle1="min beds"
        inputTitle2="min baths"
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        onBlurInput1={this.onBlurInput1}
        options1={this.dropdownDataArray1}
        currentItem1={this.getCurrentItem1()}
        shouldBeActive2={this.shouldBeActive2}
        onOptionChange2={this.onOptionChange2}
        onBlurInput2={this.onBlurInput2}
        options2={this.dropdownDataArray2}
        currentItem2={this.getCurrentItem2()}
        displayString={this.displayFunction}
      />
    );
  },

});
