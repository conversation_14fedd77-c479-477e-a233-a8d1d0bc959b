const React = require('react');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.search-by-payment-list-price',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.pureRender],

  componentWillMount() {
    this.dropdownData = [
      { name: 'Search by Payment', value: 'payment' },
      { name: 'Search by Listing Price', value: 'listprice' },
    ];

    this.dropdownDataArray = this.dropdownData.map((obj) => obj.name);
  },

  getCurrentItem() {
    if (window.rateplug && window.rateplug.rp_rate) {
      return 'Search by Payment';
    }
    return 'Search by Listing Price';
  },

  getAvailableOptions() {
    // Always return both options
    return ['Search by Payment', 'Search by Listing Price'];
  },

  onOptionChange(item) {
    if (item === 'Search by Listing Price') {
      this.switchToRegularSearch();
    } else if (item === 'Search by Payment') {
      this.switchToSearchByPayment();
    }
  },

  switchToRegularSearch() {
    this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-regular-search');
    window.sessionStorageAlias.setItem('SEARCH_BY_PAYMENT_QS', window.location.search);
    window.location.search = `theme=${document.body.dataset.theme || 'rateplug'}&saleType=1`;
  },

  switchToSearchByPayment() {
    this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-search-by-payment');
    const savedSearch = window.sessionStorageAlias && window.sessionStorageAlias.getItem('SEARCH_BY_PAYMENT_QS');
    if (savedSearch) {
      window.location.search = savedSearch;
    } else {
      // Default to payment search with current theme
      window.location.search = `theme=${document.body.dataset.theme || 'rateplug'}`;
    }
  },

  shouldShow() {
    return window.rateplug && typeof window.rateplug.rp_rate !== 'undefined';
  },

  render() {
    if (!this.shouldShow()) {
      return null;
    }

    const availableOptions = this.getAvailableOptions();

    // Always show both options when dropdown is visible
    if (availableOptions.length < 2) {
      return null;
    }

    return (
      <CustomDropdown
        ref="custom-dropdown"
        options={availableOptions}
        currentItem={this.getCurrentItem()}
        onOptionChange={this.onOptionChange}
        displayName="Search Type"
      />
    );
  },
});