const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.special-financing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  componentWillMount() {
    // Filter dropdown data to conditionally show VA special financing option
    this.dropdownData = DropdownData.specialFinancingOptions.filter((option) => {
      // Show VA option only if user is a veteran AND VA loan limits are available
      if (option.value === 'VA') {
        return window.rateplug
               && window.rateplug.rp_veteran
               && window.rateplug.rp_va_loan_limit;
      }
      // Show all other options
      return true;
    });

    this.dropdownDataArray = map(this.dropdownData, (obj) => obj.name);

    this.dropdownNameToValue = function (name) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.name == name) {
          return obj.value;
        }
      }
    };

    this.dropdownValueToName = function (val) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.value == val) {
          return obj.name;
        }
      }
    };
  },

  getCurrentItem1() {
    if (this.state.specialFinancing && this.state.specialFinancing.length > 0) {
      return this.dropdownValueToName(this.state.specialFinancing[0]);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.dropdownNameToValue(item) || null;

    this.actions.menu.updateSearchField([{ key: 'specialFinancing', value: [value] }]);
    this.actions.common.showRateplugSpecialFinancingModal();
  },

  shouldBeActive1(item) {
    const value = this.dropdownNameToValue(item) || null;
    if (value === (this.state.specialFinancing || [])[0]) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.specialFinancing && this.state.specialFinancing.length > 0) {
      return this.dropdownValueToName(this.state.specialFinancing[0]) || 'Special Financing (Highlight)';
    }

    return 'Special Financing (Highlight)';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Special Financing (Highlight)"
        ref="custom-dropdown"
        displayInPlace
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray}
        currentItem1={this.getCurrentItem1()}
        displayString={() => {
          const output = this.displayFunction() || '';
          return output;
        }}

      />
    );
  },

});
