const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.search-by-payment-listing-price',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  componentWillMount() {
    this.dropdownData = [
      { name: 'Search by Payment', value: 'payment' },
      { name: 'Search by Listing Price', value: 'list_price' },
    ];

    this.dropdownDataArray = this.dropdownData.map((obj) => obj.name);

    this.dropdownNameToValue = function (name) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.name === name) {
          return obj.value;
        }
      }
      return null;
    };

    this.dropdownValueToName = function (val) {
      for (let i = 0; i < this.dropdownData.length; i++) {
        const obj = this.dropdownData[i];
        if (obj.value === val) {
          return obj.name;
        }
      }
      return null;
    };
  },

  shouldShow() {
    // Show this dropdown ONLY when either condition is met:
    // - window.rateplug.rp_rate exists, OR
    // - SEARCH_BY_PAYMENT_QS key exists in session storage
    return !!(window.rateplug && window.rateplug.rp_rate)
           || !!window.sessionStorageAlias.getItem('SEARCH_BY_PAYMENT_QS');
  },

  getCurrentItem1() {
    // If window.rateplug.rp_rate exists → "Search by Payment" is selected
    // If window.rateplug.rp_rate does not exist → "Search by Listing Price" is selected
    if (window.rateplug && window.rateplug.rp_rate) {
      return this.dropdownValueToName('payment');
    }
    return this.dropdownValueToName('list_price');
  },

  onOptionChange1(item) {
    const value = this.dropdownNameToValue(item);

    if (value === 'payment') {
      this.switchToSearchByPayment();
    } else if (value === 'list_price') {
      this.switchToRegularSearch();
    }
  },

  shouldBeActive1(item) {
    const value = this.dropdownNameToValue(item);
    const currentValue = window.rateplug && window.rateplug.rp_rate ? 'payment' : 'list_price';
    return value === currentValue;
  },

  switchToRegularSearch() {
    this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-regular-search');
    window.sessionStorageAlias.setItem('SEARCH_BY_PAYMENT_QS', window.location.search);
    window.location.search = `theme=${document.body.dataset.theme || 'rateplug'}&saleType=1`;
  },

  switchToSearchByPayment() {
    this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-search-by-payment');
    window.location.search = window.sessionStorageAlias.getItem('SEARCH_BY_PAYMENT_QS');
  },

  displayFunction() {
    const currentItem = this.getCurrentItem1();
    return currentItem || 'Search Type';
  },

  render() {
    if (!this.shouldShow()) {
      return null;
    }

    return (
      <CustomDropdown
        className={classNames('dropdown-alt', this.props.className)}
        title="Search Type"
        ref="SearchByPaymentListingPrice"
        displayInPlace
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray}
        currentItem1={this.getCurrentItem1()}
        displayString={this.displayFunction}
      />
    );
  },

});
