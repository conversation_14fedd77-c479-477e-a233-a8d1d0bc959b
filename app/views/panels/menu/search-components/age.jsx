const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');
const CustomDropdown = require('../../../components/custom_dropdown');
const DropdownData = require('../dropdown-options-data');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.age',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    minYear: ['shared', 'menu', 'minYear'],
    maxYear: ['shared', 'menu', 'maxYear'],
  },

  componentWillMount() {
    this.dropdownData = DropdownData.ageOptions;

    this.dropdownDataArray = map(this.dropdownData, (obj) => obj.name);

    this.getYearFromString = function (age) {
      switch (age) {
        case 'Any Age':
          break;
        case 'New Construction':
          return new Date().getUTCFullYear(); // TODO: Pass in params for new construction
        case '0-5 years':
          return new Date().getUTCFullYear() - 5;
        case '0-10 years':
          return new Date().getUTCFullYear() - 10;
        case '0-20 years':
          return new Date().getUTCFullYear() - 20;
        case '0-50 years':
          return new Date().getUTCFullYear() - 50;
        case '51+ years':
          return new Date().getUTCFullYear() - 51;
        default:
          break;
      }
    };

    this.getStringFromYear = function (year) {
      if (!year) {
        return null;
      }

      const currentYear = new Date().getUTCFullYear();
      if (year < currentYear - 50) {
        return '51+ years';
      }
      if (year <= currentYear - 49) {
        return '0-50 years';
      }
      if (year <= currentYear - 20) {
        return '0-20 years';
      }
      if (year <= currentYear - 10) {
        return '0-10 years';
      }
      if (year <= currentYear - 5) {
        return '0-5 years';
      }
      if (year <= currentYear) {
        return 'New Construction';
      }
      return 'Any Age';
    };
  },

  getCurrentItem1() {
    if (this.state.minYear) {
      return this.getStringFromYear(this.state.minYear);
    }
    if (this.state.maxYear) {
      return this.getStringFromYear(this.state.maxYear);
    }

    return null;
  },

  onOptionChange1(item) {
    const value = this.getYearFromString(item) || null;

    if (!value) {
      this.actions.menu.updateSearchField([{ key: 'minYear', value: null }, { key: 'maxYear', value: null }]);
    } else if (value < new Date().getUTCFullYear() - 50) { // Max Year
      this.actions.menu.updateSearchField([{ key: 'minYear', value: null }, { key: 'maxYear', value }]);
    } else {
      this.actions.menu.updateSearchField([{ key: 'minYear', value }, { key: 'maxYear', value: null }]);
    }
  },

  shouldBeActive1(item) {
    const value = item || null;
    if (value === this.getStringFromYear(this.state.minYear)
      || value === this.getStringFromYear(this.state.maxYear)) {
      return true;
    }

    return false;
  },

  displayFunction() {
    if (this.state.minYear) {
      return this.getStringFromYear(this.state.minYear);
    }
    if (this.state.maxYear) {
      return this.getStringFromYear(this.state.maxYear);
    }

    return 'Any Age';
  },

  render() {
    return (
      <CustomDropdown
        className={classNames(this.props.className)}
        title="Age"
        ref="Age"
        displayInPlace
        shouldBeActive1={this.shouldBeActive1}
        onOptionChange1={this.onOptionChange1}
        options1={this.dropdownDataArray}
        currentItem1={this.getCurrentItem1()}
        displayString={this.displayFunction}
      />
    );
  },

});
