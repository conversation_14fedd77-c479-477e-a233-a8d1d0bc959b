const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const MapGridToggle = require('../../components/map_grid_toggle');

// Search Components
const SaleTypeDropdown = require('./search-components/sale-type');
const CarDropdown = require('./search-components/car');
const LevelsDropdown = require('./search-components/levels');
// const LotSizeDropdown = require('./search-components/lot-size');
const AgeDropdown = require('./search-components/age');
const SquareFeetDropdown = require('./search-components/square-feet');
const BedBathDropdown = require('./search-components/bed-bath');
const PriceDropdown = require('./search-components/price');
const MortgagePaymentDropdown = require('./search-components/mortgage-payment');
const DownPaymentDropdown = require('./search-components/down-payment');
const SpecialFinancingDropdown = require('./search-components/special-financing');
const PropertyTypesDropdown = require('./search-components/property-types');
const Keywords = require('./search-components/keywords');
const SearchField = require('./search-components/search-field');
const ResultsText = require('./search-components/results-text');

module.exports = React.createClass({

  displayName: 'panel.menu.edit-search',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router],

  cursors: {
    locationQuery: ['panels', 'listings', 'meta', 'locationQuery'],
  },

  menuClicked() {
    this.refs.SearchField.searchKeywordEntered(null, () => {
      this.actions.menu.toggle();
    });
  },

  resetSearch() {
    this.actions.menu.resetMenuSelections();
  },

  render() {
    return (
      <div className="edit-search">

        <div className="menu-left-item menu-left-search">
          <div className="row">

            <SaleTypeDropdown className="col-4-24 col-lg-4-24" />

            <SearchField ref="SearchField" className="col-10-24" />

            <PriceDropdown className="col-5-24 col-lg-5-24" />

            <DownPaymentDropdown className="col-5-24 col-lg-5-24" />

            <MortgagePaymentDropdown className="col-5-24 col-lg-5-24" />

            <BedBathDropdown className="col-5-24 col-lg-5-24 right-drop" />

          </div>

          <div className="more-options row">

            <p className="mt10 mb5 text-muted">
              To improve your search and user experience. narrow your search to 200 homes or less for each search.
            </p>

            <PropertyTypesDropdown className="col-1-3 col-lg-5-24" />

            <SquareFeetDropdown className="col-1-3 col-lg-5-24" />

            <AgeDropdown className="col-1-3 col-lg-3-24" />

            {/* <LotSizeDropdown className="col-1-3 col-lg-5-24" /> */}

            <LevelsDropdown className="col-1-3 col-lg-3-24" />

            <CarDropdown className="col-1-3 col-lg-3-24 right-drop" />

            <SpecialFinancingDropdown className="col-5-24 col-lg-5-24" />

          </div>

          <div className="row">

            <p className="mt10 mb5 text-muted">
              Use Keywords to find special features and amenities.
            </p>

            <Keywords />

          </div>

          <hr />

          <div className="row">

            <a role="button" tabIndex="-1" className="pull-left cursor-pointer clear-search" onClick={this.resetSearch}>
              <SVGIcon name="icon-reset" />
              <small>CLEAR SEARCH</small>
            </a>

            <a role="button" tabIndex="-1" className="btn btn-primary btn-lg pull-right view-results" onClick={this.menuClicked}>
              {this.state.locationQuery ? 'View my results' : 'Search'}
            </a>

            <ResultsText className="pull-right" />

            <div className="pull-right search-result" />

          </div>

          <div className="">
            <MapGridToggle />
          </div>

          <div className="row mt30" />
        </div>
      </div>
    );
  },

});
