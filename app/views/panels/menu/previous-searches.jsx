const React = require('react');
const mixins = require('../../../lib/mixins');
const SpinnerRound = require('../../components/spinner_round');
const PrevSearchCard = require('./prev-search-card.jsx');

module.exports = React.createClass({

  displayName: 'panel.menu',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    layout: ['layout', 'menu'],
    data: ['panels', 'previousSearches', 'data'],
    buyerData: ['shared', 'buyer', 'data'],
  },

  getInitialState() {
    return {
      ref: {
        spinner: false,
      },
      sort: {
        price: true,
      },
      displayOptions: {},
    };
  },

  componentDidMount() {
    if (!this.state.data) {
      this.actions.menu.updateSearchHistory();
    }
  },

  componentWillUpdate(nextProps, nextState) {
    if (!nextState.data && nextState.data !== null
      && nextState.buyerData && nextState.buyerData.FirstName) {
      this.actions.menu.updateSearchHistory();
    }
  },

  menuClicked() {
    this.actions.menu.toggle();
  },

  // move this to somewhere like common or utils and replace other areas that it shows up (like the login button on header)
  isLoggedIn() {
    return (this.state.buyerData && this.state.buyerData.FirstName);
  },

  loginClicked() {
    this.actions.analytics.sendEvent('login', 'start', 'menu');
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    let savedSearches;
    if (this.state.data === null) {
      savedSearches = (
        <center className="mt30 mb30">
          <SpinnerRound />
        </center>
      );
    } else if (this.state.data) {
      savedSearches = this.state.data.map((item) => (
        <PrevSearchCard
          key={item.Id}
          data={item}
        />
      ), this);
    } else if (!this.state.data) {
      if (this.isLoggedIn()) {
        savedSearches = (
          <center className="mt15 mb30">
            <p>-- No previous searches --</p>
          </center>
        );
      } else {
        savedSearches = (
          <center className="mt15 mb30">
            <p>
              <a role="button" tabIndex="-1" onClick={this.loginClicked}>Login to see your recent searches</a>
            </p>
          </center>
        );
      }
    }

    return (
      <div className="">
        <h3 className="text-center header">Your Searches</h3>
        {savedSearches}
      </div>
    );
  },

});
