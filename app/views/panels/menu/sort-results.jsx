const React = require('react');
const mixins = require('../../../lib/mixins');
const sortOptionsData = require('./sort-options-data');
const SquareIconBtn = require('../../components/square_icon_btn');

module.exports = React.createClass({

  displayName: 'panel.menu.room-type',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    sortType: ['shared', 'menu', 'sortType'],
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },

  sortClicked(sortCriteria) {
    this.actions.menu.toggleSortType(sortCriteria);
  },

  sortClickHelperDaysOnMarket() {
    this.sortClicked('daysOnMarket_asc');
  },
  sortClickHelperPrice() {
    this.sortClicked('price_asc');
  },
  sortClickHelperDistance() {
    this.sortClicked('distance_asc');
  },
  sortClickHelperYearBuilt() {
    this.sortClicked('yearBuilt_asc');
  },
  sortClickHelperSqft() {
    this.sortClicked('sqft_asc');
  },
  sortClickHelperBedrooms() {
    this.sortClicked('bedrooms_asc');
  },

  render() {
    return (
      <div className="sort-results">
        <h2>Sort Results</h2>
        <p className="text-muted">
          Choose an icon below to sort how your search results are ordered.
        </p>

        <div className="menu-left-item menu-left-sort">
          <SquareIconBtn
            active={this.state.sortType === 'daysOnMarket'}
            icon={sortOptionsData.daysOnMarket.icon}
            text={this.state.showDaysOnMarket ? sortOptionsData.daysOnMarket.displayName : 'Newest'}
            onClick={this.sortClickHelperDaysOnMarket}
          />
          <SquareIconBtn
            active={this.state.sortType === 'price'}
            icon={sortOptionsData.price.icon}
            text={sortOptionsData.price.displayName}
            onClick={this.sortClickHelperPrice}
          />
          <SquareIconBtn
            active={this.state.sortType === 'distance'}
            icon={sortOptionsData.distance.icon}
            text={sortOptionsData.distance.displayName}
            onClick={this.sortClickHelperDistance}
          />
          <SquareIconBtn
            active={this.state.sortType === 'yearBuilt'}
            icon={sortOptionsData.yearBuilt.icon}
            text={sortOptionsData.yearBuilt.displayName}
            onClick={this.sortClickHelperYearBuilt}
          />
          <SquareIconBtn
            active={this.state.sortType === 'sqft'}
            icon={sortOptionsData.sqft.icon}
            text={sortOptionsData.sqft.displayName}
            onClick={this.sortClickHelperSqft}
          />
          <SquareIconBtn
            active={this.state.sortType === 'bedrooms'}
            icon={sortOptionsData.bedrooms.icon}
            text={sortOptionsData.bedrooms.displayName}
            onClick={this.sortClickHelperBedrooms}
          />
        </div>
      </div>
    );
  },

});
