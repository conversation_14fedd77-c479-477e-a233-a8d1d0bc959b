const React = require('react');
const classNames = require('classnames');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');
const CloseButton = require('../../components/close_btn');
const ContactForm = require('./contact-form');

module.exports = React.createClass({
  displayName: 'panel.share',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    showShare: ['shared', 'shareForm', 'show'],
    buyerData: ['shared', 'buyer', 'data'],
    listingData: ['shared', 'shareForm', 'listingData'],
    agentData: ['shared', 'agent', 'data'],
  },
  show() {
    this.actions.common.showShareForm();
  },
  hide(clearData) {
    this.actions.common.hideShareForm(clearData);
  },

  checkLogin() {
    if (this.state.buyerData === null || (this.state.buyerData.Id == null && this.state.buyerData.id == null)) {
      this.hide(false);
      this.actions.login.startAction = 'share';
      this.actions.login.start({ action: 'email_share', loginButtonText: 'Join to share' }, this.loginCallBack);
      this.actions.analytics.sendEvent('login', 'start', 'share');
    }
  },

  loginCallBack(statusObj) {
    if (statusObj.status == 'Login successful') {
      this.show();
    }
  },

  getAddress() {
    const address = `${this.state.listingData.FullStreetAddress || this.state.listingData.Address.FullStreetAddress || ''} ${this.state.listingData.CityName || this.state.listingData.Address.CityName || ''}, ${this.state.listingData.State || this.state.listingData.Address.State || ''} ${this.state.listingData.ZipCode || this.state.listingData.Address.ZipCode || ''}`;
    return address;
  },

  render() {
    console.log(`${this.state.showShare}is show share`);
    if (!this.state.showShare) {
      return null;
    }
    return (
      <Modal
        show={this.state.showShare}
        className={classNames('share-modal col-lg-5 col-md-6 col-sm-16 colxs-24', {
          'share-modal-mobile': this.props.mobile,
        })}
      >
        <div className="share-container">
          <CloseButton onClick={this.hide} />
          <div className="header" />
          <div className="info">
            <div className="image-holder">
              <img alt="Your profile" src={this.state.buyerData ? (this.state.buyerData.id ? `https://graph.facebook.com/${this.state.buyerData.id}/picture?width=100&height=100` : '//nplayassets.blob.core.windows.net/search2/fb-login.png') : '//nplayassets.blob.core.windows.net/search2/1x1.png'} />
            </div>
            <p className="caption">Email This Property</p>
            {
              this.state.listingData
                ? (
                  <p className="address">
                    &nbsp;
                    {this.getAddress()}
                    &nbsp;
                  </p>
                ) : null
            }
            <p>
              To send these properties to someone via email, fill out the information below and click send.
            </p>
          </div>
          <ContactForm buyerData={this.state.buyerData} listingData={this.state.listingData} checkLogin={this.checkLogin} agentData={this.state.agentData} />
        </div>
      </Modal>
    );
  },
});
