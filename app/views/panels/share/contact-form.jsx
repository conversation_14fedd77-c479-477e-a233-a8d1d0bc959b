const React = require('react');
const forEach = require('lodash.foreach');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({
  displayName: 'panel.contact.form',
  message: '',
  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],
  getInitialState() {
    return {
      confirmation: false,
    };
  },
  componentDidMount() {
    /* getLocalStorageValues([{node:this.refs.MessageInput,value:"SM"},
    {node:this.refs.toEmailInput,value:"SE"},
    {node:this.refs.toNameInput,value:"SN"}]) */
    const message = this.actions.agent.getLocalStorage('SM');
    const email = this.actions.agent.getLocalStorage('SE');
    const name = this.actions.agent.getLocalStorage('SN');
    this.refs.MessageInput.value = message;
    this.refs.ToEmailInput.value = email;
    this.refs.ToNameInput.value = name;
  },
  getLocalStorageValues(items) {
    forEach(items, function (item) {
      const text = this.actions.agent.getLocalStorage(item.value);
      if (item.node) {
        item.node.value = text;
      }
    });
  },
  setLocalStorageValues(items) {
    forEach(items, function (item) {
      if (item.node) {
        this.actions.agent.setContactLocalStorage(item.type, item.value);
      }
    });
  },
  showConfirmation(res) {
    if (res !== 'success') {
      this.setState({ confirmation: 'failed' });
    } else {
      this.setState({ confirmation: 'success' });
    }
  },
  submitShare(e) {
    e.preventDefault();
    const messageInput = this.refs.MessageInput;
    const toEmailInput = this.refs.ToEmailInput;
    const toNameInput = this.refs.ToNameInput;
    if (!this.props.buyerData || !this.props.buyerData.FirstName || !this.props.buyerData.Email) {
      /* this.setLocalStorageValues({type:"SM", value:messageInput.value},
                               {type:"SE", value:toEmailInput.value},
                               {type:"SM", value:toNameInput.value}) */
      this.actions.agent.setContactLocalStorage('SM', messageInput.value);
      this.actions.agent.setContactLocalStorage('SE', toEmailInput.value);
      this.actions.agent.setContactLocalStorage('SN', toNameInput.value);
      this.props.checkLogin ? this.props.checkLogin() : '';
    } else {
      const emailInput = this.refs.BuyerEmailInput;
      this.actions.common.submitShare(toNameInput.value, toEmailInput.value, messageInput.value, this.props.buyerData.FirstName, emailInput.value, this.props.listingData.Id, this.props.agentData.Id, this.showConfirmation);
      this.resetLocalStorage();
      this.actions.analytics.sendEvent('share', 'send', this.props.listingData.ZipCode);
    }
  },
  handleChange(event) {
    this.setState({ value: event.target.value });
  },
  resetLocalStorage() {
    this.actions.agent.setContactLocalStorage('SM', '');
    this.actions.agent.setContactLocalStorage('SE', '');
    this.actions.agent.setContactLocalStorage('SN', '');
  },

  render() {
    return (
      <div>
        {!this.state.confirmation
          ? (
            <form className="share-form" onSubmit={this.submitShare}>
              <div className="input-group">
                <div className="row">
                  <div className="col-sm-12 col-xs-24">
                    {this.props.buyerData && this.props.buyerData.FirstName ? (
                      <input
                        className="form-control mb5 offset"
                        placeholder="Your Name"
                        type="text"
                        ref="BuyerNameInput"
                        value={this.props.buyerData.FirstName}
                      />
                    ) : null}
                  </div>
                  <div className="col-sm-12 col-xs-24">
                    {this.props.buyerData && this.props.buyerData.Email ? (
                      <input
                        className="form-control mb5"
                        placeholder="Your Email"
                        type="email"
                        required
                        ref="BuyerEmailInput"
                        defaultValue={this.props.buyerData.Email}
                        onChange={this.handleChange}
                      />
                    ) : null}
                  </div>
                </div>
                <div className="row">
                  <div className="col-sm-12 col-xs-24">
                    <input
                      className="form-control mb5 offset"
                      placeholder="To Name"
                      type="text"
                      ref="ToNameInput"
                      onChange={this.handleChange}
                    />
                  </div>
                  <div className="col-sm-12 col-xs-24">
                    <input
                      className="form-control mb5"
                      placeholder="To Email"
                      type="email"
                      required
                      ref="ToEmailInput"
                      onChange={this.handleChange}
                    />
                  </div>
                </div>
                <div className="row">
                  <div className="col-xs-24">
                    <textarea
                      className="form-control mb5 full-width"
                      placeholder="Your Message"
                      type="text"
                      ref="MessageInput"
                      rows="5"
                      required
                      onChange={this.handleChange}
                    />
                  </div>
                </div>
                <div className="row">
                  <div className="input-group-btn">
                    <button type="submit" className="btn btn-default mrn">
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </form>
          )
          : this.state.confirmation === 'success'
            ? (<div className="title"><b>Your mail has been sent.</b></div>)
            : (<div className="title"><b>Failed to send mail!</b></div>)}
      </div>
    );
  },
});
