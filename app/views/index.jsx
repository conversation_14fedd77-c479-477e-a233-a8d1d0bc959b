const React = require('react');
const ReactDOM = require('react-dom');
const classNames = require('classnames');
const mixins = require('../lib/mixins');
// Panels
const Header = require('./panels/header/index');
const FacebookHeader = require('./panels/facebook/header');
const READHeader = require('./panels/read-header');
// Leftbar = require('./panels/leftbar/index'),
const MapPanel = require('./panels/map/index');
// Sub = require('./panels/sub/index'),
const Photos = require('./panels/photos/index');
const AgentContact = require('./panels/contact/agent-contact');
const LoanOfficerContact = require('./panels/contact/loan-officer-contact');
const ShareForm = require('./panels/share/index');
const BrokerageModal = require('./panels/agent-profile/brokerage');
const BlankModal = require('./panels/modals/blank');
const ResultsHelp = require('./panels/results-count/help');
const DemoBar = require('./panels/demo-bar');
const Help = require('./panels/help/index');
const AgentNotification = require('./panels/agent-notification');
const AgentListingNotification = require('./panels/agent-listing-notification');

// Screens
const Onboarding = require('./screens/onboarding/index');
const Landing = require('./screens/landing/index');
const Home = require('./screens/home/<USER>');
const Agent = require('./screens/agent/index');
const Listing = require('./screens/listing/index');
const Grid = require('./screens/grid/index');
const Map = require('./screens/map/index');
const Tagging = require('./screens/tagging/index');
const Featured = require('./screens/featured/index');
const AgentSearch = require('./panels/agent-search');
const Buyer = require('./screens/buyer/index');
const Logout = require('./screens/logout/index');
const Facebook = require('./screens/facebook/index');
const LoginPrompter = require('./screens/login-prompter/index');
const HomeWorth = require('./screens/home-worth');
const MemberSearch = require('./screens/member-search');
const MemberListings = require('./screens/member-listings');

// Agent = require('./screens/agent/index'),
const Error = require('./screens/error/index');
const DemoExpired = require('./screens/demo-expired/index');
const ModalPopup = require('./screens/modal-popup');
const EmployeeUi = require('./panels/employee-ui');

const HomeWorthModal = require('./panels/home-worth-modal');
const DreamsweepsModal = require('./panels/dreamsweeps-modal');
const LendingTreeModal = require('./panels/lending-tree/modal');
const PayPerClickModal = require('./panels/pay-per-click/modal');
const RateplugSpecialFinancingModal = require('./panels/rateplug-landing-modal/special-financing');
const RateplugSpecialFinancingMissingModal = require('./panels/rateplug-landing-modal/special-financing-missing');
const RateplugLandingModal = require('./panels/rateplug-landing-modal');
const RateplugWelcomeModal = require('./panels/rateplug-welcome-modal');
const RateplugHomeButton = require('./panels/rateplug-home');

const App = React.createClass({

  mixins: [mixins.cursors],

  cursors: {
    blur: ['shared', 'blurContent'],
    agentSearchLayout: ['layout', 'agentSearch'],
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  render() {
    return (
      <div className={classNames('',
        `${this.state.specialFinancing ? `--highlight:SpecialFinancePrograms__${this.state.specialFinancing}` : ''}`,
        {
          blur: this.state.blur || this.state.agentSearchLayout,
        })}
      >
        <DemoBar />
        <AgentNotification />
        <AgentListingNotification />
        <Header />
        <FacebookHeader />
        <READHeader />
        <Photos />
        <MapPanel />

        <Onboarding />
        <Landing />
        <Home />
        <Agent />
        <Listing />
        <Grid />
        <Map />
        <Facebook />

        <Tagging />
        <Featured />
        <HomeWorth />

        <MemberSearch />
        <MemberListings />

        <Logout />
        <AgentContact />
        <LoanOfficerContact />
        <Buyer />
        <ShareForm />
        <BrokerageModal />

        <Error />
        <DemoExpired />

        <LoginPrompter randomizeModals />

        <ModalPopup />
        <BlankModal />
        <ResultsHelp />
        <Help />
        <AgentSearch />
        <EmployeeUi />
        <HomeWorthModal />
        <DreamsweepsModal />
        <LendingTreeModal />
        <PayPerClickModal />
        <RateplugLandingModal />
        <RateplugSpecialFinancingModal />
        <RateplugSpecialFinancingMissingModal />
        <RateplugHomeButton />
        <RateplugWelcomeModal />

      </div>
    );
  },
});

module.exports = function (app, callback) {
  const appContainer = document.getElementById('app-container');

  ReactDOM.render(
    <App />,
    appContainer,
    callback,
  );
};
