const React = require('react');
const sample = require('lodash').sample;
const mixins = require('../../../lib/mixins');
const LandingPanel = require('../../panels/landing');
const LandingBroker = require('../../panels/landing/broker');

module.exports = React.createClass({

  displayName: 'screens.landing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    layout: ['layout', 'landing'],
    agentSettings: ['shared', 'agent', 'data', 'AgentSettings'],
  },

  getInitialState() {
    const BG_IMAGES = [
      'https://nplayassets.blob.core.windows.net/search2/backgrounds/landing-brown-house.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/0.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/1.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/2.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/3.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/4.jpg',
    ];

    return {
      bgImage: sample(BG_IMAGES),
    };
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    let customBg = null;
    const customBackgrounds = ((this.utils.getAgentSettingValue(this.state.agentSettings, 'alpbackgroundimage')) || '').split('|');

    if (customBackgrounds && customBackgrounds.length > 0) {
      customBg = sample(customBackgrounds);
    }

    return (
      <div
        className="landing"
        style={{
          backgroundImage: `url('${customBg || this.state.bgImage}')`,
        }}
      >
        <LandingPanel />
        <LandingBroker />
      </div>
    );
  },
});
