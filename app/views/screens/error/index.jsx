const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.error',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors],

  cursors: {
    layout: ['layout', 'error'],
    agentData: ['shared', 'agent', 'data'],
    hasBack: ['screens', 'ref', 'hasBack'],
  },

  onOptionMenu() {
    this.actions.menu.toggle();
  },

  onOptionBack() {
    window.history.back();
  },

  orOptions() {
    if (this.state.hasBack && this.state.agentData) {
      return (
        <tspan x="0" y="264" className="style18 cursor-pointer" onClick={this.onOptionBack}>or return to previous page</tspan>,
          <tspan x="0" y="300" className="style18 cursor-pointer" onClick={this.onOptionMenu}>or try a new search</tspan>
      );
    }
    if (this.state.hasBack) {
      return (
        <tspan x="0" y="264" className="style18 cursor-pointer" onClick={this.onOptionBack}>or return to previous page</tspan>
      );
    }
    if (this.state.agentData) {
      return (
        <tspan x="0" y="264" className="style18 cursor-pointer" onClick={this.onOptionMenu}>or try a new search</tspan>
      );
    }
  },

  cat() {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 729 428.5" enableBackground="new 0 0 729 428.5">
        <rect x="581.3" y="190.5" width="34.9" height="238" className="style0" />
        <path d="M723 190.5H459.5c-3.3 0-6-2.7-6-6v-76.8c0-9.9-3.5-18.4 2-18H723c3.3 0 6 2.7 6 6v88.8 C729 187.8 726.3 190.5 723 190.5z" className="style1" />
        <path d="M713.7 199.3c-6.7-57.3-3.8-98-2.8-109.5h0.1c0 0 0.2-1.2 0.3-3.3c0 0 0 0 0 0h0c0.4-7.8 0-28.4-13.2-48.6 c0 0 0-0.1-0.1-0.1c-1.4-2.6-3.3-5.1-5.4-7.5c-12.4-15-28.6-22.5-41.4-26.4c-6.1-2-12.9-3.4-20-3.8c-1.1-0.1-1.7-0.1-1.7-0.1l0 0 c-1.1 0-2.3-0.1-3.4-0.1c-2.1 0-4.2 0.1-6.2 0.2l0 0c0 0-45.3 1.9-67.9 37l-5.3-15.8l-14 22c-15.7-6.3-27.7 3-27.7 3l-27.3-20.7 l1.7 42c0 0-4.7 3.3-7.7 22h155c5.5 1.2 11.4 1.9 17.5 1.9s12-0.7 17.5-1.9h34c-3.3 84.6 2.3 111.3 2.3 111.3c0 3.9 3.6 7 8.1 7 s8.1-3.1 8.1-7C714 200.5 713.9 199.9 713.7 199.3z" className="style2" />
        <g>
          <path d="M529 68.3c-2.2-1.1-3.5-2.9-3.5-4.6l-0.1 0c-0.9 1.9 0.5 4.5 3.3 5.9c2.7 1.3 5.7 0.9 6.6-1l0 0 C533.6 69.3 531 69.3 529 68.3z" className="style3" />
        </g>
        <g>
          <path d="M508.8 69.5c-2.3 0.5-4.5-0.2-5.6-1.5l-0.1 0c0.4 2.1 3.2 3.3 6.2 2.6c3-0.6 5-2.8 4.6-4.9l0 0 C513 67.5 510.9 69.1 508.8 69.5z" className="style3" />
        </g>
        <ellipse cx="521" cy="76" rx="5" ry="2.6" className="style4" />
        <path d="M513 80c0 0-19.2-17.9-41.3-21.5c0 0 21.3 5.6 40.4 22.1L513 80z" className="style5" />
        <polygon points="510.3,82 463.2,82.7 510.3,83.1" className="style5" />
        <path d="M501.5 78.6c0 0-23.4-7.7-36.1-5.2c0 0 4.9-1.8 36.1 6.4V78.6z" className="style5" />
        <path d="M530.1 79.6c0 0 19.2-17.9 41.3-21.5c0 0-21.3 5.6-40.4 22.1L530.1 79.6z" className="style5" />
        <path d="M695.6 89.8h-34c-20.9 4.5-35 0-35 0s-3.1-16.5 11.4-13.4l11.5 3.5c0 0-10-17.9 0-33.4 c0 0-6.5 18.1 2.5 33.4l1.2 1.7c0 0-12.3-4-18.2-3.8c0 0-7.2-1.2-7 11.8c0 0 11.4 4 33-1.2L695.6 89.8z" className="style6" />
        <polygon points="480.2,32.3 482.2,59.3 483.2,35.6 501.5,48" className="style6" />
        <polygon points="535.3,43 546,26.2 550.8,39.7 545,30.7" className="style6" />
        <path d="M556.3 65.9c0 0 11.4 5.3 9.7 23.9C566 89.8 564.2 71 556.3 65.9z" className="style6" />
        <polygon points="532.8,81.6 579.9,82.3 532.8,82.7" className="style5" />
        <path d="M541.6 78.2c0 0 23.4-7.7 36.1-5.2c0 0-4.9-1.8-36.1 6.4V78.2z" className="style5" />
        <path d="M510.3 89.7c0 0-1.8-6.5 5.7-9.8C516 79.9 511.5 83.5 510.3 89.7z" className="style6" />
        <path d="M528 79.5c0 0 5.8 2.1 5.8 9.8C533.8 89.3 530.8 82 528 79.5z" className="style6" />
        <circle cx="477.6" cy="121.1" r="9" className="style7" />
        <path d="M569.6 121.3c0-3.2-2.6-5.8-5.8-5.8h-85.7c-3.2 0-5.8 2.6-5.8 5.8s2.6 5.8 5.8 5.8H539 c0 0.4-0.1 0.8-0.1 1.2v25.2c0 6.6 5.4 12 12 12h6.7c6.6 0 12-5.4 12-12v-30.2c0-0.4 0-0.8-0.1-1.2 C569.6 121.8 569.6 121.5 569.6 121.3z" className="style6" />
        <circle cx="477.6" cy="121.1" r="2.4" className="style7" />
        <circle cx="453" cy="190.5" r="7.7" className="style8" />
        <path d="M453 87.5L453 87.5c-1.9 0-3.4 1.5-3.4 3.4V187c0 1.9 1.5 3.4 3.4 3.4l0 0c1.9 0 3.4-1.5 3.4-3.4V90.9 C456.4 89 454.9 87.5 453 87.5z" className="style8" />
        <path d="M626.2 190.5h-54.8v3.4c0 1.9 1.5 3.4 3.4 3.4h47.9c1.9 0 3.4-1.5 3.4-3.4V190.5z" className="style8" />
        <circle cx="453" cy="190.5" r="3.8" className="style9" />
        <path d="M619.9 0.2" className="style7" />
        <path d="M629.5 0.1L629.5 0.1" className="style7" />
        <g>
          <path d="M617 143.2v7.5h-3.4v-6.5c0-1.5-0.9-2.5-2.4-2.5c-1.7 0-2.8 1.3-2.8 3.1v5.9h-3.4v-16.5 h3.4v6.7c0.8-1.5 2.3-2.2 4.1-2.3C615.3 138.6 617 140.4 617 143.2z" className="style10" />
          <path d="M631.8 144.7c0 3.7-2.6 6.1-6.4 6.1c-3.9 0-6.4-2.5-6.4-6.1c0-3.7 2.6-6.1 6.4-6.1 C629.2 138.6 631.8 141.1 631.8 144.7z M622.4 144.8c0 2 1.2 3.3 3 3.3c1.8 0 3-1.3 3-3.3c0-2-1.2-3.3-3-3.3 C623.6 141.5 622.4 142.8 622.4 144.8z" className="style10" />
          <path d="M654.3 143.2v7.5h-3.4v-6.5c0-1.5-0.9-2.5-2.3-2.5c-1.6 0-2.7 1.3-2.7 3.1v5.9h-3.4v-6.5 c0-1.5-0.9-2.5-2.3-2.5c-1.7 0-2.7 1.3-2.7 3.1v5.9h-3.4v-12h3.4v2.1c0.8-1.5 2.3-2.2 4.1-2.2c2 0 3.5 1 4.1 2.8 c0.8-1.8 2.3-2.7 4.3-2.8C652.6 138.6 654.3 140.4 654.3 143.2z" className="style10" />
          <path d="M668.3 145.8h-8.6c0.4 1.5 1.6 2.4 3.1 2.4c1.1 0 2.2-0.4 3-1.3l1.8 1.8 c-1.2 1.3-3 2.1-5.1 2.1c-3.8 0-6.3-2.5-6.3-6.1c0-3.7 2.5-6.1 6.2-6.1C666.6 138.6 668.6 141.4 668.3 145.8z M665.1 143.8 c0-1.6-1.1-2.6-2.7-2.6c-1.5 0-2.5 1-2.8 2.6H665.1z" className="style10" />
          <path d="M678.3 148.9c-0.9 1.3-2.4 1.9-4.3 1.9c-2.4 0-4-1.6-4-3.6c0-2 1.6-3.3 4.4-3.4h3.9v-0.7 c0-1.7-1.1-2.7-3.1-2.7c-1.3 0-2.5 0.5-3.7 1.3l-0.7-1.3c1.5-1 2.8-1.6 4.8-1.6c2.9 0 4.5 1.5 4.5 4.1l0 7.8h-1.7V148.9z M678.3 146.7v-1.4h-3.7c-2 0-3 0.6-3 1.9c0 1.3 1 2.1 2.7 2.1C676.3 149.3 677.8 148.3 678.3 146.7z" className="style11" />
          <path d="M690.7 140l-0.7 1.4c-0.9-0.6-2.1-1-3.2-1c-1.3 0-2.3 0.5-2.3 1.6c0 2.6 6.6 1.3 6.6 5.5 c0 2.3-2 3.3-4.3 3.3c-1.7 0-3.4-0.6-4.5-1.6l0.7-1.3c1 0.9 2.5 1.5 3.9 1.5c1.4 0 2.5-0.5 2.5-1.7c0.1-2.8-6.5-1.4-6.5-5.5 c0-2.3 1.9-3.2 4.1-3.2C688.4 138.9 689.8 139.3 690.7 140z" className="style11" />
          <path d="M701.2 148.9c-0.9 1.3-2.4 1.9-4.3 1.9c-2.4 0-4-1.6-4-3.6c0-2 1.6-3.3 4.4-3.4h3.9v-0.7 c0-1.7-1.1-2.7-3.1-2.7c-1.3 0-2.5 0.5-3.7 1.3l-0.7-1.3c1.5-1 2.8-1.6 4.8-1.6c2.9 0 4.5 1.5 4.5 4.1l0 7.8h-1.7V148.9z M701.2 146.7v-1.4h-3.7c-2 0-3 0.6-3 1.9c0 1.3 1 2.1 2.7 2.1C699.3 149.3 700.8 148.3 701.2 146.7z" className="style11" />
          <path d="M717.5 144.9c0 3.5-2.3 5.9-5.6 5.9c-2.1 0-3.7-1-4.6-2.6v6.8h-1.7V139h1.7v2.5 c0.9-1.6 2.5-2.6 4.5-2.6C715.2 138.9 717.5 141.4 717.5 144.9z M715.7 144.9c0-2.5-1.7-4.4-4.2-4.4c-2.5 0-4.2 1.8-4.2 4.4 c0 2.6 1.7 4.4 4.2 4.4C714 149.2 715.7 147.4 715.7 144.9z" className="style11" />
        </g>
        <g>
          <g>
            <path d="M596.9 140.5c0-1.7-0.2-3.5-0.6-5.2c-1.3-0.3-2.6-0.5-3.9-0.6 C594.3 136.4 595.8 138.4 596.9 140.5z" className="style12" />
            <path d="M584.7 136c-2.4 0.9-4.6 2.3-6.5 4.2c0 0 0 0 0 0c0.4 1.5 1 2.9 1.7 4.2 C580.8 141.3 582.4 138.4 584.7 136z" className="style12" />
          </g>
          <g>
            <path d="M586 134.7c-1.3 0.1-2.6 0.3-3.9 0.6c-0.5 1.7-0.7 3.5-0.6 5.2 C582.6 138.4 584.1 136.4 586 134.7z" className="style11" />
            <path d="M593.7 136c2.3 2.4 3.9 5.3 4.7 8.4c0.7-1.4 1.3-2.8 1.7-4.2 C598.2 138.3 596 136.9 593.7 136z" className="style11" />
          </g>
          <path d="M596.9 140.5c0-1.7-0.2-3.5-0.6-5.2c-1.3-0.3-2.6-0.5-3.9-0.6 C594.3 136.4 595.8 138.4 596.9 140.5z" className="style12" />
          <path d="M598.5 149.3c0-6.9-3.8-12.9-9.4-16.2c-5.6 3.2-9.4 9.3-9.4 16.2c0 0 0 0 0 0 c2.7 1.6 5.9 2.5 9.3 2.5C592.6 151.8 595.8 150.9 598.5 149.3z" className="style10" />
          <g>
            <path d="M593.4 144.1v5h-2.3v-4.3c0-1-0.6-1.6-1.6-1.6c-1.1 0-1.8 0.9-1.8 2v3.9h-2.3v-11h2.3v4.4 c0.6-1 1.5-1.5 2.8-1.5C592.2 141.1 593.4 142.2 593.4 144.1z" className="style13" />
          </g>
        </g>
        <path d="M695.6 89.8c-3.3 84.6 2.3 111.3 2.3 111.3c0 3.9 3.6 7 8.1 7s8.1-3.1 8.1-7c0-0.6-0.1-1.2-0.3-1.8 c-6.7-57.3-3.8-98-2.8-109.5h0.1c0 0 0.2-1.2 0.3-3.3" className="style2" />
        <rect y="23.5" width="440" height="362" className="style14" />
        <text transform="matrix(1 0 0 1 0 69.0573)">
          <tspan x="0" y="0" className="style15">Oh Shucks...</tspan>
          <tspan x="0" y="66" className="style16">Something </tspan>
          <tspan x="0" y="132" className="style16">went wrong.</tspan>
          <tspan x="0" y="180" className="style17">{typeof this.state.layout === 'string' ? this.state.layout : ''}</tspan>
          <tspan x="0" y="228" className="style18">look at this sleeping cat...</tspan>
          {this.orOptions()}
        </text>
      </svg>
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className="oh-shucks-container">
        <div className="white-bg" />
        {this.cat()}
        <div className="grass left-grass" />
        <div className="grass right-grass" />
      </div>
    );
  },

});
