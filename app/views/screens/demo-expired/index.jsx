const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.demo-expired',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors],

  cursors: {
    demoData: ['shared', 'demo'],
    layout: ['layout', 'demo'],
  },

  cat() {
    return (
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 426.6 400 173.4" enableBackground="new 0 426.6 400 173.4">
        <g>
          <path
            fill="#F15E4C"
            d="M274,463.5c-0.6-0.1-1.1-0.4-1.7-0.5c-0.5,0.9-0.8,1.7-0.8,2.4c0.1,0.9,0.6,1.7,1.7,2.3
    c-4.4,38.5-34.5,49.1-34.9,49.3l0.4,1.1c0.3-0.1,31-11,35.7-49.9c0.9,0.3,1.7,0.5,2.3,0.5l0,0c1.1,0,1.7-0.5,1.9-0.9
    c0.6-0.9,0.5-2.3,0.1-3.6C277.1,464.1,275.5,464,274,463.5z"
          />
          <path
            fill="#F15E4C"
            d="M283.6,427.2c-8.8-2.4-17.9,3.6-20.4,13.2c-2.5,9.7,2.4,19.3,11.2,21.7c8.8,2.4,17.9-3.6,20.4-13.2
    C297.3,439.2,292.4,429.6,283.6,427.2z M274.7,434c-0.3,0.1-4.8,2.2-4.8,7.5c0,0.4-0.4,0.8-0.8,0.8c-0.4,0-0.8-0.4-0.8-0.8
    c0-6.3,5.5-8.8,5.7-8.9c0.4-0.1,0.8,0,1,0.4C275.4,433.5,275.1,433.9,274.7,434z"
          />
        </g>
        <path
          fill="#FFFFFF"
          d="M167.8,597.5v-17.8c0-0.4-0.3-0.8-0.5-1l0,0l-11.8-9.4l14.7-21.5v14.1c0,0.8,0.5,1.3,1.3,1.3h12.7
  c0.8,0,1.3-0.5,1.3-1.3c0-0.8-0.5-1.3-1.3-1.3h-11.4v-16.1c1.1,0.5,2.4,0.9,3.8,0.9c5,0,8.9-3.9,8.9-8.9s-3.9-8.9-8.9-8.9
  c-5,0-8.9,3.9-8.9,8.9c0,2.5,1,4.7,2.7,6.3h-19.2c-0.4,0-0.6,0.1-0.9,0.4l-12.7,12.7c-0.3,0.3-0.4,0.5-0.4,0.9
  c0,0.8,0.5,1.3,1.3,1.3c0.4,0,0.6-0.1,0.9-0.4l12.3-12.3H169l-16.1,23.4l0,0c-0.1,0.3-0.3,0.4-0.3,0.8v14h-16.5
  c-0.8,0-1.3,0.5-1.3,1.3c0,0.8,0.5,1.3,1.3,1.3h19v-13.8l10.2,8.1v17.1H-1.1v2.5h402.2v-2.5H167.8z M170.3,536.5
  c0-3.6,2.8-6.3,6.3-6.3c3.6,0,6.3,2.8,6.3,6.3s-2.8,6.3-6.3,6.3C173.1,542.9,170.3,540.1,170.3,536.5z"
        />
      </svg>
    );
  },

  render() {
    if (this.state.layout !== 'expired') {
      return null;
    }

    return (
      <div className="demo-expired-container">
        {this.cat()}
        <h1>Oh Shucks!</h1>
        <h2>Your demo site is no longer available!</h2>
        <p>
          Please Contact Customer Service at
          <a
            href={`${'mailto:<EMAIL>?'
              + 'Subject=Demo%20Expired%20Help&'
              + 'Body=%0A%0A%3D%3D%3DEnter%20your%20message%20above%20this%20line%3D%3D%3D%0ADemo%20Information%3A%20'}${this.state.demoData && this.state.demoData.MembershipId || ''}%20%7C%20${this.state.demoData && this.state.demoData.CampaignId || ''}%20%7C%20${this.state.demoData && this.state.demoData.CampaignRecipientId || ''}`}
          >
            <EMAIL>
          </a>
          .
          <br />
          Or visit
          &nbsp;
          <a href="https://about.homeasap.com">about.homeasap.com</a>
          &nbsp;
          for more information.
        </p>
      </div>
    );
  },

});
