const React = require('react');
const SpinnerRound = require('../../components/spinner_round');
const mixins = require('../../../lib/mixins');
const NPlayFooter = require('../../panels/n-play-footer');

module.exports = React.createClass({

  displayName: 'screens.buyer',

  mixins: [mixins.debug, mixins.cursors, mixins.actions/* mixins.pureRender */],

  cursors: {
    layout: ['layout', 'buyer'],
    buyerData: ['shared', 'buyer', 'data'],
  },

  onNav(id) {
    this.actions.agent.onNav(id);
  },

  logOut() {
    this.actions.login.logout();
  },

  closeClicked() {
    this.actions.buyer.onClose();
  },

  render() {
    if (!this.state.layout) {
      return null;
    }
    if (!this.state.buyerData) {
      return (
        <center className="mt30">
          <SpinnerRound />
        </center>
      );
    }

    return (
      <div className="layout--front menu-container agent-screen-container buyer-screen-container">
        <div className="buyer-info-container row">
          <div className="buyer-info">
            <div className="menu-top">
              <div className="text-left buyer">
                <h2 className="mt5 ml20">
                  WELCOME
                  &nbsp;
                  {this.state.buyerData.FirstName || ''}
                &nbsp;&nbsp;&nbsp;
                </h2>
              </div>
              <div className="text-right">
                <a role="button" tabIndex="-1" className="logout" onClick={this.logOut}>Logout</a>
                {
                  typeof this.state.layout === 'string'
                    ? <button type="button" className="btn btn-default btn-close" onClick={this.closeClicked}>&times;</button>
                    : null
                }
              </div>
            </div>
            <div className="row">

              <h3 className="brand-primary">Check out these features</h3>

              <table>
                <tbody>
                  <tr>
                    <td>
                      <h4>Communicate with Agents</h4>
                      <p>Have a question on a home you&apos;ve viewed&#63; Or maybe you want to schedule an appointment... We&apos;ve made it easy for you to reach out to Agent to learn more about properties in your area.</p>
                      <p>When you&apos;re viewing a property&apos;s details, simply click on the agent&apos;s name and photo that are displayed on the property&apos;s image. This will allow you to contact the agent about that property.</p>
                      <p>You can also contact them by clicking on their picture and name on the top left, simply click contact now from the options in the dropdown.</p>
                    </td>
                    <td className="margin" />
                    <td className="pic">
                      <img alt="Contact agent" src={`${window.CONFIG.CDN_URL}search2/buyer-agent-contact.jpg`} />
                    </td>
                  </tr>
                </tbody>
              </table>

              <table>
                <tbody>
                  <tr>
                    <td>
                      <h4>View Recent Searches</h4>
                      <p>Can&apos;t remember your most recent search&#63; No worries, we saved it for you. Simply click in the search box where it says &quot;Address, Neighborhood, City, Zip or School&quot; to see a list of your recent searches.</p>
                    </td>
                    <td className="margin" />
                    <td className="pic">
                      <img alt="Recent searches" src={`${window.CONFIG.CDN_URL}search2/buyer-recent-searches.jpg`} />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <NPlayFooter />
          </div>
        </div>
      </div>
    );
  },

});
