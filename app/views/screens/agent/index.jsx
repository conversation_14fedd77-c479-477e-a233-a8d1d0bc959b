const React = require('react');
const classNames = require('classnames');
const SpinnerRound = require('../../components/spinner_round');
const AgentProfile = require('../../panels/agent-profile');
const FeaturedListings = require('../../panels/featured-listings');
const ListingFullView = require('../../panels/listing/full');
const NPlayFooter = require('../../panels/n-play-footer');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const CloseButton = require('../../components/close_btn_cross');

module.exports = React.createClass({

  displayName: 'screens.agent',

  mixins: [mixins.debug, mixins.cursors, mixins.actions/* mixins.pureRender */],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    agentLayout: ['layout', 'agent', 'agentData'],
    listingLayout: ['layout', 'agent', 'listingDetail'],
    activeListing: ['screens', 'agent', 'activeListing'],
    showHeadlineInside: ['screens', 'agent', 'agentProfile', 'showHeadlineInside'],
    facebookLayout: ['layout', 'facebookHeader'],
  },
  getInitialState() {
    return {
      showHeadline: true,
    };
  },

  agentLandingClick() {
    if (this.state.agentData && this.state.agentData.Website) {
      let url = this.state.agentData.Website;
      if (url.toLowerCase().indexOf('http') < 0) {
        url = `http://${url}`;
      }
      window.open(url, '_blank');
    } else {
      this.actions.agent.gotoAgentLanding(this.state.agentData.Id);
    }
  },

  showAgentContact(tab) {
    this.actions.agent.setActiveTab(tab);
    this.actions.agent.showAgentContact(null);
  },

  closeClicked(e) {
    e.preventDefault();
    this.actions.agent.goBack();
  },
  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.activeListing);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.activeListing.ZipCode);
  },
  onClose() {
    this.actions.agent.onClose();
  },
  backToAgent() {
    this.actions.agent.onNav();
  },
  onNav(id) {
    this.actions.agent.onNav(id);
  },
  showBrokerage() {
    this.actions.agent.showBrokerage();
  },
  linkClick(url) {
    if (!url || url == '') {
      return;
    }

    if (url.toLowerCase().indexOf('http://') < 0 && url.toLowerCase().indexOf('https://') < 0) {
      url = `http://${url}`;
    }
    window.open(
      url,
      '_blank',
    );
  },
  getIconCount() {
    // let count = 0;
    // count = this.state.agentData.PinterestUrl
    //   + this.state.agentData.LinkedInUrl
    //   + this.state.agentData.TwitterUrl
    //   + this.state.agentData.FacebookUrl;
    const titleLength = this.state.agentData.Headline ? this.state.agentData.Headline.length : 0;
    if (titleLength > 40) {
      this.setState({ showHeadline: false });
    }
  },

  render() {
    if (!this.state.agentLayout && !this.state.listingLayout) {
      return null;
    }

    if (!this.state.agentData) {
      return (
        <center className="mt30">
          <SpinnerRound />
        </center>
      );
    }

    return (
      <div className={classNames('agent-profile-outer-container', {
        'facebook-header-version': this.state.facebookLayout,
        'facebook-page-admin': this.state.facebookLayout === 'admin',
      })}
      >
        <div className="menu-top fixed">
          <div className="text-left">
            <div className="details-holder">
              <div className="hidden-xs hidden-sm col-1-1 col-md-1-3">
                <div className="headline title-hide">
                  &nbsp;
                  {!this.state.showHeadlineInside ? this.state.agentData.Headline : ''}
                </div>
              </div>
              <div className="col-1-1 col-md-2-3 right-container">
                <div role="button" tabIndex="-1" aria-label="Call" className="btn contact-button" onClick={this.showAgentContact.bind(this, 'Call')}>
                  <span className={classNames('hidden-xs hidden-sm mr3', {
                    'hidden-md': !this.state.facebookLayout,
                    'm-hide': !this.state.facebookLayout,
                  })}
                  >
                    &nbsp;
                    ASK A QUESTION
                  </span>
                  <SVGIcon
                    className="contact-icon"
                    name="icon-contact-agent"
                  />
                </div>
                <div className="social-container">
                  {
                    this.state.agentData.Website
                      ? (
                        <div role="link" tabIndex="-1" aria-label="Agent landing page" onClick={this.agentLandingClick}>
                          <span className="social">
                            <SVGIcon
                              className="social-icon red"
                              name="icon-web"
                            />
                          </span>
                        </div>
                      ) : null
                  }
                  {
                    this.state.agentData.PinterestUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent pinterest" onClick={this.linkClick.bind(this, this.state.agentData.PinterestUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon"
                            name="icon-pinterest"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {
                    this.state.agentData.LinkedInUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent linkedin" onClick={this.linkClick.bind(this, this.state.agentData.LinkedInUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg"
                            name="icon-linkedin"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {
                    this.state.agentData.TwitterUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent twitter" onClick={this.linkClick.bind(this, this.state.agentData.TwitterUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg twitter-icon"
                            name="icon-twitter"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {

                    this.state.agentData.GoogleUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent google page" onClick={this.linkClick.bind(this, this.state.agentData.GoogleUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg google-icon"
                            name="icon-google"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {

                    this.state.agentData.YouTubeUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent YouTube" onClick={this.linkClick.bind(this, this.state.agentData.YouTubeUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg youtube-icon"
                            name="icon-youtube"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {

                    this.state.agentData.InstagramUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent instagram" onClick={this.linkClick.bind(this, this.state.agentData.InstagramUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg instagram-icon"
                            name="icon-instagram"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                  {

                    this.state.agentData.FacebookUrl ? (
                      <div role="link" tabIndex="-1" aria-label="Agent facebook" onClick={this.linkClick.bind(this, this.state.agentData.FacebookUrl)} target="_blank">
                        <span className="social">
                          <SVGIcon
                            className="social-icon lg facebook-icon"
                            name="icon-facebook"
                          />
                        </span>
                      </div>
                    ) : null
                  }
                </div>
                {
                  ((this.state.agentLayout && typeof this.state.agentLayout === 'string')
                    || (this.state.listingLayout && typeof this.state.listingLayout === 'string'))
                    ? <CloseButton onClick={this.onClose} />
                    : null
                }
              </div>

            </div>
          </div>
          {this.state.facebookLayout
            ? null
            : (
              <div className="text-right">
                <SVGIcon name="icon-featured-listings" />
              </div>
            )}
        </div>

        <div className="layout--full menu-container agent-screen-container">

          <div className="menu-left">
            <div className="scroll-container">
              {
                this.state.agentLayout
                  ? <AgentProfile agentData={this.state.agentData} showHeadlineInside={this.state.showHeadlineInside} />
                  : this.state.activeListing
                    ? <ListingFullView listing={this.state.activeListing} onClose={this.backToAgent} onPhotoClick={this.onPhotoClick} />
                    : (
                      <center className="mt30">
                        <SpinnerRound />
                      </center>
                    )
              }
              <NPlayFooter />
            </div>
          </div>
          <div className="menu-right" />
        </div>
        { (!this.state.facebookLayout)
          ? <FeaturedListings className="layout--1ns-3 layout--front featured-listings" onNav={this.onNav} />
          : null}
      </div>
    );
  },
});
