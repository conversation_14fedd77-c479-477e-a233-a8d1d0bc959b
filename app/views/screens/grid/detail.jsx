const React = require('react');
const Ref = require('./ref');
const BodyListings = require('../../panels/body-listings');
const ListingFullView = require('../../panels/listing/full');
const mixins = require('../../../lib/mixins');
const Sort = require('../../panels/sort');

module.exports = React.createClass({

  displayName: 'screens.grid.detail',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    data: ['screens', 'grid', 'data'],
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data[this.props.id]);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data[this.props.id].ZipCode);
  },

  onClose() {
    this.actions.grid.onNav();
  },

  partial() {
    return (
      <ListingFullView listing={this.state.data[this.props.id]} onPhotoClick={this.onPhotoClick} onClose={this.onClose} />
    );
  },

  render() {
    return (
      <div>
        <div className="map-sort">
          <Sort />
        </div>
        <div className="mt40">
          <BodyListings
            className="layout--1ns-3 body-listings"
            screen="grid"
          />
        </div>

        <div className="row col-center layout--2s-3">

          <div className="col-23-24 grid-detail">

            {this.state.data[this.props.id] ? this.partial() : <Ref />}

          </div>
        </div>

      </div>
    );
  },

});
