const React = require('react');
const classNames = require('classnames');
const Menu = require('../../panels/grid-menu/index');
const mixins = require('../../../lib/mixins');
const GridListings = require('../../panels/grid-listings');
const GridFooter = require('../../panels/grid-footer');
const NPlayFooter = require('../../panels/n-play-footer');

module.exports = React.createClass({

  displayName: 'screens.grid.grid',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils, mixins.pureRender],

  cursors: {
    listings: ['panels', 'listings', 'data'],
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    showMenu: ['screens', 'grid', 'showMenu'],
  },

  onNav(listingId) {
    this.actions.grid.onMoreInfo(listingId);
  },
  onPhotos(listing) {
    this.actions.grid.onPhotos(listing.Id);
  },

  render() {
    return (
      <div>
        <GridListings listings={this.state.listings} spinner={this.state.spinner} onNav={this.onNav} onPhotos={this.onPhotos} fromSearch showResultCount>
          <div className={
            classNames('menu-fixed',
              {
                'slide-enter slide-enter-active': this.state.showMenu,
                'slide-leave slide-leave-active': !this.state.showMenu,
              })
}
          >
            <div className="grid-menu">
              <Menu mode="search-results" />
            </div>

          </div>
        </GridListings>
        <GridFooter
          mlsIds={this.state.listings && this.utils.uniqueMLSIdsFromListings(this.state.listings)}
          className={classNames('grid-footer row col-center layout--1ns', { 'lights-out': this.state.lightsOff }, { 'lights-on': !this.state.lightsOff })}
        />
        <NPlayFooter />
      </div>
    );
  },

});
