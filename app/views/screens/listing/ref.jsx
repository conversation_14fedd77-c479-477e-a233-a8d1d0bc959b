const React = require('react');
const SpinnerRound = require('../../components/spinner_round');
const SVGIcon = require('../../components/svg_icon');
const Alert = require('../../components/alert');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.listing.ref',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.pureRender],

  cursors: {
    ref: ['screens', 'listing', 'ref'],
  },

  render() {
    return (
      <div>

        {this.state.ref.spinner
          ? (
            <center className="mt30">
              <SpinnerRound />
            </center>
          )
          : null}

        {(this.state.ref.alert || '').match(/listing was not found/i)
          ? (
            <div className="listing-404-container">
              <div className="listing-404">
                <SVGIcon name="graphics-listing-404" />
                <h2>Oh Shucks!</h2>
                <p className="listing-404-desktop">This listing no longer exists. Check out the listings to the right to see if there are any other houses that catch your eye...</p>
                <p className="listing-404-mobile">This listing no longer exists. Return to results to see if there are any other houses that catch your eye...</p>
              </div>
            </div>
          )
          : (
            <Alert
              message={this.state.ref.alert}
            />
          )}

      </div>
    );
  },

});
