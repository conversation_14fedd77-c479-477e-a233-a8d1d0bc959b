const React = require('react');
const Ref = require('./ref');
const mixins = require('../../../lib/mixins');
const ListingFullView = require('../../panels/listing/full');

module.exports = React.createClass({

  displayName: 'screens.listing',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    layout: ['layout', 'listing'],
    data: ['screens', 'listing', 'data'],
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data, 'full');

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data.ZipCode);
  },

  partial() {
    return (
      <ListingFullView listing={this.state.data} onPhotoClick={this.onPhotoClick} />
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    if (!this.state.data) {
      return (
        <div className="listing screen-listing">
          <Ref />
        </div>
      );
    }

    return (
      <div className="layout--3r-5 listing screen-listing">

        <Ref />

        {this.partial()}

      </div>
    );
  },

});
