const React = require('react');
const classnames = require('classnames');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.member-search',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    layout: ['layout', 'memberSearch'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className={classnames('member-search-screen')}>
        <iframe id="agent-search-iframe" title="Agent Search" src={window.CONFIG.AGENT_SEARCH_URL} />
      </div>
    );
  },

});
