const React = require('react');
const mixins = require('../../../lib/mixins');
const HomePanel = require('../../panels/home');
const HomeMOREPanel = require('../../panels/home-more');

module.exports = React.createClass({

  displayName: 'screens.home',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    layout: ['layout', 'home'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    if (this.state.layout === 'more') {
      return (
        <div className="home">
          <HomeMOREPanel />
        </div>
      );
    }

    return (
      <div className="home">
        <HomePanel />
      </div>
    );
  },

});
