const React = require('react');
const classnames = require('classnames');
const mixins = require('../../../lib/mixins');
const MemberListingsPanel = require('../../panels/member-listings');
const NPlayFooter = require('../../panels/n-play-footer');

module.exports = React.createClass({

  displayName: 'screens.member-listings',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    layout: ['layout', 'memberListings'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className={classnames('member-listings-screen')}>
        <MemberListingsPanel />
        <NPlayFooter />
      </div>
    );
  },

});
