const React = require('react');
const classnames = require('classnames');
const FacebookGrid = require('../../panels/facebook/grid');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.facebook',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    layout: ['layout', 'facebook'],
    headerLayout: ['layout', 'facebookHeader'],
  },

  facets: {
    listings: ['featuredListings'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className={classnames('facebook-embedded', {
        'facebook-page-admin': this.state.layout === 'admin',
        'facebook-header-version': this.state.headerLayout,
      })}
      >
        <FacebookGrid listings={this.state.listings} />
      </div>
    );
  },

});
