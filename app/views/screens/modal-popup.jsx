/* eslint-disable react/no-unescaped-entities */
const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../lib/mixins');
const CloseButton = require('../components/close_btn');

module.exports = React.createClass({

  displayName: 'screens.modal',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    layout: ['layout', 'modal'],
    agentData: ['shared', 'agent', 'data'],
  },

  close() {
    this.actions.common.toggleModal(this.state.layout);
  },

  componentDidMount() {
  },

  componentWillUpdate(nextProps, nextState) {
    if (!this.state.termsOfUseHomeASAP && nextState.layout === 'Terms' && nextState.layout !== this.state.layout) {
      this.actions.common.fetchTermsTemplate('TermsOfUseHomeASAP', (err, result) => {
        if (err) {
          return;
        }
        this.setState({ termsOfUseHomeASAP: result });
      });
    }
    if (!this.state.privacyPolicyHomeASAP && nextState.layout === 'Privacy/DMCA Policy' && nextState.layout !== this.state.layout) {
      this.actions.common.fetchTermsTemplate('PrivacyPolicyHomeASAP', (err, result) => {
        if (err) {
          return;
        }
        this.setState({ privacyPolicyHomeASAP: result });
      });
    }
    if (!this.state.contestTerms && nextState.layout === 'Enter to Win Rules' && nextState.layout !== this.state.layout) {
      this.actions.common.fetchTermsTemplate('TermsOfUseBuyerContest', (err, result) => {
        if (err) {
          return;
        }
        this.setState({ contestTerms: result });
      });
    }
  },

  getText() {
    if (this.state.layout === 'Terms') {
      const html = {
        __html: this.state.termsOfUseHomeASAP || '<div></div>',
      };
      return (
        <div className="terms-of-use">
          <div dangerouslySetInnerHTML={html} />
        </div>
      );
    }
    if (this.state.layout === 'Privacy/DMCA Policy') {
      const html = {
        __html: this.state.privacyPolicyHomeASAP || '<div></div>',
      };
      return (
        <div className="privacy-policy">
          {
            this.state.agentData && this.state.agentData.BrokerName && this.state.agentData.PrivacyPolicyUrl
              ? (
                <a href={this.state.agentData.PrivacyPolicyUrl} target="_blank">
                  {`${this.state.agentData.BrokerName} Privacy/DMCA Policy [available here]`}
                </a>
              )
              : (
                <p className="text-muted">
                  This brokerage has not provided a custom privacy/DMCA policy.
                </p>
              )
          }
          <div dangerouslySetInnerHTML={html} />
        </div>
      );
    }
    if (this.state.layout === 'Enter to Win Rules') {
      const html = {
        __html: this.state.contestTerms || '<div></div>',
      };
      return <div dangerouslySetInnerHTML={html} />;
    }
    return <div />;
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div>
        <Modal show={!!this.state.layout} onHide={this.close}>
          <Modal.Header>
            <Modal.Title>{this.state.layout}</Modal.Title>
            <CloseButton onClick={this.close} />
          </Modal.Header>
          <Modal.Body>
            {this.getText()}
          </Modal.Body>
          <Modal.Footer />
        </Modal>
      </div>
    );
  },
});
