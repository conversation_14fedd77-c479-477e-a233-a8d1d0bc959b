const React = require('react');
const BodyListings = require('../../panels/body-listings');
const Content = require('../../panels/map/content');
const FullContent = require('../../panels/map/full-content');
const Sort = require('../../panels/sort');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.map',

  mixins: [mixins.debug, mixins.cursors],

  cursors: {
    layout: ['layout', 'map'],
  },

  getInitialState() {
    return {
      showFull: false,
    };
  },

  componentWillUpdate(nextProps, nextState) {
    if (nextState.layout === false) {
      const fullContentComponent = this.refs.FullContent;
      if (fullContentComponent) {
        this.setState({ showFull: false });
      }
    }
  },

  toggleFull() {
    this.setState({ showFull: !this.state.showFull });
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div>
        { this.state.showFull
          ? <FullContent ref="FullContent" onClose={this.toggleFull} />
          : <Content onExpandViewClick={this.toggleFull} />}
        <div className="map-sort">
          <Sort />
        </div>
        <div>
          <BodyListings className="layout--1ns-3 body-listings" screen="map" />
        </div>

      </div>
    );
  },

});
