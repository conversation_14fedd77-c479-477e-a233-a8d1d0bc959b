const React = require('react');
const mixins = require('../../../lib/mixins');
const OnboardingPanel = require('../../panels/onboarding');

module.exports = React.createClass({

  displayName: 'screens.onboarding',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    layout: ['layout', 'onboarding'],
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (

      <OnboardingPanel />

    );
  },

});
