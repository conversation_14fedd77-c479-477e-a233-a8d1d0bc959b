const React = require('react');
const Grid = require('./grid');
const Detail = require('./detail');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.tagging',

  mixins: [mixins.debug, mixins.cursors],

  cursors: {
    layout: ['layout', 'tagging'],
  },

  render() {
    if (!(this.state.layout.grid
      || this.state.layout.detail)) {
      return null;
    }

    return (
      <div>

        {
          this.state.layout.grid
            ? <Grid tag={this.state.layout.grid} />
            : null
          }

        {
          this.state.layout.detail
            ? <Detail tag={this.state.layout.detail} />
            : null
          }

      </div>
    );
  },

});
