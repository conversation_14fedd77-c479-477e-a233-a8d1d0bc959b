const React = require('react');
const map = require('lodash.map');
const filter = require('lodash.filter');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const Menu = require('../../panels/grid-menu/index');
const GridFooter = require('../../panels/grid-footer');
const NPlayFooter = require('../../panels/n-play-footer');
const GridListings = require('../../panels/grid-listings');

module.exports = React.createClass({

  displayName: 'screens.tagging.grid',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils],

  cursors: {
    data: ['screens', 'tagging', 'data'],
    showMenu: ['screens', 'grid', 'showMenu'],
  },
  componentDidMount() {
    // if(!this.state.listings){
    //  this.actions.featured.getFeaturedListings();
    // }
  },

  onNav(listingId) {
    this.actions.tagging.onNav(listingId);
  },
  onPhotos(listing) {
    this.actions.tagging.onPhotos(listing.Id);
  },

  render() {
    return (
      <div>
        <GridListings
          listings={this.state.data
        && map(
          filter(this.state.data.Listings, (item) => !!item.Listing),
          function (item) {
            item.Listing.Image = this.utils.getImageUrls(item.Listing, true);
            return item.Listing;
          }, this,
        )}
          onNav={this.onNav}
          onPhotos={this.onPhotos}
        >
          <div className={
            classNames('menu-fixed',
              {
                'slide-enter slide-enter-active': this.state.showMenu,
                'slide-leave slide-leave-active': !this.state.showMenu,
              })
}
          >
            <div className="grid-menu">
              <Menu mode="tagging" />
            </div>
          </div>
        </GridListings>
        <GridFooter
          mlsIds={this.state.data && this.state.data.Listings
          && this.utils.uniqueMLSIdsFromListings(map(this.state.data.Listings, (item) => item && item.Listing))}
          className={classNames('grid-footer row col-center layout--1ns', { 'lights-out': this.state.lightsOff }, { 'lights-on': !this.state.lightsOff })}
        />
        <NPlayFooter />
      </div>
    );
  },

});
