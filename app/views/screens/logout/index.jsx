const React = require('react');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.logout',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    layout: ['layout', 'logout'],
  },

  returnToHome() {
    this.actions.common.backToHome();
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    return (
      <div className="logout-container">
        <div className="logout-background" />
        <div className="vertical-center">
          <div className="logout-logo" />
          <div className="logout-goodbye">See you later!</div>
          <div className="logout-details">You have been logged out from HomeASAP.</div>
          <button type="button" className="logout-return btn btn-primary" onClick={this.returnToHome}>Return to HomeASAP.com</button>
        </div>
      </div>
    );
  },

});
