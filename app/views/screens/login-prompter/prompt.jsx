const React = require('react');
const classNames = require('classnames');
const { OverlayTrigger, Popover } = require('react-bootstrap');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

const PropTypes = React.PropTypes;

module.exports = React.createClass({

  displayName: 'screens.login-prompt',

  mixins: [mixins.debug, mixins.actions],

  propTypes: {
    handleCloseClick: PropTypes.func,
    handleLoginClick: PropTypes.func,
    hardwall: PropTypes.bool,
  },

  render() {
    let closeButton;
    if (this.actions.common.forceLoginFirstInteraction()) {
      closeButton = null;
    } else if (this.props.hardwall) {
      const popover = <Popover>Please login to continue</Popover>;
      closeButton = (
        <OverlayTrigger trigger={['hover', 'focus']} rootClose placement="left" overlay={popover}>
          <div className="close-button" onClick={null}>
            <SVGIcon name="icon-close-button" />
          </div>
        </OverlayTrigger>
      );
    } else {
      closeButton = (
        <div className="close-button" role="button" tabIndex="-1" aria-label="Close" onClick={this.props.handleCloseClick}>
          <SVGIcon name="icon-close-button" />
        </div>
      );
    }

    const loginNote = this.props.loginNote || 'Get updates on your favorite listings and when homes are added to your search area.';

    return (
      <div className={classNames('login-prompt', this.props.hardwall ? 'hardwall-prompt' : '')}>
        {closeButton}
        {this.props.children}
        <div className="common-login">
          { this.props.hideAsapTitle
            ? null
            : <div className="title">Amazing Search. Amazing Places.</div>}
          <div className="login-button" role="button" tabIndex="-1" aria-label="Login" onClick={this.props.handleLoginClick}>
            <div>{this.props.loginButtonText || 'Get real-time updates'}</div>
          </div>
          <div className="prompt-login-note">{loginNote}</div>
          <div className="are-you-an-agent">
            <a href={`https://idx.homeasap.com${window.CONFIG.IN_PROD ? '' : '?test'}`} target="_blank">Are you an agent?</a>
          </div>
        </div>
      </div>
    );
  },

});
