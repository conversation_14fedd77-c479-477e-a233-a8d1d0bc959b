const React = require('react');
const _ = require('lodash');
const shortid = require('shortid');
const Slider = require('react-slick');
const mixins = require('../../../lib/mixins');

const Prompter = require('./prompter');
const Prompt = require('./prompt');

const modalData = require('./modal-data');
const HardwallContent = require('./hardwall-modal');
const ProgressModal = require('./progress-modal');
const ContestModal = require('./contest-modal');

module.exports = React.createClass({

  displayName: 'screens.login-prompter',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors],

  cursors: {
    loggedIn: ['shared', 'buyer', 'Id'],
  },

  getInitialState() {
    return {
      intermediateModals: [],
      firstModal: null,
      hardwallModal: null,
    };
  },

  getDefaultProps() {
    return {

    };
  },

  componentDidMount() {
    // set up the configuration and then generate modals

    this.setState({
      configuration: this.generateConfiguration(),
    }, () => {
      this.setState({
        intermediateModals: this.generateIntermediateModals(),
        firstModal: this.generateFirstModal(),
        hardwallModal: this.generateHardwallModal(),
        progressModal: this.generateProgressModal(),
        contestModal: this.generateContestModal(),
      });
    });
  },

  generateConfiguration() {
    // const queryParameters = this.actions.common.getQueryParameters();

    const defaults = {
      clicksToShowFirstModal: 2,
      clicksToShowNextModal: 4,
      photoViewsToShowModal: 8,
      countTotalNormalModals: 3,
    };

    if (this.actions.common.userCameFromFacebookAd()) {
      return {
        clicksToShowFirstModal: 2,
        clicksToShowNextModal: 4,
        photoViewsToShowModal: 8,
        countTotalNormalModals: 3,
      };
    }

    if (this.actions.common.forceLoginFirstInteraction() === 'force_login') {
      return {
        clicksToShowFirstModal: 1,
        clicksToShowNextModal: 1,
        photoViewsToShowModal: 1,
        countTotalNormalModals: 1,
      };
    }

    return defaults;
  },

  generateContestModal() {
    const loginNote = (
      <span>
        {'This promotion is not sponsored, endorsed, administered, or associated with Facebook. '}
        <a
          href="#"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            this.actions.common.toggleModal('Enter to Win Rules');
          }}
          target="_blank"
        >
          Official rules
        </a>
      </span>
    );

    const prompt = (
      <Prompt hideAsapTitle loginButtonText="Enter Now!" loginNote={loginNote}>
        <ContestModal />
      </Prompt>
    );

    return {
      prompt,
      clientId: shortid.generate(),
      modalType: 'contest',
    };
  },

  generateProgressModal() {
    const prompt = (
      <Prompt hideAsapTitle loginButtonText="Save my progress">
        <ProgressModal />
      </Prompt>
    );

    return {
      prompt,
      clientId: shortid.generate(),
      modalType: 'progress',
    };
  },

  generateFirstModal() {
    return {
      prompt: <Prompt />,
      clientId: shortid.generate(),
      modalType: 'first',
    };
  },

  generateQuoteModals() {
    let { images, quotes, colors } = modalData;

    // shuffle if set
    if (this.props.randomizeModals) {
      images = _.shuffle(images);
      quotes = _.shuffle(quotes);
      colors = _.shuffle(colors);
    }

    const LOCALSTORAGE_VIEWED_MODAL_QUOTES_KEY = 'viewed_modal_quotes';
    const LOCALSTORAGE_VIEWED_MODAL_IMAGES_KEY = 'viewed_modal_images';

    function getViewedQuotes() {
      return JSON.parse(window.localStorageAlias.getItem(LOCALSTORAGE_VIEWED_MODAL_QUOTES_KEY)) || [];
    }
    function getViewedImages() {
      return JSON.parse(window.localStorageAlias.getItem(LOCALSTORAGE_VIEWED_MODAL_IMAGES_KEY)) || [];
    }

    // remove any quotes or images that have already been shown to the user
    images = _.reject(images, _.curry(_.includes)(getViewedImages()));
    quotes = _.reject(quotes, _.curry(_.includes)(getViewedQuotes()));

    // limit the quotes to the number of available images, in case we run out due to a mismatch of numbers
    if (images.length < quotes.length) {
      quotes = quotes.slice(0, images.length);
    }

    // limit the quotes to the number we are going to show, if there are more quotes than the amount
    if (quotes.length > this.state.configuration.countTotalNormalModals) {
      quotes = quotes.slice(0, this.state.configuration.countTotalNormalModals);
    }

    // set up some quick functions for use below
    function flagQuoteAsViewed(quote) {
      if (!quote) {
        return;
      }
      const viewedQuotes = getViewedQuotes();
      viewedQuotes.push(quote);
      window.localStorageAlias.setItem(LOCALSTORAGE_VIEWED_MODAL_QUOTES_KEY, JSON.stringify(viewedQuotes));
    }

    function flagImageAsViewed(image) {
      if (!image) {
        return;
      }
      const viewedImages = getViewedImages();
      viewedImages.push(image);
      window.localStorageAlias.setItem(LOCALSTORAGE_VIEWED_MODAL_IMAGES_KEY, JSON.stringify(viewedImages));
    }

    // create a modal for each carousel starting index (not necessarily ideal
    // to create X modals, but we want to be agnostic with regards to injecting
    // modals into the prompter)

    const quoteElements = _.map(quotes, (quote, index) => {
      const image = images[index];
      const color = colors[index % colors.length];

      // flag both image and quote as used
      flagImageAsViewed(image);
      flagQuoteAsViewed(quote);

      // using modulus % cycles through the colors
      const style = {
        backgroundColor: color,
      };

      return (
        <div key={quote}>
          <div style={style} className="login-quote-container">
            <img alt="Quote" src={`//nplayassets.blob.core.windows.net/search2/loginprompt/${image}`} width="100px" height="100px" />
            <div>
              {`"${quote}"`}
            </div>
          </div>
        </div>
      );
    });

    const modals = _.map(quoteElements, (quoteElement, index) => {
      const sliderSettings = {
        initialSlide: index,
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        autoplay: true,
        autoplaySpeed: 12000,
        draggable: true,
      };

      const prompt = (
        <Prompt key={index}>
          <Slider {...sliderSettings}>
            {quoteElements}
          </Slider>
        </Prompt>
      );

      return {
        prompt,
        quote: quotes[index],
        image: images[index],
        color: colors[index % colors.length],
        clientId: shortid.generate(),
        modalType: 'quote',
      };
    });
    return modals;
  },

  generateIntermediateModals() {
    const modals = this.generateQuoteModals();
    return modals;
  },

  generateHardwallModal() {
    const prompt = (
      <Prompt hardwall>
        <HardwallContent />
      </Prompt>
    );
    return {
      prompt,
      clientId: shortid.generate(),
      modalType: 'hardwall',
    };
  },

  render() {
    if (this.state.loggedIn) {
      return null;
    }
    if (!(this.state.firstModal && this.state.hardwallModal && this.state.intermediateModals)) {
      return null;
    }
    return (
      <div id="login-screen">
        <Prompter
          intermediateModals={this.state.intermediateModals}
          firstModal={this.state.firstModal}
          hardwallModal={this.state.hardwallModal}
          progressModal={this.state.progressModal}
          contestModal={this.state.contestModal}
          contestActive={false}
          randomizeModals={this.props.randomizeModals}
          disableHardwall
          {...this.state.configuration}
        />
      </div>
    );
  },

});
