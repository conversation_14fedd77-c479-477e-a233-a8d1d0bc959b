const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'screens.progress-prompt-content',

  mixins: [mixins.debug, mixins.cursors],

  cursors: {
    savedSearches: ['shared', 'notificationCenter', 'Recent Searches'],
    savedHomes: ['shared', 'notificationCenter', 'Tagged Homes'],
    viewedHomes: ['shared', 'notificationCenter', 'Viewed Homes'],
  },

  getInitialState() {
    return {
      toggled: false,
    };
  },

  handleShowMoreClick() {
    this.setState({ toggled: true });
  },

  render() {
    // just show the second screen if they click show more
    return (
      <div className="progress-modal-content">
        <h3>Save your progress!</h3>
        <div className="progress-track">
          <div className="">
            <SVGIcon name="icon-house" />
            <div className="label">{`Viewed ${this.state.viewedHomes || 0} homes`}</div>
          </div>
          <div className="">
            <SVGIcon name="icon-bell" />
            <div className="label">{`Saved ${this.state.savedHomes || 0} homes`}</div>
          </div>
          <div className="">
            <SVGIcon name="icon-search" />
            <div className="label">{`${this.state.savedSearches || 0} searches`}</div>
          </div>
        </div>
      </div>
    );
  },

});
