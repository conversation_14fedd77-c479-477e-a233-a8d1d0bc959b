const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'screens.hardwall-prompt-content',

  mixins: [mixins.debug],

  getInitialState() {
    return {
      toggled: false,
    };
  },

  handleShowMoreClick() {
    this.setState({ toggled: true });
  },

  render() {
    // just show the second screen if they click show more
    return (
      <div className="hardwall">
        { this.state.toggled
          ? (
            <div className="hardwall-more">
              <SVGIcon name="icon-red-line-house" />
              <ul>
                <li>Amazing, accurate, fast home search</li>
                <li>No login popups or ads</li>
                <li>Set alerts - new listings, status updates, prices changes</li>
                <li>Tag, save, share, collaborate</li>
              </ul>
            </div>
          )
          : (
            <div>
              <div>Wow!</div>
              <div>You&apos;ve been busy searching some amazing homes, you should log in. You&apos;ll get to use some awesome features not available anywhere else!</div>
              <div>
                <a role="button" tabIndex="-1" aria-label="Learn more" onClick={this.handleShowMoreClick}>Learn more</a>
              </div>
            </div>
          )}
      </div>
    );
  },

});
