const React = require('react');
const moment = require('moment');

const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.contest-prompt-content',

  mixins: [mixins.debug, mixins.utils],

  render() {
    const contestImageUrl = this.utils.useMobileSite()
      ? '//nplayassets.blob.core.windows.net/images/fblogin-sweepstakes-mobile-600x640-final.png'
      : '//nplayassets.blob.core.windows.net/images/fblogin-sweepstakes-700x420-final.png';

    const firstOfNextMonth = moment().add(1, 'months').startOf('month').format('MMMM Do, YYYY');

    return (
      <div className="contest-modal-content">
        <img alt="Contest" src={contestImageUrl} />
        <div className="contest-draw-date">
          <span>{'Next drawing on: '}</span>
          <span>{firstOfNextMonth}</span>
        </div>
      </div>
    );
  },

});
