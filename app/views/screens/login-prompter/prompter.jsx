const React = require('react');
const _ = require('lodash');

const PropTypes = React.PropTypes;
const Modal = require('react-bootstrap').Modal;
const shortid = require('shortid');
const mixins = require('../../../lib/mixins');
const Prompt = require('./prompt');

module.exports = React.createClass({

  displayName: 'screens.login-prompter',

  mixins: [mixins.debug, mixins.actions, mixins.router, mixins.utils, mixins.cursors, mixins.events],

  propTypes: {
    clicksToShowFirstModal: PropTypes.number.isRequired,
    clicksToShowNextModal: PropTypes.number.isRequired,
    countTotalNormalModals: PropTypes.number.isRequired,
    intermediateModals: PropTypes.arrayOf(PropTypes.object),
    firstModal: PropTypes.object.isRequired,
    hardwallModal: PropTypes.object.isRequired,
    progressModal: PropTypes.object.isRequired,
    disableHardwall: PropTypes.bool,
  },

  cursors: {
    READAgent: ['shared', 'detectedAgent'],
    homeASAPLoginModalVisible: ['shared', 'login', 'homeASAPModal'],
  },

  queuedLogging: {},

  getInitialState() {
    return {
      propertyViewCount: 0,
      galleryViewCount: 0,
      progressionModalIndex: 0,
      currentModal: null,
      visible: false,
      outOfNormalModals: false,
    };
  },

  componentDidUpdate(prevProps, prevState) {
    if (this.state.READAgent.data && !prevState.READAgent.data) {
      if (this.state.currentModal) {
        this.executeOrQueueLoggingUntilId(this.actions.login.flagLoginPromptAutoLoggedIn);
      }
      this.setState({ visible: false });
    }
  },

  componentDidMount() {
    this.prepareModals();
    this.attachAppListeners();
  },

  createLoginPromptViewRecord() {
    // intermediate modals + first + last
    const totalModalCount = this.props.intermediateModals.length + 2;

    if (!this.state.currentModal) {
      return;
    }

    const data = {
      color: this.state.currentModal.color,
      quoteFull: this.state.currentModal.quote,
      image: this.state.currentModal.image,
      modalViewCount: this.state.progressionModalIndex,
      clicksToShowFirstModal: this.props.clicksToShowFirstModal,
      totalModalCount,
      clicksToShowNextModal: this.props.clicksToShowNextModal,
      propertyViewCount: this.state.propertyViewCount,
      modalProgression: this.props.randomizeModals ? 'random' : 'ordered',
      modalType: this.state.currentModal.modalType,
    };

    const clientId = this.state.currentModal.clientId;
    this.actions.login.createLoginPromptViewRecord(data, (err, id) => {
      this.setModalId(clientId, id, () => {
        this.sendQueuedLogging();
      });
    });
  },

  createInitialUnpromptedRecord() {
    // intermediate modals + first + last
    const totalModalCount = this.props.intermediateModals.length + 2;

    const data = {
      modalViewCount: this.state.progressionModalIndex,
      clicksToShowFirstModal: this.props.clicksToShowFirstModal,
      totalModalCount,
      clicksToShowNextModal: this.props.clicksToShowNextModal,
      propertyViewCount: this.state.propertyViewCount,
      modalProgression: this.props.randomizeModals ? 'random' : 'ordered',
      modalType: 'initial_unprompted',
    };

    this.actions.login.createLoginPromptViewRecord(data);
  },

  createWindowCloseRecord() {
    // intermediate modals + first + last
    const totalModalCount = this.props.intermediateModals.length + 2;

    const data = {
      modalViewCount: this.state.progressionModalIndex,
      clicksToShowFirstModal: this.props.clicksToShowFirstModal,
      totalModalCount,
      clicksToShowNextModal: this.props.clicksToShowNextModal,
      propertyViewCount: this.state.propertyViewCount,
      modalProgression: this.props.randomizeModals ? 'random' : 'ordered',
      modalType: 'exit_site',
    };

    this.actions.login.createWindowCloseRecord(data);
  },

  setModalId(clientId, id, callback) {
    const modals = _.map(this.state.modals, (modal) => {
      const mappedModal = modal;
      if (clientId == modal.clientId) {
        mappedModal.id = id;
      }
      return mappedModal;
    });
    this.setState({
      modals,
    }, callback);
  },

  sendQueuedLogging() {
    _.each(this.queuedLogging, (queuedFunctions, clientId) => {
      // find a modals with this client id
      const modal = _.find(this.state.modals, (m) => m.clientId == clientId);
      if (!modal) {
        console.log('No modal found for clientId');
        return;
      }

      // check if it has a database id
      if (modal.id) {
        // if it does, inject the id and execute the queued functions
        _.each(queuedFunctions, (logFunction) => {
          logFunction(modal.id);
        });

        // clear the functions for this id
        this.queuedLogging[clientId] = [];
      }
    });
  },

  handleContestLoginClick() {
    this.handleLoginClick((result) => {
      // connected is the facebook API response for success
      if (result === 'connected') {
        // TODO: dynamic contest id from endpoint. For now we will hardcode it because there is only one contest
        this.actions.login.registerCallback(() => {
          this.actions.home.submitContestEntry('DDF4ECC2-C8F7-41AE-8FD2-159F14537544');
        });
      }
    });
  },

  handleLoginClick(callback = () => {}) {
    this.executeOrQueueLoggingUntilId(this.actions.login.saveLoginPromptLoginButtonClicked);
    this.actions.analytics.sendEvent('login', 'clicked fb login');

    this.actions.login.attemptSignUp({}, (response) => {
      const result = response.status;
      this.actions.login.statusChangeCallback(response);

      this.executeOrQueueLoggingUntilId((id) => {
        this.actions.login.saveLoginPromptFacebookResult(id, result);
      });

      if (typeof callback === 'function') {
        callback(result);
      }

      this.sendQueuedLogging();
    });

    this.sendQueuedLogging();
  },

  handleCloseClick() {
    this.executeOrQueueLoggingUntilId(this.actions.login.saveLoginPromptCloseClicked);

    this.sendQueuedLogging();

    this.setState({
      visible: false,
    });
  },

  executeOrQueueLoggingUntilId(callback) {
    // if we have the id, just go execute it immediately
    if (this.state.currentModal.id) {
      callback(this.state.currentModal.id);
      return;
    }

    // otherwise actually queue it
    const clientId = this.state.currentModal.clientId;
    if (!this.queuedLogging[clientId]) {
      this.queuedLogging[clientId] = [];
    }

    this.queuedLogging[clientId].push(callback);
  },

  generateLoginPrompt({ loginButtonText = 'Log in to continue', modalType = 'continue' }) {
    return {
      prompt: <Prompt loginButtonText={loginButtonText} handleCloseClick={this.handleCloseClick} handleLoginClick={this.handleLoginClick} />,
      clientId: shortid.generate(),
      modalType,
    };
  },

  prepareModals() {
    let modals = [];

    let intermediateModals = this.props.intermediateModals;

    // only take as many as were specified in the settings
    intermediateModals = intermediateModals.slice(0, this.props.countTotalNormalModals);

    // prepare the contest modal
    const modifiedContestModal = this.props.contestModal;
    modifiedContestModal.prompt = React.cloneElement(this.props.contestModal.prompt, {
      handleCloseClick: this.handleCloseClick,
      handleLoginClick: this.handleContestLoginClick,
    });

    // add all of the modals in order
    modals.push(this.props.firstModal);
    modals = modals.concat(intermediateModals);

    if (!this.props.disableHardwall) {
      modals.push(this.props.hardwallModal);
    }

    // attach the close handler to the modals
    const mappedModals = _.map(modals, (modal) => {
      const mappedModal = modal;
      mappedModal.prompt = React.cloneElement(modal.prompt, {
        handleCloseClick: this.handleCloseClick,
        handleLoginClick: this.handleLoginClick,
      });
      return mappedModal;
    });

    // randomly insert contest modal within the intermediateModals
    if (this.props.contestActive) {
      mappedModals.splice(Math.floor(Math.random() * intermediateModals.length) + 1, 0, modifiedContestModal);
    }

    // also prepare the progress modal
    const modifiedProgressModal = this.props.progressModal;
    modifiedProgressModal.prompt = React.cloneElement(this.props.progressModal.prompt, {
      handleCloseClick: this.handleCloseClick,
      handleLoginClick: this.handleLoginClick,
    });

    // we can tack progress modal onto the end of the list of modals now
    // because the hardwall will stop users from progressing up to that point in
    // the array, and we will be able to use the id/queuing logic on the progress
    // modal without special cases
    mappedModals.push(modifiedProgressModal);

    this.setState({
      modals: mappedModals,
      progressModal: modifiedProgressModal,
    }, () => {
      _.defer(this.checkTrigger);
    });
  },

  checkTrigger() {
    // don't fire if it's not allowed
    if (!this.allowPrompting()) {
      return;
    }

    if (this.state.outOfNormalModals) {
      return;
    }

    // don't fire if we already showed this property view count modal
    if (this.lastShownPropertyViewCount && this.lastShownPropertyViewCount == this.state.propertyViewCount) {
      return;
    }

    const showImmediately = ((Number(this.props.clicksToShowFirstModal) == 0) && (Number(this.state.progressionModalIndex) == 0));
    const showFirstModal = (this.props.clicksToShowFirstModal > 0) && (this.state.propertyViewCount === this.props.clicksToShowFirstModal);
    const showIntermediateModal = (this.state.propertyViewCount > this.props.clicksToShowFirstModal) && ((this.state.propertyViewCount - this.props.clicksToShowFirstModal) % this.props.clicksToShowNextModal) === 0;
    const showLastModal = (this.state.propertyViewCount === (this.props.clicksToShowFirstModal + this.props.clicksToShowNextModal * this.props.countTotalNormalModals));

    if (!showImmediately && (Number(this.state.progressionModalIndex) == 0) && !this.state.createdInitialUnprompted) {
      return this.setState({ createdInitialUnprompted: true }, () => {
        this.createInitialUnpromptedRecord();
      });
    }

    if (showImmediately || showFirstModal || showIntermediateModal || showLastModal) {
      this.lastShownPropertyViewCount = this.state.propertyViewCount;

      if (this.props.disableHardwall && showLastModal) {
        this.setState({ outOfNormalModals: true });
      }
      this.fire();
    }
  },

  fire() {
    const currentModal = this.state.modals[this.state.progressionModalIndex] || {};

    this.setState({
      visible: true,
      currentModal,
      progressionModalIndex: this.state.progressionModalIndex + 1,
    }, () => {
      this.createLoginPromptViewRecord();
    });
  },

  allowPrompting(opt = {}) {
    const allowedRoutes = [
      this.router.ROUTE_NAMES.MAP,
      this.router.ROUTE_NAMES.GRID,
      this.router.ROUTE_NAMES.FEATURED,
      this.router.ROUTE_NAMES.TAGGING,
      this.router.ROUTE_NAMES.LISTING,
    ];

    if (opt.source !== 'routeComplete') {
      allowedRoutes.push(this.router.ROUTE_NAMES.FACEBOOK);
      allowedRoutes.push(this.router.ROUTE_NAMES.FRAME);
    }

    // don't fire if we are showing a modal
    if (this.state.visible) {
      return false;
    }

    if (this.actions.demo.inDemo()) {
      return false;
    }

    if (this.actions.common.isLoggedIn()) {
      return false;
    }

    if (this.state.READAgent.data || this.actions.read.getDetectedAgentData() || (document.referrer && document.referrer.match(/sacontrolpanel/gi))) {
      return false;
    }

    if (_.get(this.router, 'currentRoute.params.tab') === 'agent') {
      // Disable login prompt on agent profile in facebook frame
      return false;
    }

    const allowedRoute = _.includes(allowedRoutes, _.get(this.router, 'currentRoute.name'));
    return allowedRoute;
  },

  attachAppListeners() {
    this.events.on(this.events.LISTING_VIEWED, () => {
      if (!this.allowPrompting()) {
        return false;
      }

      // don't start counting this on the first route
      if (this.router.getHistoryLength() <= 1) {
        return false;
      }

      // increase the listing view count and check the trigger after
      this.setState({
        propertyViewCount: this.state.propertyViewCount + 1,
      });
    });

    this.events.on(this.events.ROUTE_COMPLETE, () => {
      if (!this.allowPrompting({ source: 'routeComplete' })) {
        return false;
      }
      _.defer(this.checkTrigger);
    });

    this.events.on(this.events.PROGRESS_CLICKED, () => {
      this.setState({
        currentModal: this.state.progressModal || {},
        visible: true,
      }, () => {
        this.createLoginPromptViewRecord();
      });
    });

    this.events.on(this.events.GALLERY_PHOTO_VIEWED, () => {
      if (!this.allowPrompting()) {
        return false;
      }

      this.setState({
        galleryViewCount: this.state.galleryViewCount + 1,
      }, () => {
        if (this.state.galleryViewCount == this.props.photoViewsToShowModal) {
          this.setState({
            currentModal: this.state.progressModal,
            visible: true,
          }, () => {
            this.createLoginPromptViewRecord();
            this.setState({ galleryViewCount: 0 });
          });
        }
      });
    });

    this.events.on(this.events.CHECK_FACEBOOK_LOGIN_STATUS_RESPONSE, (response) => {
      // only if we have a current modal
      const loggedIn = response.status == 'connected';
      if (this.state.currentModal && loggedIn) {
        this.executeOrQueueLoggingUntilId(this.actions.login.flagLoginPromptAutoLoggedIn);
      }
    });

    this.events.on(this.events.PROMPT_FOR_LOGIN, ({ loginButtonText, modalType }) => {
      if (!this.allowPrompting()) {
        return false;
      }

      const prompt = this.generateLoginPrompt({ loginButtonText, modalType });

      // add the new prompt to our modals array
      const modals = this.state.modals;
      modals.push(prompt);

      this.setState({
        currentModal: prompt,
        modals,
        visible: true,
      }, () => {
        this.createLoginPromptViewRecord();
      });
    });

    // if we have navigator and sendbeacon, use it to send some close analytics
    if (navigator && navigator.sendBeacon) {
      window.addEventListener('unload', this.createWindowCloseRecord);
    }
  },

  render() {
    if (this.state.homeASAPLoginModalVisible) {
      return null;
    }

    return (
      <div className="login-prompter">
        <Modal show={this.state.visible} onHide={this.close} className="login-prompter-modal">
          <Modal.Body>
            {this.state.currentModal ? this.state.currentModal.prompt : null}
          </Modal.Body>
        </Modal>
      </div>
    );
  },

});
