const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const Menu = require('../../panels/grid-menu/index');
const GridFooter = require('../../panels/grid-footer');
const NPlayFooter = require('../../panels/n-play-footer');
const GridListings = require('../../panels/grid-listings');

module.exports = React.createClass({

  displayName: 'screens.featured.grid',

  mixins: [mixins.debug, mixins.cursors, mixins.actions],

  cursors: {
    showMenu: ['screens', 'grid', 'showMenu'],
  },

  facets: {
    listings: ['featuredListings'],
  },

  componentDidMount() {
  },

  onNav(listingId) {
    this.actions.featured.onNav(listingId);
  },
  onPhotos(listing) {
    this.actions.featured.onPhotos(listing.Id);
  },

  render() {
    return (
      <div>
        <GridListings listings={this.state.listings} onNav={this.onNav} onPhotos={this.onPhotos}>
          <div className={
            classNames('menu-fixed',
              {
                'slide-enter slide-enter-active': this.state.showMenu,
                'slide-leave slide-leave-active': !this.state.showMenu,
              })
}
          >
            <div className="grid-menu">
              <Menu mode="featured" />
            </div>
          </div>
        </GridListings>
        <GridFooter className={classNames('grid-footer row col-center layout--1ns', { 'lights-out': this.state.lightsOff }, { 'lights-on': !this.state.lightsOff })} />
        <NPlayFooter />
      </div>
    );
  },

});
