const React = require('react');
const Ref = require('./ref');
const FeaturedListings = require('../../panels/featured-listings');
const ListingFullView = require('../../panels/listing/full');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.featured.detail',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    data: ['screens', 'featured', 'detail'],
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data.ZipCode);
  },

  onNav(id) {
    this.actions.featured.onNav(id);
  },

  onClose() {
    this.actions.featured.onNav();
  },

  partial() {
    return (
      <ListingFullView listing={this.state.data} onPhotoClick={this.onPhotoClick} onClose={this.onClose} />
    );
  },

  render() {
    return (
      <div>

        <FeaturedListings className="layout--1ns-3 featured-listings" onNav={this.onNav} />

        <div className="row col-center layout--2s-3">

          <div className="col-23-24 grid-detail">

            {this.state.data ? this.partial() : <Ref />}

          </div>
        </div>

      </div>
    );
  },

});
