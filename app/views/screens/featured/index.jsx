const React = require('react');
const Grid = require('./grid');
const Detail = require('./detail');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.featured',

  mixins: [mixins.debug, mixins.cursors],

  cursors: {
    layout: ['layout', 'featured'],
  },

  render() {
    if (!(this.state.layout.grid
      || this.state.layout.detail)) {
      return null;
    }

    return (
      <div>

        {
          this.state.layout.grid
            ? <Grid />
            : null
          }

        {
          this.state.layout.detail
            ? <Detail />
            : null
          }

      </div>
    );
  },

});
