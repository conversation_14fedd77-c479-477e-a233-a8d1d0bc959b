const React = require('react');
const classNames = require('classnames');
const _ = require('lodash');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.home-worth',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors, mixins.leaflet],

  cursors: {
    layout: ['layout', 'homeWorth'],
    agent: ['shared', 'agent', 'data'],
    searchResult: ['screens', 'homeWorth', 'searchResult'],
  },

  getInitialState() {
    const BG_IMAGES = [
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-1.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-2.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-3.jpg',
      // 'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-4.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-5.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-6.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-7.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-8.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-9.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-hv/HVLT-Background-img-10.jpg',
    ];

    return {
      bgImage: _.sample(BG_IMAGES),
    };
  },

  componentDidMount() {
    this.sessionToken = String(+new Date());
    window.addEventListener('beforeunload', this.cleanup);
  },

  handleInputChange(e) {
    this.setState({ [e.target.name]: e.target.value });
  },

  componentDidUpdate(prevProps, prevState) {
    if (window.google && !this.autocomplete) {
      const input = document.getElementById('home-worth-address-input');
      if (input) {
        this.autocomplete = new google.maps.places.Autocomplete(input, { types: ['address'], sessionToken: this.sessionToken });
        this.autocomplete.addListener('place_changed', this.autocompleteChanged);
      }
    }

    if (this.state.layout && !this.map) {
      this.setupMap();
    }

    if (!_.isEqual(prevState.searchResult, this.state.searchResult)) {
      this.updatePropertyMarker();
    } else if (this.state.searchResult && !this.marker) {
      this.updatePropertyMarker();
    }

    if ((!this.state.layout) && prevState.layout) {
      this.cleanup();
    }
  },

  componentWillUnmount() {
    this.cleanup();
    window.removeEventListener('beforeunload', this.cleanup);
  },

  cleanup() {
    this.map = null;
    this.autocomplete = null;
  },

  autocompleteChanged() {
    const place = this.autocomplete.getPlace();
    if (place && place.formatted_address) {
      const address = place.formatted_address;

      this.actions.analytics.sendEvent('home value', 'address selected');

      this.setState({ success: false }, () => {
        this.actions.homeWorth.submitHomeWorthSearch(address, place);
      });
    }
  },

  submitHomeWorthForm(e) {
    e.stopPropagation();
    e.preventDefault();

    const formData = new FormData(e.target);
    // const {
    //   name, email, phone, when,
    // } = formData.entries();

    const data = {};
    for (const [key, value] of formData) {
      data[key] = value;
    }

    if ((!data.name) || (!data.email)) {
      this.setState({ error: 'Missing data' });
    }

    data.propertyAddress = _.get(this.state.searchResult, 'formatted_address', '').replace(', USA', '');

    this.actions.analytics.sendEvent('home value', 'submitting');

    this.actions.homeWorth.submitHomeWorthForm(data, (err) => {
      if (!err) {
        this.setState({ success: true });
        this.actions.analytics.sendEvent('home value', 'submit success');
      } else {
        this.actions.analytics.sendEvent('home value', 'submit error');
        alert('Error occured, please try again later.');
      }
    });
  },

  renderFoundHome() {
    return (
      <div className="found-home">
        <div id="found-home-map" ref="map" />
        <div id="found-home-form">
          <h1>We Found It!</h1>
          <address>{_.get(this.state.searchResult, 'formatted_address', '').replace(', USA', '')}</address>
          <div id="prompt">Please let us know where we should send your home valuation report:</div>
          <form onSubmit={this.submitHomeWorthForm} name="home-worth-info-form">
            <div className="row">
              <label htmlFor="home-worth-fullname">Full Name</label>
              <input id="home-worth-fullname" name="name" type="text" placeholder="John Doe" required />
            </div>
            <div className="row">
              <label htmlFor="home-worth-email">Email</label>
              <input id="home-worth-email" name="email" type="email" placeholder="<EMAIL>" required />
            </div>
            <div className="row">
              <label htmlFor="home-worth-phone">Phone (optional)</label>
              <input id="home-worth-phone" name="phone" type="tel" placeholder="###-###-####" />
            </div>
            <div className="row">
              <label htmlFor="home-worth-when">When are you planning to sell?</label>
              <select id="home-worth-when" name="whenToSell" required defaultValue="">
                <option value="" disabled hidden>&nbsp;</option>
                <option>As soon as possible</option>
                <option>Within 3-6 months</option>
                <option>Within a year</option>
                <option>Just curious</option>
              </select>
            </div>
            <button type="submit">Request Report</button>
          </form>
        </div>
      </div>
    );
  },

  setupMap() {
    if (this.map) {
      return;
    }

    this.map = L.map(this.refs.map, {
      keyboard: false,
      dragging: false,
      zoomControl: false,
      scrollWheelZoom: 'center',
      doubleClickZoom: 'center',
    });

    this.leaflet.getBasicLayer().addTo(this.map);
  },

  updatePropertyMarker() {
    const property = _.get(this.state.searchResult, 'geometry.location');
    if (this.marker) {
      this.map.removeLayer(this.marker);
    }
    if (this.markerLabel) {
      this.map.removeLayer(this.markerLabel);
    }

    if (property && this.map) {
      const { lat, lng } = property;
      const position = [lat, lng];
      const icon = this.leaflet.favoriteMarker_svg;
      icon.options.className = `${icon.options.className} favorite`;
      this.marker = L.marker(position, { icon: this.leaflet.favoriteMarker_svg })
        .addTo(this.map);
      this.markerLabel = L.marker(position, {
        icon: new L.DivIcon({
          className: 'marker-label',
          html: `<div class="arrow-up"></div><p>${_.get(this.state.searchResult, 'formatted_address', '').replace(', USA', '')}</p>`,
          iconSize: ['auto', 'auto'],
          iconAnchor: [0, -5],
        }),
      })
        .addTo(this.map);

      if (this.utils.useMobileSite()) {
        position[0] += 0.003;
      }
      this.map.setView(position, 13);
    }
  },

  renderSuccess() {
    return (
      <div className="success">
        <h1>Your Request Has Been Received!</h1>
        <div>Thanks for submitting your request. Your report should be processed soon so keep an eye on your inbox.</div>
        {
          (this.state.agent.Phone || this.state.agent.Email)
            ? (
              <div>
                If you have any questions or want to receive your valuation quicker, please
                &nbsp;
                {[
                  this.state.agent.Phone ? (
                    <span key="call">
                      give me a call at&nbsp;
                      <a href={this.getCallAgentHref()}>{this.utils.formatPhone(this.state.agent.Phone)}</a>
                    </span>
                  ) : null,
                  (this.state.agent.Phone && this.state.agent.Email) ? <span> or </span> : null,
                  this.state.agent.Email ? (
                    <span key="email">
                      email me at&nbsp;
                      <a href={this.getEmailAgentHref()}>{this.state.agent.Email}</a>
                    </span>
                  ) : null,
                ]}
                .
              </div>
            ) : null
        }
        {
          this.state.agent.ProductServices.indexOf('IDX') !== -1
            ? <button type="button" tabIndex="-1" onClick={this.actions.landing.onNav}>Search For Homes</button>
            : <button type="button" tabIndex="-1" onClick={() => this.actions.agent.onNav()}>View My Profile</button>
        }
      </div>
    );
  },

  renderSearch() {
    return (
      <div className="home-search">
        <h1>
          <span>What&apos;s my&nbsp;</span>
          <span>home worth?</span>
        </h1>
        <form className="search-area" onSubmit={(e) => e.preventDefault()} name="home-worth-search-form">
          <input id="home-worth-address-input" name="address" placeholder="Enter address of home" />
          <button type="submit" className="search-button cursor-default">
            <SVGIcon name="icon-search" />
          </button>
        </form>
        {
          this.utils.getAgentSettingValue(this.state.agent.AgentSettings, 'home_value_show_disclosures')
            && this.state.agent.Disclosures
            ? (
              <div className="disclosures">
                <p><strong>DISCLOSURES</strong></p>
                <div dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.state.agent.Disclosures) }} />
              </div>
            ) : null
        }
      </div>
    );
  },

  renderContent() {
    if (this.state.success && this.state.searchResult) {
      return this.renderSuccess();
    }
    if (this.state.searchResult) {
      return this.renderFoundHome();
    }

    return this.renderSearch();
  },

  getCallAgentHref() {
    return `tel:${this.state.agent.Phone}`;
  },

  getEmailAgentHref() {
    const subject = 'I am interested in your services';
    const body = encodeURIComponent(`Hi ${this.state.agent.FirstName},\r\n`);
    return `mailto:${this.state.agent.Email}?subject=${subject}&body=${body}`;
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    const customBackgrounds = ((this.utils.getAgentSettingValue(this.state.agent && this.state.agent.AgentSettings, 'home_value_background_image')) || '').split('|');
    if (!this.state.customBg && customBackgrounds && customBackgrounds.length > 0) {
      this.state.customBg = _.sample(customBackgrounds);
    }

    return (
      <div className="home-worth">
        { this.state.searchResult && !this.state.success
          ? null
          : (
            <header
              role="button"
              tabIndex="-1"
              aria-label="Agent details"
              className="home-worth-agent-header"
              onClick={() => {
                this.actions.landing.onNav();
              }}
            >
              <div className="agent">
                <AgentProfileImage className="agent-image" />
                <div className="details">
                  <div className="name">{`${this.state.agent.FirstName} ${this.state.agent.LastName}`}</div>
                  <div className="broker">{this.state.agent.BrokerName}</div>
                </div>
              </div>
              <div className="contact">
                <div className="icons">
                  {this.state.agent.Phone ? <a role="button" tabIndex="-1" aria-label="Call" href={this.getCallAgentHref()} className="call"><SVGIcon name="icon-material-phone" /></a> : null}
                  {this.state.agent.Email ? <a role="button" tabIndex="-1" aria-label="Email" href={this.getEmailAgentHref()} className="email-mobile"><SVGIcon name="icon-material-email" /></a> : null}
                </div>
                {this.state.agent.Phone ? <div className="phone">{this.utils.formatPhone(this.state.agent.Phone)}</div> : null}
                {this.state.agent.Email ? <div className="email"><a href={this.getEmailAgentHref()}>{this.state.agent.Email}</a></div> : null}
              </div>
            </header>
          )}
        <section
          className={classNames('content', {
            'show-success': this.state.success && this.state.searchResult,
            'show-search': !this.state.searchResult,
            'show-found': this.state.searchResult && !this.state.success,
          })}
          style={{ backgroundImage: `url('${this.state.customBg || this.state.bgImage}')` }}
        >
          {this.renderSuccess()}
          {this.renderSearch()}
          {this.renderFoundHome()}
        </section>

        <div className="copyright">
          <p>
            <span className="text-transform-none text-block">
              &copy;
              {window.CURRENT_YEAR || ''}
              &nbsp;
              HomeASAP LLC
            </span>
            <span>&nbsp;·&nbsp;</span>
            <a role="link" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>Terms</a>
            <span>&nbsp;·&nbsp;</span>
            <a role="link" tabIndex="-1" onClick={this.actions.common.toggleModal.bind(null, 'Privacy/DMCA Policy')}>
              Privacy Policy
            </a>
            <span>&nbsp;·&nbsp;</span>
            <a href="/search/ADA" target="_blank">Accessibility</a>
          </p>
        </div>
      </div>
    );
  },
});
