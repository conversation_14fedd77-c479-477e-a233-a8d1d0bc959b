const React = require('react');
const _ = require('lodash');

module.exports = React.createClass({

  displayName: 'components.yelp-popup',

  render() {
    const categories = _.map(this.props.business.Categories, (category) => category.CategoryName);

    let imageUrl = _.isEmpty(this.props.business.ImageURL)
      ? '//nplayassets.blob.core.windows.net/search2/default-yelp.png'
      : this.props.business.ImageURL;

    imageUrl = imageUrl.replace('o.jpg', 'ms.jpg');

    const rating = this.props.business.Rating;
    const hasHalfStar = (rating * 10) % 10;
    const starCount = Math.floor(Number.parseInt(rating, 10));
    const ratingImageUrl = `//nplayassets.blob.core.windows.net/search2/yelp/web_and_ios/regular/regular_${starCount}${hasHalfStar ? '_half' : ''}@2x.png`;

    return (
      <div>
        <div className="yelp-business">
          <a className="yelp-picture" href={this.props.business.YelpURL} target="_blank" title="View on Yelp">
            <img alt="Yelp business" src={imageUrl} />
          </a>
          <div className="yelp-business-content">
            <div className="yelp-reviews">
              <div className="yelp-business-rating"><img alt="Business rating" src={ratingImageUrl} /></div>
              <div className="yelp-business-review-count">
                {this.props.business.ReviewCount}
                &nbsp;
                reviews
              </div>
            </div>
            <div className="yelp-name"><a href={this.props.business.YelpURL} target="_blank" title="View on Yelp">{this.props.business.Name}</a></div>
            <div className="yelp-categories">
              <span className="text-muted">Categories:</span>
              &nbsp;
              {categories.join(' | ')}
            </div>
            <div className="yelp-price">
              <span className="text-muted">Price:</span>
              &nbsp;
              {this.props.business.Price}
            </div>
          </div>
        </div>
      </div>
    );
  },

});
