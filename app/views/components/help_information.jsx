const React = require('react');
const classNames = require('classnames');
const Portal = require('react-portal');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.help_information',

  propTypes: {
    // displayed title
    header: React.PropTypes.string,
    // array of CSS selectors
    targets: React.PropTypes.array,
    // top, right, bottom, left, center
    anchor: React.PropTypes.string,
    // object with properties top and/or left as number of pixels
    offset: React.PropTypes.object,
    // a layout property from the tree
    requiredLayout: React.PropTypes.string,
  },

  mixins: [mixins.debug, mixins.cursors, mixins.utils, mixins.actions, mixins.pureRender],

  cursors: {
    layout: ['layout'],
  },

  getInitialState() {
    return {
      popupPositionClass: 'help-popup-right-bottom',
      visited: false,
    };
  },

  componentDidMount() {
    window.addEventListener('resize', this.reposition);
    if (!this.props.targets) {
      console.warn('HelpInformation component', this.props.header, 'is missing props.targets');
    }
  },

  activate() {
    if (this.state.active) {
      return false;
    }
    this.setState({ active: true, visited: true });
    this.actions.help.setOverlayTargets(this.props.targets);
  },

  deactivate() {
    if (!this.state.active) {
      return false;
    }
    this.setState({ active: false });
    this.actions.help.setOverlayTargets([]);
  },

  toggle() {
    console.log('click');
    if (this.state.active) {
      this.deactivate();
      console.log('deactivate');
    } else {
      this.activate();
      console.log('activate');
    }
  },

  getTargetNode() {
    // just use the first element as the main target
    return document.querySelector(this.props.targets[0]);
  },

  calculatePosition() {
    const targetBounds = this.getTargetNode().getBoundingClientRect();

    // always start in the top left of the target element
    const style = {
      top: targetBounds.top,
      left: targetBounds.left,
    };

    /**
    TODO: ideally we would not need to scroll to the top, but without scrolling
    we would need to find the first "visible" matching target. An example would
    be the tagging icon help, if the body is scrolled then the first .tag-button
    is off-screen
    */
    function restrictTargetHeightToWindow(height, top) {
      let newHeight = height;
      if (height > window.innerHeight) {
        newHeight = window.innerHeight - top;
      }
      return newHeight;
    }

    if (this.props.anchor) {
      switch (this.props.anchor) {
        case 'top':
          style.left += (targetBounds.width / 2);
          break;
        case 'bottom':
          style.left += (targetBounds.width / 2);
          style.top += restrictTargetHeightToWindow(targetBounds.height, targetBounds.top);
          break;
        case 'left':
          style.top += (restrictTargetHeightToWindow(targetBounds.height, targetBounds.top) / 2);
          break;
        case 'right':
          style.top += (restrictTargetHeightToWindow(targetBounds.height, targetBounds.top) / 2);
          style.left += targetBounds.width;
          break;
        case 'center':
          style.top += (restrictTargetHeightToWindow(targetBounds.height, targetBounds.top) / 2);
          style.left += (targetBounds.width / 2);
          break;
        default:
          break;
      }
    }

    if (this.props.offset && this.props.offset.top) {
      style.top += this.props.offset.top;
    }
    if (this.props.offset && this.props.offset.left) {
      style.left += this.props.offset.left;
    }

    return style;
  },

  reposition() {
    const portal = this.refs.popup.parentNode;
    const style = this.calculatePosition();

    portal.style.top = `${style.top}px`;
    portal.style.left = `${style.left}px`;
  },

  componentWillUnmount() {
    this.deactivate();
    window.removeEventListener('resize', this.reposition);
  },

  componentDidUpdate() {
    if (this.refs.indicator) {
      const popupPositionClass = this.determinePopupPositionClass();
      if (popupPositionClass != this.state.popupPositionClass) {
        this.setState({ popupPositionClass });
      }
    }
  },

  determinePopupPositionClass() {
    // based on the screen quadrant
    const position = this.refs.indicator.getBoundingClientRect();
    const horizontal = (position.left < (window.innerWidth / 2)) ? 'right' : 'left';
    const vertical = (position.top < (window.innerHeight / 2)) ? 'below' : 'above';
    return (`${horizontal}-${vertical}`);
  },

  render() {
    // if the required layout is false, don't render
    if (this.props.requireLayout && !this.state.layout[this.props.requireLayout]) {
      return <div />;
    }

    // if the target node isn't found, don't render
    if (!this.getTargetNode()) {
      return <div />;
    }

    const style = this.calculatePosition();

    return (
      <div>
        <Portal isOpened={this.state.layout.help} className="portal">
          <div
            ref="popup"
            className={classNames('help-information help-on', {
              'anchor-top': this.props.anchor === 'top',
              'anchor-bottom': this.props.anchor === 'bottom',
              'anchor-right': this.props.anchor === 'right',
              'anchor-left': this.props.anchor === 'left',
              'anchor-center': this.props.anchor === 'center',
              active: this.state.active,
            })}
            style={style}
          >
            <div
              role="button"
              tabIndex="-1"
              aria-label="Help"
              ref="indicator"
              onMouseEnter={this.activate}
              onMouseLeave={this.deactivate}
              onClick={this.toggle}
              className={classNames('help-indicator',
                {
                  pulse: !this.state.visited,
                })}
            >
              +
            </div>
            <div className={classNames('help-description', this.state.popupPositionClass)}>
              <h4>{this.props.header}</h4>
              <div>{this.props.children}</div>
            </div>
          </div>
        </Portal>
      </div>
    );
  },

});
