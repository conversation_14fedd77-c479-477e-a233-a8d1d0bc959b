const React = require('react');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.add_to_homescreen',

  render() {
    return (
      <div className="ath-content">
        <div className="ath-app-logo" />
        <hr />
        <div className="ath-message">
          <span>To install, tap </span>
          <div className="ath-install-icon-android ath-install-icon">
            <SVGIcon name="icon-android-save" />
          </div>
          <div className="ath-install-icon-ios ath-install-icon">
            <SVGIcon name="icon-ios-save" />
          </div>
          <span> and choose &quot;Add to Home Screen&quot;</span>
        </div>
        <div className="ath-arrow">
          <SVGIcon name="icon-bouncing-arrow" />
        </div>
      </div>
    );
  },

});
