/* eslint-disable jsx-a11y/label-has-associated-control */
const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.listing_edge_tabbar',

  mixins: [mixins.actions, mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
  },

  getIsLatLon0() {
    if (this.props.data && this.props.data.Location.Lat && this.props.data.Location.Lon) {
      return false;
    }
    return true;
  },

  render() {
    return (
      <div className={classNames(this.props.className, 'listing-edge-tabbar')}>
        <div className="listing-edge-border listing-edge-border-top" />
        <div className="listing-edge-items">
          {
          this.props.onClose
            ? (
              <div role="button" tabIndex="-1" aria-label="Close" className="listing-edge-item close" title="Close" onClick={this.props.onClose}>
                <SVGIcon name="icon-close-button" />
              </div>
            )
            : null
          }
          {
          this.props.data && !this.getIsLatLon0() && this.props.data.DisplayAddress
            ? ([
              <div role="button" tabIndex="-1" aria-label="Aerial" key="aerial" className="listing-edge-item aerial" title="Show this home in aerial view" onClick={this.props.onAerial}>
                <SVGIcon name="icon-aerial" />
                <label>Aerial</label>
              </div>,
              <div
                role="button"
                tabIndex="-1"
                aria-label="Street"
                key="street"
                className={classNames('listing-edge-item street', { 'not-available': !this.props.streetViewAvailable })}
                onClick={this.props.onStreet}
                title="Show this home in street view"
              >
                <SVGIcon name="icon-street" />
                <label>Street</label>
              </div>,
              <div
                role="button"
                tabIndex="-1"
                aria-label="Parcel"
                key="parcel"
                className={classNames('listing-edge-item parcel', { hidden: !this.props.onParcel })}
                title="Show this home in satellite view"
                onClick={this.props.onParcel}
              >
                <SVGIcon name="icon-fence" />
                <label>Satellite</label>
              </div>,
              <div
                role="button"
                tabIndex="-1"
                aria-label="Map"
                key="map-view"
                className={classNames('listing-edge-item map-view', { hidden: !this.props.onMap })}
                title="Show this home in map view"
                onClick={this.props.onMap}
              >
                <SVGIcon name="icon-map" />
                <label>Map</label>
              </div>,
            ])
            : null
          }
        </div>
        <div className="listing-edge-border listing-edge-border-bottom" />
      </div>
    );
  },

});
