const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.spinner_round',

  mixins: [mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
  },

  render() {
    const componentClass = classNames(
      'spinner-round-component',
      this.props.className,
    );

    return <div className={componentClass} />;
  },

});
