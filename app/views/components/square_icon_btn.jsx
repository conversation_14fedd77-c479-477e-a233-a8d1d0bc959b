const React = require('react');
const classNames = require('classnames');
const SVGIcon = require('./svg_icon');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.square_icon_btn',

  mixins: [mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    text: React.PropTypes.string,
    icon: React.PropTypes.string,
    onClick: React.PropTypes.func,
  },

  render() {
    const componentClass = classNames(
      'square-icon-btn',
      { active: this.props.active },
      this.props.className,
    );

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Icon"
        className={componentClass}
        onClick={this.props.onClick}
      >
        <SVGIcon name={this.props.icon} />
        <p>{this.props.text}</p>
      </div>
    );
  },

});
