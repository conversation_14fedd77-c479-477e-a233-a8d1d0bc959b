const React = require('react');
const _ = require('lodash');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.map-popup',

  mixins: [mixins.utils],

  render() {
    const beds = this.props.listing.TotalBedrooms
      ? `${this.props.listing.TotalBedrooms} bed${this.props.listing.TotalBedrooms > 1 ? 's' : ''}`
      : null;

    const baths = this.props.listing.TotalBaths
      ? `${this.props.listing.TotalBaths} bath${this.props.listing.TotalBaths > 1 ? 's' : ''}`
      : null;

    let sqft = this.props.squareFootage;
    sqft += (sqft ? ' sqft' : '');

    // form details string
    let details;
    // if the square footage is shown as a range, don't display it on the popup
    if (this.props.showOnlyRangeLivingSquare) {
      details = [beds, baths, sqft];
    } else {
      details = [beds, baths];
    }
    details = details.filter((val) => val);
    details = details.join(' | ');

    const price = (this.props.listing.RangePriceFlag == 'Y' && this.props.listing.RangeHighPrice)
      ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.RangeHighPrice)}`
      : this.props.listing.ListPrice
        ? `${this.utils.getDollarSymbol(this.props.listing)}${this.utils.addThousandSep(this.props.listing.ListPrice)}`
        : 'Price Not Provided';

    const brokerLogo = <img alt="Broker" className="popup-broker-logo" src={this.props.brokerLogoUrl} />;
    const daysOnMarket = (this.props.listing.DaysOnMarket && this.props.showDaysOnMarket)
      ? (
        <span>
          <b>{this.props.listing.DaysOnMarket}</b>
          &nbsp;
          Days On Market
        </span>
      )
      : null;

    let headerDetail;
    if (this.props.showLogoOnPin && _.includes(this.props.listing.MlsIds, this.props.agentMlsId)) {
      headerDetail = brokerLogo;
    } else if (this.props.listing.Status != 'Active') {
      headerDetail = <b>{this.props.listing.Status}</b>;
    } else {
      headerDetail = daysOnMarket;
    }

    return (
      <div>
        <div id="listing-popup-header" data-listing-id={this.props.listing.Id}>
          <div className="popup-top">
            <span className={`sale-type ${this.props.popupClass}`}>{this.props.listing.SaleType === 1 ? 'FOR SALE' : 'FOR RENT'}</span>
            <span className="pull-right">{headerDetail}</span>
          </div>
        </div>
        <div className="popup-content">
          <img alt="Listing" className="popup-listing-image" src={this.props.listing.Image} />
          <div className="popup-details">
            <h4>{price}</h4>
            {this.props.listing.FullStreetAddress ? <div>{this.props.listing.FullStreetAddress}</div> : null}
            <div>{details}</div>
            <div>
              #
              {this.props.listing.PropertyListingId}
            </div>
            {
              this.props.showBrokerOnPin && this.props.listing.ListingAgent.OfficeName
                ? <div className="popup-broker">{this.props.listing.ListingAgent.OfficeName}</div>
                : null
            }
            {this.props.showAgentOnPin && this.props.listing.ListingAgent.Name
              ? <div className="popup-agent">{this.props.listing.ListingAgent.Name}</div>
              : null}
          </div>
        </div>
      </div>
    );
  },

});
