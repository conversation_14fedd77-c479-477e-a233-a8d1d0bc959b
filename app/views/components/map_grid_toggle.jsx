const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.map_grid_toggle',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
  },

  facets: {
    headerControl: ['currentActiveHeaderControl'],
    dataLoading: ['headerControlDisabled'],
  },

  mapClicked() {
    // Don't reset viewport if there is a bounding box
    if (this.actions.map.hasBoundingBox()) {
      this.actions.map.dontResetViewport();
    } else {
      this.actions.map.allowResetViewport();
    }
    this.actions.map.onNav(null);
  },
  gridClicked() {
    this.actions.grid.onNav();
  },

  render() {
    return (
      <div className={classNames(this.props.className, 'controls-container map-grid-toggle')}>
        {
        this.state.headerControl ? (
          <div role="group" className="view-mode-buttons">
            <a
              role="button"
              tabIndex="-1"
              title="Your results will display in map view"
              className={classNames('btn btn-sm', {
                active: this.state.headerControl === 'map' || window.location.href.indexOf('/map') !== -1,
                'not-available': this.state.headerControl === false,
                disabled: this.state.dataLoading,
              })}
              onClick={this.mapClicked}
            >
              <span className="svg-container">
                <SVGIcon name="icon-map-pin" />
              </span>
              <span className="hidden-xs hidden-sm hidden-md">Map</span>
              <span className="space-placeholder hidden-lg hidden-xl" />
            </a>
            <a
              role="button"
              tabIndex="-1"
              title="Your results will display in grid view"
              className={classNames('btn btn-sm', {
                active: this.state.headerControl === 'grid' || window.location.href.indexOf('/grid') !== -1,
                'not-available': this.state.headerControl === false,
                disabled: this.state.dataLoading,
              })}
              onClick={this.gridClicked}
            >
              <span className="svg-container">
                <SVGIcon name="icon-grid" />
              </span>
              <span className="hidden-xs hidden-sm hidden-md">Grid</span>
              <span className="space-placeholder hidden-lg hidden-xl" />
            </a>
          </div>
        ) : null
        }
      </div>
    );
  },

});
