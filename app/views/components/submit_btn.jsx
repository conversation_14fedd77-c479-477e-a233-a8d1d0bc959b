const React = require('react');
const classNames = require('classnames');
const Spinner = require('./spinner');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.submit_btn',

  mixins: [mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    text: React.PropTypes.string,
    icon: React.PropTypes.string,
    spinner: React.PropTypes.bool,
  },

  render() {
    const componentClass = classNames(
      'btn',
      this.props.className,
    );

    return (
      <button
        type="submit"
        className={componentClass}
        onClick={this.props.onClick ? this.props.onClick : null}
        disabled={this.props.spinner ? true : null}
      >
        {
          !this.props.spinner && this.props.icon
            ? (
              <SVGIcon name={this.props.icon} />
            ) : null
          }
        {
          this.props.spinner
            ? <Spinner />
            : (
              this.props.text
            )
          }
      </button>
    );
  },

});
