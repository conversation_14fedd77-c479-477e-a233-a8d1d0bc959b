const React = require('react');
const classNames = require('classnames');
const defer = require('lodash.defer');
const clone = require('clone');
const DropdownButton = require('react-bootstrap').DropdownButton;
const MenuItem = require('react-bootstrap').MenuItem;
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.custom_dropdown_multiselect',

  mixins: [mixins.debug, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      open: false,
      stayOpenOnce: false,
    };
  },

  componentWillMount() {

  },

  onSelect(e/* , href, target */) {
    if (typeof e === 'object') {
      e.preventDefault();
    } else {
      // console.log("Selected: ")

      const option = this.props.options[e];

      // console.log(option)

      if (e === 0) {
        defer(() => {
          this.props.onOptionChange ? this.props.onOptionChange() : '';
        });
      } else {
        const selectedOptions = clone(this.props.selectedOptions);

        const idxOfFirstOne = selectedOptions.indexOf(this.props.options[0]);
        if (idxOfFirstOne !== -1) {
          selectedOptions.splice(idxOfFirstOne, 1);
        }

        const selectedOptionIdx = selectedOptions.indexOf(option);

        if (selectedOptionIdx != -1) {
          selectedOptions.splice(selectedOptionIdx, 1);
        } else {
          selectedOptions.push(option);
        }

        defer(() => {
          this.props.onOptionChange ? this.props.onOptionChange(selectedOptions) : '';
        });
      }
    }
    this.setState({ stayOpenOnce: true });
  },

  onClickDoNothing(e) {
    e.stopPropagation();
    e.preventDefault();
  },
  onDropdownButtonClick(e) {
    this.onClickDoNothing(e);
  },

  getDisplayString() {
    return this.props.displayString && this.props.displayString(this.state) || '';
  },

  optionsRows() {
    if (this.props.options) {
      return (
        this.props.options.map(function (option, index) {
          return (
            <MenuItem
              eventKey={index}
              key={option}
              active={
              (
              this.props.selectedOptions.indexOf(option) != -1
              )
}
              onSelect={this.onSelect}
              onClick={this.onClickDoNothing}
            >
              {
              this.props.selectedOptions.indexOf(option) != -1
              || (index === 0 && this.props.selectedOptions.length === 0)
                ? <SVGIcon name="icon-checkbox-checked" /> : null
            }
              {
              !(this.props.selectedOptions.indexOf(option) != -1
              || (index === 0 && this.props.selectedOptions.length === 0))
                ? <SVGIcon name="icon-checkbox-unchecked" /> : null
            }
              {option}
            </MenuItem>
          );
        }, this)
      );
    }
    return null;
  },

  handleOnToggle(open) {
    console.log('multiselect', open);

    // if the flag is set and it's trying to close, keep it open
    if ((!open) && this.state.stayOpenOnce) {
      this.setState({ open: true, stayOpenOnce: false });
    } else {
      this.setState({ open });
    }

    return false;
  },

  render() {
    const componentClass = classNames(
      'custom-dropdown custom-dropdown-multiselect',
      {
        active: this.props.active,
        'not-empty': this.props.selectedOptions && this.props.selectedOptions.length > 0,
      },
      this.props.className,
    );

    return (
      <div className={componentClass}>
        <DropdownButton
          id={`${(this.props.title || '').toLowerCase().replace(' ', '_')}_multi-select`}
          bsSize="large"
          ref="dropdown"
          title={this.props.displayInPlace ? this.getDisplayString() : this.props.title}
          onClick={this.onDropdownButtonClick}
          onToggle={this.handleOnToggle}
          open={this.state.open || this.state.stayOpenOnce}
        >

          {this.optionsRows()}

        </DropdownButton>
        {
        this.props.displayInPlace ? null
          : (
            <div className="display">
              <p className="text-center">{this.getDisplayString()}</p>
            </div>
          )
        }
      </div>
    );
  },

});
