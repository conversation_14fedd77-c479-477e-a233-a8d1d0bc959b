const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.close_btn_cross',

  mixins: [mixins.debug, mixins.pureRender, mixins.utils],

  onClick() {
    this.props.onClick ? this.props.onClick() : '';
  },
  render() {
    return (
      <a
        role="button"
        tabIndex="-1"
        className={classNames('close-button-cross', this.props.className)}
        onClick={this.onClick}
      >
        <SVGIcon name="icon-close-button" />
      </a>
    );
  },

});
