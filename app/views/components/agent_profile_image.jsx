const React = require('react');
const classnames = require('classnames');
const { OverlayTrigger, Popover } = require('react-bootstrap');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({
  displayName: 'components.agent_profile_image',
  mixins: [mixins.cursors],
  cursors: {
    agentData: ['shared', 'agent', 'data'],
  },

  render() {
    let { agentData } = this.state;
    if (this.props.agentData) {
      agentData = this.props.agentData;
    }
    if (!agentData || !agentData.ProfileImage) {
      return null;
    }

    const popover = (
      <Popover id="agent-pro-badge-popover">
        <div>
          <strong>Pro Agent: </strong>
          This agent is recognized for participating in ongoing education to better serve their clients.
          <br />
          <a href="https://read.homeasap.com" target="_blank">Agents can learn more here</a>
        </div>
      </Popover>
    );

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Agent"
        className={classnames('agent-profile-image-container', this.props.className)}
        onClick={this.props.onClick}
      >
        {
        (agentData.ProductServices || []).indexOf('PRO') !== -1
          ? (
            <OverlayTrigger trigger={['click']} rootClose placement="bottom" overlay={popover}>
              <div className="pro-badge-container">
                <SVGIcon name="icon-pro-badge" />
              </div>
            </OverlayTrigger>
          ) : null
      }
        <img alt="Agent profile" src={agentData.ProfileImage} />
      </div>
    );
  },
});
