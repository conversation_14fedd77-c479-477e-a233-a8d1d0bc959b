const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.alert',

  mixins: [mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    message: React.PropTypes.oneOfType([
      React.PropTypes.string,
      React.PropTypes.array,
      React.PropTypes.bool,
    ]),
    onHide: React.PropTypes.func,
    onClick: React.PropTypes.func,
    className: React.PropTypes.string,
  },

  hide(e) {
    e.preventDefault();
    this.props.onHide();
  },

  render() {
    if (this.props.message) {
      const message = (typeof this.props.message === 'string')
        ? [this.props.message]
        : this.props.message;

      return (
        <div
          role="button"
          tabIndex="-1"
          aria-label="Alert"
          className={classNames(this.props.className
            ? this.props.className : 'alert-component', {
            'cursor-pointer': this.props.onClick,
          })}
          onClick={this.props.onClick ? this.props.onClick : null}
        >

          {this.props.onHide
            ? (
              <span
                role="button"
                tabIndex="-1"
                aria-label="Close"
                className="close"
                onClick={this.hide}
              >
                ×
              </span>
            )
            : null}

          {message.map((err, k) => (<p key={k}>{err}</p>))}

        </div>
      );
    } return null;
  },

});
