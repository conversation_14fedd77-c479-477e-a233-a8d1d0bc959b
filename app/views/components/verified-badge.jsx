const React = require('react');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.verified-badge',

  mixins: [mixins.debug, mixins.pureRender, mixins.cursors],

  cursors: {
    verifiedAgent: ['shared', 'agent', 'data', 'VerifiedAgent'],
  },

  render() {
    if (!this.state.verifiedAgent) {
      return null;
    }

    return (
      <div style={{
        display: 'inline-block',
        width: 16,
        height: 16,
        transform: 'translateY(2px)',
        backgroundImage: 'url(https://nplayassets.blob.core.windows.net/images/Verified-Badge/Verified_Badge-blue.svg)',
        backgroundSize: 'contain',
        ...(this.props.style || {}),
      }}
      />
    );
  },
});
