const React = require('react');
const classNames = require('classnames');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.spinner',

  mixins: [mixins.debug, mixins.pureRender],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    type: React.PropTypes.string,
    className: React.PropTypes.string,
  },

  render() {
    const componentClass = classNames(
      'spinner-component',
      `spinner--${this.props.type || 'primary'}`,
      this.props.className,
    );

    return (
      <div className={componentClass}>
        <i className="spinner__dot spinner__dot--first" />
        <i className="spinner__dot spinner__dot--second" />
        <i className="spinner__dot spinner__dot--third" />
      </div>
    );
  },

});
