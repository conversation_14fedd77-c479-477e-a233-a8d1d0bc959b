const React = require('react');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({
  displayName: 'svg.icon',
  mixins: [mixins.pureRender],
  propTypes: {
    className: React.PropTypes.string,
    name: React.PropTypes.string.isRequired,
    style: React.PropTypes.object,
  },
  getDefaultProps() {
    return {
      className: null,
      style: {},
    };
  },
  render() {
    return (
      <svg
        className={this.props.className}
        style={this.props.style}
        dangerouslySetInnerHTML={{ __html: this._buildUseTag() }}
      />
    );
  },
  _buildUseTag() {
    return `<use xlink:href='#${this.props.name}'></use>`;
  },
});
