const React = require('react');
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.close_btn',

  mixins: [mixins.debug, mixins.pureRender, mixins.utils],

  onClick() {
    this.props.onClick ? this.props.onClick() : '';
  },
  render() {
    if (!this.utils.useMobileSite()) {
      return (
        <button type="button" className="btn btn-default close-button" onClick={this.onClick}>Close</button>
      );
    }
    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Close"
        className="close-mobile"
        onClick={this.onClick}
      >
        X
      </div>
    );
  },

});
