const React = require('react');
const classNames = require('classnames');
const querystring = require('querystring');
const mixins = require('../../lib/mixins');
const SVGIcon = require('./svg_icon');

module.exports = React.createClass({

  displayName: 'components.share_listing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      showPopup: false,
      shareUrl: location.href,
    };
  },

  showPopup() {
    this.setState({ showPopup: true });
  },

  hidePopup() {
    this.setState({ showPopup: false });
  },

  togglePopup() {
    this.setState({ showPopup: !this.state.showPopup });

    if (!this.shortened && this.utils.useMobileSite()) {
      this.shortened = true;
      // Shorten Url with Bit.ly if on mobile
      this.actions.common.shortenUrl(location.href, (err, shortUrl) => {
        if (!err) {
          this.setState({ shareUrl: shortUrl });
        }
      });
    }
  },

  shareTwitter(e) {
    e.stopPropagation();
    this.hidePopup();
    window.open(this.shareTwitterUrl(), '_blank', 'menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=600,width=600');
  },

  shareTwitterUrl() {
    /**
    TODO: this is eventually going to need to handle scenarios based on whether it's a home, condo, lot, has bedrooms or doesn't, etc
    */
    const beds = this.props.listing.TotalBedrooms || (this.props.listing.Rooms && this.props.listing.Rooms.TotalBedrooms);
    const baths = this.props.listing.TotalBaths || (this.props.listing.Rooms && this.props.listing.Rooms.TotalBaths);
    const sqft = this.props.listing.LivingSquareFeet;
    const address = this.props.listing.FullStreetAddress;

    const text = `This ${beds}bd ${baths}ba ${sqft} sqft home at ${address} looks great!`;
    const shareUrl = window.location.href;

    const base = 'https://twitter.com/intent/tweet?';

    const data = {
      text,
      url: shareUrl,
      hashtags: 'homeasap',
      via: 'homeasap',
    };

    const url = base + querystring.stringify(data);

    return url;
  },

  shareFacebook() {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${window.location.href}`, '_blank', 'menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=600,width=600');
    this.hidePopup();
  },

  showEmailShareForm(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.showShareForm(this.props.listing);
    this.hidePopup();
  },

  shareSMS(e) {
    e.preventDefault();
    e.stopPropagation();
    window.open(`sms:?&body=${encodeURIComponent(`Check out this home: ${this.state.shareUrl}`)}`, '_blank');
    this.hidePopup();
  },

  render() {
    if (this.props.listing.isTaxProperty) {
      return null;
    }

    return (
      <div
        className={classNames(this.props.className, 'share-listing', {
          'share-listing-show-popup': !this.props.alwaysShow && this.state.showPopup,
        })}
        title="Share this home"
        onClick={this.props.alwaysShow ? null : this.togglePopup}
      >
        <SVGIcon name="icon-material-share" />
        <div className="share-listing-popup" title="">
          <a
            role="button"
            tabIndex="-1"
            className="share-email"
            title="Email this home"
            onClick={this.showEmailShareForm}
          >
            <SVGIcon name="icon-share-email" />
          </a>
          <a
            role="button"
            tabIndex="-1"
            className="share-facebook"
            title="Share this home on Facebook"
            onClick={this.shareFacebook}
          >
            <SVGIcon name="icon-share-facebook" />
          </a>
          <a
            role="button"
            tabIndex="-1"
            className="share-twitter"
            title="Share this home on Twitter"
            onClick={this.shareTwitter}
          >
            <SVGIcon name="icon-share-twitter" />
          </a>
          {
            this.utils.useMobileSite()
              ? (
                <a
                  role="button"
                  tabIndex="-1"
                  className="share-link"
                  title="Send SMS"
                  target="_blank"
                  onClick={this.shareSMS}
                >
                  <SVGIcon name="icon-share-sms" />
                </a>
              ) : null
          }
          <a
            role="button"
            tabIndex="-1"
            className="share-link"
            title="Copy url"
            onClick={this.actions.common.copyUrl}
          >
            <SVGIcon name="icon-share-copy" />
          </a>
        </div>
      </div>

    );
  },

});
