const React = require('react');

const SpinnerRound = require('../../components/spinner_round');
const Card = require('../../panels/card');
const mixins = require('../../../lib/mixins');
const LazyLoad = require('../../../thirdparty/react-lazy-load/LazyLoad-WithinContainer');
const Alert = require('../../components/alert');
const NoResults = require('../../panels/listings/no-results');

module.exports = React.createClass({

  displayName: 'panel-mobile.listings',

  mixins: [mixins.debug, mixins.actions, mixins.pureRender],

  getInitialState() {
    return {};
  },

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    data: ['panels', 'listings', 'data'],
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    activeId: ['panels', 'listings', 'activeId'],
    viewedProperties: ['shared', 'viewedProperties'],
    showMenu: ['screens', 'grid', 'showMenu'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },

  onNav(id) {
    this.actions.common.flagUserAsInteractedWithSite();
    this.props.onNav(id);
  },

  noListings() {
    return (
      <NoResults fromSearch={this.props.fromSearch} />
    );
  },

  spinner() {
    return (
      <center className="mt30 mb30">
        <SpinnerRound />
      </center>
    );
  },

  cards() {
    return this.props.listings.map((i) => (
      <LazyLoad
        height="170px"
        buffer={800}
        key={i.Id}
      >
        <Card
          isActive={i.Id == this.props.activeId}
          listing={i}
          mlsId={this.actions.common.getListingMlsId(i)}
          onNav={this.onNav}
          useScreenWidthForImage
        />
      </LazyLoad>
    ), this);
  },

  body() {
    if (this.props.listings === 0) {
      return <Alert message="Please login." onClick={this.actions.login.start} />;
    }

    if (this.props.listings === false) {
      return <Alert message="Could not connect to server. Please try again later." />;
    }

    if (this.props.listings && this.props.listings.length === 0) {
      return this.noListings();
    }

    if (!this.props.listings) {
      return this.spinner();
    }

    if (this.props.spinner) {
      return (this.spinner(), this.cards());
    }

    return this.cards();
  },

  render() {
    return (
      <div className="listings-container">
        {
        this.body()
        }
      </div>
    );
  },

});
