const React = require('react');
const map = require('lodash.map');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panel.agent-profile',
  empty: '---------',

  mixins: [
    mixins.debug, mixins.actions, mixins.pureRender, mixins.cursors, mixins.utils,
  ],

  getExpertise() {
    if (!this.props.agentData || !this.props.agentData.ExpertiseDescriptions) {
      return null;
    }
    console.log(`${this.props.agentData.ExpertiseDescriptions.length}is the expertise length`);
    return map(this.props.agentData.ExpertiseDescriptions, (item) => (
      <li
        key={item}
        className={
          classNames('item col-1-2')
        }
      >
        <b>.</b>
        &nbsp;
        {item}
        &nbsp;

      </li>
    ), this);
  },

  getServiceAreas() {
    if (!this.props.agentData || !this.props.agentData.ZipCodeList || !this.props.agentData.ZipCodes) {
      return;
    }
    const areaList = [];
    this.props.agentData.ZipCodes.forEach((item) => {
      if (areaList.indexOf(item.City) == -1) {
        areaList.push(item.City);
      }
    });

    if (areaList.length === 0) {
      return;
    }

    return areaList;
  },
  getUrl(logo) {
    return (`//nplayassets.blob.core.windows.net/credentials/${logo}`);
  },

  showAgentContact(tab) {
    this.actions.agent.setActiveTab(tab);
    this.actions.agent.showAgentContact(null);
  },
  getDisclosures() {

  },

  agentLandingClick() {
    if (this.props.agentData && this.props.agentData.Website) {
      let url = this.props.agentData.Website;
      if (url.toLowerCase().indexOf('http') < 0) {
        url = `http://${url}`;
      }
      window.open(url, '_blank');
    } else {
      this.actions.agent.gotoAgentLanding(this.props.agentData.Id);
    }
  },

  linkClick(url) {
    if (!url || url == '') {
      return;
    }
    window.open(
      url,
      '_blank',
    );
  },

  awardTemplate() {
    if (!this.props.agentData || !this.props.agentData.Awards || this.props.agentData.Awards && this.props.agentData.Awards.length == 0) {
      return '';
    }
    return map(this.props.agentData.Awards, function (item) {
      return (
        <div className="section-holder">
          <p className="section-date">{this.formatDate(item.Date)}</p>
          <p className="section-title">{item.Title}</p>
          <p className="section-details">{item.Description}</p>
        </div>
      );
    }, this);
  },

  publicationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Publications || this.props.agentData.Publications && this.props.agentData.Publications.length == 0) {
      return '';
    }
    return map(this.props.agentData.Publications, function (item) {
      return (
        <div className="section-holder">
          <p className="section-date">{this.formatDate(item.Date)}</p>
          <p className="section-date">
            -
            {item.Publication}
          </p>
          <p role="button" tabIndex="-1" aria-label="Publication" className={classNames('section-title', { link: item.Url })} onClick={this.linkClick.bind(this, item.Url)}>{item.Title}</p>
          <p className="section-details">{item.Desc}</p>
        </div>
      );
    }, this);
  },

  organizationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Organizations || this.props.agentData.Organizations && this.props.agentData.Organizations.length == 0) {
      return '';
    }
    return map(this.props.agentData.Organizations, (item) => (
      <div className="section-holder">
        <p className="section-title">{item.Title}</p>
        <p className="section-details">{item.Description}</p>
      </div>
    ), this);
  },

  educationTemplate() {
    if (!this.props.agentData || !this.props.agentData.Education || this.props.agentData.Education && this.props.agentData.Education.length == 0) {
      return '';
    }
    return map(this.props.agentData.Education, (item) => (
      <div className="section-holder">
        <p className="section-title">
          {item.Qualification}
          ,
          &nbsp;
          {item.Major}
        </p>
        <p className="section-details">{item.Name}</p>
      </div>
    ), this);
  },

  formatDate(dateArg) {
    const date = new Date(dateArg);
    return (date.toLocaleDateString('en-US'));
  },

  render() {
    if (!this.props.agentData) {
      return (<h4>Agent Data Not Found!</h4>);
    }
    return (
      <div>
        <div className="profile-container">
          <div className="row panel-0">
            <div className="col-7-24 agent-image">
              <AgentProfileImage agentData={this.props.agentData} onClick={this.toggleAgentLine} />
            </div>
            <div className="col-17-24 agent-info">
              <p className="agent-name">
                {this.props.agentData.FirstName}
&nbsp;
                <span className="">{this.props.agentData.LastName}</span>
                          &nbsp;
                <VerifiedBadge />
                {this.props.mlsData && this.props.mlsData.RealEstateLicenseNumberOnOff
                  ? (
                    <span className="license-num">
                      {
                        this.props.agentData && this.props.agentData.StateLicenseNumber
                          ? ` (${this.props.agentData.StateLicenseNumber})` : ''
                      }
                    </span>
                  ) : null}
              </p>
              {
                this.props.agentData.BrokerName
                  ? <p className="brokerage">{this.props.agentData.BrokerName}</p>
                  : <p>&nbsp;</p>
              }
              <p className="tagline">{this.props.agentData.Headline}</p>
              <div className="social-container">
                {
                  this.props.agentData.Website
                    ? (
                      <div role="button" tabIndex="-1" aria-label="Agent landing page" onClick={this.agentLandingClick}>
                        <SVGIcon className="social-icon red" name="icon-web" />
                      </div>
                    ) : null
                }
                {
                  this.props.agentData.PinterestUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent pinterest" onClick={this.linkClick.bind(this, this.props.agentData.PinterestUrl)} target="_blank">
                      <SVGIcon className="social-icon" name="icon-pinterest" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.LinkedInUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent linkedin" onClick={this.linkClick.bind(this, this.props.agentData.LinkedInUrl)} target="_blank">
                      <SVGIcon className="social-icon" name="icon-linkedin" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.TwitterUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent twitter" onClick={this.linkClick.bind(this, this.props.agentData.TwitterUrl)} target="_blank">
                      <SVGIcon className="social-icon twitter-icon" name="icon-twitter" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.GoogleUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent google page" onClick={this.linkClick.bind(this, this.props.agentData.GoogleUrl)} target="_blank">
                      <SVGIcon className="social-icon google-icon" name="icon-google" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.YouTubeUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent YouTube" onClick={this.linkClick.bind(this, this.props.agentData.YouTubeUrl)} target="_blank">
                      <SVGIcon className="social-icon youtube-icon" name="icon-youtube" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.InstagramUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent Instagram" onClick={this.linkClick.bind(this, this.props.agentData.InstagramUrl)} target="_blank">
                      <SVGIcon className="social-icon instagram-icon" name="icon-instagram" />
                    </div>
                  ) : null
                }
                {
                  this.props.agentData.FacebookUrl ? (
                    <div role="button" tabIndex="-1" aria-label="Agent facebook" onClick={this.linkClick.bind(this, this.props.agentData.FacebookUrl)} target="_blank">
                      <SVGIcon className="social-icon facebook-icon" name="icon-facebook" />
                    </div>
                  ) : null
                }
              </div>
            </div>
            <div className="col-1-1 text-center mt5">
              <a role="button" tabIndex="-1" className="btn btn-primary contact-button" onClick={this.showAgentContact}>
                Contact
                &nbsp;
                {this.props.agentData.FirstName}
              </a>
            </div>
          </div>
          <div className="row panel-1 pt5">
            <div className="col-1-1">
              <p className="header">Professional Information</p>
              {this.props.agentData.YearLicensed
                ? (
                  <div className="holder">
                    <span className="title col-1-2">Licensed Since:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.YearLicensed}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.StateLicenseNumber
                ? (
                  <div className="holder">
                    <span className="title col-1-2">License:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.StateLicenseNumber}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.AssociationName
                ? (
                  <div className="holder">
                    <span className="title col-1-2">Member of:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.AssociationName}
                    </span>
                  </div>
                ) : null}
              {this.getServiceAreas()
                ? (
                  <div className="holder">
                    <span className="title col-1-2">Service Areas:</span>
                    <span className="data col-1-2">
                      {this.getServiceAreas().join(', ')}
                    </span>
                  </div>
                ) : null}
            </div>
            <div className="col-1-1">
              {this.props.agentData.LicenseType
                ? (
                  <div className="holder">
                    <span className="title col-1-2">Licensed Type:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.LicenseType}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.AgentType
                ? (
                  <div className="holder">
                    <span className="title col-1-2 ">Representing:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.AgentType}
                    </span>
                  </div>
                ) : null}
              {this.props.agentData.Languages && this.props.agentData.Languages.length > 0
                ? (
                  <div className="holder">
                    <span className="title col-1-2">Languages:</span>
                    <span className="data col-1-2">
                      {this.props.agentData.Languages.join(', ')}
                    </span>
                  </div>
                ) : null}
              {
                this.props.agentData.BrokerName ? (
                  <div className="holder">
                    <span className="title col-1-2 col-lg-2-5">Brokerage:</span>
                    <span className="data col-1-2 col-lg-3-5">
                      {this.props.agentData.BrokerName}
                      {
                        this.props.agentData.BrokerPhone ? (
                          <span>
                            <br />
                            {this.utils.formatPhone(this.props.agentData.BrokerPhone)}
                          </span>
                        ) : null
                      }
                      <br />
                      {
                        this.props.agentData.BrokerAddress ? (
                          <span>
                            {this.props.agentData.BrokerAddress}
                            <br />
                          </span>
                        ) : null
                      }
                      {
                        [
                          this.props.agentData.BrokerCity,
                          `${this.props.agentData.BrokerState || ''} ${this.props.agentData.BrokerZip || ''}`.trim(),
                        ].filter((e) => !!e).join(', ')
                      }
                    </span>
                  </div>
                ) : null
              }
            </div>
            {this.props.agentData.BrokerLogo
              ? (
                <div className="col-1-1 center">
                  <img alt="Broker logo" src={this.props.agentData.BrokerLogo} className="broker-logo" />
                </div>
              ) : null}
          </div>

          {this.props.agentData.About || this.props.agentData.AgentVideoUrl
            ? (
              <div className="panel-2 row border-line">
                <div className={classNames('col-1-1', { 'col-md-1-2': !!this.props.agentData.AgentVideoUrl })}>
                  <p className="header">Professional Experience</p>
                  <p className="about">{this.props.agentData.About ? this.props.agentData.About : ''}</p>
                </div>
                {this.props.agentData.AgentVideoUrl
                  ? (
                    <div className="col-1-1 video-container">
                      <div className="video-holder">
                        <iframe title="Agent video" width="100%" height="240" src={this.props.agentData.AgentVideoUrl.replace('youtube.com/shorts/', 'youtube.com/embed/')} />
                      </div>
                    </div>
                  ) : null}
              </div>
            ) : null}
          {this.props.agentData.Services
            ? (
              <div className="row border-line">
                <div className="col-1-1 service">
                  <p className="header">Services</p>
                  <p>{this.props.agentData.Services}</p>
                </div>
              </div>
            ) : null}
          {this.props.agentData.ExpertiseDescriptions && this.props.agentData.ExpertiseDescriptions.length > 0
            ? (
              <div className="row border-line">
                <div className="expertise col-1-1">
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-virtual-tour"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Expertise</p>
                    <ul className="expertise-text col-17-24">
                      {this.getExpertise()}
                    </ul>
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Awards && this.props.agentData.Awards.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon h40"
                      name="icon-expertise"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Awards</p>
                    {this.awardTemplate()}
                  </div>
                </div>
              </div>
            ) : null}
          {this.props.agentData.Education && this.props.agentData.Education.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-education"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Education</p>
                    {this.educationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Publications && this.props.agentData.Publications.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-license"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Publications</p>
                    {this.publicationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}
          {this.props.agentData.Organizations && this.props.agentData.Organizations.length > 0
            ? (
              <div className="row border-line">
                <div className={classNames('col-1-1')}>
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-organization"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Organizations and Groups</p>
                    {this.organizationTemplate()}
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Credentials && this.props.agentData.Credentials.length > 0
            ? (
              <div className="row border-line">
                <div className="col-1-1">
                  <div className="icon-holder">
                    &nbsp;
                    <SVGIcon
                      className="section-icon"
                      name="icon-certifications"
                    />
                  </div>
                  <div className="content-holder">
                    <p className="header">Certifications</p>
                    <div className="certifications">
                      {
                        this.props.agentData.Credentials.map(function (item) {
                          return <img alt="Credential" key={item.Id} src={this.getUrl(item.Logo)} />;
                        }, this)
                      }
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

          {this.props.agentData.Disclosures
            ? (
              <div className="row border-line">
                <div className="col-1-1">
                  <p className="header">Disclosures</p>
                  <div className="disclosures" dangerouslySetInnerHTML={{ __html: this.utils.makeUrlsClickable(this.props.agentData.Disclosures) }} />
                </div>
              </div>
            ) : null}

          <div className="row text-center mt20">
            <img alt="Equal housing opportunity" height="40" src="https://nplayassets.blob.core.windows.net/images/equal-housing-opportunity-logo.png" />
          </div>
        </div>
      </div>
    );
  },

});
