const React = require('react');
const classNames = require('classnames');
const DropdownButton = require('react-bootstrap').DropdownButton;
const MenuItem = require('react-bootstrap').MenuItem;
const mixins = require('../../../lib/mixins/index');

module.exports = React.createClass({

  displayName: 'panels.sort.drop-down',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils/* , mixins.pureRender */],

  cursors: {
    sortType: ['shared', 'menu', 'sortType'],
    sortDisabled: ['shared', 'sortDisabled'],
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },
  onSortSelect(e) {
    this.actions.menu.toggleSortType(e.toString());
    this.actions.common.scrollElementTo(document.body, 0, 500);
  },
  /* sortClicked: function (sortCriteria) {
   this.actions.menu.toggleSortType(sortCriteria)
   //this.props.onSortChange();
   this.actions.common.scrollElementTo(document.body, 0, 500)
   }, */

  getSortText() {
    switch (this.state.sortType) {
      case 'price_asc':
        return 'Price...';
      case 'price_desc':
        return 'Price...';
      case 'sqft_desc':
        return 'Square feet...';
      case 'distance_asc':
        return 'Distance...';
      case 'yearBuilt_asc':
        return 'Year built...';
      case 'yearBuilt_desc':
        return 'Year built...';
      default: // case "daysOnMarket_asc":
        return (this.state.showDaysOnMarket ? 'Days on market...' : 'Newest...');
    }
  },

  render() {
    return (
      <DropdownButton className="pull-right sort-dropdown" bsStyle="default" title={`SORT: ${this.getSortText()}`} noCaret onSelect={this.onSortSelect} key="Sort Dropdown">
        <MenuItem className={classNames({ active: this.state.sortType === 'daysOnMarket_asc' || !this.state.sortType })} eventKey="daysOnMarket_asc">{this.state.showDaysOnMarket ? 'Days on market - newest first' : 'Newest first'}</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'price_asc' })} eventKey="price_asc">Price - lowest first</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'price_desc' })} eventKey="price_desc">Price - highest first</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'sqft_desc' })} eventKey="sqft_desc">Square feet - largest first</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'distance_asc' })} eventKey="distance_asc">Distance - closest first</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'yearBuilt_asc' })} eventKey="yearBuilt_asc">Year built - newest first</MenuItem>
        <MenuItem className={classNames({ active: this.state.sortType === 'yearBuilt_desc' })} eventKey="yearBuilt_desc">Year built - oldest first</MenuItem>
      </DropdownButton>
    );
  },
});
