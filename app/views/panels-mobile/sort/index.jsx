const React = require('react');
const mixins = require('../../../lib/mixins/index');
const Sort = require('../../panels/grid-menu/sort');

module.exports = React.createClass({

  displayName: 'panels.sort',

  lastScrollPosition: 0,

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.utils/* , mixins.pureRender */],

  cursors: {
    sortType: ['shared', 'menu', 'sortType'],
    showDaysOnMarket: ['shared', 'agent', 'mlsData', 'ShowDaysOnMarket'],
  },

  handleClick() {
    if (this.state.showPanel) {
      this.setState({ showPanel: false });
    }
  },

  componentDidMount() {
    window.addEventListener('click', this.handleClick);
  },

  componentWillUnmount() {
    window.removeEventListener('click', this.handleClick);
  },

  onSortClick(e) {
    e.stopPropagation();
    this.setState({ showPanel: !this.state.showPanel });
  },

  onSortChange() {
    if (this.state.showPanel) {
      this.setState({ showPanel: false });
    }
  },

  getSortText() {
    switch (this.state.sortType) {
      case 'price_asc':
        return 'Price - lowest first';
      case 'price_desc':
        return 'Price - highest first';
      case 'sqft_desc':
        return 'Square feet - largest first';
      case 'distance_asc':
        return 'Distance - closest first';
      case 'yearBuilt_asc':
        return 'Year built - newest first';
      case 'yearBuilt_desc':
        return 'Year built - oldest first';
      default: // case "daysOnMarket_asc":
        return (this.state.showDaysOnMarket ? 'Days on market - newest first' : 'Newest first');
    }
  },

  getSort() {
    return (
      <span className="mn">
        &nbsp;
        {this.getSortText()}
      </span>
    );
  },

  render() {
    return (
      <div>
        <div role="button" tabIndex="-1" aria-label="Sort" onClick={this.onSortClick} className="sort-header">
          <b>Sort</b>
          <span>
            <b>:&nbsp;</b>
            {this.getSort()}
          </span>
        </div>
        { this.state.showPanel ? (
          <Sort key="sort" onSortChange={this.onSortChange} />) : null}
      </div>
    );
  },
});
