const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'listings.map-zoommsg',

  mixins: [mixins.actions, mixins.cursors],

  cursors: {
    zoomMsg: ['panels', 'listings', 'meta', 'zoomMsg'],
  },

  render() {
    if (!this.state.zoomMsg) {
      return null;
    }

    return (
      <div className="map-zoommsg">
        <SVGIcon name="icon-zoom-in-mobile" />
        <span>Zoom in to view property pins on the map!</span>
      </div>
    );
  },

});
