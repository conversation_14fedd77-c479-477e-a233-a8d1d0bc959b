const React = require('react');
const SVGIcon = require('../../components/svg_icon');
const Leaflet = require('../../panels/map/leaflet');
const mixins = require('../../../lib/mixins');
const ZoomMsg = require('./zoom');

module.exports = React.createClass({

  displayName: 'panel-mobile.map',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    ref: ['panels', 'map', 'ref'],
  },

  render() {
    const style = { visibility: 'hidden' };

    return (
      <div
        style={this.state.ref.className === null ? style : null}
        className="layout--full map-panel-mobile"
      >

        <div className="map-container">

          <Leaflet />

          <div className="map-logo" />

          <div className="map-north">
            <SVGIcon name="icon-north" />
          </div>

          <ZoomMsg />

        </div>
      </div>
    );
  },

});
