const React = require('react');
const ReactDOM = require('react-dom');
const classNames = require('classnames');
const { Overlay, Popover } = require('react-bootstrap');
const mixins = require('../../../lib/mixins');
const SearchField = require('../../panels/landing/search-field');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const SubmitBtn = require('../../components/submit_btn');
const VerifiedBadge = require('../../components/verified-badge');
const NPlayFooter = require('../../panels/n-play-footer');

module.exports = React.createClass({

  displayName: 'panels-mobile.landing',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    buyerData: ['shared', 'buyer', 'data'],
    saleType: ['shared', 'menu', 'saleType'],
    IsSearchLicenseAgreementShown: ['shared', 'agent', 'mlsData', 'IsSearchLicenseAgreementShown'],
    ShowLargeBrokerOnALP: ['shared', 'agent', 'mlsData', 'ShowLargeBrokerOnALP'],
  },

  getInitialState() {
    return {
      showShare: false,
    };
  },

  searchSubmit(e) {
    e.preventDefault();

    const saleType = this.state.saleType;
    console.log(`Sale Type ${saleType}`);

    const res = this.refs.SearchField.state.res;
    if (!this.refs.SearchField.state.hasPrevious // Using Previous
      && (!res.locationQuery || res.locationQuery !== this.refs.SearchField.state.value)) {
      const suggestions = this.refs.SearchField.state.suggestions;
      if (suggestions && suggestions[0] && suggestions[0].type === 'mlsListingIdMatch') {
        return this.refs.SearchField.autosuggestSelected(e, { suggestion: suggestions[0] });
      }

      console.log('Need google geocoding');
      this.actions.onboarding.geocodeAddress(this.refs.SearchField.state.value, (res1) => {
        const _res = {
          lat: res1.geometry.location.lat,
          lon: res1.geometry.location.lng,
          locationQuery: res1.formatted_address,
        };
        console.log(`Search Field: ${_res.locationQuery}`);
        this.actions.landing.onSearch(_res.lat, _res.lon, res.radius, _res.locationQuery, saleType);
      });
    } else {
      console.log(`Search Field: ${res.locationQuery}`);
      this.actions.landing.onSearch(res.lat, res.lon, res.radius, res.locationQuery, saleType);
    }
  },

  maybeCloseShare(e) {
    if (!(e.currentTarget == this.refs.shareButton)) {
      this.setState({ showShare: false });
    }
  },

  featuredListingClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/featured');
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.common.goToRoute('/agent');
  },

  agentContactClicked(e) {
    e.preventDefault();
    this.actions.agent.showAgentContact();
  },

  toggleSaleType(type) {
    this.actions.menu.updateSearchField([{ key: 'saleType', value: type }]);
  },

  getCallAgentHref() {
    return `tel:${this.state.agentData.Phone}`;
  },

  getTextAgentHref() {
    const body = `Hi ${this.state.agentData.FirstName}, `;
    return `sms:${this.state.agentData.Phone}?&body=${body}`;
  },

  getEmailAgentHref() {
    const subject = 'I am interested in your services';
    const body = encodeURIComponent(`Hi ${this.state.agentData.FirstName},\r\n`);
    return `mailto:${this.state.agentData.Email}?subject=${subject}&body=${body}`;
  },

  getAgentWebsiteHref() {
    function fixUrl(url) {
      const httpString = 'http://';
      const httpsString = 'https://';
      const unschemedString = '//';
      if (url.substr(0, httpString.length).toLowerCase() !== httpString && url.substr(0, httpsString.length).toLowerCase() !== httpsString && url.substr(0, unschemedString.length).toLowerCase() !== unschemedString) {
        url = httpString + url;
      }
      return url;
    }
    return fixUrl(this.state.agentData.Website);
  },

  handleShareAgentClick(e) {
    e.stopPropagation();
    if (this.state.showShare && this.clipboard) {
      this.clipboard.destroy('.share-copy');
    }
    this.setState({ showShare: !this.state.showShare, shareTarget: e.target }, () => {
      if (this.state.showShare) {
        this.clipboard = new ClipboardJS('.share-copy');
        this.clipboard.on('success', () => {
          alert('Copied to clipboard');
        });
      }
    });
  },

  handleSaveAgentToHomescreen() {
    window.athModal.show(true);
  },

  showDreamsweepsModal() {
    this.actions.common.showDreamsweepsModal();
  },

  hideDreamsweepsModal() {
    this.actions.common.hideDreamsweepsModal();
  },

  landingContent() {
    if (!this.state.agentData) {
      return null;
    }

    const whatsappText = `Hi, check out my digital business card! ${window.location.href}`;

    const sharePopover = (
      <Popover id="share-agent-popover">
        <div className="share-agent-popup" title="">
          <a role="button" tabIndex="-1" className="share-email" title="Share my page via email" onClick={this.actions.common.shareAgentViaEmail}>
            <SVGIcon name="icon-material-email" />
            <div>Email</div>
          </a>
          <a role="button" tabIndex="-1" className="share-textsms" title="Share my page via text messaging" onClick={this.actions.common.shareAgentViaSMS}>
            <SVGIcon name="icon-material-textsms" />
            <div>Text</div>
          </a>
          <a role="button" tabIndex="-1" className="share-copy" title="Copy my page URL to clipboard" data-clipboard-text={window.location.href}>
            <SVGIcon name="icon-material-copy" />
            <div>Copy</div>
          </a>
          <a role="button" tabIndex="-1" className="share-facebook" title="Share my page on Facebook" onClick={this.actions.common.shareAgentOnFacebook}>
            <SVGIcon name="icon-facebook" />
            <div>Facebook</div>
          </a>
          <a role="button" tabIndex="-1" className="share-twitter" title="Share my page on Twitter" onClick={this.actions.common.shareAgentOnTwitter}>
            <SVGIcon name="icon-twitter" />
            <div>Twitter</div>
          </a>
          <a role="button" tabIndex="-1" className="share-whatsapp" title="Share my page on WhatsApp" href={`whatsapp://send?text=${whatsappText}`} data-action="share/whatsapp/share">
            <SVGIcon name="icon-whatsapp" />
            <div>WhatsApp</div>
          </a>
        </div>
      </Popover>
    );

    return [
      <div className="agent-info" key="agent-info">
        <AgentProfileImage className="agent-profile-image" />
        <div className={classNames('agent-right', { 'same-size': this.state.agentData.MlsId === 'scccar' })}>
          <h1 className="agent-name">
            {this.state.agentData.NameLookup}
            &nbsp;
            <VerifiedBadge style={{ width: 22, height: 22 }} />
          </h1>
          <p className={'broker-name'.concat(this.state.ShowLargeBrokerOnALP ? ' large' : ' ')}>{this.state.agentData.BrokerName}</p>
        </div>
      </div>,
      <div className="contact-links" key="contact-links">
        { this.state.agentData.Phone ? (
          <a href={this.getCallAgentHref()} className="call">
            <SVGIcon name="icon-material-phone" />
            <div>Call</div>
          </a>
        ) : null }
        { this.state.agentData.Phone ? (
          <a href={this.getTextAgentHref()} className="text">
            <SVGIcon name="icon-material-textsms" />
            <div>Text</div>
          </a>
        ) : null }
        { this.state.agentData.Email ? (
          <a href={this.getEmailAgentHref()} className="email">
            <SVGIcon name="icon-material-email" />
            <div>Email</div>
          </a>
        ) : null }
        { this.state.agentData.Website ? (
          <a href={this.getAgentWebsiteHref()} className="web">
            <SVGIcon name="icon-material-public" />
            <div>Web</div>
          </a>
        ) : null }
        <div
          role="button"
          tabIndex="-1"
          aria-label="Share"
          onClick={this.handleShareAgentClick}
          className="share"
          ref={(shareButton) => {
            this.shareButton = shareButton;
          }}
        >
          <SVGIcon name="icon-material-share" />
          <div>Share</div>
        </div>
        <Overlay
          show={this.state.showShare}
          placement="bottom"
          container={this}
          target={() => ReactDOM.findDOMNode(this.shareButton)}
        >
          {sharePopover}
        </Overlay>
        { window.addToHomescreen.isCompatible
          ? (
            <div role="button" tabIndex="-1" aria-label="Save" onClick={this.handleSaveAgentToHomescreen} className="save">
              <SVGIcon name="icon-material-save" />
              <div>Save</div>
            </div>
          )
          : null}
      </div>,
      <div className="search-area" key="search-area">
        { this.state.agentData.HomeSearchRegisteredDateTime // hide search input if they don't have IDX search
          ? [
            <nav className={classNames('saletype-nav')}>
              {
                this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'alphidesale') !== 'true'
                  ? (
                    <a role="button" tabIndex="-1" className={classNames({ active: this.state.saleType === 1 })} onClick={this.toggleSaleType.bind(null, 1)}>
                      For Sale
                    </a>
                  ) : null
              }
              {
                this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'alphiderent') !== 'true'
                  ? (
                    <a role="button" tabIndex="-1" className={classNames({ active: this.state.saleType === 2 })} onClick={this.toggleSaleType.bind(null, 2)}>
                      For Rent
                    </a>
                  ) : null
              }
            </nav>,
            <form
              className="search-form"
              onSubmit={this.searchSubmit}
            >
              <div className="input-group">
                <SearchField ref="SearchField" saleType={this.state.saleType} />
                <SubmitBtn className="btn-primary" icon="icon-search" />
              </div>
            </form>,
          ]
          : null}
        <div className="helper-buttons">
          <div role="button" tabIndex="-1" aria-label="Featured listings" onClick={this.featuredListingClicked}>
            <a className="">FEATURED LISTINGS</a>
            <SVGIcon name="icon-chevron-right" />
          </div>
          <div role="button" tabIndex="-1" aria-label="About me" onClick={this.agentClicked}>
            <a
              role="button"
              tabIndex="-1"
              className=""
              onClick={(e) => {
                e.preventDefault();
              }}
            >
              ABOUT ME
            </a>
            <SVGIcon name="icon-chevron-right" />
          </div>
          {
            this.actions.common.getAgentHasLendingTree(this.state.agentData)
              ? (
                <div
                  role="button"
                  tabIndex="-1"
                  aria-label="See Current Rates"
                  className="lending-tree-bg"
                  onClick={() => {
                    this.actions.analytics.sendEvent('lending tree', 'open from mobile alp');
                    this.actions.common.showLendingTreeModal();
                  }}
                >
                  <a
                    role="button"
                    tabIndex="-1"
                    className=""
                    onClick={(e) => {
                      e.preventDefault();
                    }}
                  >
                    SEE CURRENT RATES
                  </a>
                  <SVGIcon name="icon-chevron-right" />
                </div>
              )
              : null
          }
          {
            this.actions.common.getAgentHasHV()
              ? (
                <div role="button" tabIndex="-1" aria-label="Your home value" className="home-worth-button" onClick={this.actions.homeWorth.onNav}>
                  <a>YOUR HOME VALUE</a>
                  <SVGIcon name="icon-chevron-right" />
                </div>
              ) : null
          }
          {
            this.utils.getAgentSettingValue(this.state.agentData && this.state.agentData.AgentSettings, 'local_market_reports_url')
              ? (
                <div
                  role="button"
                  tabIndex="-1"
                  aria-label="Local Market Reports"
                  className="local-market-reports-button"
                  onClick={() => window.open(this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'local_market_reports_url'), '_blank')}
                >
                  <a
                    role="button"
                    tabIndex="-1"
                    className=""
                    onClick={(e) => {
                      e.preventDefault();
                    }}
                  >
                    LOCAL MARKET REPORTS
                  </a>
                  <SVGIcon name="icon-chevron-right" />
                </div>
              ) : null
          }
          {
            this.actions.common.getAgentHasDS()
              ? (
                <div role="button" tabIndex="-1" aria-label="Sweepstakes" className="sweepstakes-button" onClick={this.showDreamsweepsModal}>
                  <a className="">ENTER TO WIN $200</a>
                  <SVGIcon name="icon-chevron-right" />
                </div>
              ) : null
          }
        </div>
        { this.state.IsSearchLicenseAgreementShown
          ? <a role="button" tabIndex="-1" className="implicit-agree" onClick={this.actions.common.toggleModal.bind(null, 'Terms')}>By searching you agree to the terms and conditions</a>
          : null}
      </div>,
      <div className="social-links" key="social-links">
        { this.state.agentData.FacebookUrl
          ? (
            <a target="_blank" className="facebook" title="View my page on Facebook" href={this.state.agentData.FacebookUrl.match(/\.co/) ? this.state.agentData.FacebookUrl : `https://www.facebook.com/${this.state.agentData.FacebookUrl}`}>
              <SVGIcon name="icon-facebook" />
            </a>
          )
          : null}
        { this.state.agentData.InstagramUrl
          ? (
            <a target="_blank" className="instagram" title="View my page on Instagram" href={this.state.agentData.InstagramUrl.match(/\.co/) ? this.state.agentData.InstagramUrl : `https://instagram.com/${this.state.agentData.InstagramUrl.replace('@', '')}`}>
              <SVGIcon name="icon-instagram" />
            </a>
          )
          : null}
        { this.state.agentData.YouTubeUrl
          ? (
            <a target="_blank" className="youtube" title="View my page on Youtube" href={this.state.agentData.YouTubeUrl.match(/\.co/) ? this.state.agentData.YouTubeUrl : `https://www.youtube.com/${this.state.agentData.YouTubeUrl}`}>
              <SVGIcon name="icon-youtube" />
            </a>
          )
          : null}
        { this.state.agentData.GoogleUrl
          ? (
            <a target="_blank" className="google" title="View my page on Google" href={this.state.agentData.GoogleUrl}>
              <SVGIcon name="icon-google" />
            </a>
          )
          : null}
        { this.state.agentData.TwitterUrl
          ? (
            <a target="_blank" className="twitter" title="View my page on Twitter" href={this.state.agentData.TwitterUrl.match(/\.co/) ? this.state.agentData.TwitterUrl : `https://www.twitter.com/${this.state.agentData.TwitterUrl}`}>
              <SVGIcon name="icon-twitter" />
            </a>
          )
          : null}
        { this.state.agentData.LinkedInUrl
          ? (
            <a target="_blank" className="linkedin" title="View my page on LinkedIn" href={this.state.agentData.LinkedInUrl.match(/\.co/) ? this.state.agentData.LinkedInUrl : `https://www.linkedin.com/${this.state.agentData.LinkedInUrl}`}>
              <SVGIcon name="icon-linkedin" />
            </a>
          )
          : null}
        { this.state.agentData.PinterestUrl
          ? (
            <a target="_blank" className="pinterest" title="View my page on Pinterest" href={this.state.agentData.PinterestUrl.match(/\.co/) ? this.state.agentData.PinterestUrl : `https://www.pinterest.com/${this.state.agentData.PinterestUrl}`}>
              <SVGIcon name="icon-pinterest" />
            </a>
          )
          : null}
      </div>,
    ];
  },

  render() {
    return (

      <div className={classNames('landing-wrapper-container', this.props.className)} onClickCapture={this.maybeCloseShare} onTouchStart={this.maybeCloseShare} onTouchStartCapture={this.maybeCloseShare}>
        { this.props.children }
        <div className="landing-wrapper">
          {this.landingContent()}
        </div>
        <div className="mobile-landing-footer">
          <NPlayFooter />
        </div>
      </div>

    );
  },

});
