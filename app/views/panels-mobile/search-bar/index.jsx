const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
// const MapGridToggle = require('../../components/map_grid_toggle');
// search Components
const SaleTypeDropdown = require('../../panels/menu/search-components/sale-type');
const SearchByPaymentListPriceDropdown = require('../../panels/menu/search-components/search-by-payment-listing-price');
const CarDropdown = require('../../panels/menu/search-components/car');
const LevelsDropdown = require('../../panels/menu/search-components/levels');
// const LotSizeDropdown = require('../../panels/menu/search-components/lot-size');
const AgeDropdown = require('../../panels/menu/search-components/age');
const SquareFeetDropdown = require('../../panels/menu/search-components/square-feet');
const BedBathDropdown = require('../../panels/menu/search-components/bed-bath');
const PriceDropdown = require('../../panels/menu/search-components/price');
const MortgagePaymentDropdown = require('../../panels/menu/search-components/mortgage-payment');
const DownPaymentDropdown = require('../../panels/menu/search-components/down-payment');
const SpecialFinancingDropdown = require('../../panels/menu/search-components/special-financing');
const PropertyTypesDropdown = require('../../panels/menu/search-components/property-types');
const Keywords = require('../../panels/menu/search-components/keywords');
const ActiveToggle = require('../../panels/menu/search-components/active-toggle');
const SearchField = require('./search-field');
// const ResultsText = require('../../panels/menu/search-components/results-text');
const Sort = require('../../panels/grid-menu/sort');
const RatePlugSubheader = require('../../panels/header/rateplug-subheader');
const IDXPlusSubheader = require('../../panels/header/idx-plus-subheader');
const Agent = require('./agent');

module.exports = React.createClass({

  displayName: 'panel.search-bar',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.events],

  cursors: {
    layout: ['layout'],
    listingCount: ['panels', 'listings', 'lastFullListingCount'],
    listings: ['panels', 'listings', 'data'],
    loadingListings: ['panels', 'listings', 'meta', 'spinner'],
    showHeader: ['panels', 'header', 'showHeader'],
    taggedProperty: ['screens', 'buyer', 'activeListing'],
    agentIsIDXCustomer: ['shared', 'agent', 'data', 'HomeSearchRegisteredDateTime'],
    agentLayout: ['layout', 'agent'],
  },

  facets: {
    dataLoading: ['headerControlDisabled'],
  },

  lastScrollPosition: 0,

  // we don't want to hide the header too early when scrolling or it will show a bunch of whitespace
  scrollHideThreshold: 40,

  getInitialState() {
    return {
      editingFilters: false,
      showSort: false,
    };
  },

  searchSubmit(e) {
    e.preventDefault();

    const inputs = e.target && e.target.getElementsByClassName('form-control');
    const input = inputs && inputs.length > 0 ? inputs[0] : null;
    if (input) {
      const suggestions = this.refs.SearchField.state.suggestions;
      input.blur();
      if (suggestions && suggestions[0] && suggestions[0].type === 'mlsListingIdMatch') {
        this.refs.SearchField.autosuggestSelected(e, { suggestion: suggestions[0] });
      } else {
        this.refs.SearchField.searchKeywordEntered();
      }
    }
  },

  handleScroll(e) {
    const scrollTop = e.target.scrollTop;

    if ((scrollTop > this.lastScrollPosition) && (this.lastScrollPosition > this.scrollHideThreshold) && this.state.showHeader && !this.state.editingFilters) {
      console.log('hiding header');
      this.actions.panels.hideHeaders();
    } else if ((scrollTop < this.lastScrollPosition) && !this.state.showHeader) {
      this.actions.panels.showHeaders();
    }
    this.lastScrollPosition = scrollTop;
  },

  componentDidMount() {
    this.events.on(this.events.CONTAINER_SCROLL, this.handleScroll);
  },

  componentDidUpdate() {
    if ((this.state.editingFilters || this.state.showSort) && !this.state.showHeader) {
      this.setState({ editingFilters: false, showSort: false });
      return;
    }
    if (this.state.editingFilters && this.viewingBuyerOrAgent()) {
      this.setState({ editingFilters: false });
    }
    if (this.state.showSort && this.viewingBuyerOrAgent()) {
      this.setState({ showSort: false });
    }
  },

  componentWillUnmount() {
    this.events.removeEventListener(this.events.CONTAINER_SCROLL, this.handleScroll);
    this.actions.common.enableBodyScroll();
  },

  filtersClicked() {
    this.actions.common.disableBodyScroll();
    this.setState({ editingFilters: true });
  },

  doneEditingFilters() {
    this.actions.common.enableBodyScroll();
    this.setState({ editingFilters: false });
  },

  sortClicked() {
    this.setState({ showSort: !this.state.showSort, editingFilters: false });
  },

  onSortChange() {
    if (this.state.showSort) {
      this.setState({ showSort: false });
    }
  },

  showHeaders() {
    this.actions.panels.showHeaders();
  },

  resetSearch() {
    this.actions.menu.resetMenuSelections();
  },

  filterPanel() {
    return (
      <div
        className="filters-scroll-container"
        onTouchMove={function (e) {
          e.stopPropagation();
        }}
      >
        <div className="filters">
          <Keywords />
          <SaleTypeDropdown />
          <SearchByPaymentListPriceDropdown />
          <PriceDropdown />
          <DownPaymentDropdown ref="downpayment-filter" />
          <MortgagePaymentDropdown ref="mortgage-payment-filter" />
          <SpecialFinancingDropdown ref="special-financing-filter" />
          <BedBathDropdown />
          <PropertyTypesDropdown />
          <SquareFeetDropdown />
          <AgeDropdown />
          {/* <LotSizeDropdown /> */}
          <LevelsDropdown />
          <CarDropdown />
          <ActiveToggle />
        </div>
      </div>
    );
  },

  viewingGridListings() {
    return this.state.layout.grid.grid;
  },

  viewingBuyerOrAgent() {
    return this.state.layout.agent.agentData || this.state.layout.agent.listingDetail || this.state.layout.buyer;
  },

  render() {
    if (!this.state.layout.searchBar) {
      return null;
    }

    // if viewing a listing on buyer or agent, don't render
    if (this.state.layout.agent.listingDetail
      || (typeof this.state.taggedProperty !== 'undefined' && this.state.taggedProperty !== 0)) {
      return null;
    }

    let hidePulldown = false;
    if (this.router && this.router.currentRoute && this.router.currentRoute.url && this.router.currentRoute.url.match(/agent/)) {
      hidePulldown = true;
    }
    return (
      <div className={classNames('search-bar', {
        compressed: this.viewingBuyerOrAgent(),
        'scroll-hide': !this.state.showHeader,
        'editing-filters': this.state.editingFilters,
        'agent-layout': this.state.agentLayout.agentData,
      })}
      >
        <div className="search-container">
          <SearchField
            ref="SearchField"
            onSubmit={this.searchSubmit}
            menuClick={this.actions.common.toggleSidebar}
            sortClick={() => {
              this.setState({ showSort: !this.state.showSort, editingFilters: false });
            }}
            filterClick={() => {
              this.setState({ editingFilters: !this.state.editingFilters, showSort: false });
            }}
          />
          <div className="mobile-rateplug-subheader">
            <RatePlugSubheader filterClick={(filterRef) => {
              this.setState({ editingFilters: true, showSort: false });
              if (this.refs[filterRef] && this.refs[filterRef].refs['custom-dropdown']
                && this.refs[filterRef].refs['custom-dropdown'].handleOnToggle) {
                this.refs[filterRef].refs['custom-dropdown'].handleOnToggle(true);
              }
            }}
            />
            <IDXPlusSubheader filterClick={(filterRef) => {
              this.setState({ editingFilters: true, showSort: false });
              if (this.refs[filterRef] && this.refs[filterRef].refs['custom-dropdown']
                && this.refs[filterRef].refs['custom-dropdown'].handleOnToggle) {
                this.refs[filterRef].refs['custom-dropdown'].handleOnToggle(true);
              }
            }}
            />
          </div>
        </div>
        { hidePulldown
          ? null
          : (
            <div role="button" tabIndex="-1" aria-label="Search" className="pull-down" onClick={this.showHeaders}>
              <SVGIcon name="icon-search" />
            </div>
          )}
        {
          this.state.listings && Array.isArray(this.state.listings)
            ? (
              <div className="results" key={this.state.listings.length}>
                <span>
                  Showing&nbsp;
                  <strong>
                    {this.state.listings.length}
                    &nbsp;
                    homes
                    &nbsp;
                  </strong>
                  <i>
                    out of
                    &nbsp;
                    {this.state.listingCount}
                  </i>
                </span>
              </div>
            ) : this.state.loadingListings
              ? (
                <div className="results" key="loading">
                  <span>Loading listings...</span>
                </div>
              ) : null
        }
        <Agent />
        { (this.state.showSort && this.state.showHeader)
          ? <Sort key="sort" onSortChange={this.onSortChange} />
          : null}
        <div className="editing-filters-container">
          <div className="editing-filters-controls">
            <span role="button" tabIndex="-1" aria-label="Reset search" className="editing-filters-reset" onClick={this.resetSearch}>
              <SVGIcon name="icon-reset" />
              Reset
            </span>
            <button type="button" className="btn btn-primary" onClick={this.doneEditingFilters}>APPLY</button>
          </div>
          {this.filterPanel()}
        </div>
      </div>
    );
  },
});

// <div className="search-bar-controls">
//  <div className="edit-filters" onClick={this.filtersClicked}>+ Filters</div>
//  <div className="edit-sort" onClick={this.sortClicked}>Sort</div>
//  <div className={classNames("toggle-view", {
//    "disabled": this.state.dataLoading
//  })} onClick={this.toggleScreen}>{this.state.layout.map ? "List View" : "Map View"}</div>
// </div>

//
//        {
//          this.state.listings ?
//            <div className="results">
//              <span>Showing&nbsp;<strong>{this.state.listings.length} homes</strong>
//              &nbsp;<i>out of {this.state.listingCount}</i>
//              </span>
//            </div> : this.state.loadingListings ?
//            <div className="results">
//              <span>Loading listings...</span>
//            </div> : null
//          }
//
//        { ( this.state.showSort && this.state.showHeader )
//          ? <Sort key="sort" onSortChange={this.onSortChange} />
//          : null }
//        { this.state.showSort
//          ? null
//          : <div className="pull-down"  onClick={this.showHeaders}>
//          <SVGIcon name="icon-search"></SVGIcon>
//        </div>
//          }
