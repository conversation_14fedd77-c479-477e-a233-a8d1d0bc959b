const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panel-mobile.search-bar.agent',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    agentLayout: ['layout', 'agent'],
    agentData: ['shared', 'agent', 'data'],
    mlsData: ['shared', 'agent', 'mlsData'],
    loadingAgent: ['panels', 'header', 'loadingAgent'],
  },

  getInitialState() {
    return {
      showAgentLine: false,
    };
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.agent.toggle();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
  },

  toggleAgentLine(e) {
    e.preventDefault();
    e.stopPropagation();
    this.setState({ showAgentLine: !this.state.showAgentLine });
  },

  render() {
    const style = {};

    if (!this.state.showAgentLine) {
      style.width = '0';
    }

    return (
      <div className="agent-header-mobile" style={style}>
        {
          this.state.agentData
            ? (
              <div
                role="button"
                tabIndex="-1"
                aria-label="Toggle agent"
                className={classNames('agent-container', {
                  hidden: this.state.agentLayout.agentData
                || this.state.agentLayout.listingDetail,
                  'show-agent': this.state.showAgentLine,
                })}
                onClick={this.agentClicked}
              >
                <AgentProfileImage className="agent-image" onClick={this.toggleAgentLine} />
                <p className="agent-name">
                  {this.state.agentData.FirstName}
&nbsp;
                  <span className="">{this.state.agentData.LastName}</span>
                          &nbsp;
                  <VerifiedBadge />
                  {this.state.mlsData && this.state.mlsData.RealEstateLicenseNumberOnOff
                    ? (
                      <span className="license-num">
                        {
                    this.state.agentData && this.state.agentData.StateLicenseNumber
                      ? ` (${this.state.agentData.StateLicenseNumber})` : ''
                    }
                      </span>
                    ) : null}
                </p>
                {
                this.state.agentData.BrokerName
                  ? <p className="brokerage">{this.state.agentData.BrokerName}</p>
                  : <p>&nbsp;</p>
                }
                <SVGIcon className="icon-license" name="icon-license" />
              </div>
            )
            : null
          }
      </div>
    );
  },
});
