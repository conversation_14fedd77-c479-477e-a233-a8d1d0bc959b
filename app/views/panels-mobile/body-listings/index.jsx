const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins');
const Listings = require('../listings');
const MLSDisclosure = require('../../panels/mls-disclosure');
const LastRefreshed = require('../../panels/last-refreshed');
const NPlayFooter = require('../../panels/n-play-footer');

module.exports = React.createClass({

  displayName: 'panel-mobile.body-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils, mixins.events],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    data: ['panels', 'listings', 'data'],
    spinner: ['panels', 'listings', 'meta', 'spinner'],
    zoomMsg: ['panels', 'listings', 'meta', 'zoomMsg'],
    activeId: ['panels', 'listings', 'activeId'],
    agentMlsId: ['shared', 'agent', 'data', 'MlsId'],
  },

  componentDidMount() {
    this.refs.main.addEventListener('scroll', this.handleScroll);
  },

  componentWillUnmount() {
    this.refs.main.removeEventListener('scroll', this.handleScroll);
  },

  handleScroll(e) {
    this.events.emit(this.events.CONTAINER_SCROLL, e);
  },

  onNav(id) {
    this.actions[this.props.screen].onNav(id);
  },

  mlsDisclosures() {
    if (!this.state.data || this.state.spinner) {
      return null;
    }

    const mlsIds = this.utils.uniqueMLSIdsFromListings(this.state.data);

    if (!mlsIds || mlsIds.length < 1) {
      return null;
    }

    return map(mlsIds, (mlsId) => <MLSDisclosure mlsId={mlsId} key={mlsId} />);
  },

  render() {
    return (
      <div className={this.props.className} ref="main">

        <div className="listing-shadow" />

        <Listings listings={this.state.data} activeId={this.state.activeId} spinner={this.state.spinner} onNav={this.onNav} fromSearch />
        <div className="right-rail-footer">
          {this.mlsDisclosures()}
          <LastRefreshed className="mb20 mt10" />
          <NPlayFooter />
        </div>
      </div>
    );
  },

});
