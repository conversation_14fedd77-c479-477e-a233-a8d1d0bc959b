const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../lib/mixins');
const SearchField = require('../../panels/landing/search-field');
const SVGIcon = require('../../components/svg_icon');
const SubmitBtn = require('../../components/submit_btn');
const NPlayFooter = require('../../panels/n-play-footer');

const constants = require('../../../lib/constants');

module.exports = React.createClass({

  displayName: 'panels-mobile.home',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    saleType: ['shared', 'menu', 'saleType'],
  },

  getInitialState() {
    return {
      bgIndex: 0,
    };
  },

  bgImages: (function () {
    const shuffle = function (o) {
      for (let j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x) { }
      return o;
    };

    const shuffled = shuffle([
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/0.1.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/1.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/2.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/3.jpg',
      'https://nplayassets.blob.core.windows.net/search2/bg-landing-20160614/4.jpg',
    ]);

    return shuffled;
  }()),

  bgChangeInterval: null,
  componentDidMount() {
    this.bgChangeInterval = setInterval(() => {
      this.setState({ bgIndex: (this.state.bgIndex + 1) % this.bgImages.length });
    }, 9000); // Plus 1 second in transition delay to prevent flickering

    // preload the images
    this.bgImages.forEach((url) => {
      const img = new Image();
      img.src = url;
    });
  },

  componentWillUnmount() {
    clearInterval(this.bgChangeInterval);
  },

  searchSubmit(e) {
    e.preventDefault();

    this.actions.common.flagUserAsInteractedWithSite();

    const saleType = this.state.saleType;
    console.log(`Sale Type ${saleType}`);

    const res = this.refs.SearchField.state.res;
    const newVal = this.refs.SearchField.state.value;

    if (!this.refs.SearchField.state.hasPrevious // Using Previous
      && (!res.locationQuery || res.locationQuery !== newVal)) {
      console.log('Need google geocoding');

      if (!newVal || newVal.toLowerCase().indexOf('loading') !== -1) {
        this.setState({ loading: false });
        return;
      }

      this.actions.onboarding.geocodeAddress(newVal, (res1) => {
        const _res = {
          lat: res1.geometry.location.lat,
          lon: res1.geometry.location.lng,
          locationQuery: res1.formatted_address,
        };

        window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({ ..._res, radius: constants.SEARCH_RADIUS_DEFAULT_METERS }));

        console.log(`Search Field: ${_res.locationQuery}`);
        this.actions.home.onNav(_res.lat, _res.lon, res1.radius, _res.locationQuery, saleType);
      });
    } else {
      console.log(`Search Field: ${res.locationQuery}`);
      this.actions.home.onNav(res.lat, res.lon, res.radius, res.locationQuery, saleType);
    }
  },

  toggleSaleType(type) {
    this.actions.menu.updateSearchField([{ key: 'saleType', value: type }]);
  },

  renderSearchArea() {
    return [
      <div
        className={classNames('search-area', {
          collapsed: this.props.belowTheFold,
        })}
        key="search-area"
      >
        <nav className={classNames('search-nav')}>
          <a role="button" tabIndex="-1" className={classNames({ active: this.state.saleType === 1 })} onClick={this.toggleSaleType.bind(null, 1)}>Start your search</a>
        </nav>
        <form className="search-form" onSubmit={this.searchSubmit}>
          <div className="input-group">
            <SearchField ref="SearchField" />
            <SubmitBtn className="btn-primary" icon="icon-search" />
          </div>
        </form>
      </div>,
    ];
  },

  render() {
    return (
      <div className="hero">
        {this.renderSearchArea()}
        <div className={classNames('home-wrapper-container', this.props.className)}>
          <div className="home-background" style={{ backgroundImage: 'url(\''.concat(this.bgImages[this.state.bgIndex], '\')') }} />
          <div role="button" tabIndex="-1" aria-label="Go to homeasap" className="home-logo cursor-pointer" onClick={this.actions.common.goToAboutHomeasap} />
          <div className="links">
            <p>
              <a href="https://about.homeasap.com" target="_blank">About</a>
            </p>
          </div>
          <div className="headline">
            <SVGIcon name="graphics-home-closer-mobile" className="graphics-home-closer-mobile" />
            <SVGIcon name="graphics-home-closer" className="graphics-home-closer" />
          </div>
        </div>
        <div className="search-area-background" />
        <div className="content">
          <div className="row">
            <div className="col-3-3">
              <h1 className="header">YOUR LOCAL HOME SEARCH</h1>
              <p className="description">Offered exclusively through local real estate professionals in markets nationwide. Knowledge is your real estate friend, so why not get it from the best. Find your amazing place today!</p>
            </div>
          </div>
          <div className="row selling-points">
            <div className="col-1-1 col-sm-1-3 selling-point">
              <img alt="local expertise" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-LOCAL.jpg" />
              <h2>Local expertise</h2>
              <p>HomeASAP keeps you one click away from the expertise of a local real estate agent. Get the lowdown on neighborhoods, schools, and more from those who know it best.</p>
            </div>
            <div className="col-1-1 col-sm-1-3 selling-point">
              <img alt="schedule a showing" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-SEE_IT.jpg" />
              <h2>See it in-person</h2>
              <p>{"Found a listing you've fallen in love with? Don't waste a minute and schedule a showing with an agent directly from their site. "}</p>
            </div>
            <div className="col-1-1 col-sm-1-3 selling-point">
              <img alt="selling point" src="https://nplayassets.blob.core.windows.net/search2/HOMEASAP-HIGHLIGHTS.jpg" />
              <h2>Vital details</h2>
              <p>Get the lowdown on any property. Quickly get to the details you want to know. Is there a pool? Fireplace? We lay it all out in the highlights. Want to know the ratings of nearby schools? We&apos;ve got that too.</p>
            </div>
          </div>
        </div>
        <NPlayFooter />
      </div>

    );
  },

});
