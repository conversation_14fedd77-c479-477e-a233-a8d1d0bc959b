const React = require('react');
const classNames = require('classnames');
const _ = require('lodash');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');
const Sub = require('../../panels/sub');
const MapRef = require('../../screens/map/ref');
const GridRef = require('../../screens/grid/ref');
// const ListingEdgeTabbar = require('../../components/listing_edge_tabbar');
const ListingPhotoSlider = require('../../panels/listing/photo_slider');
const ListingSnapshot = require('../../panels/listing/snapshot');
const ListingDetails = require('../../panels/listing/details/for-flyout');
// ListingFinancial = require('../../panels/listing/financial/for-flyout'),
const ListingNeighborhood = require('../../panels/listing/neighborhood/for-flyout');
// const ListingSchools = require('../../panels/listing/schools/for-flyout');
const ListingFooter = require('../../panels/listing/footer');
const ShareListing = require('../../components/share_listing');

module.exports = React.createClass({

  displayName: 'panels-mobile.listing',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    streetViewAvailable: ['screens', 'map', 'streetViewAvailable'],
    mlsDatas: ['shared', 'mlsData'],
    agentData: ['shared', 'agent', 'data'], // Not used, for triggering rerendering only
  },

  hasLatLon() {
    return _.get(this.props.data, 'Location.Lat') && _.get(this.props.data, 'Location.Lon');
  },

  getInitialState() {
    return {
      activeTab: 'Details',
      barShouldStick: false,
      barPlaceholderHeight: 0,
      showSub: false,
    };
  },

  tempData: {
    currentId: false,
  },

  componentDidMount() {
    if (this.props.data) {
      console.log(`Viewing: ${this.props.data.Id}`);
      this.tempData.currentId = this.props.data.Id;
      this.actions.common.setPropertyViewed(this.props.data);
    }
  },

  handleContact() {
    this.actions.agent.setActiveTab('Call');
    if (this.state.data && !this.state.data.isTaxProperty) {
      this.actions.agent.showAgentContact(this.state.data);
    }

    this.actions.analytics.sendEvent('detail view', 'agent', this.state.data.ZipCode);
  },

  componentDidUpdate() {
    if (this.props.data) {
      this.actions.common.setPropertyViewed(this.props.data);
    }

    const mlsId = this.actions.common.getListingMlsId(this.props.data);
    const mlsData = this.state.mlsDatas[mlsId];
    if (mlsId && !mlsData) {
      setTimeout(() => this.actions.listing.getMlsData(mlsId), 500);
    }
  },

  onCloseSub() {
    this.setState({ showSub: false });
  },

  onStreet(e) {
    e.preventDefault();
    this.actions.analytics.sendEvent('detail view', 'map street click', this.state.streetViewAvailable ? 'available' : 'not available');

    if (this.state.streetViewAvailable) {
      this.setState({ showSub: 'street' });
    }
  },

  onAerial(e) {
    e.preventDefault();
    this.actions.analytics.sendEvent('detail view', 'map aerial click');

    this.setState({ showSub: 'aerial' });
  },

  onPhotoClick(e) {
    if (this.state.barShouldStick) {
      this.scrollToTopClick();
    } else {
      this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
      this.actions.panels.toggle('photos', this.props.data);

      this.actions.analytics.sendEvent('detail view', 'photos', this.props.data.ZipCode);
    }
  },

  goToVirtualTour(e) {
    e.preventDefault();
    if (this.props.data && this.props.data.VirtualTourLink) {
      let link = this.props.data.VirtualTourLink;
      if (link.toLowerCase().indexOf('http') !== 0) {
        link = `http://${link}`;
      }
      window.open(link, '_blank');
      this.actions.analytics.sendEvent('detail view', 'virtual tour', this.props.data.ZipCode);
    }
  },

  scrollToTopClick(e) {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const scrollContainer = this.refs.scrollContainer;

    this.actions.common.scrollElementTo(scrollContainer, 0, 500);
  },

  scrollToTopIcon() {
    return (
      <div role="button" tabIndex="-1" aria-label="Back to top" className="icon-back-to-top-container" onClick={this.scrollToTopClick}>
        <SVGIcon name="icon-back-to-top" className="icon-back-to-top" />
      </div>
    );
  },

  tabItemClick(e) {
    e.preventDefault();
    e.stopPropagation();
    const refName = e.target.innerHTML;
    if (refName === 'Details') {
      this.scrollToTopClick();
      return;
    }

    const scrollContainer = this.refs.scrollContainer;
    const element = this.refs[refName];
    this.actions.common.scrollElementTo(scrollContainer,
      element.offsetTop
      - this.headerStickPixels,
      500);
  },

  renderTabs() {
    const tabs = this.getTabs();
    const items = _.map(tabs, (item) => (
      <a
        role="button"
        tabIndex="-1"
        className={classNames('item', 'pull-left', { active: this.state.activeTab === item })}
        key={item}
        onClick={this.tabItemClick}
      >
        {item}
      </a>
    ));

    return (
      <nav className={classNames('nav-scrollable listing-nav')}>
        {items}
      </nav>
    );
  },

  tabContents(listing, mlsData = {}) {
    if (!this.hasLatLon()) {
      return (
        <div className="listing-panels-container">
          <div ref="Details">
            <ListingSnapshot listing={listing} mlsData={mlsData} />
          </div>
          <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
          {/* <hr ref="Financial" />
          {this.scrollToTopIcon()}
          <ListingFinancial listing={listing} mlsData={mlsData} /> */}
          <hr />
          {this.scrollToTopIcon()}
          <ListingFooter listing={listing} mlsData={mlsData} />
        </div>
      );
    }

    return (
      <div className="listing-panels-container">
        <div ref="Details">
          <ListingSnapshot listing={listing} mlsData={mlsData} />
        </div>
        <ListingDetails listing={listing} mlsData={mlsData} onStreet={this.onStreet} onAerial={this.onAerial} streetViewAvailable={this.state.streetViewAvailable} />
        {/* <hr ref="Financial" />
       {this.scrollToTopIcon()}
       <ListingFinancial listing={listing} mlsData={mlsData} /> */}
        <hr ref="Neighborhood" />
        {this.scrollToTopIcon()}
        <ListingNeighborhood listing={listing} mlsData={mlsData} />
        {/* <hr ref="Schools" />
        {this.scrollToTopIcon()}
        <ListingSchools listing={listing} mlsData={mlsData} /> */}
        <hr />
        {this.scrollToTopIcon()}
        <ListingFooter listing={listing} mlsData={mlsData} />
      </div>
    );
  },

  onScroll(e) {
    this.updateActiveTabAtScrollTop(e.target.scrollTop);
  },

  getTabs() {
    return this.hasLatLon() ? ['Details', /* "Financial", */ 'Neighborhood'/* , "Schools" */] : ['Details'];
  },

  headerStickPixels: 103,
  updateActiveTabAtScrollTop(scrollTop) {
    if (scrollTop > this.refs.listingHeaderContainer.offsetHeight - this.headerStickPixels) {
      if (!this.state.barShouldStick) {
        this.setState({
          barShouldStick: true,
          barPlaceholderHeight: this.refs.listingHeaderContainer.offsetHeight,
        });
      }
    } else {
      if (this.state.barShouldStick) {
        this.setState({ barShouldStick: false, barPlaceholderHeight: 0 });
      }
    }

    const tabs = this.getTabs();
    for (let i = tabs.length - 1; i > -1; i--) {
      const tabName = tabs[i];
      const tabElem = this.refs[tabName] && this.refs[tabName];
      if (tabElem && scrollTop
        >= tabElem.offsetTop
        - this.headerStickPixels
      ) {
        if (this.state.activeTab !== tabName) {
          this.setState({ activeTab: tabName });
        }
        return;
      }
    }
  },

  showShareForm(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.showShareForm(this.props.data);
  },

  render() {
    if (this.props.data === false) {
      return '--- Not Available ---';
    }

    const mlsId = this.actions.common.getListingMlsId(this.props.data);
    const mlsData = this.state.mlsDatas[mlsId] || {};

    return (

      <div className="listing-mobile listing">
        { this.state.showSub
          ? (
            <Sub
              data={this.props.data}
              streetViewAvailable={this.state.streetViewAvailable}
              showSub={this.state.showSub}
              onCloseSub={this.onCloseSub}
            />
          )
          : null}
        {
        !this.props.data
          ? (
            <div>
              <MapRef />
              <GridRef />
            </div>
          )
          : (
            <div className="scroll-container" ref="scrollContainer" onScroll={this.onScroll}>

              <div
                ref="listingHeaderContainer"
                className={classNames('listing-header-container', { 'visibility-hidden': this.state.barShouldStick })}
              >

                <ListingPhotoSlider listing={this.props.data} mlsData={mlsData} onPhotoClick={this.onPhotoClick} useOne />

                { this.renderTabs() }

              </div>

              <ShareListing className="pull-right top-icons" title="Share this home" listing={this.props.data} />

              {
              this.props.data.VirtualTourLink
                ? (
                  <a
                    role="button"
                    tabIndex="-1"
                    className="pull-right top-icons"
                    title="Virtual tour"
                    onClick={this.goToVirtualTour}
                  >
                    <SVGIcon name="icon-virtual-tour" className="" />
                  </a>
                )
                : null
              }
              { mlsData.ShowBrokerOnSlider
                ? (
                  <a role="button" tabIndex="-1" className="pull-right top-icons" title="Ask a question" onClick={this.handleContact}>
                    <SVGIcon name="icon-chat-bubbles" />
                  </a>
                )
                : null }

              { this.tabContents(this.props.data, mlsData) }
            </div>
          )
        }

        {
        this.props.data && this.state.barShouldStick ? (
          <div role="button" tabIndex="-1" aria-label="Scroll to top" className="overlay-container cursor-pointer" onClick={this.scrollToTopClick}>

            <div
              className="listing-header-container stick"
              style={{
                marginTop: -1 * (this.state.barPlaceholderHeight - this.headerStickPixels),
              }}
            >

              <ListingPhotoSlider listing={this.props.data} mlsData={mlsData} onPhotoClick={this.onPhotoClick} useOne useSm />

              { this.renderTabs() }

            </div>
          </div>
        ) : null
        }

        {
        this.props.onClose
          ? <div role="button" tabIndex="-1" aria-label="Close" className="icon-mobile-toggle" onClick={this.props.onClose}><SVGIcon name="icon-close-circle" /></div>
          : null
        }
      </div>
    );
  },

});
