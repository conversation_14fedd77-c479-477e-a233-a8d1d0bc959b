const React = require('react');
const Listings = require('../listings');
const mixins = require('../../../lib/mixins');
const LastRefreshed = require('../../panels/last-refreshed');
const MLSDisclosure = require('../../panels/mls-disclosure');
const NPlayFooter = require('../../panels/n-play-footer');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel-mobile.featured-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    activeId: ['screens', 'featured', 'activeId'],
    agentData: ['shared', 'agent', 'data'],
    isFeature: ['screens', 'agent', 'agentProfile', 'mobileShowFeatured'],
  },

  facets: {
    data: ['featuredListings'],
  },

  componentDidMount() {
    if (!this.state.isFeature) {
      return;
    }

    const element = this.refs.feature;
    this.actions.common.scrollElementTo(element.offsetParent,
      element.getBoundingClientRect().top,
      500);
    this.actions.agent.resettMobileFeature();
  },

  onNav(id) {
    this.props.onNav ? this.props.onNav(id) : '';
  },

  render() {
    return (
      <div className={this.props.className}>

        <div ref="feature" className="featured-header">
          <SVGIcon name="icon-featured-listings" />
        </div>

        {
          (!this.state.data || this.state.data.length === 0)
          && this.state.agentData.HomeSearchRegisteredDateTime === 'demo'
            ? [
              <img
                alt="Featured"
                src={window.CONFIG.CDN_URL.concat('search2/future-featured-3.png')}
                width="100%"
                key="future-featured"
              />,
              <div key="footer" className="right-rail-footer mt20">
                <NPlayFooter />
              </div>,
            ]
            : [
              <Listings key="listings" listings={this.state.data} activeId={this.state.activeId} onNav={this.onNav} />,

              <MLSDisclosure key="mlsDisclosure" mlsId={this.state.agentData && this.state.agentData.MlsId} />,
              <div key="footer" className="right-rail-footer">
                <LastRefreshed className="mb20 mt10" />
                <NPlayFooter />
              </div>,
            ]
        }
      </div>
    );
  },

});
