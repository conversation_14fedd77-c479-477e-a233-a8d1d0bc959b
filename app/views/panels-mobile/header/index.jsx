const React = require('react');
const Sidebar = require('react-sidebar').default;
const mixins = require('../../../lib/mixins');
const AgentProfileImage = require('../../components/agent_profile_image');
const SVGIcon = require('../../components/svg_icon');
const VerifiedBadge = require('../../components/verified-badge');

module.exports = React.createClass({

  displayName: 'panels-mobile.header',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router, mixins.utils],

  cursors: {
    layout: ['layout'],
    agentLayout: ['layout', 'agent'],
    login: ['layout', 'login'],
    agentData: ['shared', 'agent', 'data'],
    buyerData: ['shared', 'buyer', 'data'],
    notificationHeader: ['shared', 'notificationCenter', 'header'],
    data: ['panels', 'header', 'data'],
    loadingAgent: ['panels', 'header', 'loadingAgent'],
    mlsData: ['shared', 'agent', 'mlsData'],
    showHeader: ['panels', 'header', 'showHeader'],
    taggedProperty: ['screens', 'buyer', 'activeListing'],
  },

  facets: {
    headerControl: ['currentActiveHeaderControl'],
  },

  showSortRail() {
    this.actions.panels.showSortRail();
  },

  hideSortRail() {
    this.actions.panels.hideSortRail();
  },

  agentClicked(e) {
    e.preventDefault();
    this.actions.agent.toggle();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
  },

  menuClicked() {
    this.actions.menu.toggle();
  },

  loginClicked() {
    this.actions.common.setSidebar(false);
    this.actions.login.startAction = 'mobile header';
    this.actions.login.start({ action: 'mobile_menu', direct: true });

    this.actions.analytics.sendEvent('login', 'start', 'header');
  },

  notificationClicked() {
    this.loginClicked();
  },

  tagClicked() {
    this.actions.tagging.onNav();
  },

  buyerProfile() {
    this.actions.common.goToRoute('/buyer');
  },

  handleProfileClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.common.goToRoute('/agent');
  },

  handleFeaturedListingsClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.featured.onNav();
  },

  handleContactAgentClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.setSidebar(false);
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();
    this.actions.agent.hideAgentHeaderDropdown();
    this.actions.agent.setActiveTab('Call');
    this.actions.agent.showAgentContact();
  },

  handleHomePageClick(e) {
    e.preventDefault();
    e.stopPropagation();
    this.actions.common.goToHomePage();
    this.actions.agent.hideAgentHeaderDropdown();
  },

  render() {
    if (!this.state.layout.header) {
      return null;
    }

    // if viewing a listing on buyer or agent, don't render
    if (this.state.layout.agent.listingDetail
      || (typeof this.state.taggedProperty !== 'undefined' && this.state.taggedProperty !== 0)) {
      return null;
    }

    const sidebarContent = (
      <div className="mobile-slideout">
        <div
          role="button"
          tabIndex="-1"
          aria-label="Your profile"
          className="buyer-area"
          onClick={this.state.buyerData.FirstName
            ? this.buyerProfile
            : (this.state.notificationHeader && this.state.notificationHeader.totalCount)
              ? this.notificationClicked : this.loginClicked}
        >
          <div className="buyer-area-top">
            <img
              alt="Your profile"
              src={(this.state.buyerData && this.state.buyerData.ProfileImage)
                ? this.state.buyerData.ProfileImage
                : '//nplayassets.blob.core.windows.net/search2/fb-login.png'}
            />
          </div>
          <div className="buyer-area-bottom">
            {
            this.state.buyerData && this.state.buyerData.FirstName
              ? (
                <p>
                  Welcome,
                  <br />
                  {this.state.buyerData.FirstName}
                </p>
              )
              : (
                <p>
                  Login
                  {this.state.notificationHeader && this.state.notificationHeader.totalCount ? <span className="notification-dot" /> : null}
                </p>
              )
            }
            <div>
              <div role="button" tabIndex="-1" aria-label="Notifications" className="tag-control" onClick={this.tagClicked}>
                <SVGIcon name="icon-bell" />
              </div>
            </div>
          </div>
        </div>
        { this.state.agentData
          ? (
            <div role="button" tabIndex="-1" aria-label="Toggle agent" className="agent-area-top" onClick={this.agentClicked}>
              <AgentProfileImage className="agent-image" />
              <div className="agent-right">
                <p className="agent-name">
                  <span>{this.state.agentData.FirstName}</span>
&nbsp;
                  <span className="">{this.state.agentData.LastName}</span>
                          &nbsp;
                  <VerifiedBadge />
                  {this.state.mlsData && this.state.mlsData.RealEstateLicenseNumberOnOff
                    ? (
                      <span className="license-num">
                        {
                      this.state.agentData && this.state.agentData.StateLicenseNumber
                        ? ` (${this.state.agentData.StateLicenseNumber})` : ''
                      }
                      </span>
                    ) : null}
                </p>
                {
                  this.state.agentData.BrokerName
                    ? <p className="brokerage">{this.state.agentData.BrokerName}</p>
                    : null
                  }
              </div>
            </div>
          )
          : null}
        <div className="agent-area-bottom">
          <ul>
            <li role="button" tabIndex="-1" aria-label="Profile" onClick={this.handleProfileClick}>
              <SVGIcon name="icon-license" />
              <span>Hello! View My Profile</span>
            </li>
            <li role="button" tabIndex="-1" aria-label="Featured" onClick={this.handleFeaturedListingsClick}>
              <SVGIcon name="icon-front-of-house" />
              <span>Featured Listings</span>
            </li>
            {
              this.actions.common.getAgentHasLendingTree(this.state.agentData)
                ? (
                  <li
                    role="button"
                    tabIndex="-1"
                    aria-label="See Current Rates"
                    onClick={() => {
                      this.actions.analytics.sendEvent('lending tree', 'open from mobile menu');
                      this.actions.common.showLendingTreeModal();
                    }}
                  >
                    <SVGIcon name="icon-rates" />
                    <span>See Current Rates</span>
                  </li>
                )
                : null
            }
            {
              this.actions.common.getAgentHasHV()
                ? (
                  <li role="button" tabIndex="-1" aria-label="Home value" onClick={this.actions.homeWorth.onNav}>
                    <SVGIcon name="icon-homevalue" />
                    <span>What&apos;s your Home Worth?</span>
                  </li>
                )
                : null
            }
            {
              this.utils.getAgentSettingValue(this.state.agentData && this.state.agentData.AgentSettings, 'local_market_reports_url')
                ? (
                  <li
                    role="button"
                    tabIndex="-1"
                    aria-label="Local Market Reports"
                    onClick={() => window.open(this.utils.getAgentSettingValue(this.state.agentData.AgentSettings, 'local_market_reports_url'), '_blank')}
                  >
                    <SVGIcon name="icon-homevalue" />
                    <span>Local Market Reports</span>
                  </li>
                )
                : null
            }
            {
              this.actions.common.getAgentHasDS()
                ? (
                  <li role="button" tabIndex="-1" aria-label="Sweepstakes" onClick={this.actions.common.showDreamsweepsModal}>
                    <SVGIcon name="icon-star" />
                    <span>Enter To Win $200</span>
                  </li>
                )
                : null
            }
            <li role="button" tabIndex="-1" aria-label="Contact" onClick={this.handleContactAgentClick}>
              <SVGIcon name="icon-phone-filled" />
              <span>Contact Now</span>
            </li>
            <li role="button" tabIndex="-1" aria-label="Back to home" onClick={this.handleHomePageClick}>
              <SVGIcon name="icon-search" />
              <span>Return to Home Page</span>
            </li>
            <li>
              <SVGIcon name="icon-material-share" />
              <span className="share-agent" title="">
                <span>Share My Site</span>
                <a role="button" tabIndex="-1" className="share-facebook" title="Share my site on Facebook" onClick={this.actions.common.shareAgentOnFacebook}>
                  <SVGIcon name="icon-facebook" />
                </a>
                <a role="button" tabIndex="-1" className="share-twitter" title="Share my site on Twitter" onClick={this.actions.common.shareAgentOnTwitter}>
                  <SVGIcon name="icon-twitter" />
                </a>
                <a role="button" tabIndex="-1" className="share-email" title="Share my site via Email" onClick={this.actions.common.shareAgentViaEmail}>
                  <SVGIcon name="icon-share-email" />
                </a>
                <a role="button" tabIndex="-1" className="share-sms" title="Share my site via SMS" onClick={this.actions.common.shareAgentViaSMS}>
                  <SVGIcon name="icon-share-sms" />
                </a>
              </span>
            </li>
          </ul>
        </div>
        <div className="logo" />
      </div>
    );

    return (
      <Sidebar
        styles={{
          overlay: { zIndex: 1004 },
          sidebar: { zIndex: 1005, position: 'fixed' },
        }}
        sidebar={sidebarContent}
        open={this.state.layout.sidebar}
        onSetOpen={this.actions.common.setSidebar}
      >
        <noscript />
      </Sidebar>
    );
  },
});
