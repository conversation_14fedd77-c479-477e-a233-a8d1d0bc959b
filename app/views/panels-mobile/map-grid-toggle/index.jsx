const React = require('react');
const SVGIcon = require('../../components/svg_icon');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel-mobile.map-grid-toggle',

  mixins: [mixins.debug, mixins.actions, mixins.cursors],

  cursors: {
    grid: ['layout', 'grid', 'grid'],
    gridDetail: ['layout', 'grid', 'detail'],
    map: ['layout', 'map'],
  },

  facets: {
    dataLoading: ['headerControlDisabled'],
  },

  toggleScreen() {
    this.actions.common.flagUserAsInteractedWithSite();
    this.actions.common.forceDeliverChargedLeadFromAd();

    if (this.state.dataLoading) {
      return;
    }
    if (this.state.map) {
      this.actions.grid.onNav();
      return;
    }
    if (this.state.grid) {
      this.actions.map.onNav(null, { dontResetViewport: true });
    }
  },

  render() {
    if (!this.state.dataLoading && this.state.map) {
      return <div role="button" tabIndex="-1" aria-label="Toggle list view" className="icon-mobile-toggle" onClick={this.toggleScreen}><SVGIcon name="icon-mobile-to-list" /></div>;
    }
    if (!this.state.dataLoading && this.state.grid && !this.state.gridDetail) {
      return <div role="button" tabIndex="-1" aria-label="Toggle map view" className="icon-mobile-toggle" onClick={this.toggleScreen}><SVGIcon name="icon-mobile-to-map" /></div>;
    }

    return null;
  },
});
