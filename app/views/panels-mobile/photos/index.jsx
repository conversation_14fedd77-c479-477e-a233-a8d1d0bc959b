import { PhotoSwipe } from 'react-photoswipe';

const React = require('react');
const map = require('lodash.map');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel-mobile.photos',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.events],

  cursors: {
    layout: ['layout', 'photos'],
    data: ['panels', 'photos', 'data'],
    photoCaptions: ['shared', 'photo', 'captions'],
  },

  captionsBeingLoadedForId: '',

  componentDidMount() {
    this.updatePhotoCaptions(this.props, this.state);

    window.addEventListener('orientationchange', this.orientationChange);
  },

  orientationChange() {
    if (this.state.layout && this.state.data) {
      this.actions.analytics.sendEvent('photo viewer', 'orientation', window.orientation != 0 ? 'landscape' : 'portrait');
    }
  },

  shouldComponentUpdate(nextProps, nextState) {
    this.updatePhotoCaptions(nextProps, nextState);
    return true;
  },

  componentWillUnmount() {
    window.removeEventListener('orientationchange', this.orientationChange);
  },

  index: 0,
  updatePhotoCaptions(props, state) {
    if (!state.data) {
      return;
    }

    if (state.data.Id !== this.captionsBeingLoadedForId
      && state.data.Id !== state.photoCaptions.Id) {
      this.captionsBeingLoadedForId = state.data.Id;
      this.actions.common.getPhotoCaptions(state.data);
      this.index = 0;
    }
  },

  getCaption(index) {
    if (!this.state.data || !this.state.photoCaptions) {
      return '';
    }

    if (this.state.data.REALData && this.state.photoCaptions.REALData) {
      if (this.state.data.Id === this.state.photoCaptions.Id) {
        return this.state.photoCaptions && this.state.photoCaptions.Captions
        && this.state.photoCaptions.Captions[index]
          ? this.state.photoCaptions.Captions[index] : '';
      }
    }

    if (this.state.photoCaptions
      && (
        this.state.data.MlsIds && this.state.data.MlsIds[0]
      && (this.state.data.MlsIds[0] === this.state.photoCaptions.MlsId)
      )
      && (
        this.state.data.PropertyListingId
      && (this.state.data.PropertyListingId === (this.state.photoCaptions.PropertyListingId || this.state.photoCaptions.ListingId))
      )
    ) {
      return this.state.photoCaptions && this.state.photoCaptions.Captions
      && this.state.photoCaptions.Captions[index]
        ? this.state.photoCaptions.Captions[index] : '';
    }
    return '';
  },

  onClose(e) {
    e.preventDefault();
    e.stopPropagation();

    this.actions.panels.reset('photos', true);
  },

  slideChange(index/* , elem */) {
    this.index = index;
    this.forceUpdate();
  },

  renderPhotos() {
    const photos = this.state.data && this.state.data.ImageUrls || [];

    const items = map(photos, (obj, idx) => ({
      src: obj.xlg,
      msrc: obj.lg,
      w: 1704,
      h: 1136,
      title: `${(idx + 1)} / ${photos.length} - ${this.getCaption(idx)}`,
    }));

    const isOpen = true;

    const options = {
      history: false,
      counterEl: false,
      shareEl: false,
      zoomEl: false,
      arrowEl: false,
      maxSpreadZoom: 4,
      preload: [1, 2],
    };

    const handleClose = () => {
      false;
    };

    return (
      <PhotoSwipe
        isOpen={isOpen}
        items={items}
        options={options}
        onClose={handleClose}
        afterChange={() => {
          this.events.emit(this.events.GALLERY_PHOTO_VIEWED);
        }}
      />
    );
  },

  render() {
    if (!this.state.layout || !this.state.data) {
      return null;
    }

    return (
      <div className="photoslider-mobile" ref="main">
        {this.renderPhotos()}
        <div role="button" tabIndex="-1" aria-label="Tag close" className="close" onClick={this.onClose}>
          <SVGIcon name="icon-close-button" className="tag-close-button" />
        </div>
      </div>
    );
  },

});
