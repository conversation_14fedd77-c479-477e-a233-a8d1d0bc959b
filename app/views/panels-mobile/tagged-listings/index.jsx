const React = require('react');
const map = require('lodash.map');
const filter = require('lodash.filter');
const mixins = require('../../../lib/mixins');
const Listings = require('../listings');
const MLSDisclosure = require('../../panels/mls-disclosure');

module.exports = React.createClass({

  displayName: 'panel-mobile.tagged-listings',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.utils],

  propTypes: process.env.NODE_ENV === 'production' ? {} : {
    className: React.PropTypes.string,
    screen: React.PropTypes.string,
  },

  cursors: {
    data: ['screens', 'tagging', 'data'],
    activeId: ['screens', 'tagging', 'activeId'],
    // agentMlsId: ['shared', 'agent', 'data', 'MlsId']
  },
  componentDidMount() {
    this.checkLogin();
  },

  checkLogin() {
    if (!this.actions.login.LOGIN_STATE.LoggedIn) {
      this.actions.login.registerCallback(this.onLogin);
    } else {
      this.actions.tagging.getListingsByTag();
    }
  },

  onLogin(res) {
    if (!this.state.data && res.state == this.actions.login.LOGIN_STATE.LoggedIn) {
      this.actions.tagging.getListingsByTag();
    }
  },

  onNav(id) {
    this.props.onNav ? this.props.onNav(id) : '';
  },

  mlsDisclosures() {
    if (!this.state.data || !this.state.data.Listings) {
      return null;
    }

    const mlsIds = this.utils.uniqueMLSIdsFromListings(
      map(this.state.data.Listings, (item) => item && item.Listing, this),
    );

    if (!mlsIds || mlsIds.length < 1) {
      return null;
    }

    return map(mlsIds, (mlsId) => <MLSDisclosure mlsId={mlsId} key={mlsId} />);
  },

  render() {
    return (
      <div className={this.props.className}>

        <div className="tagging-header">
          My Homes
          {typeof this.props.tag === 'string' ? `: ${this.props.tag}` : ''}
        </div>

        <Listings
          listings={
        this.state.data
        && map(
          filter(this.state.data.Listings, (item) => !!item.Listing), function (item) {
            item.Listing.Image = this.utils.getImageUrls(item.Listing, true);
            return item.Listing;
          }, this,
        )
}
          activeId={this.state.activeId}
          onNav={this.onNav}
        />

        <div className="right-rail-footer">
          {this.mlsDisclosures()}
        </div>

      </div>
    );
  },

});
