const React = require('react');
const Ref = require('./ref');
const mixins = require('../../../lib/mixins');
const ListingMobile = require('../../panels-mobile/listing');

module.exports = React.createClass({

  displayName: 'screens-mobile.listing',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.leaflet],

  cursors: {
    layout: ['layout', 'listing'],
    data: ['screens', 'listing', 'data'],
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data, 'full');

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data.ZipCode);
  },

  onClose() {
    if (this.state.data) {
      this.actions.menu.onAutosuggest.onNav(
        this.state.data.Location.Lat,
        this.state.data.Location.Lon,
        this.leaflet.opts.circleDefaultRadius, null, { currentLocation: true },
      );
    }
  },

  partial() {
    return (
      <ListingMobile data={this.state.data} onPhotoClick={this.onPhotoClick} onClose={this.onClose} />
    );
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    if (!this.state.data) {
      return (
        <div className="listing">
          <Ref />
        </div>
      );
    }

    return (
      <div className="listing">

        <Ref />

        {this.partial()}

      </div>
    );
  },

});
