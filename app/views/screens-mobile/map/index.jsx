const React = require('react');
const classNames = require('classnames');
const ListingMobile = require('../../panels-mobile/listing');
const SVGIcon = require('../../components/svg_icon');
const BodyListingsHorizontal = require('../../panels/body-listings-horizontal');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens-mobile.map',

  mixins: [mixins.debug, mixins.cursors, mixins.actions/* mixins.pureRender */],

  cursors: {
    layout: ['layout', 'map'],
    ref: ['screens', 'map', 'ref', 'display'],
    mobilePreviewData: ['screens', 'map', 'mobilePreviewData'],
    data: ['screens', 'map', 'data'],
  },

  getInitialState() {
    return {
      mobilePreviewHelperShouldShow: !window.localStorageAlias.getItem('SWIPE_HINT_SHOWN'),
    };
  },

  // toggleFull: function() {
  //
  //  this.setState({showFull: !this.state.showFull})
  // },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos');

    this.actions.analytics.sendEvent('detail view', 'photos', (this.state.mobilePreviewData || this.state.data).ZipCode);
  },

  onNav(id) {
    this.actions.map.onNav(id);
  },

  hideListing() {
    const id = this.state.data && this.state.data.Id;
    this.actions.map.onNav(id);

    setTimeout(() => {
      this.actions.map.setMobileCardDataWithId(id);
      this.actions.map.setActiveMarker(id);
    }, 0);
  },

  dismissMobilePreviewData() {
    this.actions.map.setMobileCardDataWithId(null);
  },

  previewTouched() {
    window.localStorageAlias.setItem('SWIPE_HINT_SHOWN', 1);
    this.setState({ mobilePreviewHelperShouldShow: false });
  },

  render() {
    if (!this.state.layout) {
      return null;
    }

    if (this.state.mobilePreviewData) {
      return (
        <div className="mobile-preview-container">
          <BodyListingsHorizontal activeId={this.state.mobilePreviewData && this.state.mobilePreviewData.Id} />
          <div
            className={classNames('mobile-preview-helper', {
              hidden: !this.state.mobilePreviewHelperShouldShow,
            })}
            onTouchStart={this.previewTouched}
          >
            <SVGIcon name="graphics-swipe" />
          </div>
        </div>
      );
    }

    return (
      this.state.ref
        ? (
          <ListingMobile
            data={this.state.data}
            onClose={this.hideListing}
            onPhotoClick={this.onPhotoClick}
          />
        ) : null
    );
  },

});
