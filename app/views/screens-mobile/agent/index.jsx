const React = require('react');
const classNames = require('classnames');
const SpinnerRound = require('../../components/spinner_round');
const AgentProfile = require('../../panels-mobile/agent-profile');
const FeaturedListings = require('../../panels-mobile/featured-listings');
const ListingMobile = require('../../panels-mobile/listing');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'screens-mobile.agent',

  mixins: [mixins.debug, mixins.cursors, mixins.actions, mixins.events],

  cursors: {
    agentData: ['shared', 'agent', 'data'],
    mlsData: ['shared', 'agent', 'mlsData'],
    agentLayout: ['layout', 'agent', 'agentData'],
    listingLayout: ['layout', 'agent', 'listingDetail'],
    activeListing: ['screens', 'agent', 'activeListing'],
    showHeader: ['panels', 'header', 'showHeader'],
    agentIsIDXCustomer: ['shared', 'agent', 'data', 'HomeSearchRegisteredDateTime'],
    buyerData: ['shared', 'buyer', 'data'],
  },

  componentDidMount() {

  },

  componentDidUpdate() {
    if (!this.state.attachedScrollEvent && this.refs.main) {
      this.setState({ attachedScrollEvent: true }, () => {
        this.refs.main.addEventListener('scroll', this.handleScroll);
      });
    }
  },

  componentWillUnmount() {
    if (this.refs.main) {
      this.refs.main.removeEventListener('scroll', this.handleScroll);
      this.setState({ attachedScrollEvent: false });
    }
  },

  handleScroll(e) {
    this.events.emit(this.events.CONTAINER_SCROLL, e);
  },

  showAgentContact(tab) {
    this.actions.agent.setActiveTab(tab);
    this.actions.agent.showAgentContact(null);
  },

  closeClicked(e) {
    e.preventDefault();
    this.actions.agent.goBack();
  },
  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.activeListing);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.activeListing.ZipCode);
  },
  onClose() {
    this.actions.agent.onClose();
  },
  backToAgent() {
    this.actions.agent.onNav();
  },
  onNav(id) {
    this.actions.agent.onNav(id);
  },
  showBrokerage() {
    this.actions.agent.showBrokerage();
  },
  render() {
    if (!this.state.agentLayout && !this.state.listingLayout) {
      return null;
    }

    if (!this.state.agentData) {
      return (
        <center className="mt30">
          <SpinnerRound />
        </center>
      );
    }

    return (
      <div className={classNames('agent-profile-mobile-outer-container', { 'listing-layout': this.state.listingLayout })} ref="main">
        <div className={classNames('menu-top fixed', {
          'scroll-hide': this.state.showHeader,
          'no-search-form': !this.state.agentIsIDXCustomer,
        })}
        >
          <div className="details-holder">
            <div className="col-1-1 text-center">
              <a
                role="button"
                tabIndex="-1"
                className="btn btn-primary contact-button"
                onClick={this.showAgentContact.bind(this, 'Call')}
              >
                Contact&nbsp;
                {this.state.agentData && this.state.agentData.FirstName}
              </a>
            </div>
          </div>
        </div>

        <div className="agent-screen-container-mobile">
          {
            this.state.agentLayout
              ? <AgentProfile agentData={this.state.agentData} mlsData={this.state.mlsData} buyerData={this.state.buyerData} />
              : this.state.activeListing
                ? (
                  <ListingMobile
                    data={this.state.activeListing}
                    onClose={this.backToAgent}
                    onPhotoClick={this.onPhotoClick}
                  />
                ) : (
                  <center className="mt30">
                    <SpinnerRound />
                  </center>
                )
}
          <FeaturedListings className="layout--front featured-listings" onNav={this.onNav} />
        </div>

        {
          ((this.state.agentLayout && typeof this.state.agentLayout === 'string')
          || (this.state.listingLayout && typeof this.state.listingLayout === 'string'))
            ? <div className="icon-mobile-toggle" role="button" tabIndex="-1" aria-label="Close" onClick={this.onClose}><SVGIcon name="icon-close-circle" /></div>
            : null
        }
      </div>
    );
  },
});
