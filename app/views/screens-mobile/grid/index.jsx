const React = require('react');
const BodyListings = require('../../panels-mobile/body-listings');
const ListingMobile = require('../../panels-mobile/listing');
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens-mobile.grid',

  mixins: [mixins.debug, mixins.cursors, mixins.actions/* mixins.pureRender */],

  cursors: {
    layout1: ['layout', 'grid', 'grid'],
    layout2: ['layout', 'grid', 'detail'],
    data: ['screens', 'grid', 'data'],
    activeId: ['panels', 'listings', 'activeId'],
  },

  // toggleFull: function() {
  //
  //  this.setState({showFull: !this.state.showFull})
  // },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.data[this.props.id]);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.data[this.props.id].ZipCode);
  },

  hideListing() {
    this.actions.grid.onNav();
  },

  render() {
    if ((!this.state.layout1) && (!this.state.layout2)) {
      return null;
    }

    const listing = this.state.data && this.state.data[this.state.activeId];

    return (
      <div className="mobile-grid">

        {
          // this.state.showFull ?
          //  <FullContent ref="FullContent" onClose={this.toggleFull}/> :
          //  <Content onExpandViewClick={this.toggleFull}/>
          }

        {
        this.state.layout2 && this.state.activeId
          ? (
            <ListingMobile
              data={listing}
              onClose={this.hideListing}
              onPhotoClick={this.onPhotoClick}
            />
          ) : null
        }

        <div className="mt40 mobile-listings-container">
          <BodyListings className="body-listings" screen="grid" />
        </div>

      </div>
    );
  },

});
