const React = require('react');
const SpinnerRound = require('../../components/spinner_round');
const SVGIcon = require('../../components/svg_icon');
const mixins = require('../../../lib/mixins');
const TaggedListings = require('../../panels-mobile/tagged-listings');
const NPlayFooter = require('../../panels/n-play-footer');
const ListingMobile = require('../../panels-mobile/listing');

module.exports = React.createClass({

  displayName: 'screens-mobile.buyer',

  mixins: [mixins.debug, mixins.cursors, mixins.actions/* mixins.pureRender */],

  cursors: {
    layout: ['layout', 'buyer'],
    buyerData: ['shared', 'buyer', 'data'],
    taggedProperty: ['screens', 'buyer', 'activeListing'],
  },

  onNav(id) {
    this.actions.buyer.onNav(id);
  },

  logOut() {
    this.actions.login.logout();
  },

  closeClicked() {
    this.actions.buyer.onClose();
  },

  closeTaggedProperty() {
    this.actions.buyer.onNav();
  },

  onPhotoClick(e) {
    this.actions.common.setPhotoSliderIndex(isNaN(e) ? 0 : e);
    this.actions.panels.toggle('photos', this.state.taggedProperty);

    this.actions.analytics.sendEvent('detail view', 'photos', this.state.taggedProperty.ZipCode);
  },

  render() {
    if (!this.state.layout) {
      return null;
    }
    if (!this.state.buyerData) {
      return (
        <center className="mt30">
          <SpinnerRound />
        </center>
      );
    }

    return (
      <div className="buyer-screen-container">

        <div className="buyer-info-container row">
          <div className="buyer-info">
            <div className="row">
              <h3 className="brand-primary">Check out these features</h3>

              <h4>Communicate with Agents</h4>
              <p>Have a question on a home you&apos;ve viewed&#63; Or maybe you want to schedule an appointment... We&apos;ve made it easy for you to reach out to Agent to learn more about properties in your area.</p>
              <p>When you&apos;re viewing a property&apos;s details, simply click on the agent&apos;s name and photo that are displayed on the property&apos;s image. This will allow you to contact the agent about that property.</p>
              <p>You can also contact them by clicking on their picture and name on the top left, simply click contact now from the options in the dropdown.</p>
              <img alt="Contact" src={`${window.CONFIG.CDN_URL}search2/buyer-agent-contact.jpg`} />
              <br />
              <br />
              <h4>Tag and Save Homes</h4>
              <p>Did you know that you can tag and save homes to easily view later&#63; Simply click the tag icon in the upper right corner of the listing&apos;s photo. This will take you to the tag menu where you can favorite or dislike a home. You can even create your own tags to use!</p>
              <p>To make your life even easier, we&apos;ve stored all your tags in one place. When you click on the tag icon, click the my homes link to view all your tagged homes. You can even sort all of the homes you have tagged and favorited! Pretty cool huh&#63;</p>
              <img alt="Tag home" src={`${window.CONFIG.CDN_URL}search2/buyer-tag.jpg`} />
              <br />
              <br />
              <h4>View Recent Searches</h4>
              <p>Can&apos;t remember your most recent search&#63; No worries, we saved it for you. Simply click in the search box where it says &quot;Address, Neighborhood, City, Zip or School&quot; to see a list of your recent searches.</p>
              <img alt="Recent searches" src={`${window.CONFIG.CDN_URL}search2/buyer-recent-searches.jpg`} />
              <br />
              <br />
            </div>

            <a className="logout" role="button" tabIndex="-1" onClick={this.logOut}>Logout</a>
          </div>
        </div>
        <TaggedListings className="tagged-listings" onNav={this.onNav} />
        <div className="buyer-footer">
          <NPlayFooter />
        </div>

        {
        this.state.taggedProperty !== 0
          ? (
            <ListingMobile
              data={this.state.taggedProperty}
              onClose={this.closeTaggedProperty}
              onPhotoClick={this.onPhotoClick}
            />
          ) : null
        }

        {
        typeof this.state.layout === 'string'
          ? <div className="icon-mobile-toggle" role="button" tabIndex="-1" onClick={this.closeClicked} aria-label="Close button"><SVGIcon name="icon-close-circle" /></div> : null
        }
      </div>
    );
  },

});
