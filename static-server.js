const CONFIG_KEY = process.env.SEARCH_ENV === 'development_server' ? 'development_server'
  : process.env.SEARCH_ENV === 'test' ? 'test'
    : process.env.SEARCH_ENV === 'production' ? 'production'
      : 'development_client';
const st = require('st');
const http = require('http');
const Config = require('./config')[CONFIG_KEY];

const ROUTER_ROOT = Config.ROUTER_PATH || '/';

console.log(Config);
console.log(`Static Server: Router Root is ${ROUTER_ROOT}`);

const mount = st({
  path: 'public/',
  url: ROUTER_ROOT,

  cache: {
    fd: {
      max: 1000, // number of fd's to hang on to
      maxAge: 1000 * 60 * 60 * 24 * 365,
    },

    stat: {
      max: 5000, // number of stat objects to hang on to
      maxAge: 1000 * 60 * 60 * 24 * 365,
    },

    content: {
      max: 1024 * 1024 * 64, // how much memory to use on caching contents
      maxAge: 1000 * 60 * 60 * 24 * 365,
    },
  },

  index: 'index.html',

  gzip: true,
});

const PORT = process.env.PORT || 5000;

http.createServer((req, res) => {
  if (!String(req.url).match(/\/(.*\.(png|jpg|gif|js|css|svg|xml|json)|(robots\.txt|favicon\.ico))$/g)) {
    req.url = ROUTER_ROOT;
    res.setHeader('cache-control', 'no-cache');
  }
  // console.log(req.url)
  if (mount(req, res)) {
    console.log(`Served ${req.url}`);
  }
}).listen(PORT, () => {
  console.log(`Listening to port ${PORT}...`);
});
