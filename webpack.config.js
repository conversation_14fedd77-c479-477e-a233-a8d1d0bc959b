/**
 * webpack.config.js
 * */

const webpack = require('webpack');
const clone = require('clone');
const WebpackStrip = require('strip-loader');

module.exports = (type, PORT) => {
  /** ******* BASE DEV CONFIG ******** */
  const baseConfig = {
    debug: false,
    watch: false,
    cache: true,
    profile: true,
    entry: {
      app: ['./index'],
      // tests: './test/tests'
    },
    output: {
      publicPath: '/public/',
      path: 'public/build/js',
      filename: '[name].js', // Based on keys in entry above
    },
    resolve: {
      extensions: ['', '.js', '.jsx', '.json'],
      modulesdirectories: ['bower_components', 'node_modules'],
    },
    module: {
      noParse: /\.min\.js/,
      loaders: [
        {
          test: /\.jsx?$/,
          loader: 'babel',
          query: { cacheDirectory: true, presets: ['es2015', 'react', 'stage-0'] },
          exclude: /(node_modules|bower_components)/,
        },
      ],
    },
    externals: [
      // /^[a-z\-0-9]+$/, // Every non-relative module
    ],
    plugins: [
      new webpack.optimize.OccurenceOrderPlugin(),
      new webpack.optimize.DedupePlugin(),
      new webpack.optimize.AggressiveMergingPlugin(),
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
    ],
  };

  if (type !== 'prod' && type !== 'live') {
    return baseConfig;
  }

  /** ******* PROD CONFIG ******** */
  const prodConfig = clone(baseConfig);
  prodConfig.cache = false;

  prodConfig.module.loaders.push(
    // Remove debug and console statements on prod
    {
      test: /\.js$/,
      loader: WebpackStrip.loader('debug'),
      exclude: /(node_modules|bower_components)/,
    },
    {
      test: /\.js$/,
      loader: 'babel',
      query: { cacheDirectory: true, presets: ['es2015', 'react', 'stage-0'] },
      exclude: /(node_modules|bower_components)/,
    },
  );

  // This has effect on the react lib size
  prodConfig.plugins.unshift(
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: JSON.stringify('production'),
      },
    }),
  );

  prodConfig.plugins.push(
    new webpack.IgnorePlugin(/debug/),
    new webpack.optimize.UglifyJsPlugin({
      output: { comments: false },
      compress: { drop_console: true },
    }),
  );

  /** ******* LIVE CONFIG ******** */
  const liveConfig = {
    entry: [
      `webpack-dev-server/client?http://localhost:${PORT}`,
      'webpack/hot/only-dev-server',
      './index',
    ],
    output: {
      publicPath: '/',
      path: __dirname,
      filename: 'build/js/app.js',
    },
    plugins: [
      new webpack.HotModuleReplacementPlugin(),
      new webpack.NoErrorsPlugin(),
    ],
    resolve: {
      extensions: ['', '.js', '.jsx', '.json'],
      modulesdirectories: ['bower_components', 'node_modules'],
    },
    module: {
      loaders: [
        {
          test: /\.jsx?$/,
          loader: 'react-hot',
          exclude: /(node_modules|bower_components)/,
        },
        {
          test: /\.jsx?$/,
          loader: 'babel',
          query: { cacheDirectory: true, presets: ['es2015', 'react', 'stage-0'] },
          exclude: /(node_modules|bower_components)/,
        },
        {
          test: /\.js$/,
          loader: 'babel',
          query: { cacheDirectory: true, presets: ['es2015', 'react', 'stage-0'] },
          exclude: /(node_modules|bower_components)/,
        },
      ],
    },
  };

  if (type === 'prod') {
    return prodConfig;
  }
  if (type === 'live') {
    return liveConfig;
  }
  return baseConfig;
};
