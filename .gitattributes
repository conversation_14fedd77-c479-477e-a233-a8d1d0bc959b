# Auto detect text files and perform LF normalization
# http://davidlaing.com/2012/09/19/customise-your-gitattributes-to-become-a-git-ninja/
* text=auto

# These files are text and should be normalized (Convert crlf => lf)
*.css text eol=lf
*.less text eol=lf
*.scss text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.htm text eol=lf
*.html text eol=lf
*.xml text eol=lf
*.txt text eol=lf
*.ini text eol=lf
*.inc text eol=lf
*.php text eol=lf
*.svg text eol=lf
.htaccess text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf



# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.exe binary
*.dll binary
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary


# Documents
*.doc  diff=astextplain
*.DOC  diff=astextplain
*.docx diff=astextplain
*.DOCX diff=astextplain
*.dot  diff=astextplain
*.DOT  diff=astextplain
*.pdf  diff=astextplain
*.PDF  diff=astextplain
*.rtf  diff=astextplain
*.RTF  diff=astextplain
