{"env": {"browser": true, "commonjs": true, "es6": true}, "extends": ["plugin:react/recommended", "airbnb"], "globals": {"L": "readonly", "mapboxgl": "readonly", "google": "readonly", "OneSignal": "readonly", "FB": "readonly", "ClipboardJS": "readonly", "BASE_API_URL": "readonly", "spatialStream": "readonly", "Dmp": "readonly"}, "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018}, "plugins": ["react"], "rules": {"max-len": 0, "global-require": 0, "eqeqeq": 0, "func-names": 0, "no-plusplus": 0, "no-unused-expressions": 0, "no-restricted-syntax": 0, "no-restricted-properties": 0, "no-func-assign": 0, "no-nested-ternary": 0, "no-param-reassign": 0, "no-mixed-operators": 0, "no-underscore-dangle": 0, "no-restricted-globals": 0, "consistent-return": 0, "prefer-destructuring": 0, "class-methods-use-this": 0, "react/no-deprecated": 0, "guard-for-in": 0, "prefer-promise-reject-errors": 0, "new-cap": 0, "no-lonely-if": 0, "no-empty": 0, "no-alert": 0, "no-bitwise": 0, "no-console": 0, "curly": ["error", "all"], "brace-style": ["error", "1tbs", {"allowSingleLine": false}], "one-var-declaration-per-line": ["error", "always"], "react/destructuring-assignment": 0, "react/no-will-update-set-state": 0, "react/no-did-update-set-state": 0, "react/no-find-dom-node": 0, "react/no-string-refs": 0, "react/no-danger": 0, "react/jsx-no-bind": 0, "react/no-is-mounted": 0, "react/forbid-prop-types": 0, "react/no-array-index-key": 0, "react/jsx-no-target-blank": 0, "react/jsx-props-no-spreading": 0, "jsx-a11y/no-noninteractive-element-interactions": 0, "jsx-a11y/no-noninteractive-element-to-interactive-role": 0, "jsx-a11y/label-has-associated-control": [2, {"assert": "either"}], "jsx-a11y/anchor-is-valid": 0, "jsx-a11y/click-events-have-key-events": 0}}