{"name": "searchspa", "description": "Search 2.0", "homepage": "https://github.com/homeasap/searchspa", "version": "1.0.1", "keywords": [], "author": "HomeASAP Developers <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/homeasap/searchspa.git"}, "private": true, "license": "GNU", "bugs": {"url": "https://github.com/homeasap/searchspa/issues"}, "engines": {"node": "8.17.0", "npm": "6.13.4"}, "dependencies": {"add-component-symlinks": "homeasap/add-component-symlinks#68711f", "async": "^2.1.4", "babel": "^6.0.0", "babel-core": "^6.0.0", "babel-loader": "^6.0.0", "babel-preset-es2015": "^6.1.2", "babel-preset-react": "^6.1.2", "babel-preset-stage-0": "^6.1.2", "babel-runtime": "^6.0.14", "baobab": "~1.1.1", "chalk": "^1.0.0", "classnames": "^2.1.3", "clipboard-copy": "^2.0.0", "clone": "^1.0.2", "date-format-lite": "^0.7.4", "deep-freeze-strict": "^1.1.1", "del": "^1.2.0", "error-parser": "^1.0.0", "extend": "^2.0.1", "geo-viewport": "^0.1.1", "gulp": "^3.8.11", "gulp-autoprefixer": "^2.3.1", "gulp-base64": "^0.1.2", "gulp-htmlmin": "^1.2.0", "gulp-if": "^1.2.5", "gulp-jade": "^1.0.1", "gulp-less": "^3.0.3", "gulp-minify-css": "^1.1.0", "gulp-natural-sort": "^0.1.1", "gulp-plumber": "^1.0.1", "gulp-rename": "^1.2.2", "gulp-rev": "^4.0.0", "gulp-size": "^1.2.3", "gulp-slash": "^1.1.3", "gulp-svgmin": "^1.1.2", "gulp-svgstore": "^5.0.2", "hammerjs": "^2.0.8", "jstransformer-uglify-js": "^1.0.0", "leaflet": "^0.7.7", "leaflet-geojson-stream": "0.0.0", "lodash": "^4.6.1", "lodash.debounce": "^3.1.0", "lodash.defer": "^3.1.0", "lodash.filter": "^3.1.1", "lodash.foreach": "^3.0.3", "lodash.map": "^3.1.1", "lodash.sortby": "^3.1.0", "moment": "^2.12.0", "object-assign": "^4.0.1", "path-to-regexp": "^6.2.0", "paths-js": "^0.3.5", "promise": "^7.0.4", "react": "^15.3.2", "react-addons-css-transition-group": "^15.3.2", "react-addons-pure-render-mixin": "^15.3.2", "react-addons-transition-group": "^15.3.2", "react-autosuggest": "^7.0.1", "react-bootstrap": "^0.30.5", "react-dom": "^15.3.2", "react-draggable": "^3.0.5", "react-motion": "^0.4.5", "react-photoswipe": "^1.2.0", "react-portal": "^3.0.0", "react-select": "^1.1.0", "react-sidebar": "^2.2.1", "react-slick": "homeasap/react-slick#f2b5618", "react-swipe": "^2.3.0", "react-swipeable-views": "^0.7.11", "run-sequence": "^1.1.0", "screenfull": "^3.0.0", "scroll-to": "github:nashibao/scroll-to", "shortid": "^2.2.6", "slick-carousel": "^1.6.0", "st": "^0.5.4", "strip-loader": "^0.1.0", "swipe-js-iso": "^2.0.1", "sync-exec": "^0.6.2", "vinyl-paths": "^1.0.0", "web-client-router": "homeasap/web-client-router#57c4562", "webpack": "^1.10.1", "wkx": "^0.4.2", "xhr": "^2.0.2", "yargs": "^3.15.0", "zenscroll": "^3.1.0"}, "devDependencies": {"eslint": "^6.8.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.3", "eslint-plugin-react-hooks": "^4.1.2", "gulp-watch": "^4.2.4", "husky": "^3.1.0", "react-hot-loader": "^1.2.5", "should": "~6.0.1", "spa-server": "^0.1.0", "webpack-dev-server": "^1.10.1"}, "directories": {"test": "test"}, "scripts": {"start": "node static-server.js", "postinstall": "add-component-symlinks && gulp", "lint": "eslint . --ignore-path .gitignore --ext .js,.jsx", "lint:fix": "npm run lint -- --fix", "test": "echo 'No Test'"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}}