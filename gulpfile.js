// Build out CLI Args
const yargs = require('yargs');

const { argv } = yargs.usage('Usage: \n'
+ '$ gulp [option] - options: js validate less sass svgstore versioning jade watch \n'
+ '  --prod    [prod optimizations, production environment] \n'
+ '  --live    [live reload - JS only] \n'
+ '  --livecss [live reload - CSS only] \n'
+ '  --port    [custom port for webserver || 3000]')

  .alias('p', 'prod')
  .alias('l', 'live')
  .alias('c', 'livecss')
  .alias('h', 'help');

// If help argument is passed, display help and exit
if (argv.help) {
  console.log(yargs.help());
  process.exit(0);
}

const PORT = Number(argv.port || process.env.PORT || 3000);
const prodMode = argv.prod
  || process.env.SEARCH_ENV === 'test'
  || process.env.SEARCH_ENV === 'production'
  || process.env.NPM_CONFIG_PRODUCTION === true;

// Taken from:
// https://github.com/google/web-starter-kit/blob/master/gulpfile.js#L78-L88
const AUTOPREFIXER_BROWSERS = [
  'ie >= 9',
  'ie_mob >= 10',
  'ff >= 30',
  'chrome >= 34',
  'safari >= 7',
  'opera >= 23',
  'ios >= 7',
  'android >= 4.4',
  'bb >= 10',
];

const gulp = require('gulp');
const plumber = require('gulp-plumber');
const rename = require('gulp-rename');
const runSequence = require('run-sequence');
const chalk = require('chalk');
const path = require('path');
const exec = require('sync-exec');
const gulpSlash = require('gulp-slash');

// Js Webpack
const webpack = require('webpack');
const errorParser = require('error-parser');

// Jade
const jade = require('gulp-jade');
const htmlmin = require('gulp-htmlmin');

// SVGs
const svgMin = require('gulp-svgmin');
const svgStore = require('gulp-svgstore');

// Less
const less = require('gulp-less');
const autoprefixer = require('gulp-autoprefixer');
// Inline base64 Images
const base64 = require('gulp-base64');

// CSS Plugins
const minifyCss = require('gulp-minify-css');
const gulpif = require('gulp-if');

// Image Plugins
//  gulpImage = require('gulp-image'),

// Versioning
const rev = require('gulp-rev');
const del = require('del');
const vinylPaths = require('vinyl-paths');

// Test Webserver
const watch = !prodMode && require('gulp-watch');
const webserver = !prodMode && require('spa-server');
const WebpackDevServer = !prodMode && require('webpack-dev-server');

// File Size
const fileSize = require('gulp-size');

const CONFIG_KEY = process.env.SEARCH_ENV === 'development_server' ? 'development_server'
  : process.env.SEARCH_ENV === 'test' ? 'test'
    : process.env.SEARCH_ENV === 'production' ? 'production'
      : 'development_client';
const WebPackConfig = require('./webpack.config.js');
const Config = require('./config')[CONFIG_KEY];

console.log(`Config Key: ${CONFIG_KEY}`);
Config.GIT_HASH = ''.concat(
  process.env.TEAMCITY_PROJECT_NAME || '',
  ' ',
  process.env.TEAMCITY_BUILDCONF_NAME || '',
  ' ',
  process.env.BUILD_NUMBER || '',
  ' ',
  process.env.BUILD_VCS_NUMBER || '',
);
if (!Config.GIT_HASH.trim()) {
  try {
    Config.GIT_HASH = exec('git rev-parse --short HEAD', 1000).stdout.replace('\n', '') || 'Not Available';
  } catch (e) {
    console.log(e);
    Config.GIT_HASH = 'Not Available';
  }
}
Config.BUILD_DATE = new Date();
console.log(Config);

const ROUTER_ROOT = Config.ROUTER_PATH || '';

// Default
gulp.task('default', () => {
  if (prodMode) {
    runSequence(
      ['clean-build', 'clean-dist'],
      ['js', 'less', 'svgstore', 'images'],
      'versioning',
      'jade', 'minify-html',
      'clean-build',
    );
  } else {
    runSequence(
      ['clean-build', 'clean-dist'],
      ['js', 'less', 'svgstore', 'images'],
      'jade',
    );
  }
});

// Jade
gulp.task('jade', () => {
  const LOCALS = {
    __appVersion: Date.now(),
    __PROD: prodMode,
    __LIVE: !!argv.livecss === true,
    __rev: {},
    __ROOT: ROUTER_ROOT,
    __Config: Config,
  };

  if (prodMode) {
    // eslint-disable-next-line import/no-dynamic-require
    LOCALS.__rev = require(path.join(process.cwd(), gulpSlash('public/dist/rev-manifest.json')));
  }

  return gulp.src([
    'app/assets/jade/**/*.jade',
    '!app/assets/jade/**/layout.jade',
    '!app/assets/jade/templates/*',
  ])
    .pipe(gulpSlash())
    .pipe(plumber())
    .pipe(jade({
      locals: LOCALS,
      pretty: true,
    }))
    .pipe(gulp.dest('./public'));
});

gulp.task('minify-html', () => gulp.src('./public/*.html')
  .pipe(htmlmin({
    collapseWhitespace: true,
    minifyJS: true,
    minifyCSS: true,
    caseSensitive: true,
  }))
  .pipe(gulp.dest('./public')));

// SVG
gulp.task('svgstore', () => gulp
  .src('./app/assets/svg/**/*.svg',
    { base: path.join(process.cwd(), gulpSlash('app/assets/svg')) })
  .pipe(gulpSlash())
  .pipe(plumber())
  .pipe(rename((thispath) => {
    const name = thispath.dirname.split(path.sep);
    name.push(thispath.basename);
    thispath.basename = name.join('-');
  }))
  .pipe(svgMin())
  .pipe(svgStore({ inlineSvg: true }))
  .pipe(rename({ basename: 'assets' }))
  .pipe(gulp.dest('./public/build/svg')));

// Less
gulp.task('less', () => gulp.src('./app/assets/less/index.less')
  .pipe(gulpSlash())
  .pipe(plumber())
  .pipe(less())
  .on('error', (err) => {
    console.log(chalk.red(err.message));
    this.emit('end');
  })
  .pipe(autoprefixer(
    { browsers: AUTOPREFIXER_BROWSERS },
  ))
// Inline base64 Images
  .pipe(gulpif(prodMode, base64({
    baseDir: path.join(process.cwd(), gulpSlash('app/assets/images')),
    maxImageSize: 8 * 1024, // 8KB
    debug: true,
  })))
  .pipe(gulpif(prodMode, minifyCss()))
  .pipe(gulp.dest('./public/build/css')));

// Images
gulp.task('images', () => {
  if (prodMode) {
    return gulp.src('./app/assets/images/**/*.*',
      { base: path.join(process.cwd(), '/app/assets/images') })
      .pipe(gulpSlash())
      .pipe(plumber())
    // .pipe(gulpImage())
      .pipe(fileSize({ title: 'images/' }))
      .pipe(gulp.dest('./public/dist/images'))
      .pipe(gulp.dest('./public/images'));
  }
  return gulp.src('./app/assets/images/**/*.*',
    { base: path.join(process.cwd(), '/app/assets/images') })
    .pipe(gulpSlash())
    .pipe(plumber())
    .pipe(gulp.dest('./public/build/images'))
    .pipe(gulp.dest('./public/images'));
});

// Versioning
gulp.task('clean-dist', () => gulp.src('./public/dist', { read: false })
  .pipe(gulpSlash())
  .pipe(plumber())
  .pipe(vinylPaths(del)));
gulp.task('clean-build', () => {
  if (prodMode) {
    return gulp.src('./public/build', { read: false })
      .pipe(gulpSlash())
      .pipe(plumber())
      .pipe(vinylPaths(del));
  }
  return true;
});

gulp.task('versioning', () => gulp.src([
  './public/build/css/*.css',
  './public/build/js/*.js',
  './public/build/svg/*.svg'],
{ base: path.join(process.cwd(), 'public/build') })
  .pipe(gulpSlash())
  .pipe(plumber())
  .pipe(rev())
  .pipe(fileSize({ showFiles: true, gzip: true }))
  .pipe(gulp.dest('./public/dist'))
  .pipe(rev.manifest())
  .pipe(gulp.dest('./public/dist')));

// Compile JS with Webpack
gulp.task('js', (cb) => {
  let conf;

  // Add prod optimizations
  if (prodMode) {
    console.log(chalk.red('Using prod config'));
    conf = WebPackConfig('prod');
  } else {
    conf = WebPackConfig();
  }

  webpack(conf, (err, stats) => {
    try {
      // Handle errors
      const jsonStats = stats.toJson();

      if (prodMode) {
        require('fs').writeFile(
          path.join(process.cwd(), gulpSlash('public/dist/webpack.stats.json')),
          JSON.stringify(jsonStats),
          (err1) => {
            if (err1) {
            // return console.log(err);
            }
            console.log('Webpack profile stats was saved!');
          },
        );
      }

      if (err || jsonStats.errors.length) {
        console.log(chalk.red(errorParser(err || jsonStats.errors)));
      }
    } catch (e) {
      console.error(e);
    }
    return cb();
  });
});

// Regular Webserver
if (!prodMode) {
  gulp.task('webserver', () => {
    webserver.create({
      path: './public',
      open: true,
      port: PORT,
      fallback: 'index.html',
    })
      .start(() => {

      });
  });
}

// Live Webserver
if (!prodMode) {
  gulp.task('live-webserver', () => {
    const conf = WebPackConfig('live', PORT);

    new WebpackDevServer(webpack(conf), {
      contentBase: './public',
      hot: true,
      historyApiFallback: true,
      inline: true,
      watchOptions: {
        aggregateTimeout: 0,
      },
      quiet: false,
    })
      .listen(PORT, 'localhost', (err) => {
        if (err) {
          console.log(err);
        }
        console.log(chalk.red(`Listening at localhost:${PORT}`));
      });
  });
}

// Watcher
if (!prodMode) {
  gulp.task('watch', () => {
    function runner(tasks) {
      return () => {
        gulp.start(tasks);
      };
    }

    watch([gulpSlash('./app/assets/svg/**/*.svg'), gulpSlash('!public/**/*')],
      { read: false }, runner(['svgstore']));

    watch([gulpSlash('./app/assets/less/**/*.less'), gulpSlash('!public/**/*')],
      { read: false }, runner(['less']));

    watch([gulpSlash('./app/assets/jade/**/*.jade'), gulpSlash('!public/**/*')],
      { read: false }, runner(['jade']));

    // Run jade on watch for liveCSS
    if (argv.livecss === true) {
      gulp.start(['jade']);
    }

    // Regular or live server:
    if (argv.live === true) {
      gulp.start(['live-webserver']);
    } else {
      gulp.start(['webserver']);

      watch([gulpSlash('./app/**/*.js'), gulpSlash('./app/**/*.jsx')],
        { read: false }, runner(['js']));
    }
  });
}
